<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jky.boot</groupId>
		<artifactId>jky-jeecg-boot-cdtpublic-parent</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>
	
	<artifactId>jky-jeecg-boot-cdtpublic-common</artifactId>
	
	<properties>
	</properties>
	
	<dependencies>
		<!-- jeecg-boot通用接口内部扩展依赖 -->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-base-common</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpmime -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.5.13</version>
		</dependency>

		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-security-patches-auth-starter</artifactId>
			<version>1.0</version>
		</dependency>
		<!--“任意文件上传”漏洞修复-->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-security-patches-file-starter</artifactId>
			<version>1.0</version>
		</dependency>
		<!--“SQL 注入”漏洞修复-->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-security-patches-sql-starter</artifactId>
			<version>1.1</version>
		</dependency>
		<!-- 请求参数被 waf 拦截解决方案-->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-security-patches-dict-starter</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
		</dependency>

	</dependencies>
</project>
