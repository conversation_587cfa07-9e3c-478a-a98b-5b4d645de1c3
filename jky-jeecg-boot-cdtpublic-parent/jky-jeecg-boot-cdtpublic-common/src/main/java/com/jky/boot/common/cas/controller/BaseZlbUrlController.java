package com.jky.boot.common.cas.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jky.boot.common.cas.entity.BaseZlbUrl;
import com.jky.boot.common.cas.service.IBaseZlbUrlService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 浙里办URL配置信息
 * @Author: jeecg-boot
 * @Date:   2023-06-14
 * @Version: V1.0
 */
@Api(tags="base_zlb_url")
@RestController
@RequestMapping("/cas/baseZlbUrl")
@Slf4j
public class BaseZlbUrlController extends JeecgController<BaseZlbUrl, IBaseZlbUrlService> {
	@Autowired
	private IBaseZlbUrlService baseZlbUrlService;
	
	/**
	 * 分页列表查询
	 *
	 * @param baseZlbUrl
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "base_zlb_url-分页列表查询")
	@ApiOperation(value="base_zlb_url-分页列表查询", notes="base_zlb_url-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BaseZlbUrl>> queryPageList(BaseZlbUrl baseZlbUrl,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BaseZlbUrl> queryWrapper = QueryGenerator.initQueryWrapper(baseZlbUrl, req.getParameterMap());
		Page<BaseZlbUrl> page = new Page<BaseZlbUrl>(pageNo, pageSize);
		IPage<BaseZlbUrl> pageList = baseZlbUrlService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param baseZlbUrl
	 * @return
	 */
	@AutoLog(value = "base_zlb_url-添加")
	@ApiOperation(value="base_zlb_url-添加", notes="base_zlb_url-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_url:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BaseZlbUrl baseZlbUrl) {
		baseZlbUrlService.save(baseZlbUrl);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param baseZlbUrl
	 * @return
	 */
	@AutoLog(value = "base_zlb_url-编辑")
	@ApiOperation(value="base_zlb_url-编辑", notes="base_zlb_url-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_url:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BaseZlbUrl baseZlbUrl) {
		baseZlbUrlService.updateById(baseZlbUrl);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "base_zlb_url-通过id删除")
	@ApiOperation(value="base_zlb_url-通过id删除", notes="base_zlb_url-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_url:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		baseZlbUrlService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "base_zlb_url-批量删除")
	@ApiOperation(value="base_zlb_url-批量删除", notes="base_zlb_url-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_url:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.baseZlbUrlService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "base_zlb_url-通过id查询")
	@ApiOperation(value="base_zlb_url-通过id查询", notes="base_zlb_url-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BaseZlbUrl> queryById(@RequestParam(name="id",required=true) String id) {
		BaseZlbUrl baseZlbUrl = baseZlbUrlService.getById(id);
		if(baseZlbUrl==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(baseZlbUrl);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param baseZlbUrl
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:base_zlb_url:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, BaseZlbUrl baseZlbUrl) {
//        return super.exportXls(request, baseZlbUrl, BaseZlbUrl.class, "base_zlb_url");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("base_zlb_url:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, BaseZlbUrl.class);
//    }

}
