package com.jky.boot.common.entity;

import lombok.Data;

public class ErrorCode {
    public static final int ACCESS_SUCCESS = 0;//执行成功/数据上传成功
    public static final String ACCESS_SUCCESS_MSG = "执行成功";
    public static final int ACCESS_FAILURE = 1;//执行失败
    public static final String ACCESS_FAILURE_MSG = "执行失败";
    public static final int NO_SERVICE = 100;     //请求的服务/资源不存在
    public static final String NO_SERVICE_MSG = "请求的服务或资源不存在";
    public static final int DATA_ERROR = 200;  //数据格式错误，无法正确解析
    public static final String DATA_ERROR_MSG = "数据格式错误，无法正确解析";
    public static final int TIMESTAMP_ERROR = 201;//时间戳重复或错误
    public static final String TIMESTAMP_ERROR_MSG = "时间戳重复或错误";
    public static final int PARAMETER_ERROR = 202;//缺少参数
    public static final String PARAMETER_ERROR_MSG = "参数缺失";
    public static final int BUSINESS_ERROR = 203;//业务异常
    public static final int UNAUTHORIZED = 401;//未授权
}
