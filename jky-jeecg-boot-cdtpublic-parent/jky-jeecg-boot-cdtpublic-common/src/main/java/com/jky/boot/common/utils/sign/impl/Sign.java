package com.jky.boot.common.utils.sign.impl;

import com.jky.boot.common.utils.EncodeUtil;
import com.jky.boot.common.utils.sign.ISign;
import com.sun.org.apache.xerces.internal.impl.dv.util.HexBin;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;

public class Sign implements ISign {

    public String sign(String data, long timestamp, PrivateKey key) throws Exception {
        return sign(data.getBytes(StandardCharsets.UTF_8), timestamp, key);
    }

    public String sign(String data, PrivateKey key) throws Exception {
        return sign(data.getBytes(StandardCharsets.UTF_8), 0, key);
    }

    public String sign(byte[] data, PrivateKey key) throws Exception {
        return sign(data, 0, key);
    }

    public String sign(byte[] data, PrivateKey key, String encryptType) throws Exception {
        return sign(data, 0, key, encryptType);
    }

    public String sign(byte[] data, long timestamp, PrivateKey key) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA1");
        md.update(data);
        if (timestamp > 0) {
            md.update(EncodeUtil.toBEHTTP(timestamp));
        }

        byte[] hash = md.digest();
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encrypted = cipher.doFinal(hash);
        return HexBin.encode(encrypted);
    }

    public String sign(byte[] data, long timestamp, PrivateKey key, String encryptType) throws Exception {
        MessageDigest md = MessageDigest.getInstance(encryptType);
        md.update(data);
        if (timestamp > 0) {
            md.update(EncodeUtil.toBE(timestamp));
        }

        byte[] hash = md.digest();
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encrypted = cipher.doFinal(hash);
        return HexBin.encode(encrypted);
    }


}
