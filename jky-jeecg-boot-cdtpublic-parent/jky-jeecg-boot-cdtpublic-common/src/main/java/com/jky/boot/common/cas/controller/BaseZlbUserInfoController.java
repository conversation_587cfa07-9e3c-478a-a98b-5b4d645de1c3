package com.jky.boot.common.cas.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jky.boot.common.cas.entity.BaseZlbUserInfo;
import com.jky.boot.common.cas.service.IBaseZlbUserInfoService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 浙里办法人信息表
 * @Author: jeecg-boot
 * @Date:   2023-06-14
 * @Version: V1.0
 */
@Api(tags="base_zlb_user_info")
@RestController
@RequestMapping("/cas/baseZlbUserInfo")
@Slf4j
public class BaseZlbUserInfoController extends JeecgController<BaseZlbUserInfo, IBaseZlbUserInfoService> {
	@Autowired
	private IBaseZlbUserInfoService baseZlbUserInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param baseZlbUserInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "base_zlb_user_info-分页列表查询")
	@ApiOperation(value="base_zlb_user_info-分页列表查询", notes="base_zlb_user_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BaseZlbUserInfo>> queryPageList(BaseZlbUserInfo baseZlbUserInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BaseZlbUserInfo> queryWrapper = QueryGenerator.initQueryWrapper(baseZlbUserInfo, req.getParameterMap());
		Page<BaseZlbUserInfo> page = new Page<BaseZlbUserInfo>(pageNo, pageSize);
		IPage<BaseZlbUserInfo> pageList = baseZlbUserInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param baseZlbUserInfo
	 * @return
	 */
	@AutoLog(value = "base_zlb_user_info-添加")
	@ApiOperation(value="base_zlb_user_info-添加", notes="base_zlb_user_info-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_user_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BaseZlbUserInfo baseZlbUserInfo) {
		baseZlbUserInfoService.save(baseZlbUserInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param baseZlbUserInfo
	 * @return
	 */
	@AutoLog(value = "base_zlb_user_info-编辑")
	@ApiOperation(value="base_zlb_user_info-编辑", notes="base_zlb_user_info-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_user_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BaseZlbUserInfo baseZlbUserInfo) {
		baseZlbUserInfoService.updateById(baseZlbUserInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "base_zlb_user_info-通过id删除")
	@ApiOperation(value="base_zlb_user_info-通过id删除", notes="base_zlb_user_info-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_user_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		baseZlbUserInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "base_zlb_user_info-批量删除")
	@ApiOperation(value="base_zlb_user_info-批量删除", notes="base_zlb_user_info-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_user_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.baseZlbUserInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "base_zlb_user_info-通过id查询")
	@ApiOperation(value="base_zlb_user_info-通过id查询", notes="base_zlb_user_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BaseZlbUserInfo> queryById(@RequestParam(name="id",required=true) String id) {
		BaseZlbUserInfo baseZlbUserInfo = baseZlbUserInfoService.getById(id);
		if(baseZlbUserInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(baseZlbUserInfo);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param baseZlbUserInfo
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:base_zlb_user_info:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, BaseZlbUserInfo baseZlbUserInfo) {
//        return super.exportXls(request, baseZlbUserInfo, BaseZlbUserInfo.class, "base_zlb_user_info");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("base_zlb_user_info:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, BaseZlbUserInfo.class);
//    }

}
