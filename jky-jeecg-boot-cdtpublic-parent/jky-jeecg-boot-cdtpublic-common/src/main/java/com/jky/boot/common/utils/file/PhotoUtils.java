package com.jky.boot.common.utils.file;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

/**
 * 图片操作
 *
 * <AUTHOR>
 * @version 2024-08-05 15:18
 */
@Slf4j
public class PhotoUtils {

    /**
     * 图片大小压缩
     *
     * @param file 上传的 MultipartFile
     * @return 压缩后的 inputStream
     */
    public static InputStream compress(MultipartFile file) throws IOException {
        ByteArrayOutputStream res = new ByteArrayOutputStream();
        // 允许文件最大的大小（kb）
        long fileMax = 3 * 1024 * 1024;
        long curSize = file.getSize();
        if (curSize <= fileMax) {
            return file.getInputStream();
        }
        double factor = 1;
        // 计算系数
        while(curSize * factor > fileMax) {
            factor /= 2;
        }
        Thumbnails.of(file.getInputStream()).scale(Math.sqrt(factor)).outputQuality(1).toOutputStream(res);
        ByteArrayInputStream tarInput = new ByteArrayInputStream(res.toByteArray());
        res.close();
        return tarInput;
    }
}
