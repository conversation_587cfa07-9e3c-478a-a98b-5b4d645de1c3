package com.jky.boot.common.utils.verify;

import java.security.PrivateKey;
import java.security.cert.X509Certificate;

public interface IVerify {
    boolean verify(String data, long timestamp, String encodedEncryptedStr,
                   PrivateKey privatekey) throws Exception;

    boolean verify(String data, long timestamp, String encodedEncryptedStr,
                   X509Certificate userCert) throws Exception;

	/*public boolean verify256(String data, long timestamp, byte[] encodedEncryptedStr,
			X509Certificate userCert) throws Exception;*/
}
