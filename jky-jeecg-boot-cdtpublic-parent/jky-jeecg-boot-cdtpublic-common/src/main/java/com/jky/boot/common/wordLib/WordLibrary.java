package com.jky.boot.common.wordLib;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Set;
import org.springframework.core.io.ClassPathResource;

/**
 * 敏感词库
 *
 * <AUTHOR>
 * @version 2024-02-18  18:35
 */
public class WordLibrary {
    private static final Set<String> sensitiveWords;

    private static final String filePath = "/library/sensitive_words_lines.dic";

    static {
        sensitiveWords = new HashSet<>();
        load(filePath);
    }

    public static void addSensitiveWord(String word) {
        sensitiveWords.add(word);
    }

    public static boolean isSensitiveWord(String word) {
        return sensitiveWords.contains(word);
    }

    public static void load(String filePath) {
        BufferedReader reader;
        try {
            InputStream inputStream = new ClassPathResource(filePath).getInputStream();
            InputStreamReader tmp = new InputStreamReader(inputStream);
            reader = new BufferedReader(tmp);
            String line;
            while ((line = reader.readLine()) != null) {
                addSensitiveWord(line);
            }
            reader.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
