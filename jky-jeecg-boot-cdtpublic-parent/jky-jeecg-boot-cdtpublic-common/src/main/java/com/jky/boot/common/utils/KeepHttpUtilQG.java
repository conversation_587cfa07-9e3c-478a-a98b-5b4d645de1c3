package com.jky.boot.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jky.boot.common.utils.http.RestTemplateUtils;
import com.jky.boot.common.utils.sign.ISign;
import com.jky.boot.common.utils.sign.impl.Sign;
import com.jky.boot.common.utils.verify.IVerify;
import com.jky.boot.common.utils.verify.impl.Verify;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.*;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class KeepHttpUtilQG {

    private static final String encode = "utf-8";

    public static String SJ_VALID_USER;

    @Value("${sj_valid_user:111271CA11D}")
    public void setSjValidUser(String sjValidUser) {
        KeepHttpUtilQG.SJ_VALID_USER = sjValidUser;
    }

    public static CommonResponse sendHttpClientPost(String path, String data) {
        CommonResponse response;
        try {
            path = toUrl(path, data);
            log.info("pushUrl: {}", path);
            log.info("pushData: {}", data);
            // 使用post方式提交数据
            ResponseEntity<CommonResponse> responseEntity = RestTemplateUtils.postForEntity(path, data, CommonResponse.class);
            // 执行post请求，并获取服务器端的响应HttpResponse
            // 获取服务器端返回的状态码和输入流，将输入流转换成字符串
            if (!responseEntity.getStatusCode().is2xxSuccessful() || Objects.isNull(responseEntity.getBody())) {
                response = new CommonResponse();
                response.setErrorcode(responseEntity.getStatusCode().value());
                response.setMessage("访问失败");
                return response;
            }
            response = new CommonResponse(JSONObject.toJSONString(responseEntity.getBody()));
        } catch (Exception e) {
            log.error("KeepHttpUtilQG sendHttpClientPost, error :", e);
            response = new CommonResponse();
            response.setErrorcode(1);
            response.setMessage("访问失败");
        }
        log.info("pushResponse：{}", JSONObject.toJSONString(response));
        response.setUrl(path);
        return response;
    }

    public static CommonResponse sendHttpClientPostSJ(String path, String data) {
        CommonResponse response = null;
        String result = null;
        try {
            path = toUrlSj(path, data);
            // 使用post方式提交数据
            HttpPost httpPost = new HttpPost(path);
            StringEntity entity = new StringEntity(data, encode);// 解决中文乱码问题
            entity.setContentEncoding(encode);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            // 执行post请求，并获取服务器端的响应HttpResponse
            HttpResponse httpResponse = new DefaultHttpClient()
                    .execute(httpPost);
            // 获取响应消息实体
            HttpEntity httpEntity = httpResponse.getEntity();
            // 获取服务器端返回的状态码和输入流，将输入流转换成字符串
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                InputStream inputStream = httpResponse.getEntity().getContent();
                result = changeInputStream(inputStream);
//                log.info("KeepHttpUtilQG sendHttpClientPostSJ, result ：{}", result);
                response = new CommonResponse(result);
            } else {
                response = new CommonResponse();
                response.setErrorcode(httpResponse.getStatusLine()
                        .getStatusCode());
                response.setMessage("访问失败");
            }
        } catch (Exception e) {
            log.error("KeepHttpUtilQG sendHttpClientPostSJ, 异常", e);
            response = new CommonResponse();
            response.setErrorcode(1);
            response.setMessage(e.getMessage());
        }
        response.setUrl(path);
        return response;
    }

    public static CommonResponse sendHttpClientGetSJ(String path) {
        CommonResponse response;
        String result;

        try (DefaultHttpClient defaultHttpClient = new DefaultHttpClient()) {
            path = toUrlSj(path, "1");
            HttpGet httpGet = new HttpGet(path);
            CloseableHttpResponse httpResponse = defaultHttpClient.execute(httpGet);
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                InputStream inputStream = httpResponse.getEntity().getContent();
                result = changeInputStream(inputStream);
//                log.info("KeepHttpUtilQG sendHttpClientGetSJ, result ：{}", result);
                response = new CommonResponse(result);
            } else {
                response = new CommonResponse();
                response.setErrorcode(httpResponse.getStatusLine()
                        .getStatusCode());
                response.setMessage("访问失败");
            }
        } catch (Exception e) {
            log.error("KeepHttpUtilQG sendHttpClientGetSJ, error", e);
            e.printStackTrace();
            response = new CommonResponse();
            response.setErrorcode(1);
            response.setMessage(e.getMessage());
        }
        response.setUrl(path);
        return response;
    }

//    public static CommonResponse sendHttpClientGetSJ(String path) {
//        CommonResponse response;
//        String result;
//        CloseableHttpClient  httpClient=null;
//        CloseableHttpResponse httpResponse=null;
//        try {
//            path = toUrlSj(path, "1");
//            RequestConfig requestConfig =
//                    RequestConfig.custom().setConnectTimeout(10000).setConnectionRequestTimeout(5000).build();
//            // 创建HttpClient构建器
//            httpClient =   HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).build();
//            HttpGet httpGet = new HttpGet(path);
//            // 执行post请求，并获取服务器端的响应HttpResponse
//            httpResponse = httpClient.execute(httpGet);
//            // 获取服务器端返回的状态码和输入流，将输入流转换成字符串
//            if (httpResponse.getStatusLine().getStatusCode() == 200) {
//                InputStream inputStream = httpResponse.getEntity().getContent();
//                result = changeInputStream(inputStream);
//                log.info("KeepHttpUtilQG sendHttpClientGetSJ, result ：{}",result);
//                response = new CommonResponse(result);
//            } else {
//                response = new CommonResponse();
//                response.setErrorcode(httpResponse.getStatusLine()
//                        .getStatusCode());
//                response.setMessage("访问失败");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            response = new CommonResponse();
//            response.setErrorcode(1);
//            response.setMessage(e.getMessage());
//        }finally {
//            if (httpResponse != null) {
//                try {
//                    httpResponse.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            if (httpClient != null) {
//                try {
//                    httpClient.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        response.setUrl(path);
//        return response;
//    }


    public static String sendHttpClientUpload(String filePath, String imagurl) {
        CommonResponse response = null;
        String result = null;
        String id = null;
        File file = new File(filePath);
        try {
            if (!file.exists()) {
                return "1"; //文件不存在
            } else {
                String path = toUrl(imagurl, getBytes(file));
                HttpPost Post = new HttpPost(path);
                FileBody fileBody = new FileBody(file);
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
                builder.addPart("file", fileBody);
                HttpEntity entity = builder.build();
                Post.setEntity(entity);
                HttpResponse httpResponse = new DefaultHttpClient().execute(Post);
                if (httpResponse.getStatusLine().getStatusCode() == 200) {
                    InputStream inputStream = httpResponse.getEntity()
                            .getContent();
                    result = changeInputStream(inputStream);
                    response = new CommonResponse(result);
                    response.setUrl(path);
                    JSONObject jsonObject = JSON.parseObject(response.getData().toString());
                    id = jsonObject.get("id").toString();
                } else {
                    return "2"; //访问失败
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "3"; //出错
        }
        return id;
    }



    /**
     * 向省级服务 http-api 上传文件
     * @param filePath
     * @param imagurl
     * @return
     */
    public static CommonResponse sendHttpApiUpload(String filePath, String imagurl) {
        CommonResponse response = null;
        String result = null;
        String id = null;
        File file = new File(filePath);
        try {
            if (!file.exists()) {
                //return "1"; //文件不存在
                return null; //文件不存在
            } else {
                String path = toSjUploadUrl(imagurl, getBytes(file));
                HttpPost Post = new HttpPost(path);
                FileBody fileBody = new FileBody(file);
                MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
                builder.addPart("file", fileBody);
                HttpEntity entity = builder.build();
                Post.setEntity(entity);
                HttpResponse httpResponse = new DefaultHttpClient().execute(Post);
                if (httpResponse.getStatusLine().getStatusCode() == 200) {
                    InputStream inputStream = httpResponse.getEntity()
                            .getContent();
                    result = changeInputStream(inputStream);
                    response = new CommonResponse(result);
                    response.setUrl(path);
                    //String data = response.getResult();
                    return response;
                } else {
                    //return "2"; //访问失败
                    return null; //出错
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            //return "3"; //出错
            return null; //出错
        }
    }

    public static String toUrl(String url, String data) throws Exception {
        long ts = new Date().getTime();
        List<Object> pfxList = CommonUtil.readPfx();
        PrivateKey privateKey = (PrivateKey) pfxList.get(0);
        X509Certificate cert = (X509Certificate) pfxList.get(1);

        ISign sign = new Sign();
        String sign_hex = sign.sign(data, ts, privateKey).replaceAll(" ", "");
        IVerify verify = new Verify();

        url = url
                + "?v=1.0.0.e1&ts="
                + ts
                + "&sign="
                + sign_hex
                + "&user="
                + Long.toHexString(
                Long.parseLong(cert.getSerialNumber().toString()))
                .toUpperCase();
//        log.info("KeepHttpUtilQG toUrl, url ：{},data ：{}", url, data);
        return url;
    }

    public static String toUrlSj(String url, String data) throws Exception {
        long ts = System.currentTimeMillis();
        List<Object> pfxList = CommonUtil.readPfx();
        PrivateKey privateKey = (PrivateKey) pfxList.get(0);
        X509Certificate cert = (X509Certificate) pfxList.get(1);

        ISign sign = new Sign();
        String sign_hex = sign.sign(data, ts, privateKey).replaceAll(" ", "");

        url = url
                + "?v=" + CityUtil.cityCoode + "_1.0.0.e1&ts="
                + ts
                + "&sign="
                + sign_hex
                + "&user="+SJ_VALID_USER;
//        log.info("KeepHttpUtilQG toUrlSj, url ：{},data ：{}", url, data);
        return url;
    }

    public static String toUrl(String url, byte[] data) throws Exception {
        long ts = System.currentTimeMillis();
        List<Object> pfxList = CommonUtil.readPfx();
        PrivateKey privateKey = (PrivateKey) pfxList.get(0);
        X509Certificate cert = (X509Certificate) pfxList.get(1);

        ISign sign = new Sign();
        String sign_hex = sign.sign(data, ts, privateKey).replaceAll(" ", "");
        IVerify verify = new Verify();

        url = url
                + "?v=1.0.0.e1&ts="
                + ts
                + "&sign="
                + sign_hex
                + "&user="
                + Long.toHexString(
                Long.parseLong(cert.getSerialNumber().toString()))
                .toUpperCase();
//        log.info("KeepHttpUtilQG toUrlSj, url ：{},data ：{}", url, data);
        return url;
    }


    /**
     * 向省级服务 http-api 上传文件
     * @param url
     * @param data
     * @return
     * @throws Exception
     */
    public static String toSjUploadUrl(String url, byte[] data) throws Exception {
        long ts = System.currentTimeMillis();
        List<Object> pfxList = CommonUtil.readPfx();
        PrivateKey privateKey = (PrivateKey) pfxList.get(0);
        X509Certificate cert = (X509Certificate) pfxList.get(1);

        ISign sign = new Sign();
        String sign_hex = sign.sign(data, ts, privateKey).replaceAll(" ", "");
        IVerify verify = new Verify();

        url = url
                + "?v="+ CityUtil.cityCoode+".1.0.0.e1&ts="
                + ts
                + "&sign="
                + sign_hex
                + "&user="+SJ_VALID_USER;
        //log.info("KeepHttpUtilQG toSjUploadUrl, url ：{},data ：{}", url, data);
        return url;
    }


    /*
     * // 把从输入流InputStream按指定编码格式encode变成字符串String
     */
    public static String changeInputStream(InputStream inputStream) {

        // ByteArrayOutputStream 一般叫做内存流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int len = 0;
        String result = "";
        if (inputStream != null) {

            try {
                while ((len = inputStream.read(data)) != -1) {
                    byteArrayOutputStream.write(data, 0, len);

                }
                result = byteArrayOutputStream.toString(encode);

            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        return result;
    }

    public static byte[] getBytes(File file) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }


}
