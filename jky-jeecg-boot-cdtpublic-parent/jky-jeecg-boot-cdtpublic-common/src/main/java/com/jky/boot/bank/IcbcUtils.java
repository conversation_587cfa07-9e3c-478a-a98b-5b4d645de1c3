//package com.jky.boot.bank;
//
//
//import com.icbc.api.core.*;
//import com.icbc.api.utils.IcbcEncrypt;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 工商银行工具类
// *
// * <AUTHOR>
// * @version 2024-11-07 9:06
// */
//@Slf4j
//public class IcbcUtils {
//
//    private static final String PRI_KEY = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCGXL5JUyPcZ1QWkz2FsBa+jsCtgw77PJl8gt5u/K63yzJeIx3xDHSWokVbQKolnOzI4mhFbpkFEnsA67Roy6QbcJ+G7n4wVG76fVzHt1EwKZINrVITJGGuYpnzSjoiLr78OH0XAckiWIJr3V1XgvShFV7GILv/B9ZDRWTdACRP7m6+zItExDRhp6EzMgcngtQH+4E8Gm18dVzRGoRqWfd2v2GzawHac80I+VfrSK7LE5OMYN2aMjfANyTSx6Mxl41IbvolXb0ox4uq0c8ezg4hxqn6SoW+2FVHQgzFlsqMiabWqhcEJCNGZSrmLAshA6PwIYpyGifuFpy0ihKwoMB5AgMBAAECggEAKU+emgDcB8olSoOe6NnJRHGzwCWJl6l8L5I1Pb6nIY9yPwthC1ofBqmB1Zq+Na5YBzPFh4OHvM1/0DqoT4g9PsqT4fK2DboIHTAqNKWiasJ9S4M8tvH6rplWaD0sPpJM2lOr8kT0SIEaKHv5VQzIljZx20z5QGJyeGB/sIt4nrJm0rDdRQ+iovDhgmxDXPUfi1TMAfLFg0ZU0TBE6Nr/XCtGVMqVkZIITZ2Mg2AycnaRN4XWietz0aCSGFd5qS4CqCC6mzDo6kt8RFMCDb9D3Y9aqlcfDfvhjoskAq8lNaK+9SJuqRh6SesH75AqhHHx6/6tTeKlWcVAVgeQB8RXAQKBgQDcOCBmhyFss9qYNzrbcLbykWL8v+SRkdefkCY2G76RHm455mahdB1PQTnY/YSckVnHOTsz5korpb8RIbGSWBiq/ohgspc+Ha7Aesbj/oMmRrY1XVdsvUVk8Z++sc/JPSZSAaKInlNx4ubL3cgOz1UclOlquItWiJhAVsQzmLVnaQKBgQCcMXQDvCANZSLWRp8NtyVdySwNlcMq8G/ZQtIhA17ysR1qX2ojAjRRN3Mji3Ki33vno4d2h//B8ekggwy9kxcdKRrh98ZEJ4Zgn3jiuz958w+JtPBV8wckV5tpeIlWePvbTOS/eCV3czsQQYj7EPVDkJJJFZDpRqoQTLL3US/+kQKBgByyQ6aYnUZo+Wc0azsFKRHa2CdPah19/7cPNat/WgnXCU4KM/1+AAO/4CXig1FTr3tCffB5ZdjiNE+rMFR9nllJ6cOQAh1aCNeUKC61j6mBNWUm0Kme0FgjFEnpKcZzbXbqN5/N4dnJU4HlWJh1dAXRcckyWxXfntdjdxWyJJFhAoGAUQRt0wof7hLJsS/MjMa1ASK6UQmfxIQW4DEC4dDnzScFxQlc+m/IR2Blvsg0XDtj/9EZEOiWRzl93TlcwHCtipipsxBcrTl7QF83yxOpZ04mfx11lH+M8tLmcBzv0ATNTwFhnddqR71jo5j6vpAbSEKTPomLMxVOXUoLgrGAHaECgYBVT8XBxPO3AmEMAv7qMMUFXCPDvXky+sSIfjShLW3jyObjWGFdCZM3qAb4QTzd6NKgeeiVp9W61SG/nogzu/WyiFQ9uWMQCJYUw1WhWtdM0kAEUwfqFe3IdtZTyL9MwCgub8TTRUhtLyIyvwV9PhCYVOQMayZtOAEGPpRGpccaug==";
//    private static final String AES_KEY = "dm6VjPu64adOs82AP40fPg==";
//    private static final String URL = "https://web-zj.dccnet.com.cn:8443/api";
//    private static final String API_NAME = "com.icbc.dtpl.zzkj.msgnotify";
//    private static final String APP_ID = "zzkj";
//
//    public static String sendMsg(String jsonString) {
//        try {
//            ApiClient ac = new ApiClient(PRI_KEY);
//            ApiRequest req = new ApiRequest(URL, API_NAME, APP_ID);
//            String encryptData = IcbcEncrypt.encryptContent(jsonString, "AES", AES_KEY, "UTF-8");
//            req.setRequestField("encryptData", encryptData);
//            ApiResponse ar = ac.execute(req);
//            log.info("signBlock: " + ar.getSignBlock());
//            String res = (String) ar.getMap("response").get("encryptData");
//            String decryptRes = IcbcEncrypt.decryptContent(res, "AES", AES_KEY, "UTF-8");
//            log.info("解密: " + decryptRes);
//            return decryptRes;
//        } catch (ApiError e) {
//            log.error("网络错误", e);
//        } catch (ApiFailure e) {
//            log.error("api返回失败", e);
//        } catch (Exception e) {
//            log.error("系统错误", e);
//        }
//        return null;
//    }
//}
