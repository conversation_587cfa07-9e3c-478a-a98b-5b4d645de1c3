package com.jky.boot.common.utils.verify.impl;

import com.jky.boot.common.utils.EncodeUtil;
import com.jky.boot.common.utils.sign.ISign;
import com.jky.boot.common.utils.sign.impl.Sign;
import com.jky.boot.common.utils.verify.IVerify;
import com.sun.org.apache.xerces.internal.impl.dv.util.HexBin;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Arrays;

public class Verify implements IVerify {
    public boolean verify(String data, long timestamp, String encodedEncryptedStr,
                          PrivateKey privatekey) throws Exception {
        ISign is = new Sign();
        String signStr = is.sign(data.getBytes(), timestamp, privatekey, "SHA-256");
        return signStr.equalsIgnoreCase(encodedEncryptedStr);
    }

    public boolean verify(String data, long timestamp, String encodedEncryptedStr,
                          X509Certificate userCert) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(data.getBytes(StandardCharsets.UTF_8));
        if (timestamp > 0) {
            md.update(EncodeUtil.toBE(timestamp));
        }
        byte[] hash = md.digest();

        byte[] encryptedStr = HexBin.decode(encodedEncryptedStr);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, userCert);
        byte[] plain = cipher.doFinal(encryptedStr);
        boolean ok = Arrays.equals(hash, plain);
        return ok;
    }

	/*public boolean verify256(String data, long timestamp, byte[] encodedEncryptedStr,
			X509Certificate userCert) throws Exception {
		MessageDigest md = MessageDigest.getInstance("SHA-256");
		md.update(data.getBytes("utf-8"));
		if(timestamp>0){
			md.update(EncodeUtil.toBE(timestamp));
		}
		byte[] hash = md.digest();

		byte[] encryptedStr = HexBin.decode(AbstractBasePacket.hex2str(encodedEncryptedStr));//encodedEncryptedStr;//HexBin.decode(encodedEncryptedStr);
		Cipher cipher = Cipher.getInstance("RSA");
		cipher.init(Cipher.DECRYPT_MODE, userCert);
		byte[] plain = cipher.doFinal(encryptedStr);
		boolean ok = Arrays.equals(hash, plain);
		return ok;
	}*/
}
