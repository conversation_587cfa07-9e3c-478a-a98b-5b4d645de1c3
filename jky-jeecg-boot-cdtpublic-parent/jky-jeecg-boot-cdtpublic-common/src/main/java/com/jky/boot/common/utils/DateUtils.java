package com.jky.boot.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.*;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyyMM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd 例如2025-04-01
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期带横线 即年-月-日 如2018-08-08
     */
    public static final String dateCross() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy-MM-dd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    public static String yearCompare(Date fromDate, Date toDate) {
        Map<String, Integer> stringIntegerMap = dayComparePrecise(fromDate, toDate);
        double month = stringIntegerMap.get("month");
        double year = stringIntegerMap.get("year");
        //返回2位小数，并且四舍五入
        DecimalFormat df = new DecimalFormat("######0.0");
        return df.format(year + month / 12);
    }

    public static Map<String, Integer> dayComparePrecise(Date fromDate, Date toDate) {
        Calendar from = Calendar.getInstance();
        from.setTime(fromDate);
        Calendar to = Calendar.getInstance();
        to.setTime(toDate);

        int fromYear = from.get(Calendar.YEAR);
        int fromMonth = from.get(Calendar.MONTH);
        int fromDay = from.get(Calendar.DAY_OF_MONTH);

        int toYear = to.get(Calendar.YEAR);
        int toMonth = to.get(Calendar.MONTH);
        int toDay = to.get(Calendar.DAY_OF_MONTH);
        int year = toYear - fromYear;
        int month = toMonth - fromMonth;
        int day = toDay - fromDay;
        Map<String, Integer> map = new HashMap<>();
        map.put("year", year);
        map.put("month", month);
        map.put("day", day);
        return map;
    }

    public static String getTime(Date date, int day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -day);
        return sdf.format(cal.getTime());
    }


    public static Date addSecond(Date sDate, int s) {
        try {
            Date date = parseDate(sDate);
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(date);
            calendar.add(Calendar.SECOND, s);// 增加一秒
            return calendar.getTime();
        } catch (Exception e) {
            return null;
        }
    }


    // 获取当天的开始时间
    public static Date getDayBegin() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // 获取当天的结束时间
    public static Date getDayEnd() {
        Calendar cal = new GregorianCalendar();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    //    获取当前星期
    public static Integer getCurrentWeek() {
        Calendar cal = new GregorianCalendar();
        Integer week = cal.get(Calendar.DAY_OF_WEEK);
        if (week == 1) {
            return 7;
        } else {
            return week - 1;
        }
    }

    public static Integer getWeek(Date date) {
        Calendar cal = new GregorianCalendar();
        cal.setTime(date);
        Integer week = cal.get(Calendar.DAY_OF_WEEK);
        if (week == 1) {
            return 7;
        } else {
            return week - 1;
        }
    }


    public static String hoursMinutes(Integer minutes) {
        if (minutes == null) {
            return null;
        }
        Integer hour = (minutes / 60);
        Integer minute = (minutes % 60);
        return hour + ":" + (minute / 10 == 0 ? "0" + minute : minute);
    }


    public static Integer getMinute(String time) {
        if (StringUtils.isNotEmpty(time)) {
            String[] times = time.split(":");
            return Integer.valueOf(times[0]) * 60 + Integer.valueOf(times[1]);
        }
        return null;
    }

    public static Date setMinutes(final Date date, String time) {
        if (StringUtils.isNotEmpty(time)) {
            final Calendar c = Calendar.getInstance();
            c.setLenient(false);
            c.setTime(date);
            String[] times = time.split(":");
            c.set(Calendar.MINUTE, Integer.valueOf(times[1]));
            c.set(Calendar.HOUR_OF_DAY, Integer.valueOf(times[0]));
            return c.getTime();
        }
        return null;

    }

    public static Date setMinutesHour(Date date, Integer times) {
        if (times != null) {
            final Calendar c = Calendar.getInstance();
            c.setLenient(false);
            c.setTime(date);
            c.set(Calendar.MINUTE, times % 60);
            c.set(Calendar.HOUR_OF_DAY, times / 60);
            return c.getTime();
        }
        return null;

    }

    /**
     * 获取季度的年份
     *
     * @param times 例如：times 为 -1，上季度；0，当前季度；1，下季度
     * @return 季度年份呢
     */
    public static int getQuarterYear(Integer times) {
        LocalDate now = LocalDate.now();
        LocalDate tarQuarter = now.plusMonths(3L * times);
        return tarQuarter.getYear();
    }

    /**
     * 获取季度
     *
     * @param times 例如：times 为 -1，上季度；0，当前季度；1，下季度
     * @return 季度
     */
    public static int getQuarter(Integer times) {
        LocalDate currentDate = LocalDate.now();
        LocalDate tarQuarter = currentDate.plusMonths(3L * times);
        return tarQuarter.get(IsoFields.QUARTER_OF_YEAR);
    }

    /**
     * LocalDate 转 String
     */
    public static String localDateToString(LocalDate rec, String pattern) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        return rec.format(df);
    }

    /**
     * LocalDate 转 String
     */
    public static Date parseDate(String date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date res = null;
        try {
            res = sdf.parse(date);
        } catch (Exception ignored) {

        }
        return res;
    }

    /**
     * String 类型日期转标准格式 String 类型日期
     */
    public static String parseStringToString(String dateStr, String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Date tar;
        try {
            tar = format.parse(dateStr);
        } catch (Exception e) {
            return null;
        }
        SimpleDateFormat tarFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        return tarFormat.format(tar);
    }

    /**
     * 时间加减
     *
     * @param date
     * @param calendarField ：Calendar.YEAR/ Calendar.MONTH /Calendar.DAY
     * @param amount
     * @return
     */
    public static Date add(final Date date, final int calendarField, final int amount) {
        if (date == null) {
            return null;
        }
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }

    public static String addDays(String date, int amount) {
        return LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE).plusDays(amount).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
