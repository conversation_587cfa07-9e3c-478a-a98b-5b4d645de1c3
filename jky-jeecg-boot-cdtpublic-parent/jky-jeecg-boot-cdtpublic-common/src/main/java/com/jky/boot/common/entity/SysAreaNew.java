package com.jky.boot.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * @Description: sys_area_new
 * @Author: jeecg-boot
 * @Date:   2023-06-21
 * @Version: V1.0
 */
@Data
@TableName("sys_area_new")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_area_new对象", description="sys_area_new")
public class SysAreaNew implements Serializable {
    private static final long serialVersionUID = 1L;

	/**areaId*/
	@Excel(name = "areaId", width = 15)
    @ApiModelProperty(value = "areaId")
    private String areaId;
	/**fid*/
	@Excel(name = "fid", width = 15)
    @ApiModelProperty(value = "fid")
    private String fid;
	/**areaName*/
	@Excel(name = "areaName", width = 15)
    @ApiModelProperty(value = "areaName")
    private String areaName;
	/**memo*/
	@Excel(name = "memo", width = 15)
    @ApiModelProperty(value = "memo")
    private String memo;
	/**areaLeave*/
	@Excel(name = "areaLeave", width = 15)
    @ApiModelProperty(value = "areaLeave")
    private Integer areaLeave;
	/**dailName*/
	@Excel(name = "dailName", width = 15)
    @ApiModelProperty(value = "dailName")
    private String dailName;
	/**oldFlag*/
	@Excel(name = "oldFlag", width = 15)
    @ApiModelProperty(value = "oldFlag")
    private Integer oldFlag;
	/**areaShort*/
	@Excel(name = "areaShort", width = 15)
    @ApiModelProperty(value = "areaShort")
    private String areaShort;
}
