package com.jky.boot.timeJob;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.base.dao.StuLogoutApply;
import com.jky.boot.base.service.IStuLogoutApplyService;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.CancelAccountUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2024-03-07  15:08
 */
@Component
@RestController
@Slf4j
public class StuLogoutJob {

    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private IStudentinfoService studentInfoService;
    @Autowired
    private IStuLogoutApplyService stuLogoutApplyService;

    /**
     * 注销时，校验划转给驾校资金结果，划转成功时，生成退费订单
     *
     * 注销前金额已经结算完成，不需要使用这里
     */
//    @XxlJob(value = "logoutTrans")
//    @PostMapping("/logoutTrans")
    public ReturnT<?> logoutTrans(String stunum) {
        List<BankOrder> orders = bankOrderService.list(
                Wrappers.lambdaQuery(BankOrder.class)
                        .eq(BankOrder::getTransFlag, 2)
//                        .eq(BankOrder::getOrdertype, 6)
        );
        for (BankOrder order : orders) {
            if (order.getStatus() == 0) {
                XxlJobLogger.log("{}订单银行未回执，跳过", order.getId());
                continue;
            }
            if (order.getStatus() == 2) {
                XxlJobLogger.log("{}订单划转失败", order.getId());
            }
            if (order.getStatus() == 1) {
                Result<?> result = studentInfoService.unfreeze(order.getStunum(), 2, null);
                if (!result.isSuccess()) {
                    XxlJobLogger.log("{}订单划转成功，但投递解冻消息失败", order.getId());
                } else {
                    XxlJobLogger.log("{}订单划转成功，投递解冻消息成功", order.getId());
                }
            }
            // 标记该转校划转订单已核查
            order.setTransFlag(21);
            bankOrderService.updateById(order);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 校验划转结果
     */
    @XxlJob(value = "logoutUnfreeze")
    @PostMapping("/logoutUnfreeze")
    public ReturnT<?> logoutUnfreeze(String param) {
        List<BankOrder> orders = bankOrderService.list(
                Wrappers.lambdaQuery(BankOrder.class)
                        .eq(BankOrder::getTransFlag, 2)
                        .eq(BankOrder::getOrdertype, 4)
        );
        for (BankOrder order : orders) {
            try {
                if (order.getStatus() == 0) {
                    XxlJobLogger.log("{}订单银行未回执，跳过", order.getId());
                    continue;
                }
                if (order.getStatus() == 2) {
                    XxlJobLogger.log("{}订单划转失败", order.getId());
                }
                if (order.getStatus() == 1) {
                    Studentinfo stu = studentInfoService.getOneByStuNum(order.getStunum());
                    StuLogoutApply logoutApply = stuLogoutApplyService.getOne(
                            Wrappers.<StuLogoutApply>lambdaQuery()
                                    .eq(StuLogoutApply::getStunum, order.getStunum())
                                    .eq(StuLogoutApply::getApplydate, stu.getApplydate())
                                    .eq(StuLogoutApply::getAuditStatus, 1)
                                    .orderByDesc(StuLogoutApply::getCreate_time)
                                    .last("limit 1")
                    );
                    Date date = DateUtils.parseDate(stu.getApplydate(), DateUtils.YYYYMMDD);
                    if (Objects.isNull(logoutApply) || logoutApply.getAuditTime().getTime() < date.getTime()) {
                        XxlJobLogger.log("学员未查到注销申请，{}，{}", order.getStunum(), stu.getApplydate());
                        continue;
                    }
                    if (Objects.equals(order.getBankcode(), "9IC3C55B")) {
                        logoutApply.setRemark("建行学员请至手机端解约");
                    } else {
                        CancelAccountUtils.doLogout(stu, logoutApply, "无需审核");
                        XxlJobLogger.log("学员完成注销{}", order.getStunum());
                    }
                    stuLogoutApplyService.updateById(logoutApply);
                }
                // 标记该转校划转订单已核查
                order.setTransFlag(22);
                bankOrderService.updateById(order);
            } catch (Exception e) {
                log.error("校验划转结果异常orderid:{}", order.getId(),e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 建行学员校验是否解约
     */
    @XxlJob(value = "ccbBreakOff")
    public ReturnT<?> ccbBreakOff(String param) {
        List<StuLogoutApply> applyList = stuLogoutApplyService.list(
                Wrappers.<StuLogoutApply>lambdaQuery()
                        .eq(StuLogoutApply::getRemark, "建行学员请至手机端解约")
        );
        for (StuLogoutApply apply : applyList) {
            XxlJobLogger.log(apply.getStunum() + " 开始");
            BankReg bankReg = bankRegService.getOneByStuNum(apply.getStunum());
            if (Objects.nonNull(bankReg) && bankReg.getStatus() != 0) {
                XxlJobLogger.log(apply.getStunum() + " 还未解约");
                continue;
            }
            Studentinfo stu = studentInfoService.getOneByStuNum(apply.getStunum());
            CancelAccountUtils.doLogout(stu, apply, "无需审核");
            stuLogoutApplyService.updateById(apply);
            XxlJobLogger.log(apply.getStunum() + " 完成注销");
        }
        return ReturnT.SUCCESS;
    }
}
