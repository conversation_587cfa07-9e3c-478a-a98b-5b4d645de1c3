package com.jky.boot.timeJob;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jky.boot.core.module.gzpt.entity.TMMsgreplyDetail;
import com.jky.boot.core.module.gzpt.service.ITMMsgreplyDetailService;
import com.jky.common.util.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Component
public class MessageReplyTimeJob {
    @Autowired
    private ITMMsgreplyDetailService itmMsgreplyDetailService;


    /**
     * 处理监管端消息定时任务
     * 0 0 7-21 * * ?
     */
    @XxlJob(value = "MessageReplyHandle")
    public ReturnT<String> MessageReplyHandle(String params) {

        MessageReplyTimeConfig detailConfig = new MessageReplyTimeConfig();
        if (StringUtils.isNotBlank(params)) {
            try {
                detailConfig = new ObjectMapper().readValue(params, MessageReplyTimeConfig.class);
            } catch (JsonProcessingException e) {
                XxlJobLogger.log("参数反序列化失败");
                return ReturnT.FAIL;
            }
        } else {
            XxlJobLogger.log("无效参数");
            return ReturnT.FAIL;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime countDate = now.plusHours(-24L);
        if (StringUtils.isNotBlank(detailConfig.getStartTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                Date parse = sdf.parse(detailConfig.getStartTime() + " 00:00:00");
                Instant instant = parse.toInstant();
                ZoneId zone = ZoneId.systemDefault();
                countDate = LocalDateTime.ofInstant(instant, zone);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        LambdaQueryWrapper<TMMsgreplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(detailConfig.getStartTime())) {
            queryWrapper.ge(TMMsgreplyDetail::getCreateTime, detailConfig.getStartTime());
        } else {
            queryWrapper.ge(TMMsgreplyDetail::getCreateTime, countDate);
        }
        if (StringUtils.isNotBlank(detailConfig.getEndTime())) {
            queryWrapper.le(TMMsgreplyDetail::getCreateTime, detailConfig.getEndTime());
        }
        queryWrapper.eq(TMMsgreplyDetail::getIsHandle, 0);
        List<TMMsgreplyDetail> tmMsgreplyDetailList = itmMsgreplyDetailService.list(queryWrapper);
        for (TMMsgreplyDetail tmMsgreplyDetail : tmMsgreplyDetailList) {
            String s = null;
            if (tmMsgreplyDetail.getMsgtype() == 1) {//教练员
                s = itmMsgreplyDetailService.coachDtoHandle(tmMsgreplyDetail);
            }else
            if (tmMsgreplyDetail.getMsgtype() == 2) {//教练车
                s = itmMsgreplyDetailService.carInfoDtoHandle(tmMsgreplyDetail);
            }else
            if (tmMsgreplyDetail.getMsgtype() == 4) {//考核员
                s = itmMsgreplyDetailService.examinerDtoHandle(tmMsgreplyDetail);
            }else
            if (tmMsgreplyDetail.getMsgtype() == 5) {//安全员
                s = itmMsgreplyDetailService.securityguardDtoHandle(tmMsgreplyDetail);
            }
            if ("success".equals(s)||s.contains("不存在")) {

                tmMsgreplyDetail.setIsHandle(1);
                XxlJobLogger.log("data:{}更新成功",tmMsgreplyDetail.getData());
                itmMsgreplyDetailService.updateById(tmMsgreplyDetail);
            } else {
                XxlJobLogger.log("{}更新有误", tmMsgreplyDetail.getData());
                XxlJobLogger.log(s);
            }
        }
        return ReturnT.SUCCESS;
    }

    @Data
    public static class MessageReplyTimeConfig {
        private String startTime;
        private String endTime;
    }
}
