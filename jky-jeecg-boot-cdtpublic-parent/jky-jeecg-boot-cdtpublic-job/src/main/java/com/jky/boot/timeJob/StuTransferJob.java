package com.jky.boot.timeJob;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.StuTransfer;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IStuTransferAndDropOutService;
import com.jky.boot.core.module.gzpt.service.IStuTransferService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.PushJgUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024-01-03  19:07
 */
@Component
public class StuTransferJob {
    @Autowired
    private IStuTransferService stuTransferService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private IStudentinfoService studentInfoService;

    @Autowired
    private IStuTransferAndDropOutService stuTransferAndDropOutService;
    /**
     * 转校前金额已经结算完成，不需要使用这里
     */
//    @XxlJob(value = "checkTrans")
    public ReturnT<?> checkTrans(String stunum) {
        List<StuTransfer> list = stuTransferService.list(
                Wrappers.lambdaQuery(StuTransfer.class)
                        .eq(StuTransfer::getFlow, 1)
                        .eq(StringUtils.isNotBlank(stunum), StuTransfer::getStunum, stunum)
        );
        if (CollectionUtils.isEmpty(list)) {
            XxlJobLogger.log("无正在划转的学员！");
            return ReturnT.SUCCESS;
        }
        for (StuTransfer one : list) {
            BankOrder order = bankOrderService.getOne(
                    Wrappers.lambdaQuery(BankOrder.class)
                            .eq(BankOrder::getStunum, one.getStunum())
                            .eq(BankOrder::getTransFlag, 1)
//                            .eq(BankOrder::getOrdertype, 5)
            );
            if (order.getStatus() == 0) {
                XxlJobLogger.log("{}订单银行未回执，跳过", order.getId());
                continue;
            }
            if (order.getStatus() == 2) {
                one.setFlow(12);
                XxlJobLogger.log("{}订单划转失败", order.getId());
            }
            if (order.getStatus() == 1) {
                Result<?> result = studentInfoService.unfreeze(one.getStunum(), 1, one.getId());
                if (!result.isSuccess()) {
                    one.setFlow(13);
                    XxlJobLogger.log("{}订单划转成功，但投递解冻消息失败", order.getId());
                } else {
                    one.setFlow(2);
                    XxlJobLogger.log("{}订单划转成功，投递解冻消息成功", order.getId());
                }
                one.setResult(result.getMessage());
            }
            one.setTransRes(order.getRemark());
            stuTransferService.updateById(one);

            // 标记该转校划转订单已核查
            order.setTransFlag(11);
            bankOrderService.updateById(order);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 转校时，退费，校验解冻结果
     */
    @XxlJob(value = "checkUnfreeze")
    public ReturnT<?> checkUnfreeze(String stunum) {
        List<StuTransfer> list = stuTransferService.list(
                Wrappers.lambdaQuery(StuTransfer.class)
                        .eq(StuTransfer::getFlow, 2)
                        .eq(StringUtils.isNotBlank(stunum), StuTransfer::getStunum, stunum)
        );
        if (CollectionUtils.isEmpty(list)) {
            XxlJobLogger.log("无正在解冻的学员！");
            return ReturnT.SUCCESS;
        }
        for (StuTransfer one : list) {
            BankOrder order = bankOrderService.getOne(
                    Wrappers.lambdaQuery(BankOrder.class)
                            .eq(BankOrder::getStunum, one.getStunum())
                            .eq(BankOrder::getOrdertype, 4)
                            .eq(BankOrder::getTransFlag, 1)
            );
            if (order.getStatus() == 0) {
                XxlJobLogger.log("{}订单银行未回执，跳过", order.getId());
                continue;
            }
            if (order.getStatus() == 2) {
                one.setFlow(23);
                XxlJobLogger.log("{}订单解冻失败", order.getId());
            }
            if (order.getStatus() == 1) {
                one.setResult(order.getRemark());
                XxlJobLogger.log("{}订单解冻成功", order.getId());
                if ("9IC3C55B".equals(order.getBankcode())) {
                    one.setFlow(26);
                    one.setResult("建行学员请至手机端解约");
                } else {
                    one.setFlow(22);
                    // 解冻成功，执行转校操作
                    studentInfoService.doTrans(one);
                    one.setFlow(3);
                    XxlJobLogger.log("执行转校操作成功", order.getId());

                    // 通知监管
                    try {
                        CommonResponse resp = PushJgUtils.pushJg("/transfer", one);
                        one.setFlow(resp.getErrorcode() == 0 ? 4 : 42);
                    } catch (Exception e) {
                        XxlJobLogger.log("通知监管失败", one.getId());
                        one.setFlow(42);
                    }
                }
            }
            stuTransferService.updateById(one);

            // 标记该转校解冻订单已核查
            order.setTransFlag(12);
            bankOrderService.updateById(order);
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 转校定时任务
     */
    @XxlJob(value = "stuTransferCheck")
    public ReturnT<?> searchJgTransfer(String stunum) {
        //转校 1、遍历同步监管转校审核状态
        stuTransferAndDropOutService.searchJgTransfer(stunum);
        //转校 2、监管转校审核通过，先进行学员资金解冻
        try {
            stuTransferAndDropOutService.unFreezeStuTransfer(stunum);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //转校 3、监管转校审核通过，进行学员资金解冻之后 检测解冻订单是否完成，如果完成则将学员转校
        stuTransferAndDropOutService.checkUnfreezeTransfer(stunum);
        return ReturnT.SUCCESS;
    }

    /**
     * 学员转车型定时任务
     */
    @XxlJob(value = "stuCarTypeTransferCheck")
    public ReturnT<?> unFreezeStuCarType(String stunum) {
        //学员转车型 1、先进行学员资金解冻
        stuTransferAndDropOutService.unFreezeStuCarType(stunum);
        //学员转车型 2、学员资金解冻之后 检测解冻订单是否完成，如果完成则将学员转校
        stuTransferAndDropOutService.checkUnfreezeCarType(stunum);
        return ReturnT.SUCCESS;
    }
}
