package com.jky.boot.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.base.dao.SchedulingApply;
import com.jky.boot.base.dao.SchedulingClass;
import com.jky.boot.base.dao.SchedulingImi;
import com.jky.boot.base.dao.SchedulingOpr;
import com.jky.boot.base.service.ISchedulingApplyService;
import com.jky.boot.base.service.ISchedulingClassService;
import com.jky.boot.base.service.ISchedulingImiService;
import com.jky.boot.base.service.ISchedulingOprService;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.entity.StudentContract;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.IStudentContractService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 预约记录划转 job
 *
 * <AUTHOR>
 * @version 2024-12-17 10:25
 */
@Component
@Slf4j
public class ReservationApplyPayJob {

    @Autowired
    private IStudentinfoService stuService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private IStudentContractService stuContractService;
    @Autowired
    private ISchedulingOprService schedulingOprService;
    @Autowired
    private ISchedulingImiService schedulingImiService;
    @Autowired
    private ISchedulingApplyService schedulingApplyService;
    @Autowired
    private ISchedulingClassService schedulingClassService;
    @Autowired
    private IBankRegService bankRegService;

    @XxlJob(value = "reservationApplyPay")
    public ReturnT<?> reservationApplyPay(String params) {
        log.info("=========资金划转订单开始生成==========");
        String stunum = null;
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSONObject.parseObject(params);
            stunum = jsonObject.getString("stunum");
        }
        String id = null;
        long regCount = bankRegService.count(Wrappers.<BankReg>lambdaQuery()
                .eq(BankReg::getStatus, 3)
                .eq(StringUtils.isNotBlank(stunum), BankReg::getStunum, stunum)
                .gt(BankReg::getRemainingAmount, BigDecimal.ZERO)
        );
        Integer size = 100;
        long listSize = (regCount / size) + 1;
        for (int i = 0; i <= listSize; i++) {
            List<BankReg> list = bankRegService.list(Wrappers.<BankReg>lambdaQuery()
                    .eq(BankReg::getStatus, 3)
                    .eq(StringUtils.isNotBlank(stunum), BankReg::getStunum, stunum)
                    .gt(BankReg::getRemainingAmount, BigDecimal.ZERO)
                    .gt(StringUtils.isNotBlank(id), BankReg::getId, id)
                    .last("limit " + size)
            );
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            id = list.get(list.size() - 1).getId();
            for (BankReg reg : list) {
                dealOpr(reg);
                dealClass(reg);
                dealImi(reg);
            }
        }
        log.info("=========资金划转结束执行==========");
        return ReturnT.SUCCESS;
    }

    private void dealOpr(BankReg reg) {
        List<SchedulingApply> dealList = getDealList(1, reg.getStunum());
        for (SchedulingApply apply : dealList) {
            if (orderExist(apply.getStunum(),apply.getId())) {
                log.info("已存在订单，跳过stunum:{}，applyId:{}",apply.getStunum(),apply.getId());
                continue;
            }
            SchedulingOpr byId = schedulingOprService.getById(apply.getClassId());
            if (new Date().before(DateUtils.addDays(byId.getClassDate(), 1))) {
                continue;
            }
            Studentinfo stu = stuService.getOneByStuNum(apply.getStunum());
            StudentContract contract = stuContractService.getOneByStunum(apply.getStunum());
            if (contract == null) {
                log.info("学员合同不存在跳过{}",apply.getStunum());
                continue;
            }
            BigDecimal balance = (byId.getSubject() == 2) ? contract.getSub2Cost() : contract.getSub3Cost();
            if (balance.compareTo(BigDecimal.ZERO) <= 0) {
                apply.setPaid(3);
                continue;
            }
            BigDecimal transAmount = (byId.getSubject() == 2) ? contract.getSub2CostPer() : contract.getSub3CostPer();
            BigDecimal target = transAmount.min(balance);
            genBankOrder(stu, target, Long.valueOf(byId.getSubject()), apply.getId(), byId.getCoachnum(), true, reg);
            // 更新余额
            BigDecimal remain = balance.subtract(target);
            if (byId.getSubject() == 2) {
                contract.setSub2Cost(remain);
            } else {
                contract.setSub3Cost(remain);
            }
            stuContractService.updateById(contract);
            apply.setPaid(1);
        }
        schedulingApplyService.updateBatchById(dealList);
    }

    private void dealClass(BankReg reg) {
        List<SchedulingApply> dealList = getDealList(2, reg.getStunum());
        for (SchedulingApply apply : dealList) {
            if (orderExist(apply.getStunum(),apply.getId())) {
                log.info("已存在订单，跳过stunum:{}，applyId:{}",apply.getStunum(),apply.getId());
                continue;
            }
            SchedulingClass byId = schedulingClassService.getById(apply.getClassId());
            if (new Date().before(DateUtils.addDays(byId.getClassDate(), 1))) {
                continue;
            }
            Studentinfo stu = stuService.getOneByStuNum(apply.getStunum());
            StudentContract contract = stuContractService.getOneByStunum(apply.getStunum());
            if (contract == null) {
                log.info("学员合同不存在跳过{}",apply.getStunum());
                continue;
            }
            if (contract.getTheoryCost().compareTo(BigDecimal.ZERO) <= 0) {
                apply.setPaid(3);
                continue;
            }
            BigDecimal transAmount = contract.getTheoryCostPer().multiply(BigDecimal.valueOf(byId.getClassHours()));
            BigDecimal target = transAmount.min(contract.getTheoryCost());
            genBankOrder(stu, target, 1L, apply.getId(), byId.getCoachnum(), false, reg);

            // 更新余额
            BigDecimal remain = contract.getTheoryCost().subtract(target);
            contract.setTheoryCost(remain);
            stuContractService.updateById(contract);
            apply.setPaid(1);
        }
        schedulingApplyService.updateBatchById(dealList);
    }

    private void dealImi(BankReg reg) {
        List<SchedulingApply> dealList = getDealList(3, reg.getStunum());
        for (SchedulingApply apply : dealList) {
            if (orderExist(apply.getStunum(),apply.getId())) {
                log.info("已存在订单，跳过stunum:{}，applyId:{}",apply.getStunum(),apply.getId());
                continue;
            }
            SchedulingImi byId = schedulingImiService.getById(apply.getClassId());
            if (new Date().before(DateUtils.addDays(byId.getClassDate(), 1))) {
                continue;
            }
            Studentinfo stu = stuService.getOneByStuNum(apply.getStunum());
            StudentContract contract = stuContractService.getOneByStunum(apply.getStunum());
            if (contract == null) {
                log.info("学员合同不存在跳过{}",apply.getStunum());
                continue;
            }
            if (contract.getImiCost().compareTo(BigDecimal.ZERO) <= 0) {
                apply.setPaid(3);
                continue;
            }
            BigDecimal transAmount = contract.getImiCostPer().multiply(BigDecimal.valueOf(byId.getClassHours()));
            BigDecimal target = transAmount.min(contract.getImiCost());
            genBankOrder(stu, target, Long.valueOf(byId.getSubject()), apply.getId(), byId.getCoachnum(), false, reg);

            // 更新余额
            BigDecimal remain = contract.getImiCost().subtract(target);
            contract.setImiCost(remain);
            stuContractService.updateById(contract);
            apply.setPaid(1);
        }
        schedulingApplyService.updateBatchById(dealList);
    }

    private List<SchedulingApply> getDealList(Integer type, String stunum) {
        return schedulingApplyService.list(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getCancel, 0)
                        .eq(SchedulingApply::getPaid, 0)
                        .eq(SchedulingApply::getPushJs, 1)
                        .eq(SchedulingApply::getType, type)
                        .eq(StringUtils.isNotBlank(stunum), SchedulingApply::getStunum, stunum)
        );
    }


    /**
     *
     * @param stu
     * @param transMoney
     * @param orderType
     * @param applyId
     * @param coachnum
     * @param isSc 是否实操
     */
    private void genBankOrder(Studentinfo stu, BigDecimal transMoney, Long orderType, String applyId, String coachnum, boolean isSc,BankReg reg) {
        //农商银行（06C3CRCB） 东航（****************）、高新（****************）两个驾校教练员分账
        Boolean isSchoolAccount = true;
        Set<String> inscodeList = EnvControl.SEPARATE_ACCOUNTINST.keySet();
        if (isSc && StringUtils.equalsIgnoreCase(stu.getBankcode(), "06C3CRCB") && inscodeList.contains(stu.getInscode()) && StringUtils.isNotBlank(stu.getCoachnum())) {
            isSchoolAccount = false;
        }
        if (isSchoolAccount) {
            BankOrder bankOrder = new BankOrder();
            bankOrder.setStunum(stu.getStunum());
            bankOrder.setInscode(stu.getInscode());
            bankOrder.setInsname(stu.getInsname());
            bankOrder.setCreatedate(new Date());
            bankOrder.setStatus(0L);
            bankOrder.setOrdertype(orderType);
            bankOrder.setBankcode(stu.getBankcode());
            bankOrder.setBankname(stu.getBankname());
            bankOrder.setStuname(stu.getName());
            bankOrder.setTransferAmount(transMoney);
            bankOrder.setIdcard(stu.getIdcard());
            bankOrder.setApplyId(applyId);
            bankOrder.setCoachnum(stu.getCoachnum());
            bankOrder.setUserType(0);
            bankOrder.setSerialno(reg.getSerialno());
            bankOrderService.save(bankOrder);
        } else {
            //教练员分账
            //教练员所分金额
            String insRate =EnvControl.SEPARATE_ACCOUNTINST.get(stu.getInscode());
//            BigDecimal coachTransMoney=transMoney.multiply(new BigDecimal(insRate)).setScale(2,BigDecimal.ROUND_HALF_UP);
            BigDecimal insTransMoney=transMoney.multiply(new BigDecimal(insRate)).setScale(2,BigDecimal.ROUND_HALF_UP);
            //驾校所分金额
            BigDecimal coachTransMoney=transMoney.subtract(insTransMoney).setScale(2,BigDecimal.ROUND_HALF_UP);

            //驾校
            BankOrder bankOrder = new BankOrder();
            bankOrder.setStunum(stu.getStunum());
            bankOrder.setInscode(stu.getInscode());
            bankOrder.setInsname(stu.getInsname());
            bankOrder.setCreatedate(new Date());
            bankOrder.setStatus(0L);
            bankOrder.setOrdertype(orderType);
            bankOrder.setBankcode(stu.getBankcode());
            bankOrder.setBankname(stu.getBankname());
            bankOrder.setStuname(stu.getName());
            bankOrder.setTransferAmount(insTransMoney);
            bankOrder.setIdcard(stu.getIdcard());
            bankOrder.setCoachnum(stu.getCoachnum());
            bankOrder.setApplyId(applyId);
            bankOrder.setUserType(0);
            bankOrder.setSerialno(reg.getSerialno());
            bankOrderService.save(bankOrder);

            // 教练员
            bankOrder.setId(null);
            bankOrder.setIdcard(stu.getIdcard());
            bankOrder.setStuname(stu.getName());
            bankOrder.setCoachnum(coachnum);
            bankOrder.setTransferAmount(coachTransMoney);
            bankOrder.setUserType(2);
            bankOrder.setSerialno(reg.getSerialno());
            bankOrderService.save(bankOrder);

        }

    }

    private boolean orderExist(String stunum,String applyId){
        List<BankOrder> orders = bankOrderService.list(
                Wrappers.lambdaQuery(BankOrder.class)
                        .eq(BankOrder::getStunum, stunum)
                        .eq(BankOrder::getApplyId, applyId)
        );
        return !CollectionUtils.isEmpty(orders);
    }
}
