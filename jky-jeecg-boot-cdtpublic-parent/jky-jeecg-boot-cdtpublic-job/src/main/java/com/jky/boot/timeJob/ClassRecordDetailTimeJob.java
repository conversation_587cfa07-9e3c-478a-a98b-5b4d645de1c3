package com.jky.boot.timeJob;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.service.IClassRecordDetailService;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.common.util.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Component
public class ClassRecordDetailTimeJob {
    @Autowired
    private IClassRecordDetailService classRecordDetailService;
    @Autowired
    private IInstitutionService InsService;
    @Autowired
    private IPlatformService platformService;





    /**
     *  老学员电子教学日志定时任务
     *  0 0 7-21 * * ?
     */
    @XxlJob(value = "pushDetailToJs")
    public ReturnT pushDetailToJs(String config) {
        ClassRecordDetailConfig detailConfig = new ClassRecordDetailConfig();
        if (StringUtils.isNotBlank(config)) {
            try {
                detailConfig = new ObjectMapper().readValue(config, ClassRecordDetailConfig.class);
            } catch (JsonProcessingException e) {
                XxlJobLogger.log("参数反序列化失败");
                return ReturnT.FAIL;
            }
        } else {
            XxlJobLogger.log("无效参数");
            return ReturnT.FAIL;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime countDate = now.plusHours(-24L);
        if (StringUtils.isNotBlank(detailConfig.getCountDate())) {
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                Date parse = sdf.parse(detailConfig.getCountDate() + " 00:00:00");
                Instant instant = parse.toInstant();
                ZoneId zone = ZoneId.systemDefault();
                countDate= LocalDateTime.ofInstant(instant, zone);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        LambdaQueryWrapper<ClassRecordDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(detailConfig.getIsHasStartTime())&&"1".equals(detailConfig.getIsHasStartTime())){
            if (StringUtils.isNotBlank(detailConfig.getStartTime())) {
                queryWrapper.gt(ClassRecordDetail::getCreateTime, detailConfig.getStartTime());
            } else {
                queryWrapper.gt(ClassRecordDetail::getCreateTime, countDate);
            }
        }
        if (StringUtils.isNotBlank(detailConfig.getEndTime())){
            queryWrapper.le(ClassRecordDetail::getCrdate,detailConfig.getEndTime());
        }
        if (StringUtils.isNotBlank(detailConfig.getInscode())){
            queryWrapper.eq(ClassRecordDetail::getInscode,detailConfig.getInscode());
        }
        queryWrapper.in(ClassRecordDetail::getIstojs,0,2);
        Page<ClassRecordDetail> page = new Page<ClassRecordDetail>(0, 1000);
        Page<ClassRecordDetail> classRecordDetailPage = classRecordDetailService.page(page, queryWrapper);

        for (ClassRecordDetail detail :classRecordDetailPage.getRecords()){

            if (StringUtils.isBlank(detail.getStunum())) {
                XxlJobLogger.log("id:{}该条教学日志没有所属的学员编号",detail.getId());
                continue;
            }
            Institution ins = InsService.getByInscode(detail.getInscode());
            detail.setPlatnum(ins.getPlatform());
//            if (StringUtils.isBlank(detail.getPlatnum())) {
//                if (ins == null) {
//                    XxlJobLogger.log("id:{}未找到该条电子教学日志所属的驾校",detail.getId());
//                    continue;
//                }
//
//            }else {
//                if (detail.getPlatnum().length()<=5) {
//                    Map<String, String> platformMap = new HashMap<>();
//                    platformMap.put("A0002", "163AB4F0E6C");
//                    platformMap.put("A0016", "157C1C2BAF4");
//                    platformMap.put("A0188", "882076CA688");
//                    platformMap.put("A0010", "15886BD9C39");
//                    String platnum = platformMap.get(detail.getPlatnum());
//                    if (StringUtils.isBlank(platnum)) {
//                        XxlJobLogger.log("id:{}未找到该条电子教学日志相关的计时厂商",detail.getId());
//                        continue;
//                    }
//                    detail.setPlatnum(platnum);
//                }
//            }
            Platform platform = platformService.getByPlatformSerialNumber(detail.getPlatnum());
            if (platform == null) {
                XxlJobLogger.log("id:{}未找到该条电子教学日志相关的计时厂商",detail.getId());
                continue;
            }
            String result = "";
            try {
                CommonResponse push = PushJsUtil.push(detail, platform.getApi() + "gzdetail");
                result = push.getMessage();
                XxlJobLogger.log("CommonResponse:"+push);
                if (push.getErrorcode() == 0) {
                    detail.setIstojs(1);
                    classRecordDetailService.updateById(detail);
                    XxlJobLogger.log("id:{}推送成功",detail.getId());
                    continue;
                }
            } catch (Exception e) {
                e.printStackTrace();
                detail.setIstojs(2);
                classRecordDetailService.updateById(detail);
                XxlJobLogger.log("推送时出现了异常"+e);
                continue;
            }
            XxlJobLogger.log("推送时出现了异常:{}",result);
            continue;
        }
        return ReturnT.SUCCESS;
    }





    @Data
    public static class ClassRecordDetailConfig {
        private String countDate;
        private String startTime;
        private String endTime;
        private String inscode;
        private String isHasStartTime;
    }
}
