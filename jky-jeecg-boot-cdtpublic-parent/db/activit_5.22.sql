/*
 Navicat Premium Data Transfer

 Source Server         : MYSQL8_localhost-3308
 Source Server Type    : MySQL
 Source Server Version : 80017
 Source Host           : localhost:3306

 Target Server Type    : MySQL
 Target Server Version : 80017
 File Encoding         : 65001

 Date: 17/01/2022 14:42:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for act_evt_log
-- ----------------------------
DROP TABLE IF EXISTS `act_evt_log`;
CREATE TABLE `act_evt_log`  (
  `LOG_NR_` bigint(20) NOT NULL COMMENT '主键',
  `TYPE_` varchar(64)  NULL DEFAULT NULL COMMENT '类型',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `TIME_STAMP_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '时间',
  `USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '用户id',
  `DATA_` longblob NULL COMMENT '事件内容',
  `LOCK_OWNER_` varchar(255)  NULL DEFAULT NULL COMMENT '锁定节点',
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '锁定时间',
  `IS_PROCESSED_` tinyint(4) NULL DEFAULT 0 COMMENT '是否正在执行',
  PRIMARY KEY (`LOG_NR_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '事件日志，默认不开启。' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ge_bytearray
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_bytearray`;
CREATE TABLE `act_ge_bytearray`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `DEPLOYMENT_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '部署id',
  `BYTES_` longblob NULL COMMENT '内容',
  `GENERATED_` tinyint(4) NULL DEFAULT NULL COMMENT '0为用户上传，1为系统自动生成，比如系统会自动根据xml生成png',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_BYTEARR_DEPL`(`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `act_ge_bytearray_ibfk_1` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '所有二进制内容都会保存在这个表里，比如部署的process.bpmn20.xml; process.png; user.' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_property`;
CREATE TABLE `act_ge_property`  (
  `NAME_` varchar(64)  NOT NULL COMMENT '主键，参数名',
  `VALUE_` varchar(300)  NULL DEFAULT NULL COMMENT '参数值',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  PRIMARY KEY (`NAME_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '全局参数，默认三个参数next.dbid，IdGenerator区间，schema.history，自动执行sql历史，' ROW_FORMAT = Dynamic;

INSERT INTO `act_ge_property`(`NAME_`, `VALUE_`, `REV_`) VALUES ('next.dbid', '797501', 320);
INSERT INTO `act_ge_property`(`NAME_`, `VALUE_`, `REV_`) VALUES ('schema.history', 'create(5.22.0.0)', 1);
INSERT INTO `act_ge_property`(`NAME_`, `VALUE_`, `REV_`) VALUES ('schema.version', '5.22.0.0', 1);

-- ----------------------------
-- Table structure for act_hi_actinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_actinst`;
CREATE TABLE `act_hi_actinst`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `EXECUTION_ID_` varchar(64)  NOT NULL,
  `ACT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '环节id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `CALL_PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '父流程实例id',
  `ACT_NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '环节名称',
  `ACT_TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '环节类型',
  `ASSIGNEE_` varchar(255)  NULL DEFAULT NULL COMMENT '办理人',
  `START_TIME_` datetime(0) NOT NULL COMMENT '开始时间',
  `END_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `DURATION_` bigint(20) NULL DEFAULT NULL COMMENT '持续时间',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_START`(`START_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_END`(`END_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_PROCINST`(`PROC_INST_ID_`, `ACT_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_EXEC`(`EXECUTION_ID_`, `ACT_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史节点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_attachment
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_attachment`;
CREATE TABLE `act_hi_attachment`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '用户id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `DESCRIPTION_` varchar(4000)  NULL DEFAULT NULL COMMENT '描述',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `URL_` varchar(4000)  NULL DEFAULT NULL COMMENT 'url',
  `CONTENT_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '内容 ACT_GE_BYTEARRAY',
  `TIME_` datetime(0) NULL DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '附件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_comment
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_comment`;
CREATE TABLE `act_hi_comment`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型，默认有event; comment理解成操作和评论。',
  `TIME_` datetime(0) NOT NULL COMMENT '时间',
  `USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '用户id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `ACTION_` varchar(255)  NULL DEFAULT NULL COMMENT '操作',
  `MESSAGE_` varchar(4000)  NULL DEFAULT NULL COMMENT '消息',
  `FULL_MSG_` longblob NULL COMMENT '完整消息',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_detail
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_detail`;
CREATE TABLE `act_hi_detail`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型 FormProperty;VariableUpdate',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `ACT_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '环节实例id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `VAR_TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '变量类型',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `TIME_` datetime(0) NOT NULL COMMENT '时间',
  `BYTEARRAY_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '内容 ACT_GE_BYTEARRAY',
  `DOUBLE_` double NULL DEFAULT NULL COMMENT '浮点型值',
  `LONG_` bigint(20) NULL DEFAULT NULL COMMENT '长整型值',
  `TEXT_` varchar(4000)  NULL DEFAULT NULL COMMENT '文本值',
  `TEXT2_` varchar(4000)  NULL DEFAULT NULL COMMENT 'jpa变量text存className;text2存id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_PROC_INST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_ACT_INST`(`ACT_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_TIME`(`TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_NAME`(`NAME_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_TASK_ID`(`TASK_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史详情信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_identitylink
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_identitylink`;
CREATE TABLE `act_hi_identitylink`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `GROUP_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '组id',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型，assignee; candidate; owner; starter;participant',
  `USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '用户id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_USER`(`USER_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_TASK`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_PROCINST`(`PROC_INST_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史参与者' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_procinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_procinst`;
CREATE TABLE `act_hi_procinst`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `BUSINESS_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '业务标识',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `START_TIME_` datetime(0) NOT NULL COMMENT '开始时间',
  `END_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `DURATION_` bigint(20) NULL DEFAULT NULL COMMENT '持续时间',
  `START_USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '流程发起人id',
  `START_ACT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '发起环节id',
  `END_ACT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '结束环节id',
  `SUPER_PROCESS_INSTANCE_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '父流程实例id',
  `DELETE_REASON_` varchar(4000)  NULL DEFAULT NULL COMMENT '删除原因',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `PROC_INST_ID_`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PRO_INST_END`(`END_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_PRO_I_BUSKEY`(`BUSINESS_KEY_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史流程实例' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_taskinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_taskinst`;
CREATE TABLE `act_hi_taskinst`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `TASK_DEF_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '任务定义标识（环节id）',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `PARENT_TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '父任务id',
  `DESCRIPTION_` varchar(4000)  NULL DEFAULT NULL COMMENT '描述',
  `OWNER_` varchar(255)  NULL DEFAULT NULL COMMENT '被代理人',
  `ASSIGNEE_` varchar(255)  NULL DEFAULT NULL COMMENT '办理人',
  `START_TIME_` datetime(0) NOT NULL COMMENT '开始时间',
  `CLAIM_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '签收时间',
  `END_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `DURATION_` bigint(20) NULL DEFAULT NULL COMMENT '持续时间',
  `DELETE_REASON_` varchar(4000)  NULL DEFAULT NULL COMMENT '删除原因',
  `PRIORITY_` int(11) NULL DEFAULT NULL COMMENT '优先级',
  `DUE_DATE_` datetime(0) NULL DEFAULT NULL COMMENT '截止日期',
  `FORM_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '表单标识',
  `CATEGORY_` varchar(255)  NULL DEFAULT NULL COMMENT '分类',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_TASK_INST_PROCINST`(`PROC_INST_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_varinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_varinst`;
CREATE TABLE `act_hi_varinst`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `VAR_TYPE_` varchar(100)  NULL DEFAULT NULL COMMENT '类型',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `BYTEARRAY_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '内容 ACT_GE_BYTEARRAY',
  `DOUBLE_` double NULL DEFAULT NULL COMMENT '浮点值',
  `LONG_` bigint(20) NULL DEFAULT NULL COMMENT '长整型值',
  `TEXT_` varchar(4000)  NULL DEFAULT NULL COMMENT '文本值',
  `TEXT2_` varchar(4000)  NULL DEFAULT NULL COMMENT 'jpa变量text存\r\nalter table 	 act_hi_varinst	modify column 	            className;text2存id',
  `CREATE_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `LAST_UPDATED_TIME_` datetime(0) NULL DEFAULT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_PROC_INST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_NAME_TYPE`(`NAME_`, `VAR_TYPE_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_TASK_ID`(`TASK_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '历史流程变量' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_group
-- ----------------------------
DROP TABLE IF EXISTS `act_id_group`;
CREATE TABLE `act_id_group`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '用户组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_info
-- ----------------------------
DROP TABLE IF EXISTS `act_id_info`;
CREATE TABLE `act_id_info`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `USER_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '用户id',
  `TYPE_` varchar(64)  NULL DEFAULT NULL COMMENT '类型',
  `KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '属性名',
  `VALUE_` varchar(255)  NULL DEFAULT NULL COMMENT '属性值',
  `PASSWORD_` longblob NULL COMMENT '密码',
  `PARENT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '上级关联',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '用户详细信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_membership
-- ----------------------------
DROP TABLE IF EXISTS `act_id_membership`;
CREATE TABLE `act_id_membership`  (
  `USER_ID_` varchar(64)  NOT NULL COMMENT '用户id',
  `GROUP_ID_` varchar(64)  NOT NULL COMMENT '组id',
  PRIMARY KEY (`USER_ID_`, `GROUP_ID_`) USING BTREE,
  INDEX `ACT_FK_MEMB_GROUP`(`GROUP_ID_`) USING BTREE,
  CONSTRAINT `act_id_membership_ibfk_1` FOREIGN KEY (`GROUP_ID_`) REFERENCES `act_id_group` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_id_membership_ibfk_2` FOREIGN KEY (`USER_ID_`) REFERENCES `act_id_user` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '用户与组的关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_user
-- ----------------------------
DROP TABLE IF EXISTS `act_id_user`;
CREATE TABLE `act_id_user`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `FIRST_` varchar(255)  NULL DEFAULT NULL COMMENT '名',
  `LAST_` varchar(255)  NULL DEFAULT NULL COMMENT '姓',
  `EMAIL_` varchar(255)  NULL DEFAULT NULL COMMENT '邮箱',
  `PWD_` varchar(255)  NULL DEFAULT NULL COMMENT '密码',
  `PICTURE_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '头像id ACT_GE_BYTEARRAY',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '用户基本信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_procdef_info
-- ----------------------------
DROP TABLE IF EXISTS `act_procdef_info`;
CREATE TABLE `act_procdef_info`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `INFO_JSON_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '内容',
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `ACT_UNIQ_INFO_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_IDX_INFO_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_INFO_JSON_BA`(`INFO_JSON_ID_`) USING BTREE,
  CONSTRAINT `act_procdef_info_ibfk_1` FOREIGN KEY (`INFO_JSON_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_procdef_info_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '流程定义的动态变更信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_re_deployment
-- ----------------------------
DROP TABLE IF EXISTS `act_re_deployment`;
CREATE TABLE `act_re_deployment`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `CATEGORY_` varchar(255)  NULL DEFAULT NULL COMMENT '分类',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  `DEPLOY_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '部署时间',
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '部署信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_re_model
-- ----------------------------
DROP TABLE IF EXISTS `act_re_model`;
CREATE TABLE `act_re_model`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '标识',
  `CATEGORY_` varchar(255)  NULL DEFAULT NULL COMMENT '分类',
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '创建时间',
  `LAST_UPDATE_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '最后更新时间',
  `VERSION_` int(11) NULL DEFAULT NULL COMMENT '版本',
  `META_INFO_` varchar(4000)  NULL DEFAULT NULL COMMENT '元数据',
  `DEPLOYMENT_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '部署id，ACT_RE_DEPLOYMENT',
  `EDITOR_SOURCE_VALUE_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '设计器原始信息id，ACT_GE_BYTEARRAY',
  `EDITOR_SOURCE_EXTRA_VALUE_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '设计器扩展信息id，ACT_GE_BYTEARRAY',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_SOURCE`(`EDITOR_SOURCE_VALUE_ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_SOURCE_EXTRA`(`EDITOR_SOURCE_EXTRA_VALUE_ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_DEPLOYMENT`(`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `act_re_model_ibfk_1` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_re_model_ibfk_2` FOREIGN KEY (`EDITOR_SOURCE_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_re_model_ibfk_3` FOREIGN KEY (`EDITOR_SOURCE_EXTRA_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '模型（用于Web Designer）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_re_procdef
-- ----------------------------
DROP TABLE IF EXISTS `act_re_procdef`;
CREATE TABLE `act_re_procdef`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `CATEGORY_` varchar(255)  NULL DEFAULT NULL COMMENT '分类',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '标识',
  `VERSION_` int(11) NOT NULL COMMENT '版本',
  `DEPLOYMENT_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '部署id',
  `RESOURCE_NAME_` varchar(4000)  NULL DEFAULT NULL COMMENT '资源名称',
  `DGRM_RESOURCE_NAME_` varchar(4000)  NULL DEFAULT NULL COMMENT '图片资源名称',
  `DESCRIPTION_` varchar(4000)  NULL DEFAULT NULL COMMENT '描述',
  `HAS_START_FORM_KEY_` tinyint(4) NULL DEFAULT NULL COMMENT '拥有开始表单标识',
  `HAS_GRAPHICAL_NOTATION_` tinyint(4) NULL DEFAULT NULL COMMENT '拥有图形信息',
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL COMMENT '暂停状态 1激活 2暂停',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `ACT_UNIQ_PROCDEF`(`KEY_`, `VERSION_`, `TENANT_ID_`) USING BTREE
) ENGINE = InnoDB  COMMENT = '流程定义' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_event_subscr
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_event_subscr`;
CREATE TABLE `act_ru_event_subscr`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `EVENT_TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型',
  `EVENT_NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `ACTIVITY_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '环节id',
  `CONFIGURATION_` varchar(255)  NULL DEFAULT NULL COMMENT '配置',
  `CREATED_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_EVENT_SUBSCR_CONFIG_`(`CONFIGURATION_`) USING BTREE,
  INDEX `ACT_FK_EVENT_EXEC`(`EXECUTION_ID_`) USING BTREE,
  CONSTRAINT `act_ru_event_subscr_ibfk_1` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '事件订阅' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_execution
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_execution`;
CREATE TABLE `act_ru_execution`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `BUSINESS_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '业务标识',
  `PARENT_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '父执行id',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `SUPER_EXEC_` varchar(64)  NULL DEFAULT NULL COMMENT '父流程实例中对应的执行',
  `ACT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '环节id',
  `IS_ACTIVE_` tinyint(4) NULL DEFAULT NULL COMMENT '是否激活',
  `IS_CONCURRENT_` tinyint(4) NULL DEFAULT NULL COMMENT '是否并行分支',
  `IS_SCOPE_` tinyint(4) NULL DEFAULT NULL COMMENT '是否处于多实例或环节嵌套状态',
  `IS_EVENT_SCOPE_` tinyint(4) NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL COMMENT '暂停状态 1激活 2暂停',
  `CACHED_ENT_STATE_` int(11) NULL DEFAULT NULL COMMENT '缓存的状态，1 事件监听 2 人工任务 3 异步作业',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '锁定时间',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_EXEC_BUSKEY`(`BUSINESS_KEY_`) USING BTREE,
  INDEX `ACT_FK_EXE_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_EXE_PARENT`(`PARENT_ID_`) USING BTREE,
  INDEX `ACT_FK_EXE_SUPER`(`SUPER_EXEC_`) USING BTREE,
  INDEX `ACT_FK_EXE_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `act_ru_execution_ibfk_1` FOREIGN KEY (`PARENT_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_execution_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_execution_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `act_ru_execution_ibfk_4` FOREIGN KEY (`SUPER_EXEC_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '执行' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_identitylink
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_identitylink`;
CREATE TABLE `act_ru_identitylink`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `GROUP_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '组id',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型',
  `USER_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '用户id',
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '任务id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_IDENT_LNK_USER`(`USER_ID_`) USING BTREE,
  INDEX `ACT_IDX_IDENT_LNK_GROUP`(`GROUP_ID_`) USING BTREE,
  INDEX `ACT_IDX_ATHRZ_PROCEDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_TSKASS_TASK`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_FK_IDL_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  CONSTRAINT `act_ru_identitylink_ibfk_1` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_identitylink_ibfk_2` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_identitylink_ibfk_3` FOREIGN KEY (`TASK_ID_`) REFERENCES `act_ru_task` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '参与者' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_job`;
CREATE TABLE `act_ru_job`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '类型',
  `LOCK_EXP_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '锁定逾期时间',
  `LOCK_OWNER_` varchar(255)  NULL DEFAULT NULL COMMENT '锁定环节',
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL COMMENT '是否唯一',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `PROCESS_INSTANCE_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `RETRIES_` int(11) NULL DEFAULT NULL COMMENT '重试次数',
  `EXCEPTION_STACK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '异常堆栈id;ACT_GE_BYTEARRAY',
  `EXCEPTION_MSG_` varchar(4000)  NULL DEFAULT NULL COMMENT '异常信息',
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL COMMENT '截止时间',
  `REPEAT_` varchar(255)  NULL DEFAULT NULL COMMENT '重复',
  `HANDLER_TYPE_` varchar(255)  NULL DEFAULT NULL COMMENT '处理器类型',
  `HANDLER_CFG_` varchar(4000)  NULL DEFAULT NULL COMMENT '处理器配置',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `act_ru_job_ibfk_1` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '异步作业' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_task`;
CREATE TABLE `act_ru_task`  (
  `ID_` varchar(64)  NOT NULL COMMENT '主键',
  `REV_` int(11) NULL DEFAULT NULL COMMENT '乐观锁',
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '执行id',
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程实例id',
  `PROC_DEF_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '流程定义id',
  `NAME_` varchar(255)  NULL DEFAULT NULL COMMENT '名称',
  `PARENT_TASK_ID_` varchar(64)  NULL DEFAULT NULL COMMENT '父任务id',
  `DESCRIPTION_` varchar(4000)  NULL DEFAULT NULL COMMENT '描述',
  `TASK_DEF_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '任务定义标识（环节id）',
  `OWNER_` varchar(255)  NULL DEFAULT NULL COMMENT '被代理人id',
  `ASSIGNEE_` varchar(255)  NULL DEFAULT NULL COMMENT '办理人',
  `DELEGATION_` varchar(64)  NULL DEFAULT NULL COMMENT '委托状态 PENDING委托中; RESOLVED已处理',
  `PRIORITY_` int(11) NULL DEFAULT NULL COMMENT '优先级',
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL COMMENT '创建时间',
  `DUE_DATE_` datetime(0) NULL DEFAULT NULL COMMENT '截止时间',
  `CATEGORY_` varchar(255)  NULL DEFAULT NULL COMMENT '分类',
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL COMMENT '暂停状态 1激活 2暂停',
  `TENANT_ID_` varchar(255)  NULL DEFAULT NULL COMMENT '租户id',
  `FORM_KEY_` varchar(255)  NULL DEFAULT NULL COMMENT '表单标识',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_TASK_CREATE`(`CREATE_TIME_`) USING BTREE,
  INDEX `ACT_FK_TASK_EXE`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_TASK_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_TASK_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `act_ru_task_ibfk_1` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_task_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_task_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  COMMENT = '任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_variable
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_variable`;
CREATE TABLE `act_ru_variable`  (
  `ID_` varchar(64)  NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255)  NOT NULL,
  `NAME_` varchar(255)  NOT NULL,
  `EXECUTION_ID_` varchar(64)  NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64)  NULL DEFAULT NULL,
  `TASK_ID_` varchar(64)  NULL DEFAULT NULL,
  `BYTEARRAY_ID_` varchar(64)  NULL DEFAULT NULL,
  `DOUBLE_` double NULL DEFAULT NULL,
  `LONG_` bigint(20) NULL DEFAULT NULL,
  `TEXT_` varchar(4000)  NULL DEFAULT NULL,
  `TEXT2_` varchar(4000)  NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_VARIABLE_TASK_ID`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_EXE`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_BYTEARRAY`(`BYTEARRAY_ID_`) USING BTREE,
  CONSTRAINT `act_ru_variable_ibfk_1` FOREIGN KEY (`BYTEARRAY_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_variable_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_variable_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB  ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for act_z_business
-- ----------------------------
DROP TABLE IF EXISTS `act_z_business`;
CREATE TABLE `act_z_business`  (
  `id` varchar(100)  NOT NULL,
  `create_by` varchar(100)  NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `del_flag` int(11) NULL DEFAULT 0,
  `update_by` varchar(100)  NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `proc_def_id` varchar(100)  NULL DEFAULT NULL COMMENT '流程定义id',
  `proc_inst_id` varchar(100)  NULL DEFAULT NULL COMMENT '流程实例id',
  `result` int(11) NULL DEFAULT 0 COMMENT '结果状态 0未提交默认 1处理中 2通过 3驳回',
  `status` int(11) NULL DEFAULT 0 COMMENT '状态 0草稿默认 1处理中 2结束',
  `table_id` varchar(255)  NULL DEFAULT NULL COMMENT '关联表id',
  `title` varchar(500)  NULL DEFAULT NULL COMMENT '申请标题',
  `user_id` varchar(100)  NULL DEFAULT NULL COMMENT '创建用户id',
  `apply_time` datetime(0) NULL DEFAULT NULL COMMENT '提交申请时间',
  `is_history` bit(1) NULL DEFAULT NULL COMMENT '历史标记',
  `table_name` varchar(100)  NULL DEFAULT NULL COMMENT '数据表名',
  `Process_Key` varchar(100)  NULL DEFAULT NULL COMMENT '流程key',
  `act_Status_Field` varchar(100)  NULL DEFAULT NULL COMMENT '业务表状态字段',
  `model_id` varchar(100)  NULL DEFAULT NULL COMMENT 'modelId',
  `category_id` varchar(50)  NULL DEFAULT NULL,
  `src_proc_inst_id` varchar(100)  NULL DEFAULT NULL COMMENT '原流程实例id，没有修改与proc_inst_id相同',
  `remark` varchar(500)  NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_buess_proc_inst_id`(`proc_inst_id`) USING BTREE,
  INDEX `idx_bus_table_id`(`table_id`) USING BTREE,
  INDEX `idx_buess_src_inst_id`(`src_proc_inst_id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '流程业务关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_z_from
-- ----------------------------
DROP TABLE IF EXISTS `act_z_from`;
CREATE TABLE `act_z_from`  (
  `id` varchar(36)  NOT NULL COMMENT '主键',
  `create_by` varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `sys_org_code` varchar(64)  NULL DEFAULT NULL COMMENT '所属部门',
  `from_name` varchar(100)  NOT NULL COMMENT '流程表单名称',
  `from_page_path` varchar(200)  NOT NULL COMMENT '流程表单页面',
  `from_page_dd_path` varchar(200)  NULL DEFAULT NULL COMMENT '钉钉表单页面',
  `from_page_app_path` varchar(200)  NULL DEFAULT NULL COMMENT 'app表单页面',
  `pid` varchar(32)  NULL DEFAULT NULL COMMENT '父级ID',
  `has_child` varchar(3)  NULL DEFAULT NULL COMMENT '是否有子节点',
  `event_bean` varchar(100)  NOT NULL COMMENT '业务事件(springbean对象别名)',
  `from_type` varchar(32)  NULL DEFAULT NULL COMMENT '页面类型',
  `table_name` varchar(32)  NULL DEFAULT NULL COMMENT '业务表名(可不填由实体类指定)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '流程关联表单'  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_z_node
-- ----------------------------
DROP TABLE IF EXISTS `act_z_node`;
CREATE TABLE `act_z_node`  (
  `id` varchar(255)  NOT NULL,
  `create_by` varchar(255)  NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `del_flag` int(11) NULL DEFAULT NULL,
  `update_by` varchar(255)  NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `model_id` varchar(255)  NULL DEFAULT NULL COMMENT '模型ID',
  `node_id` varchar(255)  NULL DEFAULT NULL COMMENT '节点id',
  `type` varchar(255)  NULL DEFAULT NULL COMMENT '节点关联类型 0角色 1用户 2部门,99业务表单',
  `relate_id` varchar(255)  NULL DEFAULT NULL COMMENT '关联其他表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '流程节点扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_z_node_conditions
-- ----------------------------
DROP TABLE IF EXISTS `act_z_node_conditions`;
CREATE TABLE `act_z_node_conditions`  (
  `id` varchar(36)  NOT NULL COMMENT '主键',
  `create_by` varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `sys_org_code` varchar(64)  NULL DEFAULT NULL COMMENT '所属部门',
  `model_id` varchar(50)  NULL DEFAULT NULL COMMENT '模型ID',
  `node_id` varchar(50)  NULL DEFAULT NULL COMMENT '节点ID',
  `field_name` varchar(50)  NULL DEFAULT NULL COMMENT '条件字段',
  `operator_code` varchar(32)  NULL DEFAULT NULL COMMENT '判断条件',
  `operator_value` varchar(32)  NULL DEFAULT NULL COMMENT '判断值',
  `and_or_code` varchar(32)  NULL DEFAULT NULL COMMENT '逻辑关系',
  `order_by` int(10) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB   COMMENT = '流程条件配置(暂未使用)' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_z_process
-- ----------------------------
DROP TABLE IF EXISTS `act_z_process`;
CREATE TABLE `act_z_process`  (
  `id` varchar(255)  NOT NULL,
  `create_by` varchar(255)  NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `del_flag` int(11) NULL DEFAULT NULL,
  `update_by` varchar(255)  NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `category_id` varchar(255)  NULL DEFAULT NULL COMMENT '所属分类',
  `deployment_id` varchar(255)  NULL DEFAULT NULL COMMENT '部署id',
  `model_id` varchar(255)  NULL DEFAULT NULL COMMENT '模型ID',
  `description` varchar(255)  NULL DEFAULT NULL COMMENT '描述/备注',
  `diagram_name` varchar(255)  NULL DEFAULT NULL COMMENT '流程图片名',
  `latest` bit(1) NULL DEFAULT NULL COMMENT '最新版本',
  `name` varchar(255)  NULL DEFAULT NULL COMMENT '流程名称',
  `process_key` varchar(255)  NULL DEFAULT NULL COMMENT '流程标识名称',
  `status` int(11) NULL DEFAULT NULL COMMENT '流程状态 部署后默认1激活',
  `version` int(11) NULL DEFAULT NULL COMMENT '版本',
  `business_table` varchar(255)  NULL DEFAULT NULL COMMENT '关联业务表名',
  `route_name` varchar(255)  NULL DEFAULT NULL COMMENT '关联前端表单路由名',
  `roles` varchar(225)  NULL DEFAULT NULL COMMENT '授权的角色',
  `act_Status_Field` varchar(255)  NULL DEFAULT NULL COMMENT '业务状态字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '流程业务部署扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_z_task
-- ----------------------------
DROP TABLE IF EXISTS `act_z_task`;
CREATE TABLE `act_z_task`  (
  `id` varchar(36)  NOT NULL,
  `create_by` varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `sys_org_code` varchar(64)  NULL DEFAULT NULL COMMENT '所属部门',
  `task_id` varchar(50)  NULL DEFAULT NULL COMMENT '任务ID',
  `assignee` varchar(50)  NULL DEFAULT NULL COMMENT '审核人',
  `assignee_status` int(11) NULL DEFAULT NULL COMMENT '审核人状态',
  `task_status` int(11) NULL DEFAULT NULL COMMENT '当前任务状态',
  `comment` varchar(1500)  NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_act_z_task_id_checker`(`task_id`, `assignee`) USING BTREE
) ENGINE = InnoDB   COMMENT = '流程自定义任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dd_user
-- ----------------------------
DROP TABLE IF EXISTS `dd_user`;
CREATE TABLE `dd_user`  (
  `ddid` varchar(255)  NOT NULL COMMENT '钉钉用户ID',
  `userid` varchar(255)  NOT NULL COMMENT '系统用户ID',
  `ddname` varchar(255)  NULL DEFAULT NULL COMMENT '钉钉用户名',
  `position` varchar(255)  NULL DEFAULT NULL COMMENT '钉钉职务',
  `avatar` varchar(255)  NULL DEFAULT NULL COMMENT '钉钉图片',
  `createdate` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `id` varchar(32)  NULL DEFAULT NULL COMMENT '序列号',
  PRIMARY KEY (`ddid`, `userid`) USING BTREE,
  UNIQUE INDEX `userid`(`userid`) USING BTREE
) ENGINE = InnoDB   COMMENT = '钉钉用户表'  ROW_FORMAT = Dynamic;
-- 流程配置菜单
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1238331160370012162', null, '流程管理', '/activiti', 'layouts/RouteView', NULL, NULL, 1, NULL, '1', 1.20, 0, 'cluster', 1, 0, 0, 0, NULL, 'admin', '2020-03-13 13:08:50', 'admin', '2020-10-26 09:24:26', 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1238331682929958913', '1238331160370012162', '流程模型', '/activiti/ModelList', 'jky/activiti/ModelList', NULL, NULL, 1, NULL, '1', 2.00, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1238331682929958914', '1238331160370012162', '已发布模型', '/activiti/ProcessModelList', 'jky/activiti/ProcessModelList', NULL, NULL, 1, NULL, '1', 3.00, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1238331682929958918', '1238331160370012162', '进行中的流程', '/activiti/processInsManage', 'jky/activiti/processInsManage', NULL, NULL, 1, NULL, '1', 7.00, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1238331682929958919', '1238331160370012162', '已结束流程', '/activiti/processFinishManage', 'jky/activiti/processFinishManage', NULL, NULL, 1, NULL, '1', 8.00, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1362309325150158849', '1238331160370012162', '流程表单', '/activiti/ActFromList', 'jky/activiti/ActFromList', NULL, NULL, 1, NULL, '1', 1.00, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2021-02-18 15:53:48', NULL, NULL, 0, 0, '1', 0);


INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('e9585fbd33a211ebaf971866dae76c2f', '1238331160370012162', '我的申请', '/activiti/applyList', 'jky/activiti/applyList', NULL, NULL, 1, NULL, '1', 0.10, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', 'admin', '2021-03-17 22:02:16', 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('e9585fbe33a211ebaf971866dae76c2f', '1238331160370012162', '我的待办', '/activiti/todoManage', 'jky/activiti/todoManage', NULL, NULL, 1, NULL, '1', 0.20, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', 'admin', '2021-03-17 22:02:24', 0, 0, '1', 0);
INSERT INTO `sys_permission`(`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('e9585fbf33a211ebaf971866dae76c2f', '1238331160370012162', '我的已办', '/activiti/doneManage', 'jky/activiti/doneManage', NULL, NULL, 1, NULL, '1', 0.30, 0, 'bars', 1, 1, 0, 0, NULL, 'admin', '2020-03-13 13:10:55', 'admin', '2021-03-17 22:02:32', 0, 0, '1', 0);



-- 管理员角色授权菜单
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939948392449', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331160370012162', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939960975362', 'f6817f48af4fb3af11b9e8bf182f618b', '1362309325150158849', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939960975363', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958913', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939973558273', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958914', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939973558274', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958918', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('1482973939973558275', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958919', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');

INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('e9585fbd33a211ebaf971866dae76c2f', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958914', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('e9585fbe33a211ebaf971866dae76c2f', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958918', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');
INSERT INTO `sys_role_permission`(`id`, `role_id`, `permission_id`, `data_rule_ids`, `operate_date`, `operate_ip`) VALUES ('e9585fbf33a211ebaf971866dae76c2f', 'f6817f48af4fb3af11b9e8bf182f618b', '1238331682929958919', NULL, '2022-01-17 15:11:54', '0:0:0:0:0:0:0:1');

-- 流程字典 from_type 流程表单类型
INSERT INTO `sys_dict`(`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES ('1362234522133319682', '表单类型', 'from_type', '', 0, 'admin', '2021-02-18 10:56:34', NULL, NULL, 0);
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1362234653184348162', '1362234522133319682', '申请页面', '0', '', 1, 1, 'admin', '2021-02-18 10:57:05', 'admin', '2021-02-18 10:58:48');
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1362234675611291649', '1362234522133319682', '审批页面', '1', '', 1, 1, 'admin', '2021-02-18 10:57:10', 'admin', '2021-02-18 10:58:53');

CREATE TABLE `act_areacode_checker`  (
  `id` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `model_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '流程模型id',
  `node_key` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点KEY',
  `area_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '行政区划code',
  `user_name` varchar(3200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审核人',
  `create_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  `sys_org_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '所属部门',
  `realname` varchar(2550) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_model_key_code`(`model_id`, `node_key`, `area_code`) USING BTREE
) ENGINE = InnoDB COMMENT = '省市县审核人配置表'  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
