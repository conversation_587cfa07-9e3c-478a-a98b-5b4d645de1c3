-- 一、迁移新模型id
set @modeId:=190001;
-- 模型
select * from act_re_model where id_ = @modeId; 
-- 模型资源
select * from act_ge_bytearray b 
where name_ ='source' and id_  in (select EDITOR_SOURCE_VALUE_ID_ from act_re_model where id_ = @modeId) 
UNION all
select * from act_ge_bytearray b where name_ ='source-extra' and id_  in (select EDITOR_SOURCE_EXTRA_VALUE_ID_ from act_re_model where id_ = @modeId); --
-- 自定义模型节点信息
select * from act_z_node where model_id = @modeId;
-- 流程表单
select * from act_z_from where id in (select relate_id from act_z_node where model_id = @modeId and  type = 99);

-- 二、修改审核人
set @taskId:=387791;
select * from act_ru_identitylink
-- update act_ru_identitylink set USER_ID_ = 'zhangzw'
where TASK_ID_ = @taskId;
select * from act_hi_identitylink
-- update act_hi_identitylink set USER_ID_ ='zhangzw' 
where TASK_ID_ = @taskId;