package com.jky.boot.system.module.manage.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.model.SysUserSysDepartModel;
import org.jeecg.modules.system.vo.SysUserDepVo;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.system.module.manage.entity.JkySysUser;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 */
public interface JkySysUserMapper extends BaseMapper<JkySysUser> {

    @Select("select * from sys_user where username=  #{username} ")
    LoginUser getLoginUserByName(@Param("username") String username);

    @Select("select * from sys_user where username=  #{username} ")
    JkySysUser getUserByName(@Param("username") String username);
    /**
	 * 根据手机号查询用户信息
	 * @param phone
	 * @return
	 */
    @Select("select * from  sys_user  where phone = #{phone} and del_flag = 0")
	public JkySysUser getUserByPhone(@Param("phone") String phone);

	/**
	 * 根据用户名设置部门ID
	 * @param username
	 * @param departId
	 */
    @Update("UPDATE sys_user SET org_code = #{orgCode} where username = #{username}")
	void updateUserDepart(@Param("username") String username,@Param("orgCode") String orgCode);


    /**
	 *  根据用户Ids,查询用户所属部门名称信息
	 * @param userIds
	 * @return
	 */
    @Select("<script>"
    		+ "select d.depart_name,ud.user_id from sys_user_depart ud,sys_depart d where d.id = ud.dep_id and ud.user_id in"+
    		"  <foreach collection='userIds' index='index' item='id' open='(' separator=',' close=')'>"+
    		"	 #{id}"+
    		"  </foreach>"
    		+"</script>")
	List<SysUserDepVo> getDepNamesByUserIds(@Param("userIds")List<String> userIds);

	/**
	 * 彻底删除被逻辑删除的用户
	 */
	@Delete("DELETE FROM sys_user WHERE del_flag = 1 AND id IN (${userIds})")
	int deleteLogicDeleted(@Param("userIds") String userIds);

	/**
	 * 根据邮箱查询用户信息
	 * @param email
	 * @return
	 */
	@Select("select * from  sys_user  where email = #{email} and del_flag = 0")
	public JkySysUser getUserByEmail(@Param("email")String email);

	/**
	 *  根据部门Ids,查询部门下用户信息
	 * @param departIds
	 * @return
	 */
	List<JkySysUser> queryByDepIds(@Param("departIds")List<String> departIds,@Param("username") String username);


	/**
	 *  根据部门Id查询用户信息
	 * @param page
	 * @param departId
	 * @return
	 */
	IPage<JkySysUser> getUserByDepId(Page<?> page, @Param("departId") String departId, @Param("username") String username);

	/**
	 *  根据部门Ids,查询部门下用户信息
	 * @param page
	 * @param departIds
	 * @return
	 */
	IPage<JkySysUser> getUserByDepIds(Page<?> page, @Param("departIds") List<String> departIds, @Param("username") String username);

	/**
	 * 根据角色Id查询用户信息
	 * @param page
	 * @param
	 * @return
	 */
	IPage<JkySysUser> getUserByRoleId(Page<?> page, @Param("roleId") String roleId, @Param("username") String username);

	/**
	 * 根据 orgCode 查询用户，包括子部门下的用户
	 *
	 * @param page 分页对象, xml中可以从里面进行取值,传递参数 Page 即自动分页,必须放在第一位(你可以继承Page实现自己的分页对象)
	 * @param orgCode
	 * @param userParams 用户查询条件，可为空
	 * @return
	 */
	List<SysUserSysDepartModel> getUserByOrgCode(IPage<?> page, @Param("orgCode") String orgCode, @Param("userParams") JkySysUser userParams);


   /**
    * 查询 getUserByOrgCode 的Total
    *
    * @param orgCode
    * @param userParams 用户查询条件，可为空
    * @return
    */
   Integer getUserByOrgCodeTotal(@Param("orgCode") String orgCode, @Param("userParams") JkySysUser userParams);

   /**
    * <AUTHOR>
    * @Date 2019/12/13 16:10
    * @Description: 批量删除角色与用户关系
    */
	void deleteBathRoleUserRelation(@Param("roleIdArray") String[] roleIdArray);

   /**
    * <AUTHOR>
    * @Date 2019/12/13 16:10
    * @Description: 批量删除角色与权限关系
    */
	void deleteBathRolePermissionRelation(@Param("roleIdArray") String[] roleIdArray);

	/**
	 * 查询被逻辑删除的用户
	 */
	List<JkySysUser> selectLogicDeleted(@Param(Constants.WRAPPER) Wrapper<JkySysUser> wrapper);

	/**
	 * 还原被逻辑删除的用户
	 */
	int revertLogicDeleted(@Param("userIds") String userIds, @Param("entity") JkySysUser entity);

   /** 更新空字符串为null【此写法有sql注入风险，禁止随便用】 */
   int updateNullByEmptyString(@Param("fieldName") String fieldName);

	@Select("SELECT r.role_code FROM `sys_user_role` userRole LEFT JOIN `sys_role` r on userRole.role_id = r.id WHERE userRole.`user_id` = #{userId} ")
	public List<String> getRolesByUserId(@Param("userId")String userId);
}
