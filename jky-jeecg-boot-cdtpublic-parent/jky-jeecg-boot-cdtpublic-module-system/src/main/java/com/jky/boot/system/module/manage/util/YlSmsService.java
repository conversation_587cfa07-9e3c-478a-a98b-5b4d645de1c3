package com.jky.boot.system.module.manage.util;


import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;


/**
 * 维那多短信接口工具类
 * <AUTHOR>
 */
@Service(value = "jpYlSms")
@Slf4j
public class YlSmsService {

	int SMS_TYPE =   1;//短信接口:0维纳多，1索游

    @Value(value = "${jky.sms.sy.url:http://api.sms.exiaozhi.com:8899/sms/SMSSendYD.action}")
	String SMS_URL = "http://api.sms.exiaozhi.com:8899/sms/SMSSendYD.action";//短信接口地址

    @Value(value = "${jky.sms.sy-account:902247}")
	String SMS_ACCOUNT = "902247";//账号

    @Value(value = "${jky.sms.sy-password:MiuPOsLuCYjdeFvqe84S}")
	String SMS_PASSWORD = "MiuPOsLuCYjdeFvqe84S";//密码

	/**
	 * 发送短信（参数type：0维纳多，1索游）
	 * @param tel 多个电话号码用逗号分开，最多25个
	 * @param smscontent 短信内容
	 * @param systemName 系统名，短信前缀 如“浙江省交科院”
	 * @return ResultInfo 发送结果及错误信息
	 */
	public Result<?> sendSmsInfo(String tel, String smscontent,String systemName){
		smscontent = "【"+systemName+"】" + smscontent;
		return sendSmsInfoSY(tel, smscontent);
	}
	static Map<String,String> errMsgMap = new HashMap<String,String>();
	static {
		errMsgMap.put("6002","发送帐号不正确");
		errMsgMap.put("6008","无效的手机号码");
		errMsgMap.put("6009","手机号码是黑名单");
		errMsgMap.put("6010","发送密码不正确");
		errMsgMap.put("6011","短信内容超过了最大长度限制");
		errMsgMap.put("6012","用户设置了 ip 限制");
		errMsgMap.put("6013","短信账号余额不足");
		errMsgMap.put("6014","发送短信内容不能为空");
		errMsgMap.put("6015","发送内容中含非法字符");
		errMsgMap.put("6019","短信账户已停机，请联系客服");
		errMsgMap.put("6021","扩展号码未备案");
		errMsgMap.put("6023","发送手机号码超过太长，已超过 300 个号码");
		errMsgMap.put("6024","定制时间不正确");
		errMsgMap.put("6025","扩展号码太长(总长度超过 20 位)");

	}
    //===================================================索游短信接口================================================
    //索游短信发送错误信息
	static Map<String,String> errMsgMapSY = new HashMap<String,String>();
	static {
		errMsgMapSY.put("0","接受成功");
		errMsgMapSY.put("-100","系统错误");
		errMsgMapSY.put("-101","用户名或密码错误");
		errMsgMapSY.put("-102","手机号码串长度非法");//25个号码以内
		errMsgMapSY.put("-103","短信为空或超长");//500字以内
		errMsgMapSY.put("-104","授权余额不足");
		errMsgMapSY.put("-105","手机号码格式不正确");
		errMsgMapSY.put("-106","IP地址未绑定");
	}

    /**
	 * 索游发送短信消息
	 * @param tel 多个电话号码用逗号分开，最多25个
	 * @param smscontent 短信内容
	 * @return ResultInfo 发送结果及错误信息
	 */
	public Result<?> sendSmsInfoSY(String tel, String smscontent){
		try {
			String message = getSendString(SMS_ACCOUNT,tel,SMS_PASSWORD,smscontent,"200001");
	    	String strReturn = sendPost(SMS_URL,message);//发短信返回结果
	    	String errId = strReturn.split(",")[0];//errId错误代码
	    	boolean sendState = false;
	    	String sendMessage = "";
	    	if(strReturn == null || "".equals(strReturn)){
	    		sendState = false;
	    		sendMessage ="发送超时。";
	    	}else if("0".equals(errId)){//代码返回为0表示发送成功
	    		sendState = true;
	    		sendMessage ="发送成功。";
	    	}else{
    			String errMsg = "短信发送失败，错误代码为" + errId + "，错误说明："+errMsgMapSY.get(errId)+"。";
    			System.out.println(errMsg);
    			sendMessage = errMsgMapSY.get(errId) == null ? "errId="+errId :errMsgMapSY.get(errId);
    			log.error(errMsg);
    		}
	    	if(sendState){
				return Result.ok(sendMessage);
			}else{
				return Result.error(sendMessage);
			}
		} catch (Exception e) {
			log.error("短信发送系统错误。",e);
			return Result.error("系统错误。"+e.getMessage());
		}

	}

	/**
     * 索游请求数据
     * @param url 请求地址
     * @param param url参数
     * @return String
     */
	public static String sendPost(String url, String param) {
		if (url == null) {
			return null;
		}
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";
		try {
			URL realUrl = new URL(url);
			URLConnection conn = realUrl.openConnection();

			conn.setConnectTimeout(4000);
			conn.setReadTimeout(8000);
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("Content-Type",
					"application/x-www-form-urlencoded;charset=gbk");

			conn.setDoOutput(true);
			conn.setDoInput(true);
			out = new PrintWriter(conn.getOutputStream());
			out.print(param);
			out.flush();
			in = new BufferedReader(
					new InputStreamReader(conn.getInputStream()));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			log.error("短信发送连接超时。",e);
			result = "";
		}
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}

	//拼接发送短信URL参数
	public static String getSendString(String userid,String mobiles,String pwd,String msg,String extnum) throws UnsupportedEncodingException{
		StringBuilder buf = new StringBuilder();
		buf.append("usr=");
		buf.append(userid);
		buf.append("&mobile=");
		buf.append(mobiles);
		buf.append("&sms=");
		buf.append(URLEncoder.encode(msg, "GBK"));
		buf.append("&extdsrcid=");
		buf.append(extnum);
		buf.append("&sign=");
		buf.append(string2MD5(userid+"|"+pwd+"|"+mobiles));
		return buf.toString();
	}

	public static String string2MD5(String inStr) {
		MessageDigest md5 = null;
		try {
			md5 = MessageDigest.getInstance("MD5");
		} catch (Exception e) {
			System.out.println(e.toString());
			e.printStackTrace();
			return "";
		}
		char[] charArray = inStr.toCharArray();
		byte[] byteArray = new byte[charArray.length];

		for (int i = 0; i < charArray.length; i++)
			byteArray[i] = (byte) charArray[i];
		byte[] md5Bytes = md5.digest(byteArray);
		StringBuffer hexValue = new StringBuffer();
		for (int i = 0; i < md5Bytes.length; i++) {
			int val = ((int) md5Bytes[i]) & 0xff;
			if (val < 16)
				hexValue.append("0");
			hexValue.append(Integer.toHexString(val));
		}
		return hexValue.toString();
	}


}
