package com.jky.boot.system.module.manage.util;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.common.exception.ServiceException;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.entity.JkySysUser;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.service.JkySysUserService;
import com.jky.common.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全服务工具类
 */
@Component
public class JkSecurityUtils {
    @Autowired
    private JkySysDepartService sysDeptService;

    @Autowired
    private JkySysUserService sysUserService;

    /**驾校编码长度*/
    private static final int inscodeLength = 16;

    private static JkySysDepartService jkySysDepartService;
    private static JkySysUserService jkySysUserService;

    @PostConstruct
    public void init() {
        jkySysDepartService = sysDeptService;
        jkySysUserService = sysUserService;
    }

    /**
     * 用户ID
     **/
    public static String getUserId() {
        try {
            return getLoginUser().getId();
        } catch (Exception e) {
            throw new ServiceException("获取用户ID异常", HttpStatus.UNAUTHORIZED.value());
        }
    }

    /**
     * 获取用户部门编码
     **/
    public static String getDeptOrgCode() {
        try {
            return getLoginUser().getOrgCode();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED.value());
        }
    }

    /**
     * 获取用户部门id
     */
    public static String getDeptId() {
        return jkySysDepartService.selectDeptByleader(JkSecurityUtils.getDeptOrgCode()).getId();
    }


    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED.value());
        }
    }

    /**
     * 获取用户真实姓名
     **/
    public static String getRealname() {
        try {
            return getLoginUser().getRealname();
        } catch (Exception e) {
            throw new ServiceException("获取用户账户异常", HttpStatus.UNAUTHORIZED.value());
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            throw new ServiceException("获取用户信息异常", HttpStatus.UNAUTHORIZED.value());
        }
    }

    /**
     * 是否为管理员
     *
     * @return 结果
     */
    public static boolean isAdmin() {
        return false;
    }

    /**
     * 是否为学校账户
     *
     * @return 结果
     */
    public static boolean isSchoolOperator() {
        String inscode = getLoginUser().getOrgCode();
        return inscode.length() == inscodeLength;
    }


    /**
     * 递归获取部门id
     * @param orgCode 部门编码
     * @return
     */
    private static List<String> getDeptIdStartWithPriorIdByOrgCode(String orgCode){
        List<JkySysDepart> list = jkySysDepartService.getDeptStartWithPriorIdByOrgCode(orgCode);
        if(list!=null&&list.size()>0){
            return list.stream().map(JkySysDepart::getId).distinct().collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 递归获取部门id
     * @param deptId 部门id
     * @return
     */
    private static List<String> getDeptIdStartWithPriorIdByDeptId(String deptId){
        List<JkySysDepart> list = jkySysDepartService.getDeptStartWithPriorIdByDeptId(deptId);
        if(list!=null&&list.size()>0){
            return list.stream().map(JkySysDepart::getId).distinct().collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 递归获取部门编码
     * @param orgCode 部门编码
     * @return
     */
    public static List<String> getOrgCodeStartWithPriorIdByOrgCode(String orgCode){
        List<JkySysDepart> list = jkySysDepartService.getDeptStartWithPriorIdByOrgCode(orgCode);
        if(list!=null&&list.size()>0){
            return list.stream().map(JkySysDepart::getOrgCode).distinct().collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 递归获取部门编码
     * @param deptId 部门id
     * @return
     */
    public static List<String> getOrgCodeStartWithPriorIdByDeptId(String deptId){
        List<JkySysDepart> list = jkySysDepartService.getDeptStartWithPriorIdByDeptId(deptId);
        if(list!=null&&list.size()>0){
            return list.stream().map(JkySysDepart::getOrgCode).distinct().collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 取部门id/orgcode交集
     * @param first
     * @param next
     * @return
     */
    public static List<String> deptStrBeMixed(List<String> first,List<String> next){
        if(first == null) return null;
        if(next == null) return first;
        return first.stream().filter(item -> next.contains(item)).collect(Collectors.toList());
    }


    /**
     * 用户权限+部门id 交集
     * @param choiceId 部门id
     * @return
     */
    public static List<String> getOrgcodeBeMixedChoiceRoleBydeptId(String choiceId){
        List<String> role = getOrgCodeStartWithPriorIdByDeptId(getDeptId());
        if(role == null) return null;
        if(StringUtils.isBlank(choiceId)) return role;

        //解决前端有时候传id,有时候传orgcode
        choiceId = JkSecurityUtils.getDeptIdByUnknow(choiceId);

        List<String> choice = getOrgCodeStartWithPriorIdByDeptId(choiceId);
        List<String> returnStr = role.stream().filter(item -> choice.contains(item)).collect(Collectors.toList());
        if(returnStr == null || returnStr.size() < 1){
            returnStr = returnStr == null ? new ArrayList<>() : returnStr;
            returnStr.add("-1");
        }
        return returnStr;
    }

    /**
     * 用户权限+部门id 交集
     * @param choiceId 部门id
     * @param next 部门idL
     * @return
     */
    public static List<String> getOrgcodeBeMixedChoiceRoleBydeptId(String choiceId,List<String> next){
        List<String> first = getOrgcodeBeMixedChoiceRoleBydeptId(choiceId);
        if(next == null || next.size() < 1){
            return first;
        }
        List<String> returnStr = first.stream().filter(item -> next.contains(item)).collect(Collectors.toList());
        if(returnStr == null || returnStr.size() < 1){
            returnStr = returnStr == null ? new ArrayList<>() : returnStr;
            returnStr.add("-1");
        }
        return returnStr;
    }



    /**
     * 获取默认培训机构编号
     * @return
     */
    public static String getDefaultInscode() {
        String orgCode = getDeptOrgCode();
        if(StringUtils.isBlank(orgCode)){
            return "1111111111111111";
        }
        if(orgCode.length()>=inscodeLength){
            return orgCode.substring(0,12)+"0000";
        }else{
            return StrUtil.padAfter(orgCode, inscodeLength , "0");
        }
    }


    /**
     *
     * @param idOrCode
     * @return
     * （驾校编码是16位）
     */
    public static String getDeptIdByUnknow(String idOrCode){
        if(StringUtils.isBlank(idOrCode)){
            return null;
        }
        if(idOrCode.length() == inscodeLength){
            //code转id
            return jkySysDepartService.selectDeptByleader(idOrCode).getId();
        }
        return idOrCode;
    }

    public static String getOrgCodeByUnknown(String idOrCode){
        if(StringUtils.isBlank(idOrCode)){
            return null;
        }
        if(idOrCode.length() == inscodeLength){
            // id 转 code
            return idOrCode;
        }
        return jkySysDepartService.selectDeptById(idOrCode).getOrgCode();
    }

    /**
     * 根据传来的 insCode 或 departId，返回部门类
     */
    public static JkySysDepart getDepartByUnknown(String idOrCode){
        if(idOrCode.length() == inscodeLength){
            return jkySysDepartService.selectDeptByleader(idOrCode);
        }
        return jkySysDepartService.selectDeptById(idOrCode);
    }

    /**
     * 返回 inscode 对应的所有用户的账号
     *
     * @param insCode 驾校编号
     * @return 该驾校所有用户账号，用“，”连接
     */
    public static String getUserAccount(String insCode) {
        List<JkySysUser> list = jkySysUserService.list(
                Wrappers.<JkySysUser>lambdaQuery()
                        .eq(JkySysUser::getOrgCode, insCode)
        );
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> collect = list.stream().map(JkySysUser::getUsername).collect(Collectors.toList());
        return String.join(",", collect);
    }
}
