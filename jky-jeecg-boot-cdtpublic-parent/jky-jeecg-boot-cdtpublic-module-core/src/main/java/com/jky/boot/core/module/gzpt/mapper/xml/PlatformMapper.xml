<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.PlatformMapper">

    <select id="getByPlatformSerialNumber" resultType="com.jky.boot.core.module.gzpt.entity.Platform">
        select id,
               serial_number as serialNumber,
               platform_name as platformName,
               api,
               ip,
               crdate
        from t_m_platform
        where serial_number = #{serialNumber}
    </select>
</mapper>