package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 学员合同表
 */
@Data
@TableName("t_m_student_contract")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class StudentContract implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 学员编号
     */
    private String stunum;
    /**
     * 合同存放路径
     */
    private String pdfurl;
    /**
     * 合同签订/创建时间
     */
    private Date signtime;
    /**
     * 课堂单价
     */
    private BigDecimal theoryCostPer;
    /**
     * 课堂总额
     */
    private BigDecimal theoryCost;
    /**
     * 模拟单价
     */
    private BigDecimal imiCostPer;
    /**
     * 模拟总额
     */
    private BigDecimal imiCost;
	/**
	 * 远程单价
	 */
	private BigDecimal remoteCostPer;
	/**
	 * 科目二实操单价
	 */
	private BigDecimal sub2CostPer;
    /**
     * 科二实操总额
     */
    private BigDecimal sub2Cost;
	/**
	 * 科目三实操单价
	 */
	private BigDecimal sub3CostPer;
    /**
     * 科三实操总额
     */
    private BigDecimal sub3Cost;
    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;
    /**
     * 是否签名
     */
    private Integer isSign;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
}
