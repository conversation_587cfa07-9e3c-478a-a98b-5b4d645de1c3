package com.jky.boot.core.module.gzpt.utils;

import com.jky.boot.core.module.gzpt.vo.Result;
import com.sun.org.apache.xerces.internal.impl.dv.util.HexBin;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;

import javax.crypto.Cipher;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;


/**
 * http工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {

    private static final String ENCODE = "utf-8";


    /**
     * post请求
     *
     * @param path
     * @param data
     * @return
     */
    public static Result sendHttpClientPost(String path, String data) {

        System.out.println("path: " + path);
        System.out.println("data: " + data);
        Result response;
        String result = null;
        try {
            // 使用post方式提交数据
            HttpPost httpPost = new HttpPost(path);
            // 解决中文乱码问题
            StringEntity entity = new StringEntity(data, ENCODE);
            entity.setContentEncoding(ENCODE);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            // 执行post请求，并获取服务器端的响应HttpResponse
			/*HttpResponse httpResponse = new DefaultHttpClient()
					.execute(httpPost);*/
            //执行post请求，设置超时时间
            HttpResponse httpResponse = MyHttpClient.getHttpClient(60000, 60000)
                    .execute(httpPost);
            // 获取响应消息实体
            HttpEntity httpEntity = httpResponse.getEntity();
            // 获取服务器端返回的状态码和输入流，将输入流转换成字符串
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                InputStream inputStream = httpResponse.getEntity().getContent();
                result = changeInputStream(inputStream);
                System.out.println("result----:" + result);
                response = new Result(result);
            } else {
                response = new Result();
                response.setMessage("访问失败");
            }
        } catch (Exception e) {
            response = new Result();
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * delete
     *
     * @param data
     * @return
     */
    public static Result sendHttpClientDelete(String path, String data) {
        Result response;
        String result;
        try {
            HttpDelete httpDelete = new HttpDelete(path);
            HttpResponse httpResponse = new DefaultHttpClient()
                    .execute(httpDelete);

            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                InputStream inputStream = httpResponse.getEntity().getContent();
                result = changeInputStream(inputStream);
                response = new Result(result);
            } else {
                response = new Result();
                response.setMessage("访问失败");
            }
        } catch (Exception e) {
            response = new Result();
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * Put
     *
     * @param data
     * @return
     */
    public static Result sendHttpClientPut(String path, String data) {

        System.out.println("path: " + path);
        System.out.println("data: " + data);
        Result response;
        String result = null;
        try {
            // 使用post方式提交数据
            HttpPut httpPut = new HttpPut(path);
            // 解决中文乱码问题
            StringEntity entity = new StringEntity(data, ENCODE);
            entity.setContentEncoding(ENCODE);
            entity.setContentType("application/json");
            httpPut.setEntity(entity);
            // 执行post请求，并获取服务器端的响应HttpResponse
			/*HttpResponse httpResponse = new DefaultHttpClient()
					.execute(httpPost);*/
            //执行post请求，设置超时时间
            HttpResponse httpResponse = MyHttpClient.getHttpClient(60000, 60000)
                    .execute(httpPut);
            // 获取响应消息实体
            HttpEntity httpEntity = httpResponse.getEntity();
            // 获取服务器端返回的状态码和输入流，将输入流转换成字符串
            if (httpResponse.getStatusLine().getStatusCode() == 200) {
                InputStream inputStream = httpResponse.getEntity().getContent();
                result = changeInputStream(inputStream);
                System.out.println("result----:" + result);
                response = new Result(result);
            } else {
                response = new Result();
                response.setMessage("访问失败");
            }
        } catch (Exception e) {
            response = new Result();
            response.setMessage(e.getMessage());
        }
        return response;
    }


    /**
     * 把从输入流InputStream按指定编码格式ENCODE变成字符串String
     *
     * @param inputStream
     * @return
     */
    public static String changeInputStream(InputStream inputStream) {
        // ByteArrayOutputStream 一般叫做内存流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int len = 0;
        String result = "";
        if (inputStream != null) {

            try {
                while ((len = inputStream.read(data)) != -1) {
                    byteArrayOutputStream.write(data, 0, len);

                }
                result = byteArrayOutputStream.toString(ENCODE);

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static byte[] getBytes(File file) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }

    public static String sign(String data, long timestamp, PrivateKey key)
            throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(data.getBytes(StandardCharsets.UTF_8));
        md.update(toBE(timestamp));
        byte[] hash = md.digest();
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encrypted = cipher.doFinal(hash);
        return HexBin.encode(encrypted);
    }

    public static byte[] toBE(long data) {
        byte[] buffer = new byte[8];
        buffer[0] = (byte) (data >>> 56);
        buffer[1] = (byte) (data >>> 48);
        buffer[2] = (byte) (data >>> 40);
        buffer[3] = (byte) (data >>> 32);
        buffer[4] = (byte) (data >>> 24);
        buffer[5] = (byte) (data >>> 16);
        buffer[6] = (byte) (data >>> 8);
        buffer[7] = (byte) (data);
        return buffer;
    }


}

