package com.jky.boot.core.module.gzpt.service.impl;

import com.jky.boot.core.module.gzpt.entity.Bank;
import com.jky.boot.core.module.gzpt.mapper.BankMapper;
import com.jky.boot.core.module.gzpt.service.IBankService;
import com.jky.boot.core.module.gzpt.vo.JhMsgsend;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 资金托管银行管理
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class BankServiceImpl extends ServiceImpl<BankMapper, Bank> implements IBankService {

    @Autowired
    private BankMapper bankMapper;
    @Override
    public int saveJhMegsend(JhMsgsend jhMsgsend) {
        return bankMapper.saveJhMegsend(jhMsgsend);
    }
}
