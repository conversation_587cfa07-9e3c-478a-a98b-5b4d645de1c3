package com.jky.boot.core.module.gzpt.entity;

import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.IgnoreScanAnno;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_stage_trainning_time
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Data
@TableName("t_m_stage_trainning_time")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_stage_trainning_time对象", description="报审申请管理对象")
public class StageTrainningTime implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;
	/**培训机构名称*/
	@Excel(name = "培训机构名称", width = 15)
    @ApiModelProperty(value = "培训机构名称")
    private String insname;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private String stunum;
	/**身份证*/
//	@Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    @CryptoFieldAnno
    private String idcard;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    @CryptoFieldAnno
    private String stuname;
	/**科目*/
	@Excel(name = "科目", width = 15)
    @ApiModelProperty(value = "科目")
    @Dict(dicCode = "sys_km")
    private Long subject;
	/**总学时(分)*/
	@Excel(name = "总学时(分)", width = 15)
    @ApiModelProperty(value = "总学时(分)")
    private Long totaltime;
	/**实操学时(分)*/
	@Excel(name = "实操学时(分)", width = 15)
    @ApiModelProperty(value = "实操学时(分)")
    private Long vehicletime;
	/**课堂学时(分)*/
	@Excel(name = "课堂学时(分)", width = 15)
    @ApiModelProperty(value = "课堂学时(分)")
    private Long classtime;
	/**模拟学时(分)*/
	@Excel(name = "模拟学时(分)", width = 15)
    @ApiModelProperty(value = "模拟学时(分)")
    private Long simulatortime;
	/**远程学时(分)*/
	@Excel(name = "远程学时(分)", width = 15)
    @ApiModelProperty(value = "远程学时(分)")
    private Long networktime;
	/**里程(h/km)*/
	@Excel(name = "里程(h/km)", width = 15)
    @ApiModelProperty(value = "里程(h/km)")
    private BigDecimal mileage;
	/**报名时间*/
	@Excel(name = "报名时间", width = 15)
    @ApiModelProperty(value = "报名时间")
    private String applydate;
	/**审核状态 0=未审核,1=审核通过,2=推送失败*/
	@Excel(name = "审核状态 0=未审核,1=审核通过,2=推送失败", width = 15)
    @ApiModelProperty(value = "审核状态 0=未审核,1=审核通过,2=推送失败")
    @Dict(dicCode = "sys_general_audit")
    private String auditstate;
	/**审核人*/
	@Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    @CryptoFieldAnno
    private String operatorname;
	/**审核时间*/
	@Excel(name = "审核时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "审核时间")
    private Date auditdate;
	/**市区编号*/
	@Excel(name = "市区编号", width = 15)
    @ApiModelProperty(value = "市区编号")
    private String district;
	/**车型*/
	@Excel(name = "车型", width = 15)
    @ApiModelProperty(value = "车型")
    private String traintype;
	/**是否校验 0=未校验,1=已校验*/
	@Excel(name = "是否校验 0=未校验,1=已校验", width = 15)
    @ApiModelProperty(value = "是否校验 0=未校验,1=已校验")
    @Dict(dicCode = "sys_check")
    private Integer ispushsj;
	/**是否推送计时 0=未推送,1=推送成功,2=推送失败*/
	@Excel(name = "是否推送计时 0=未推送,1=推送成功,2=推送失败", width = 15)
    @ApiModelProperty(value = "是否推送计时 0=未推送,1=推送成功,2=推送失败")
    @Dict(dicCode = "sys_push")
    private Integer ispushjs;
	/**是否人脸对比 0=未对比,1=已对比*/
	@Excel(name = "是否人脸对比 0=未对比,1=已对比", width = 15)
    @ApiModelProperty(value = "是否人脸对比 0=未对比,1=已对比")
    private Integer isface;
	/**创建时间*/
	@Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date crdate;
	/**推送计时时间*/
	@Excel(name = "推送计时时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "推送计时时间")
    private Date pushjsdate;
	/**校验时间*/
	@Excel(name = "校验时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "校验时间")
    private Date pushsjdate;
	/**有效学时*/
	@Excel(name = "有效学时", width = 15)
    @ApiModelProperty(value = "有效学时")
    private Long duration;
	/**是否推送银行 0 未推送 1 推送成功 2 推送失败*/
	@Excel(name = "是否推送银行", width = 15,replace = {"未推送_0","推送成功_1","推送失败_2"})
    @ApiModelProperty(value = "是否推送银行 0 未推送 1 推送成功 2 推送失败")
    @Dict(dicCode = "sys_push")
    private Integer isbank;
	/**说明*/
	@Excel(name = "说明", width = 15)
    @ApiModelProperty(value = "说明")
    private String resultmeg;
	/**电子教学日志+培训时间*/
	@Excel(name = "电子教学日志+培训时间", width = 15)
    @ApiModelProperty(value = "电子教学日志+培训时间")
    private String recnums;

    @TableField(exist = false)
    @Excel(name = "消息")
    private String message;
    @TableField(exist = false)
    @Excel(name = "教练员编号")
    private String coachnum;
    @TableField(exist = false)
    @Excel(name = "教练员")
    @CryptoFieldAnno
    private String coachname;
    @TableField(exist = false)
    @Excel(name = "性别", replace = {"男_1","女_2"})
    @Dict(dicCode = "sys_user_sex")
    private String sex;
    @TableField(exist = false)
    @IgnoreScanAnno
    private Long[] ids;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    @IgnoreScanAnno
    private List<String> inscodes;

    @TableField(exist = false)
    private String pushjsdate_begin;
    @TableField(exist = false)
    private String pushjsdate_end;

    @TableField(exist = false)
    private String auditdate_begin;
    @TableField(exist = false)
    private String auditdate_end;

    @TableField(exist = false)
    private String pushsjdate_begin;
    @TableField(exist = false)
    private String pushsjdate_end;




	/**迁移追加字段*/
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**迁移追加字段*/
}
