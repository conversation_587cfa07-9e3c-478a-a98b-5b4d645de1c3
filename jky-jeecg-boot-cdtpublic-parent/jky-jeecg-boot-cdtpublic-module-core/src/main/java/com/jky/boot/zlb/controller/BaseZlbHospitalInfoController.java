package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.zlb.entity.BaseZlbHospitalInfo;
import com.jky.boot.zlb.service.IBaseZlbHospitalInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: base_zlb_hospital_info
 * @Author: jeecg-boot
 * @Date:   2023-06-15
 * @Version: V1.0
 */
@Api(tags="base_zlb_hospital_info")
@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/zlb/baseZlbHospitalInfo")
@Slf4j
public class BaseZlbHospitalInfoController extends JeecgController<BaseZlbHospitalInfo, IBaseZlbHospitalInfoService> {
	@Autowired
	private IBaseZlbHospitalInfoService baseZlbHospitalInfoService;
	 @Autowired
	 private OssZWYUtils ossZWYUtils;
	/**
	 * 分页列表查询
	 *
	 * @param baseZlbHospitalInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "base_zlb_hospital_info-分页列表查询")
	@ApiOperation(value="base_zlb_hospital_info-分页列表查询", notes="base_zlb_hospital_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BaseZlbHospitalInfo>> queryPageList(BaseZlbHospitalInfo baseZlbHospitalInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<BaseZlbHospitalInfo> queryWrapper = QueryGenerator.initQueryWrapper(baseZlbHospitalInfo, req.getParameterMap());
		queryWrapper.setEntityClass(BaseZlbHospitalInfo.class);
		Page<BaseZlbHospitalInfo> page = new Page<BaseZlbHospitalInfo>(pageNo, pageSize);
		IPage<BaseZlbHospitalInfo> pageList = baseZlbHospitalInfoService.page(page, queryWrapper);
		for (BaseZlbHospitalInfo baseZlbHospitalInfo1 : pageList.getRecords()){
			String photopath = baseZlbHospitalInfo1.getLogo();
			if (StringUtils.isNotBlank(photopath) && photopath.contains(OssZWYUtils.UPLOAD_PATH)) {
				baseZlbHospitalInfo1.setLogo(ossZWYUtils.getPhotoUrl(photopath));
			}
		}
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param baseZlbHospitalInfo
	 * @return
	 */
	@AutoLog(value = "base_zlb_hospital_info-添加")
	@ApiOperation(value="base_zlb_hospital_info-添加", notes="base_zlb_hospital_info-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_hospital_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BaseZlbHospitalInfo baseZlbHospitalInfo) {
		baseZlbHospitalInfoService.save(baseZlbHospitalInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param baseZlbHospitalInfo
	 * @return
	 */
	@AutoLog(value = "base_zlb_hospital_info-编辑")
	@ApiOperation(value="base_zlb_hospital_info-编辑", notes="base_zlb_hospital_info-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_hospital_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BaseZlbHospitalInfo baseZlbHospitalInfo) {
		baseZlbHospitalInfo.setLogo(OssZWYUtils.subStr(baseZlbHospitalInfo.getLogo()));
		baseZlbHospitalInfoService.updateById(baseZlbHospitalInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "base_zlb_hospital_info-通过id删除")
	@ApiOperation(value="base_zlb_hospital_info-通过id删除", notes="base_zlb_hospital_info-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_hospital_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		baseZlbHospitalInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "base_zlb_hospital_info-批量删除")
	@ApiOperation(value="base_zlb_hospital_info-批量删除", notes="base_zlb_hospital_info-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:base_zlb_hospital_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.baseZlbHospitalInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "base_zlb_hospital_info-通过id查询")
	@ApiOperation(value="base_zlb_hospital_info-通过id查询", notes="base_zlb_hospital_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BaseZlbHospitalInfo> queryById(@RequestParam(name="id",required=true) String id) {
		BaseZlbHospitalInfo baseZlbHospitalInfo = baseZlbHospitalInfoService.getById(id);
		if(baseZlbHospitalInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(baseZlbHospitalInfo);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param baseZlbHospitalInfo
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:base_zlb_hospital_info:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, BaseZlbHospitalInfo baseZlbHospitalInfo) {
//        return super.exportXls(request, baseZlbHospitalInfo, BaseZlbHospitalInfo.class, "base_zlb_hospital_info");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("base_zlb_hospital_info:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, BaseZlbHospitalInfo.class);
//    }

	 /**
	  *
	  * @param areaCode 区域码 ，四位，如3306
	  * @param hospitalName 医院名称
	  * @param lon 经度
	  * @param lat 纬度
	  * @param type 1按距离升序， 2按价格升序
	  * @param score 4就是4星及以上，5就是五星
	  * @param pageNo
	  * @param pageSize
	  * @return
	  */
	 @GetMapping(value = "/searchHospital")
	 public Result<?> searchHospital(@RequestParam(name="areaCode", required = false) String areaCode,
									 @RequestParam(name="hospitalName", required = false) String hospitalName,
									 @RequestParam(name="lon", required = false) Double lon,
									 @RequestParam(name="lat", required = false) Double lat,
									 @RequestParam(name="type", required = false) Integer type,
									 @RequestParam(name="score", required = false) Integer score,
								     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 return Result.OK(baseZlbHospitalInfoService.searchHospital(areaCode,hospitalName, lon,lat,pageNo,pageSize,type,score));
	 }

}
