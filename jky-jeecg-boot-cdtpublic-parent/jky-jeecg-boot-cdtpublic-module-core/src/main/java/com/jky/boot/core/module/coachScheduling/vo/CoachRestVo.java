package com.jky.boot.core.module.coachScheduling.vo;

import com.jky.boot.core.module.coachScheduling.entity.CoachRest;
import lombok.Data;

import java.util.Date;
import java.util.Iterator;

@Data
public class CoachRestVo {
    private Integer reststart ;
    private Integer restEnd;
    private Date reststartDate;
    private Date restEndDate;
    private Integer resttype;
    private Double days;
    private Integer startHour;
    private Integer endHour;

    public CoachRestVo(Integer reststart, Integer restEnd, Date reststartDate, Date restEndDate, Integer resttype, Double days,Integer startHour,Integer endHour) {
        this.reststart = reststart;
        this.restEnd = restEnd;
        this.reststartDate = reststartDate;
        this.restEndDate = restEndDate;
        this.resttype = resttype;
        this.days = days;
        this.startHour = startHour;
        this.endHour = endHour;
    }

}
