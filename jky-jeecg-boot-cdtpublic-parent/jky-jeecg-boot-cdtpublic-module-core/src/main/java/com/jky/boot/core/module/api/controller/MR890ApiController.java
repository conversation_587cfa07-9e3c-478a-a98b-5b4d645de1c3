package com.jky.boot.core.module.api.controller;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.comparator.VersionComparator;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.ServletUtils;
import com.jky.boot.core.module.api.dto.DeviceStateUploadParams;
import com.jky.boot.core.module.api.dto.Mr890Result;
import com.jky.boot.core.module.api.dto.ZlbLogInOutQueryParams;
import com.jky.boot.core.module.api.util.MiaxisFaceUtil;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.mapper.TMInsDeviceMapper;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.service.JkySysUserService;
import com.jky.boot.zlb.entity.TMStudentInfoEnter;
import com.jky.boot.zlb.service.ITMStudentInfoEnterService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: MR890
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Api(tags = "MR890")
@RestController
@RequestMapping("/MR890")
@Slf4j
public class MR890ApiController {
    @Autowired
    private ITMMr890Service mr890Service;
    @Autowired
    private ITMMr890FaceRecordService mr890FaceRecordService;
    @Autowired
    private ITMMr890VersionService mr890VersionService;
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private JkySysDepartService sysDeptService;
    @Autowired
    private ITMInsDeviceService insDeviceService;

    public List<String> NO_NEED_KEY=Arrays.asList("A1","A2","A3","B1","B2");

    @Autowired
    private TMInsDeviceMapper tmInsDeviceMapper;

    @Autowired
    private OssZWYUtils ossZWYUtils;

    @Value("${white.ipList: ''}")
    private String legalIPList;

    @Autowired
    private JkySysUserService sysUserService;

    /**
     * MR890设备app向金华公众平台查询绑定信息
     */
    @AutoLog(value = "MR890设备app向金华公众平台查询绑定信息", operateType = CommonConstant.OPERATE_TYPE_1)
    //@RequiresPermissions("gzpt:mr890Info:devicebindQuery")
    @PostMapping("/devicebindQuery")
    public Mr890Result devicebindQuery(@RequestBody Map<String, Object> param) {
        Mr890Result mr890Result = checkIllegalIP("devicebindQuery");
        if(!mr890Result.successed()){
            return mr890Result;
        }
        log.info("[MR890查询绑定信息] param:{}", param);
        String deviceId = param.get("deviceId").toString();
        TMMr890 mr890 = mr890Service.getById(deviceId);
        if (mr890 == null) {
            return Mr890Result.filed("此设备不存在", 102);
        }
        HashMap<String, Object> map = new HashMap<>(16);
        map.put("centername", mr890.getCenterName());
        return Mr890Result.success(map);
    }

    /**
     * MR890设备app向金华公众平台上报设备状态信息
     */
    @PostMapping("/deviceStateUpload")
    public Mr890Result deviceStateUpload(@Validated @RequestBody DeviceStateUploadParams param) {
        Mr890Result mr890Result = checkIllegalIP("deviceStateUpload");
        if(!mr890Result.successed()){
            return mr890Result;
        }
        log.info("[MR890上报设备状态信息] param:{}", param);
        String versionName = param.getVersionName();
        String deviceId = param.getDeviceId();
        TMMr890 mr890 = mr890Service.getById(deviceId);
        if (mr890 == null) {
            return Mr890Result.filed("此设备不存在", 102);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("isUseFace", mr890.getUseFace());
        map.put("upgrade", 0);
        int compare = VersionComparator.INSTANCE.compare(versionName, "1.0.4");
        if (compare > 0) {
            Long insDeviceId = mr890.getInsDeviceId();
            TMInsDevice insDevice = insDeviceService.getById(insDeviceId);
            String ipList = insDevice.getIpList();
            HttpServletRequest request = ServletUtils.getRequest();
            String clientIp = ServletUtil.getClientIP(request);

            log.info("[测试ip地址] clientIp:{}", clientIp);
            if (StringUtils.isNotBlank(ipList) && !ipList.contains(clientIp)) {
                map.put("deviceIsAvailable", 1);
                map.put("devicesIsAvailableMsg", "此ip地址不在设备使用范围内");
            } else {
                map.put("deviceIsAvailable", 0);
            }
        }
        if (!mr890.getVersion().equals(versionName)) {
            TMMr890Version mr890Version = mr890VersionService.getOne(
                    Wrappers.<TMMr890Version>lambdaQuery().eq(TMMr890Version::getVersionName, mr890.getVersion())
            );
            if (mr890Version == null) {
                return Mr890Result.filed("版本号不存在");
            } else {
                map.put("upgrade", 1);
                String versionUrl = mr890Version.getVersionUrl();
                log.info("[获取apk包路径] versionUrl:{}", versionUrl);
                if (StringUtils.isNotBlank(versionUrl) && versionUrl.contains(OssZWYUtils.UPLOAD_PATH)) {
                    map.put("upgradeUrl", versionUrl);
                } else {
//                    map.put("upgradeUrl", "https://sxjp.jtj.sx.gov.cn:18086/sxgzpt" + mr890Version.getVersionUrl());
                    map.put("upgradeUrl", "http://10.75.11.197:18086/sxgzpt" + mr890Version.getVersionUrl());
                }
            }
        }

        TMMr890 mr890Update = new TMMr890();
        mr890Update.setDeviceId(deviceId);
        Integer state = param.getState();
        mr890Update.setStatus(state);
        if (state == 1) {
            mr890Update.setLastOnlineTime(new Date());
        }
        map.put("comparisontype", 1);//绍兴 0:公安库比对 1:证件照本地比对
        mr890Service.updateById(mr890Update);
        log.info("[MR890上报返回结果] map:{}", map);
        return Mr890Result.success(map);
    }

    /**
     * MR890设备app向金华公众平台上报学员实人认证信息
     */
    @PostMapping("/realPerAuthUpload")
    public Mr890Result realPerAuthUpload(@RequestBody TMMr890FaceRecord mr890FaceRecord) {
        Mr890Result mr890Result = checkIllegalIP("realPerAuthUpload");
        if(!mr890Result.successed()){
            return mr890Result;
        }
        log.info("[MR890上报学员实人认证信息] idcard:{}", mr890FaceRecord.getIdcard());
        mr890FaceRecord.setSignType(2);
        //查询上次认证成功的记录
//        TMMr890FaceRecord mr890fr = mr890FaceRecordService.selectMr890FaceRecordByIdCard(mr890FaceRecord.getIdcard());
        TMMr890FaceRecord mr890fr = mr890FaceRecordService.getOne(
                Wrappers.lambdaQuery(TMMr890FaceRecord.class)
                        .eq(TMMr890FaceRecord::getIdcard, mr890FaceRecord.getIdcard())
                        .eq(TMMr890FaceRecord::getStatus, 1)
                        .ne(TMMr890FaceRecord::getSignType, 7)
                        .gt(TMMr890FaceRecord::getCreateTime, DateUtils.getDate())
                        .orderByDesc(TMMr890FaceRecord::getCreateTime).last(" limit 1")
        );
        if (mr890fr != null) {
            Date createTime = mr890fr.getCreateTime();
            //距离上次认证大于45分钟为签退
            if (System.currentTimeMillis() - createTime.getTime() > 20 * 60 * 1000) {
                if (mr890fr.getSignType() == 2) {
                    mr890FaceRecord.setSignType(3);
                }
            } else if (mr890fr.getSignType() == 3) {
                mr890FaceRecord.setSignType(2);
            } else {
                mr890FaceRecord.setSignType(7);
            }
        }
        log.info("[认证信息] mr890fr:{}", mr890fr);
        if (mr890FaceRecord.getStatus() == 1) {
            SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, "9S51WGHAJ3L2HS6K".getBytes());
            Map<String, Object> aesMap = new HashMap<>();
            aesMap.put("deviceId", mr890FaceRecord.getDeviceId());
            aesMap.put("idcard", mr890FaceRecord.getIdcard());
            aesMap.put("createTime", DateUtils.dateTimeNow());
            String key = aes.encryptHex(JSON.toJSONString(aesMap));
            mr890FaceRecord.setKey(key);
        }

        if (StringUtils.isBlank(mr890FaceRecord.getOrderId())) {
            if (!StringUtils.isBlank(mr890FaceRecord.getSsoUrlPhoto())) {
                //离线,图片base64解密
                byte[] bytes = Base64Decoder.decode(mr890FaceRecord.getSsoUrlPhoto());
                InputStream inputStream = new ByteArrayInputStream(bytes);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                SimpleDateFormat fmt = new SimpleDateFormat("yyyy/MM/dd");
                String bizPath = OssZWYUtils.UPLOAD_PATH + "/" + fmt.format(calendar.getTime());
                String pictureUrl = ossZWYUtils.uploadFile(bizPath + "/" + System.currentTimeMillis() + ".png", inputStream);
                mr890FaceRecord.setSsoUrlPhoto(substrPhotoPath(pictureUrl));
            }
        } else if (!StringUtils.isBlank(mr890FaceRecord.getCid())) {
            String authRecord = null;
            try {
                authRecord = MiaxisFaceUtil.recordByOrderId(mr890FaceRecord.getCid(), mr890FaceRecord.getOrderId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (authRecord == null) {
                mr890FaceRecord.setSsoUrlPhoto("查询照片异常");
            } else {
                JSONObject jsonObject = JSON.parseObject(authRecord);
                JSONObject materialInfo = jsonObject.getJSONObject("MaterialInfo");
                JSONObject facialPictureFront = materialInfo.getJSONObject("facialPictureFront");
                String pictureUrl = facialPictureFront.getString("pictureUrl");
                mr890FaceRecord.setSsoUrlPhoto(pictureUrl);
            }
        }
        mr890FaceRecordService.save(mr890FaceRecord);
        return Mr890Result.success();
    }


    //@RequiresPermissions("gzpt:mr890Info:zlbloginoutQuery")
    @PostMapping("/zlbloginoutQuery")
    public Mr890Result zlbloginoutQuery(@Validated @RequestBody ZlbLogInOutQueryParams zlbLogInOutQueryParams) {
//        Mr890Result mr890Result = checkIllegalIP();
//        if(!mr890Result.successed()){
//            return mr890Result;
//        }
//        log.info("[学员实人认证信息查询] zlbLogInOutQueryParams:{}", zlbLogInOutQueryParams);
        String idcard = zlbLogInOutQueryParams.getIdcard();
        String key;
        Integer eventtype = zlbLogInOutQueryParams.getEventtype();
        if (eventtype == 2) {
            key = "loginkey";
        } else if (eventtype == 3) {
            key = "logoutkey";
        } else {
            return Mr890Result.filed("类型不正确");
        }
        Map<String, Object> map = new HashMap<>();
        Studentinfo studentinfo = new Studentinfo();
        studentinfo.setInscode(zlbLogInOutQueryParams.getInscode());
        studentinfo.setCardtype(zlbLogInOutQueryParams.getIdcard().length() == 18 ? "1" : "0");
        map.put("idcard", idcard);
        if(NO_NEED_KEY.contains(zlbLogInOutQueryParams.getTraintype())){
            map.put(key,"noneedkey");
            map.put("time", DateUtils.dateTimeNow());
            return Mr890Result.success(map);
        }

        if (!"1".equals(studentinfo.getCardtype())) {
            SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, "9S51WGHAJ3L2HS6K".getBytes());
            Map<String, Object> aesMap = new HashMap<>();
            aesMap.put("deviceId", "");
            aesMap.put("idcard", idcard);
            aesMap.put("createTime", DateUtils.dateTimeNow());
            String aKey = aes.encryptHex(JSON.toJSONString(aesMap));
            map.put(key, aKey);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String time = sdf.format(new Date());
            map.put("time", time);
            return Mr890Result.success(map);
        }

        JkySysDepart sysDept = sysDeptService.selectDeptByleader(studentinfo.getInscode());
        String deptId = sysDept.getId();
        List<TMInsDevice> insDeviceList = insDeviceService.selectInsDeviceListLikeInsCodeList(deptId);
//        log.info("[学员实人认证信息查询] deptId：{},insDeviceList:{}", deptId, insDeviceList);
        if (insDeviceList.isEmpty()) {
            map.put(key, null);
            map.put("time", null);
            return Mr890Result.success(map);
        }

        List<TMMr890FaceRecord> list = mr890FaceRecordService.list(
                Wrappers.lambdaQuery(TMMr890FaceRecord.class)
                        .eq(TMMr890FaceRecord::getIdcard, idcard)
                        .eq(TMMr890FaceRecord::getStatus, 2)
        );
        if (!list.isEmpty()) {
            // 有 status 为 2 的记录，则直接生成 key 返回
            SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, "9S51WGHAJ3L2HS6K".getBytes());
            Map<String, Object> aesMap = new HashMap<>();
            aesMap.put("deviceId", "");
            aesMap.put("idcard", idcard);
            aesMap.put("createTime", DateUtils.dateTimeNow());
            String aKey = aes.encryptHex(JSON.toJSONString(aesMap));
            map.put(key, aKey);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String time = sdf.format(new Date());
            map.put("time", time);
            return Mr890Result.success(map);
        }

        for (TMInsDevice insDevice : insDeviceList) {
            Integer enable = insDevice.getEnable();
            //默认为空  enable==1 开启 不生成key  0关闭设备 自动生成key
            if (enable == null || enable == 1) {
//                TMMr890FaceRecord mr890FaceRecord = mr890FaceRecordService.zlbloginoutQuery(idcard, eventtype);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.add(Calendar.MINUTE, -20);
                TMMr890FaceRecord mr890FaceRecord = mr890FaceRecordService.getOne(
                        Wrappers.lambdaQuery(TMMr890FaceRecord.class)
                                .eq(TMMr890FaceRecord::getIdcard, idcard)
                                .eq(TMMr890FaceRecord::getStatus, 1)
                                .gt(TMMr890FaceRecord::getCreateTime, calendar.getTime())
                                .orderByDesc(TMMr890FaceRecord::getCreateTime).last(" limit 1")
                );
                if (mr890FaceRecord != null) {
                    map.put(key, mr890FaceRecord.getKey());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                    String time = sdf.format(mr890FaceRecord.getCreateTime());
                    map.put("time", time);
                    //用完要更新，防止学员使用签退的key继续在设备上签到
//                    LambdaUpdateWrapper<TMMr890FaceRecord> updateWrapper= Wrappers.lambdaUpdate(TMMr890FaceRecord.class)
//                            .set(TMMr890FaceRecord::getStatus,0)
//                            .eq(TMMr890FaceRecord::getId,mr890FaceRecord.getId());
//                    mr890FaceRecordService.update(updateWrapper);
                    return Mr890Result.success(map);
                } else {
                    map.put(key, null);
                    map.put("time", null);
                }
            } else {
                //设备关停则直接生成 enable==0 关停
                SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, "9S51WGHAJ3L2HS6K".getBytes());
                Map<String, Object> aesMap = new HashMap<>();
                aesMap.put("deviceId", "");
                aesMap.put("idcard", idcard);
                aesMap.put("createTime", DateUtils.dateTimeNow());
                String aKey = aes.encryptHex(JSON.toJSONString(aesMap));
                map.put(key, aKey);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String time = sdf.format(new Date());
                map.put("time", time);
                return Mr890Result.success(map);
            }
        }
        log.info("[学员实人认证返回结果] map:{}", map);
        return Mr890Result.success(map);
    }

    //@RequiresPermissions("gzpt:mr890Info:getStudentInfoStatus")
    @GetMapping("/getStudentInfoStatus")
    public Mr890Result getStudentInfoStatus(@RequestParam String idcard, @RequestParam String deviceId) {
        Studentinfo studentinfo = studentinfoService.selectStudentinfoByIdCard(idcard);
        if (studentinfo == null) {
            log.info("[学员信息不存在] idcard:{},deviceId:{}", idcard, deviceId);
            return Mr890Result.filed("学员信息不存在", 101);
        }
        if (StringUtils.isNotBlank(studentinfo.getFromarea())) {
            log.info("[跨地市转校学员无需课堂培训] idcard:{},deviceId:{}", idcard, deviceId);
            return Mr890Result.filed("跨地市转校学员无需课堂培训", 101);
        }
        return Mr890Result.success();
//        String stuInscode = studentinfo.getInscode();
//        JkySysDepart sysDept = sysDeptService.selectDeptByleader(stuInscode);
//        String deptCode = sysDept.getId();
//
//        TMMr890 mr890 = mr890Service.getById(deviceId);
//        if (mr890 == null) {
//            return Mr890Result.filed("此设备不存在", 102);
//        }
//        Long insDeviceId = mr890.getInsDeviceId();
//        TMInsDevice insDevice = insDeviceService.getById(insDeviceId);
//
//        String inscodeList = insDevice.getInscodeList();
//        if (inscodeList.contains(deptCode)) {
//            return Mr890Result.success();
//        }
//        return Mr890Result.filed("该学员不能到此培训点培训", 103);
    }

    private String substrPhotoPath(String pictureUrl) {
        log.info("[人脸认证照片路径] pictureUrl:{} ", pictureUrl);
        if (StringUtils.isNotBlank(pictureUrl) && pictureUrl.contains(OssZWYUtils.UPLOAD_PATH) && pictureUrl.contains("?")) {
            String subPath = StringUtils.substringBetween(pictureUrl, OssZWYUtils.UPLOAD_PATH, "?");
            log.info("[分割后照片路径] subPath:{} ", subPath);
            return "/" + OssZWYUtils.UPLOAD_PATH + subPath;
        }
        return pictureUrl;
    }

    /**
     * 一诺(*************、*************)
     * 维尔(************)、
     * 交科院(***************、***************
     * ***************、**************)
     */
    private Mr890Result checkIllegalIP(String method) {
        HttpServletRequest request = ServletUtils.getRequest();
        String clientIp = ServletUtil.getClientIP(request);
        if (StringUtils.isNotBlank(legalIPList) && !legalIPList.contains(clientIp)) {
            log.info("非法ip,禁止访问 method:{},ip:{}",method,clientIp);
            return Mr890Result.filed("非法ip,禁止访问",null);
        }
        return Mr890Result.success();
    }

    @GetMapping("/getTempUrl")
    public void getTempUrl() {
//        String tempUrl = ossZWYUtils.getTempUrl("sxgzpt/2023/08/24/1692845811488.png");
//        System.out.println(tempUrl);
//        TMMr890FaceRecord mr890fr = mr890FaceRecordService.selectMr890FaceRecordByIdCard("330683200310071610");
//        System.out.println(mr890fr);

//        TMMr890FaceRecord mr890fr = mr890FaceRecordService.getOne(
//                Wrappers.lambdaQuery(TMMr890FaceRecord.class)
//                        .eq(TMMr890FaceRecord::getIdcard, "330683200310071610")
//                        .eq(TMMr890FaceRecord::getStatus, 1)
//                        .ne(TMMr890FaceRecord::getSignType, 7)
//                        .gt(TMMr890FaceRecord::getCreateTime, DateUtils.getDate())
//                        .orderByDesc(TMMr890FaceRecord::getCreateTime).last(" limit 1")
//        );
//        System.out.println(mr890fr);

        TMStudentInfoEnter stuInfoEnter = studentInfoEnterService.getOne(
                Wrappers.lambdaQuery(TMStudentInfoEnter.class)
                        .eq(TMStudentInfoEnter::getIdcard, "370111195511205248")
        );
        System.out.println(stuInfoEnter);

    }

    @Autowired
    private ITMStudentInfoEnterService studentInfoEnterService;
}
