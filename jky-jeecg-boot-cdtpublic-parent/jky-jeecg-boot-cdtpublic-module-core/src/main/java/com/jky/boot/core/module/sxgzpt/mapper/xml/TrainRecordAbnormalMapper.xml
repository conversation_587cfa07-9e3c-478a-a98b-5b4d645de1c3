<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.sxgzpt.mapper.TrainRecordAbnormalMapper">

    <resultMap type="com.jky.boot.core.module.sxgzpt.entity.TrainRecordAbnormal" id="TrainRecordAbnormalResult">
        <result property="id"    column="id"    />
        <result property="stunum"    column="stunum"    />
        <result property="type"    column="type"    />
        <result property="rnum"    column="rnum"    />
        <result property="abnormalSeq"    column="abnormal_seq"    />
        <result property="abnormalName"    column="abnormal_name"    />
        <result property="crdate"    column="crdate"    />
        <result property="groupId"    column="group_id"    />
        <result property="status"    column="status"    />
        <result property="disposeDate"    column="dispose_date"    />
        <result property="operator"    column="operator"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="reason"    column="reason"    />
        <result property="starttime"    column="starttime"    />
    </resultMap>

    <sql id="selectTrainRecordAbnormalVo">
        select id, stunum, type, rnum, abnormal_seq, abnormal_name, crdate, group_id, status, dispose_date, operator, operator_name, reason,starttime from t_m_train_record_abnormal
    </sql>

    <select id="selectTrainRecordAbnormalList" parameterType="com.jky.boot.core.module.sxgzpt.entity.TrainRecordAbnormal" resultMap="TrainRecordAbnormalResult">
        <include refid="selectTrainRecordAbnormalVo"/>
        <where>
            <if test="stunum != null  and stunum != ''"> and stunum = #{stunum}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="rnum != null  and rnum != ''"> and rnum = #{rnum}</if>
            <if test="abnormalSeq != null  and abnormalSeq != ''"> and abnormal_seq = #{abnormalSeq}</if>
            <if test="abnormalName != null  and abnormalName != ''"> and abnormal_name like concat('%', #{abnormalName}, '%')</if>
            <if test="groupId != null  and groupId != ''"> and group_id = #{groupId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="disposeDate != null "> and dispose_date = #{disposeDate}</if>
            <if test="operator != null "> and operator = #{operator}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
        </where>
        order by crdate desc
    </select>

    <select id="getClassRecordDetailListByGroupIds" resultType="com.jky.boot.core.module.gzpt.entity.ClassRecordDetail">
        SELECT datail.*,abnormal.status as abnormalStatus,abnormal.id as  abnormalId,abnormal.type as  abnormalType ,abnormal.rnum as abnormalRnum from sxgzpt.t_m_train_record_abnormal abnormal JOIN sxgzpt.t_m_class_record_detail  datail
            on abnormal.rnum=concat(datail.stunum,datail.recnum) and  datail.starttime_date = abnormal.starttime
            WHERE abnormal.group_id in
            <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        and abnormal.crdate >= #{crdate}
        <!--<if test="starttime != null">-->
            <!--and  datail.starttime_date BETWEEN #{starttime} and #{crdate}-->
        <!--</if>-->
    </select>


</mapper>
