package com.jky.boot.core.module.coachScheduling.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.coachScheduling.entity.CoachRest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: coach_rest
 * @Author: jeecg-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
public interface CoachRestMapper extends BaseMapper<CoachRest> {

    void alterScheduling(CoachRest coachRest);

    List<CoachRest> queryCoachRest(CoachRest coachRest);
}
