package com.jky.boot.core.module.sxgzpt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import com.jky.boot.core.module.gzpt.utils.NationwideUtil;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.core.module.sxgzpt.entity.Device;
import com.jky.boot.core.module.sxgzpt.service.IDeviceService;
import com.jky.boot.core.module.sxgzpt.vo.DeviceAssignVo;
import com.jky.boot.core.module.sxgzpt.vo.DeviceJsVo;
import com.jky.boot.core.module.sxgzpt.vo.DeviceQgVo;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * 计时设备管理
 * <AUTHOR>
 * @since   2023-04-12
 * @version V1.0
 */
@Api(tags = "计时设备（终端管理）", value = "计时设备（终端管理）")
@RestController
@RequestMapping("/gzpt/device")
@Slf4j
public class DeviceController extends JeecgController<Device, IDeviceService> {

   @Autowired
   private IDeviceService deviceService;
   @Autowired
   private IInstitutionService institutionService;
   @Autowired
   private IPlatformService platformService;

   /**
    * 分页列表查询
    *
    * @param device
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
   //@AutoLog(value = "计时设备（终端管理）分页列表查询")
   @ApiOperation(value="计时设备（终端管理）分页列表查询", notes="计时设备（终端管理）分页列表查询")
   @GetMapping(value = "/list")
   public Result<IPage<Device>> queryPageList(Device device,
                                  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                  HttpServletRequest req) {
       List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(device.getInscode());
       device.setInscode(null);
       QueryWrapper<Device> queryWrapper = QueryGenerator.initQueryWrapper(device, req.getParameterMap());
       queryWrapper.in("inscode", insCodes);
       Page<Device> page = new Page<>(pageNo, pageSize);
       IPage<Device> pageList = deviceService.page(page, queryWrapper);
       return Result.OK(pageList);
   }

   /**
    *   添加
    *
    * @param device
    * @return
    */
   @AutoLog(value = "计时设备（终端管理）添加")
   @ApiOperation(value="计时设备（终端管理）添加", notes="计时设备（终端管理）添加")
   //@RequiresPermissions("org.jeecg.modules.demo:device:add")
   @PostMapping(value = "/add")
   public Result<String> add(@RequestBody Device device) {
       String realInsCode = JkSecurityUtils.getOrgCodeByUnknown(device.getInscode());
       Institution ins = institutionService.getByInscode(realInsCode);
       device.setInscode(realInsCode);
       device.setInsname(ins.getName());
       deviceService.save(device);
       return Result.OK("添加成功！");
   }

   /**
    *  编辑
    *
    * @param device
    * @return
    */
   @AutoLog(value = "计时设备（终端管理）编辑")
   @ApiOperation(value="计时设备（终端管理）编辑", notes="计时设备（终端管理）编辑")
   //@RequiresPermissions("org.jeecg.modules.demo:device:edit")
   @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
   public Result<String> edit(@RequestBody Device device) {
       deviceService.updateById(device);
       return Result.OK("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   @AutoLog(value = "计时设备（终端管理）通过id删除")
   @ApiOperation(value="计时设备（终端管理）通过id删除", notes="计时设备（终端管理）通过id删除")
   //@RequiresPermissions("org.jeecg.modules.demo:device:delete")
   @DeleteMapping(value = "/delete")
   public Result<String> delete(@RequestParam(name="id") String id) {
       deviceService.removeById(id);
       return Result.OK("删除成功!");
   }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   @AutoLog(value = "计时设备（终端管理）批量删除")
   @ApiOperation(value="计时设备（终端管理）批量删除", notes="计时设备（终端管理）批量删除")
   //@RequiresPermissions("org.jeecg.modules.demo:device:deleteBatch")
   @DeleteMapping(value = "/deleteBatch")
   public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
       this.deviceService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.OK("批量删除成功!");
   }

   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "计时设备（终端管理）通过id查询")
   @ApiOperation(value="计时设备（终端管理）通过id查询", notes="计时设备（终端管理）通过id查询")
   @GetMapping(value = "/queryById")
   public Result<Device> queryById(@RequestParam(name="id") String id) {
       Device device = deviceService.getById(id);
       if(device==null) {
           return Result.error("未找到对应数据");
       }
       return Result.OK(device);
   }

//   /**
//   * 导出excel
//   *
//   * @param request
//   * @param device
//   */
//   //@RequiresPermissions("org.jeecg.modules.demo:device:exportXls")
//   @RequestMapping(value = "/exportXls")
//   public ModelAndView exportXls(HttpServletRequest request, Device device) {
//       return super.exportXls(request, device, Device.class, "device");
//   }
//
//   /**
//     * 通过excel导入数据
//   *
//   * @param request
//   * @param response
//   * @return
//   */
//   //@RequiresPermissions("device:importExcel")
//   @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//   public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//       return super.importExcel(request, response, Device.class);
//   }

    /**
     * 推送计时
     */
    @GetMapping("/pushJs/{id}")
    @RequiresPermissions("gzpt:deviceList:pushjs")
    public Result<?> pushJs(@PathVariable String id) {
        if(StringUtils.isEmpty(id)){
            return Result.error("参数错误！");
        }
        Device byId = deviceService.getById(id);
        if(ObjectUtils.isEmpty(byId)){
            return Result.error("该计时设备不存在！");
        }
        Institution ins = institutionService.getByInscode(byId.getInscode());
        Platform platform =
            platformService.getByPlatformSerialNumber(ins.getPlatform());
        CommonResponse push;
        DeviceJsVo deviceJsVo = new DeviceJsVo();
        BeanUtils.copyProperties(byId, deviceJsVo);
        try {
            push = PushJsUtil.push(deviceJsVo, platform.getApi() + "/gzdevice");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String result = push.getMessage();
        if (push.getErrorcode() != 0) {
            return Result.error(result);
        }
        return Result.ok(result);
    }

    /**
     * 推送全国
     */
    @GetMapping("/pushQg/{id}")
    public Result<?> pushQg(@PathVariable String id) {
        if(StringUtils.isEmpty(id)){
            return Result.error("参数错误！");
        }
        Device byId = deviceService.getById(id);
        if(ObjectUtils.isEmpty(byId)){
            return Result.error("该计时设备不存在！");
        }
        DeviceQgVo tar = new DeviceQgVo();
        BeanUtils.copyProperties(byId, tar);
        CommonResponse response = NationwideUtil.pushDevice(tar);
        JSONObject jo = JSON.parseObject(response.getData().toString());
        byId.setDevnum(jo.getString("devnum"));
        byId.setKey(jo.getString("key"));
        byId.setPasswd(jo.getString("passwd"));
        deviceService.updateById(byId);
        return Result.ok("推送全国成功！");
    }

    /**
     * 计时设备绑定教练车
     */
    @PostMapping("/binding")
    public Result<?> bindingCar(@RequestBody Device bingdingInfo) {
        Device one = deviceService.getById(bingdingInfo.getId());
        one.setCarnum(bingdingInfo.getCarnum());
        deviceService.updateById(one);

        // 推送计时
        DeviceAssignVo toJs = new DeviceAssignVo();
        BeanUtils.copyProperties(one, toJs);
        CommonResponse push = this.pushJs(toJs, one.getInscode(), 1);
        if (push.getErrorcode() != 0) {
            return Result.error(push.getResult());
        }
        return Result.ok(push.getResult());
    }

    /**
     * 计时设备解绑
     */
    @PostMapping("/unbinding")
    public Result<?> unbindingCar(@RequestBody Device device) {
        Device one = deviceService.getById(device.getId());
        String carNum = one.getCarnum();
        one.setCarnum(null);
        deviceService.updateById(one);

        // 推送计时
        DeviceAssignVo toJs = new DeviceAssignVo();
        toJs.setCarnum(carNum);
        toJs.setDevnum(one.getDevnum());
        toJs.setSim(one.getSim());
        CommonResponse push = this.pushJs(toJs, one.getInscode(), 0);
        if (push.getErrorcode() != 0) {
            return Result.error(push.getResult());
        }
        return Result.ok(push.getResult());
    }

    private CommonResponse pushJs(DeviceAssignVo toJs, String insCode, Integer status) {
        Institution ins = institutionService.getByInscode(insCode);
        Platform platform =
            platformService.getByPlatformSerialNumber(ins.getPlatform());
        CommonResponse push;
        try {
            if(status == 1) {
                push = PushJsUtil.push(toJs, platform.getApi() + "/gzdevassign");
            }else {
                push = PushJsUtil.push(toJs, platform.getApi() + "/gzdevRembinding");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return push;
    }
}
