package com.jky.boot.bank.contoller;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayEbppIndustrySupervisionOrderCreateResponse;
import com.alipay.api.response.AlipayUserInfoShareResponse;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.bank.dto.ParamsDto;
import com.jky.boot.bank.utils.AlipayUtils;
import com.jky.boot.common.exception.JkyCdtpublicException;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 支付宝小程序接口
 *
 * <AUTHOR>
 * @version 2024-12-12 14:25
 */
@RequestMapping("/alipay")
@RestController
@Slf4j
public class AlipayController {

    @Autowired
    private IStudentinfoService stuService;
    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IBankOrderService bankOrderService;

    /**
     * 支付宝提供 authCode 获取支付的学员信息
     *
     * @param authCode authCode
     * @return 学员信息
     */
    @GetMapping(value = "/stuInfo")
    public Result<?> stuInfo(@Param("authCode") String authCode) {
        try {
            AlipayUserInfoShareResponse userInfo = AlipayUtils.getUserInfo(authCode);
            String idcard = userInfo.getCertNo();
            Studentinfo stu = stuService.getOneByIdCard(idcard);
            if (Objects.isNull(stu)) {
                return Result.error("请先报名");
            }
            if (!"alipay".equals(stu.getBankcode())) {
                return Result.error("非支付宝支付学员");
            }
            return Result.ok(stu);
        } catch (Exception e) {
            log.error("获取学员信息失败: ", e);
            return Result.error("获取学员信息失败");
        }
    }

    /**
     * 支付宝小程序提交订单
     *
     * @param authCode authCode
     * @return 支付宝创建订单 response
     */
    @PostMapping(value = "/orderCreate")
    @Transactional
    @RedissonLock(key = "#authCode", keyPrefix = "alipay:orderCreate:", waitTime = 1, lockTime = 5)
    public Result<?> orderCreate(@Param("authCode") String authCode) throws AlipayApiException {
        AlipayUserInfoShareResponse userInfo = AlipayUtils.getUserInfo(authCode);
        String idcard = userInfo.getCertNo();
        Studentinfo stu = stuService.getOneByIdCard(idcard);
        if (Objects.isNull(stu)) {
            return Result.error("请先报名");
        }
        if (!"alipay".equals(stu.getBankcode())) {
            return Result.error("非支付宝支付学员");
        }
        BankReg exist = bankRegService.getOneByStuNum(stu.getStunum());
        if (Objects.nonNull(exist)) {
            return Result.ok(exist.getAlipayUrl());
        }
        // 保存 reg
        BankReg reg = new BankReg();
        reg.setStunum(stu.getStunum());
        reg.setStuname(stu.getName());
        reg.setIdcard(stu.getIdcard());
        reg.setInscode(stu.getInscode());
        reg.setInsname(stu.getInsname());
        reg.setSerialno(userInfo.getOpenId());
        reg.setStatus(0);
        reg.setBankcode("alipay");
        reg.setBankname("支付宝");
        reg.setBalanceAmount(new BigDecimal(stu.getFrozenAmount()));
        bankRegService.save(reg);
        // 调用支付宝创建订单
        ParamsDto params = new ParamsDto();
        params.setOpenId(userInfo.getOpenId()).setInscode(stu.getInscode());
        params.setOutOrderNo(reg.getId()).setAmount(new BigDecimal(stu.getFrozenAmount()));
        AlipayEbppIndustrySupervisionOrderCreateResponse aliResp = AlipayUtils.orderCreate(params);
        if (!"10000".equals(aliResp.getCode())) {
            // 回滚
            throw new JkyCdtpublicException("创建订单失败: " + aliResp.getSubMsg());
        }
        // 保存 alipayOrderNo 和 alipayOrderDetailUrl
        bankRegService.update(
                Wrappers.<BankReg>lambdaUpdate()
                        .set(BankReg::getBlockid, aliResp.getAlipayOrderNo())
                        .set(BankReg::getAlipayUrl, aliResp.getAlipayOrderDetailUrl())
                        .eq(BankReg::getId, reg.getId())
        );
        return Result.ok(aliResp.getAlipayOrderDetailUrl());
    }

    /**
     * 获取冻结余额
     *
     * @param authCode authCode
     * @return 冻结余额
     */
    @GetMapping(value = "/remainder")
    public Result<?> remainder(@Param("authCode") String authCode) {
        try {
            AlipayUserInfoShareResponse userInfo = AlipayUtils.getUserInfo(authCode);
            String idcard = userInfo.getCertNo();
            Studentinfo stu = stuService.getOneByIdCard(idcard);
            if (Objects.isNull(stu)) {
                return Result.error("请先报名");
            }
            if (!"alipay".equals(stu.getBankcode())) {
                return Result.error("非支付宝支付学员");
            }
            BankReg bankReg = bankRegService.getOneByStuNum(stu.getStunum());
            return Result.ok(bankReg);
        } catch (Exception e) {
            log.error("获取冻结金额失败: ", e);
            return Result.error("获取冻结金额失败");
        }
    }

    /**
     * 获取资金明细
     *
     * @param authCode authCode
     * @return 资金明细
     */
    @GetMapping(value = "/orderDetails")
    public Result<?> orderDetails(@Param("authCode") String authCode) {
        try {
            AlipayUserInfoShareResponse userInfo = AlipayUtils.getUserInfo(authCode);
            String idcard = userInfo.getCertNo();
            Studentinfo stu = stuService.getOneByIdCard(idcard);
            if (Objects.isNull(stu)) {
                return Result.error("请先报名");
            }
            if (!"alipay".equals(stu.getBankcode())) {
                return Result.error("非支付宝支付学员");
            }
            List<BankOrder> list = bankOrderService.list(
                    Wrappers.<BankOrder>lambdaQuery()
                            .eq(BankOrder::getStunum, stu.getStunum())
            );
            return Result.ok(list);
        } catch (Exception e) {
            log.error("获取资金明细失败: ", e);
            return Result.error("获取资金明细失败");
        }
    }

}
