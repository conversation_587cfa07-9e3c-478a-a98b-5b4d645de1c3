<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.sxgzpt.mapper.DeviceMapper">

    <resultMap type="com.jky.boot.core.module.sxgzpt.entity.Device" id="DeviceResult">
        <result property="devnum"    column="devnum"    />
        <result property="termtype"    column="termtype"    />
        <result property="vender"    column="vender"    />
        <result property="model"    column="model"    />
        <result property="imei"    column="imei"    />
        <result property="sn"    column="sn"    />
        <result property="inscode"    column="inscode"    />
        <result property="cerbase64"    column="cerbase64"    />
        <result property="carnum"    column="carnum"    />
        <result property="carcolor"    column="carcolor"    />
        <result property="sim"    column="sim"    />
        <result property="district"    column="district"    />
        <result property="key"    column="key"    />
        <result property="passwd"    column="passwd"    />
        <result property="installdate"    column="installdate"    />
        <result property="hardversion"    column="hardversion"    />
        <result property="softversion"    column="softversion"    />
        <result property="simtype"    column="simtype"    />
        <result property="telephone"    column="telephone"    />
        <result property="state"    column="state"    />
        <result property="regdate"    column="regdate"    />
        <result property="isconnect"    column="isconnect"    />
        <result property="carsign"    column="carsign"    />
        <result property="insname"    column="insname"    />
        <result property="traintype"    column="traintype"    />
        <result property="keepRecordStatus"    column="keep_record_status"    />
        <result property="keepRecordDate"    column="keep_record_date"    />
    </resultMap>

    <sql id="selectDeviceVo">
        select devnum, termtype, vender, model, imei, sn, inscode, cerbase64, carnum, carcolor, sim, district, `key`, passwd, installdate, hardversion, softversion, simtype, telephone, state, regdate, isconnect, carsign, insname, traintype, keep_record_status, keep_record_date from t_m_device
    </sql>

    <select id="selectDeviceList" parameterType="com.jky.boot.core.module.sxgzpt.entity.Device" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="termtype != null "> and termtype = #{termtype}</if>
            <if test="vender != null  and vender != ''"> and vender = #{vender}</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="imei != null  and imei != ''"> and imei = #{imei}</if>
            <if test="sn != null  and sn != ''"> and sn = #{sn}</if>
            <if test="inscode != null  and inscode != ''"> and inscode = #{inscode}</if>
            <if test="cerbase64 != null  and cerbase64 != ''"> and cerbase64 = #{cerbase64}</if>
            <if test="carnum != null  and carnum != ''"> and carnum = #{carnum}</if>
            <if test="carcolor != null  and carcolor != ''"> and carcolor = #{carcolor}</if>
            <if test="sim != null  and sim != ''"> and sim = #{sim}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="key != null  and key != ''"> and key = #{key}</if>
            <if test="passwd != null  and passwd != ''"> and passwd = #{passwd}</if>
            <if test="installdate != null "> and installdate = #{installdate}</if>
            <if test="hardversion != null  and hardversion != ''"> and hardversion = #{hardversion}</if>
            <if test="softversion != null  and softversion != ''"> and softversion = #{softversion}</if>
            <if test="simtype != null "> and simtype = #{simtype}</if>
            <if test="telephone != null  and telephone != ''"> and telephone = #{telephone}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="regdate != null "> and regdate = #{regdate}</if>
            <if test="isconnect != null "> and isconnect = #{isconnect}</if>
            <if test="carsign != null  and carsign != ''"> and carsign = #{carsign}</if>
            <if test="insname != null  and insname != ''"> and insname like concat('%', #{insname}, '%')</if>
            <if test="traintype != null  and traintype != ''"> and traintype = #{traintype}</if>
            <if test="keepRecordStatus != null "> and keep_record_status = #{keepRecordStatus}</if>
            <if test="keepRecordDate != null "> and keep_record_date = #{keepRecordDate}</if>
        </where>
    </select>


</mapper>
