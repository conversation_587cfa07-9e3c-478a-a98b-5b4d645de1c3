package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.vo.InstitutionVo;

import java.util.List;

/**
 * @Description: 培训机构表
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IInstitutionService extends IService<Institution> {
    /**
     * 查询培训机构
     *
     * @param id 培训机构主键
     * @return 培训机构
     */
    Institution selectInstitutionById(Long id);

    /**
     * 查询培训机构列表
     *
     * @param institution 培训机构
     * @return 培训机构集合
     */
    List<Institution> selectInstitutionList(Institution institution);

    /**
     * 新增培训机构
     *
     * @param institution 培训机构
     * @return 结果
     */
    int insertInstitution(Institution institution);

    /**
     * 修改培训机构
     *
     * @param institution 培训机构
     * @return 结果
     */
    int updateInstitution(Institution institution);

    int updateInstitutionByInscode(Institution institution);

    /**
     * 批量删除培训机构
     *
     * @param ids 需要删除的培训机构主键集合
     * @return 结果
     */
    int deleteInstitutionByIds(Long[] ids);

    /**
     * 删除培训机构信息
     *
     * @param id 培训机构主键
     * @return 结果
     */
    int deleteInstitutionById(Long id);


    /**
     * 通过inscode查询
     *
     * @param Inscode
     * @return
     */
    Institution getByInscode(String Inscode);

    //获取厂商编号
    String getPlatform(String Inscode);


    IPage<InstitutionVo> searchInstitution(String areaCode, String name, Double lon, Double lat,
                                           String distanceSort,String priceSort,Integer score,
                                           Integer pageNo, Integer pageSize);

    IPage<InstitutionVo> getInsByTrainType(String areaCode, String name, Double lon, Double lat,
                                           String distanceSort, String priceSort, String trainType,
                                           Integer pageNo, Integer pageSize);
}
