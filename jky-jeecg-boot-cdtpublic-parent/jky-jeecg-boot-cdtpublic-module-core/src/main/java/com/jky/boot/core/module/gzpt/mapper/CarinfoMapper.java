package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.jky.boot.core.module.gzpt.entity.Carinfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 车辆信息对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface CarinfoMapper extends BaseMapper<Carinfo> {
    /**
     * 查询车辆信息
     *
     * @param id 车辆信息主键
     * @return 车辆信息
     */
    Carinfo selectCarInfoById(String id);

    /**
     * 查询车辆信息列表
     *
     * @param carInfo 车辆信息
     * @return 车辆信息集合
     */
    List<Carinfo> selectCarInfoList(Carinfo carInfo);

    /**
     * 新增车辆信息
     *
     * @param carInfo 车辆信息
     * @return 结果
     */
    int insertCarInfo(Carinfo carInfo);

    /**
     * 修改车辆信息
     *
     * @param carInfo 车辆信息
     * @return 结果
     */
    int updateCarInfo(Carinfo carInfo);

    /**
     * 删除车辆信息
     *
     * @param id 车辆信息主键
     * @return 结果
     */
    int deleteCarInfoById(String id);

    /**
     * 批量删除车辆信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCarInfoByIds(String[] ids);

    //修改状态
    int updateByStatus(String[] ids, int status);

    //转校
    int updateTransfer(String[] ids, String inscode, String insname);


    //根据id查询教练车
    List<Carinfo> findCarInfoByIds(String[] ids);

    Carinfo findCarInfoByCarnum(String carnum);

    void updatePhotoUrlAndPhotoByCarnum(String carnum, String photourl, String photo);
}
