package com.jky.boot.zlb.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jky.boot.zlb.entity.TMTransfer;
import com.jky.boot.zlb.service.ITMTransferService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: t_m_transfer
 * @Author: jeecg-boot
 * @Date:   2023-06-19
 * @Version: V1.0
 */
@Api(tags="t_m_transfer")
@RestController
@RequestMapping("/zlb/tmtransfer/tMTransfer")
@Slf4j
public class TMTransferController extends JeecgController<TMTransfer, ITMTransferService> {
	@Autowired
	private ITMTransferService tMTransferService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tMTransfer
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_transfer-分页列表查询")
	@ApiOperation(value="t_m_transfer-分页列表查询", notes="t_m_transfer-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMTransfer>> queryPageList(TMTransfer tMTransfer,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TMTransfer> queryWrapper = QueryGenerator.initQueryWrapper(tMTransfer, req.getParameterMap());
		Page<TMTransfer> page = new Page<TMTransfer>(pageNo, pageSize);
		IPage<TMTransfer> pageList = tMTransferService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tMTransfer
	 * @return
	 */
	@AutoLog(value = "t_m_transfer-添加")
	@ApiOperation(value="t_m_transfer-添加", notes="t_m_transfer-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_transfer:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMTransfer tMTransfer) {
		tMTransferService.save(tMTransfer);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tMTransfer
	 * @return
	 */
	@AutoLog(value = "t_m_transfer-编辑")
	@ApiOperation(value="t_m_transfer-编辑", notes="t_m_transfer-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_transfer:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMTransfer tMTransfer) {
		tMTransferService.updateById(tMTransfer);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_transfer-通过id删除")
	@ApiOperation(value="t_m_transfer-通过id删除", notes="t_m_transfer-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_transfer:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tMTransferService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_transfer-批量删除")
	@ApiOperation(value="t_m_transfer-批量删除", notes="t_m_transfer-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_transfer:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tMTransferService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "t_m_transfer-通过id查询")
	@ApiOperation(value="t_m_transfer-通过id查询", notes="t_m_transfer-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMTransfer> queryById(@RequestParam(name="id",required=true) String id) {
		TMTransfer tMTransfer = tMTransferService.getById(id);
		if(tMTransfer==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMTransfer);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMTransfer
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_transfer:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMTransfer tMTransfer) {
//        return super.exportXls(request, tMTransfer, TMTransfer.class, "t_m_transfer");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_transfer:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMTransfer.class);
//    }

}
