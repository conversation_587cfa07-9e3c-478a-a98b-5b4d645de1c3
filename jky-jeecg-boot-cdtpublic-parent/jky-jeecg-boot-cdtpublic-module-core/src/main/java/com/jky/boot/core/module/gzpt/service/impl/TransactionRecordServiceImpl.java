package com.jky.boot.core.module.gzpt.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Orderarray;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.TransactionRecord;
import com.jky.boot.core.module.gzpt.mapper.OrderarrayMapper;
import com.jky.boot.core.module.gzpt.mapper.TransactionRecordMapper;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.service.ITransactionRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 交易记录查询结果对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class TransactionRecordServiceImpl extends ServiceImpl<TransactionRecordMapper, TransactionRecord> implements ITransactionRecordService {
    @Autowired
    private TransactionRecordMapper transactionRecordMapper;
    @Autowired
    private IStudentinfoService iStudentinfoService;

    @Autowired
    private IInstitutionService iInstitutionService;
    @Autowired
    private OrderarrayMapper orderarrayMapper;

    @Override
    public String updateTransactionRecord2(Map<String, Object> param) {
        JSONObject data = JSONObject.parseObject(param.get("data").toString());
        if (!data.containsKey("queryid")) {
            return "流水号不能为空";
        }
        String queryid = data.get("queryid").toString();
        TransactionRecord transactionRecord = transactionRecordMapper.selectById(queryid);

        if (transactionRecord == null) {
            return "流水号不存在";
        }
        transactionRecord.setCounts(Integer.valueOf(data.get("counts").toString()));
        String message = param.get("message").toString();
        if (message.equals("查询成功")) {
            transactionRecord.setStatus(1);
            JSONArray jsonArray = JSONObject.parseArray(data.get("orderarray").toString());


            for (int i = 0; i < jsonArray.size(); i++) {
                Orderarray orderarray = new Orderarray();
                orderarray.setCrdate(new Date());

                JSONObject json = (JSONObject) jsonArray.get(i);
                String stunum = json.get("stunum").toString();
                if (StringUtils.isNotEmpty(stunum)) {
                    Studentinfo studentinfo = new Studentinfo();
                    studentinfo.setStunum(stunum);
                    List<Studentinfo> studentinfos = iStudentinfoService.selectStudentinfoList(studentinfo);
                    if (studentinfos.size() > 0) {
                        json.put("stuname", studentinfos.get(0).getName());
                        orderarray.setStuname(studentinfos.get(0).getName());
                    }
                }
                Institution inscode = iInstitutionService.getByInscode(json.get("inscode").toString());
                if (inscode != null) {
                    json.put("insname", inscode.getName());
                    orderarray.setInsname(inscode.getName());

                }

                if (transactionRecord.getTraType() == 2) {//学员
                    transactionRecord.setOrderarray(jsonArray.toJSONString());
                } else {//教练员
                    orderarray.setStunum(stunum);
                    orderarray.setInscode(json.get("inscode").toString());
                    orderarray.setOrderno(json.get("orderno").toString());
                    orderarray.setAmount(json.get("amount").toString());
                    orderarray.setCrtime(json.get("crtime").toString());
                    orderarray.setQueryid(queryid);
                    orderarrayMapper.insert(orderarray);
                }

            }

        } else {
            transactionRecord.setStatus(2);
        }
        transactionRecord.setMessage(message);
        transactionRecordMapper.updateById(transactionRecord);
        return null;
    }

    @Override
    public List<TransactionRecord> selectTransactionRecordList(TransactionRecord transactionRecord) {
        return transactionRecordMapper.selectTransactionRecordList(transactionRecord);
    }

    @Override
    public IPage<TransactionRecord> selectTransactionRecordPage(Page<TransactionRecord> page, TransactionRecord transactionRecord) {
        return transactionRecordMapper.selectTransactionRecordPage(page,transactionRecord);
    }
}
