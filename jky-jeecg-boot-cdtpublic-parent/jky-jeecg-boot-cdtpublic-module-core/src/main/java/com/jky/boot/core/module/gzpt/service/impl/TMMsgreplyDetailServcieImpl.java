package com.jky.boot.core.module.gzpt.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.core.module.gzpt.dto.MessageDto;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.mapper.TMMsgreplyDetailMapper;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@Service
public class TMMsgreplyDetailServcieImpl extends ServiceImpl<TMMsgreplyDetailMapper, TMMsgreplyDetail> implements ITMMsgreplyDetailService {

    @Autowired
    private ICoachService iCoachService;
    @Autowired
    private ICarinfoService iCarinfoService;
    @Autowired
    private ITMExaminerService itmExaminerService;
    @Autowired
    private ITMSecurityguardService itmSecurityguardService;

    @Override
    public String coachDtoHandle(TMMsgreplyDetail tmMsgreplyDetail) {
        String data = tmMsgreplyDetail.getData();
        String s = "success";
        try {

            CoachDto messageDto =JSON.parseObject(data,CoachDto.class);
            System.out.println("data解析成功"+messageDto.toString());
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientGetSJ(
                    UrlAddressUtils.coach(messageDto.getCoachnum(), messageDto.getType().toString())
            );
            System.out.println("-------------------接口响应的结果对象是:"+commonResponse.toString());
            if (commonResponse.getErrorcode()==1){
                return commonResponse.getMessage();
            }
            CoachDto dataDto = JSON.parseObject(commonResponse.getData().toString(), CoachDto.class);
            Coach coachOne = iCoachService.getOne(new LambdaQueryWrapper<Coach>().eq(Coach::getCoachnum, dataDto.getCoachnum()));
            switch (messageDto.getType()){
                case "1"://教练员审核
                    if (Objects.nonNull(coachOne)){
                        coachOne.setAuditstate(Long.valueOf(dataDto.getStatus()));
                        coachOne.setAuditreason(dataDto.getReason());
                        String date = dataDto.getDate();
                        coachOne.setAuditdate(date);
                        iCoachService.updateById(coachOne);
                    }
                    break;
                case "2"://教练员启用/停训
                    if (Objects.nonNull(coachOne)){
                        coachOne.setStopTrain(Long.valueOf(dataDto.getStatus()));
                        iCoachService.updateById(coachOne);
                    }
                    break;
                case "3"://教练员离职
                    if (Objects.nonNull(coachOne)){
                        coachOne.setEmploystatus(dataDto.getStatus());
                        iCoachService.updateById(coachOne);
                    }
                    break;
                case "6"://教练员转校
                    if (Objects.nonNull(coachOne)){
                        coachOne.setInscode(dataDto.getNewinscode());
                        iCoachService.updateById(coachOne);
                    }
                    break;
                default:
                    s = "coachnum:"+dataDto.getCoachnum()+"该教练不存在";
                    break;
            }

        } catch (Exception e) {
            return e.getMessage();
        }
        return s;
    }

    @Override
    public String carInfoDtoHandle(TMMsgreplyDetail tmMsgreplyDetail) {
        String data = tmMsgreplyDetail.getData();
        String s ="success";
        try{
            CarDto messageDto =JSON.parseObject(data,CarDto.class);
            System.out.println("data2解析成功");
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientGetSJ(
                    UrlAddressUtils.carInfo(messageDto.getCarnum(), messageDto.getType().toString())
            );
            System.out.println("-------------------接口响应的结果对象是:"+commonResponse.toString());
            if (commonResponse.getErrorcode()==1){
                return commonResponse.getMessage();
            }
            CarDto dataDto = JSON.parseObject(commonResponse.getData().toString(), CarDto.class);
            Carinfo carinfo = iCarinfoService.getOne(new LambdaQueryWrapper<Carinfo>().eq(Carinfo::getCarnum, dataDto.getCarnum()));

            switch (messageDto.getType()){
                case "2"://教练车启用/停用
                    if (Objects.nonNull(carinfo)){
                        carinfo.setStatus(Long.valueOf(dataDto.getStatus()));
                        iCarinfoService.updateById(carinfo);
                    }
                    break;
                case "4":
                    if (Objects.nonNull(carinfo)) {
                        carinfo.setInscode(dataDto.getNewinscode());
                        iCarinfoService.updateById(carinfo);
                    }
                    break;
                default:
                    s = "carnum:"+dataDto.getCarnum()+"教练车不存在";
                    break;
            }

        }catch (Exception e){
            return e.getMessage();
        }
        return s;
    }

    @Override
    public String examinerDtoHandle(TMMsgreplyDetail tmMsgreplyDetail) {
        String data = tmMsgreplyDetail.getData();
        String s = "success";
        try{
            ExaminerDto messageDto =JSON.parseObject(data,ExaminerDto.class);
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientGetSJ(
                    UrlAddressUtils.examiner(messageDto.getExamnum(), messageDto.getType().toString())
            );
            System.out.println("-------------------接口响应的结果对象是:"+commonResponse.toString());
            if (commonResponse.getErrorcode()==1){
                return commonResponse.getMessage();
            }
            ExaminerDto dataDto = JSON.parseObject(commonResponse.getData().toString(), ExaminerDto.class);
            TMExaminer examiner = itmExaminerService.getOne(new LambdaQueryWrapper<TMExaminer>().eq(TMExaminer::getExamnum, dataDto.getExamnum()));

            if ("1".equals(messageDto.getType())){//考核员审核
                    if (Objects.nonNull(examiner)){
                        examiner.setAuditstate(Integer.valueOf(dataDto.getStatus()));
                        examiner.setAuditreason(dataDto.getReason());

                        String date = dataDto.getDate();
                        Date datetime = new SimpleDateFormat("yyyyMMddHHmmss" ) .parse(date);
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String format1 = format.format(datetime);
                        Date parse = format.parse(format1);

                        examiner.setAuditdate(parse);
                        itmExaminerService.updateById(examiner);
                    }
            }else {
                s = "examnum:"+dataDto.getExamnum()+"考核员不存在";
            }

        }catch (Exception e){
            return e.getMessage();
        }
        return s;
    }

    @Override
    public String securityguardDtoHandle(TMMsgreplyDetail tmMsgreplyDetail) {
        String data = tmMsgreplyDetail.getData();
        String s = "success";
        try{
            Securityguard messageDto =JSON.parseObject(data,Securityguard.class);
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientGetSJ(
                    UrlAddressUtils.securityguard(messageDto.getSecunum(), messageDto.getType().toString())
            );
            System.out.println("-------------------接口响应的结果对象是:"+commonResponse.toString());
            if (commonResponse.getErrorcode()==1){
                return commonResponse.getMessage();
            }
            Securityguard dataDto = JSON.parseObject(commonResponse.getData().toString(), Securityguard.class);
            TMSecurityguard securityguard = itmSecurityguardService.getOne(new LambdaQueryWrapper<TMSecurityguard>().eq(TMSecurityguard::getSecunum, dataDto.getSecunum()));

            if ("1".equals(messageDto.getType())){//考核员审核
                if (Objects.nonNull(securityguard)){
                    securityguard.setAuditstate(Integer.valueOf(dataDto.getStatus()));
                    securityguard.setAuditreason(dataDto.getReason());

                    String date = dataDto.getDate();
                    Date datetime = new SimpleDateFormat("yyyyMMddHHmmss" ) .parse(date);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String format1 = format.format(datetime);
                    Date parse = format.parse(format1);

                    securityguard.setAuditdate(parse);
                    itmSecurityguardService.updateById(securityguard);
                }
            }else {
                s = "examnum:"+dataDto.getSecunum()+"考核员不存在";
            }

        }catch (Exception e){
            return e.getMessage();
        }
        return s;
    }

    @Data
    private static class CoachDto{
        private String coachnum;
        private String status;
        private String oldinscode;
        private String newinscode;
        private String reason;
        private String date;
        private String type;
    }
    @Data
    private static class CarDto{
        private String carnum;
        private String status;
        private String oldinscode;
        private String newinscode;
        private String reason;
        private String date;
        private String type;
    }
    @Data
    private static class ExaminerDto{
        private String examnum;
        private String status;
        private String oldinscode;
        private String newinscode;
        private String reason;
        private String date;
        private String type;
    }
    @Data
    private static class Securityguard{
        private String secunum;
        private String status;
        private String oldinscode;
        private String newinscode;
        private String reason;
        private String date;
        private String type;
    }
}
