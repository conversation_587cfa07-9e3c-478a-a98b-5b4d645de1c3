package com.jky.boot.zlb.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainingRecords implements Serializable {
    /**
     * app状态
     * 0:绑定车辆 ;
     * 1：教练签到 ;
     * 2：绑定学员;
     * 3：学员签到;
     * 4：学员签退;
     * 5：教练签退;
     * 6：解绑车辆
     */
    private Integer appStatus;
    /**
     * 教练员姓名
     */
    private String coachName;
    /**
     * 教练员身份证
     */
    private String coachIdCard;
    /**
     * 教练员编号
     */
    private String coachNum;
    /**
     * 教练员的签到时间
     */
    private Date coachCheckin;
    /**
     * 教练员的签退时间
     */
    private Date coachCheckout;
    /**
     * 学员姓名
     */
    private String studentName;
    /**
     * 学员证件号
     */
    private String studentCardNum;
    /**
     * 证件类型
     */
    private Integer studentCardType;
    /**
     * 学员编号
     */
    private String studentNum;
    /**
     * 学员签到时间
     */
    private Date studentCheckin;
    /**
     * 学员签到key
     */
    private String studentCheckinKey;
    /**
     * 学员签退时间
     */
    private Date studentCheckout;
    /**
     * 学员签退key
     */
    private String studentCheckoutKey;
    /**
     * 学员签到科目
     */
    private String subject;
    /**
     * 终端imei
     */
    private String terminalImei;
    /**
     * 终端类型
     */
    private Integer terminalType;
    /**
     * 车牌号
     */
    private String plateNumber;
    /**
     * classId
     */
    private String classId;
    /**
     * 新模式
     */
    private boolean newPattern;

    private String teachPermitted;

    /**
     * 学员车型
     */
    private String trainType;

    /**
     * 经度
     */
    private String locationLat;
    /**
     * 纬度
     */
    private String locationLon;
}
