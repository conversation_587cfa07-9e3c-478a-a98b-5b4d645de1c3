package com.jky.boot.zlb.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.InstitutionDetails;
import com.jky.boot.zlb.entity.TMInstitution;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.zlb.vo.InstitutionDetailVo;

import java.util.List;

/**
 * @Description: t_m_institution
 * @Author: jeecg-boot
 * @Date:   2023-06-13
 * @Version: V1.0
 */
public interface ITMInstitutionService extends IService<TMInstitution> {
    Page<TMInstitution> list(Page<TMInstitution> page,String district);

    Page<TMInstitution> inlist(Page<TMInstitution> page,String inscode);

    InstitutionDetailVo getInstitutionDetails(String inscode);

}
