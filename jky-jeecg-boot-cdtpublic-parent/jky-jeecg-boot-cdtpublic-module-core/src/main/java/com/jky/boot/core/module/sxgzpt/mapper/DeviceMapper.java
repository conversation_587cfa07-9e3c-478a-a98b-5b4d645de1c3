package com.jky.boot.core.module.sxgzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.boot.core.module.sxgzpt.entity.Device;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 终端信息Mapper接口
 *
 * <AUTHOR>
 * @since 2022-09-02
 */

@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 查询终端信息列表
     *
     * @param device 终端信息
     * @return 终端信息集合
     */
    List<Device> selectDeviceList(Device device);
}