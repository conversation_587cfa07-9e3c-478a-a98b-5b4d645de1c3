<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.BankOrderDayMapper">
    <insert id="insertBankOrderDay" parameterType="com.jky.boot.core.module.gzpt.entity.BankOrderDay">
        insert into t_m_bank_order_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="stunum != null and stunum != ''">stunum,</if>
            <if test="stuname != null">stuname,</if>
            <if test="idcard != null">idcard,</if>
            <if test="inscode != null">inscode,</if>
            <if test="insname != null">insname,</if>
            <if test="totaltime != null">totaltime,</if>
            <if test="status != null">status,</if>
            <if test="freezeAmount != null">freeze_amount,</if>
            <if test="calculationAmount != null">calculation_amount,</if>
            <if test="bankcode != null">bankcode,</if>
            <if test="remark != null">remark,</if>
            <if test="createdate != null">createdate,</if>
            <if test="updatedate != null">updatedate,</if>
            <if test="subject != null">subject,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="stunum != null and stunum != ''">#{stunum},</if>
            <if test="stuname != null">#{stuname},</if>
            <if test="idcard != null">#{idcard},</if>
            <if test="inscode != null">#{inscode},</if>
            <if test="insname != null">#{insname},</if>
            <if test="totaltime != null">#{totaltime},</if>
            <if test="status != null">#{status},</if>
            <if test="freezeAmount != null">#{freezeAmount},</if>
            <if test="calculationAmount != null">#{calculationAmount},</if>
            <if test="bankcode != null">#{bankcode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="updatedate != null">#{updatedate},</if>
            <if test="subject != null">#{subject},</if>
        </trim>
    </insert>
    <update id="updateBankOrderDay">
        update t_m_bank_order_day
        <trim prefix="SET" suffixOverrides=",">
            <if test="stunum != null and stunum != ''">stunum = #{stunum},</if>
            <if test="stuname != null">stuname = #{stuname},</if>
            <if test="idcard != null">idcard = #{idcard},</if>
            <if test="inscode != null">inscode = #{inscode},</if>
            <if test="insname != null">insname = #{insname},</if>
            <if test="totaltime != null">totaltime = #{totaltime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="freezeAmount != null">freeze_amount = #{freezeAmount},</if>
            <if test="calculationAmount != null">calculation_amount = #{calculationAmount},</if>
            <if test="bankcode != null">bankcode = #{bankcode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="updatedate != null">updatedate = #{updatedate},</if>
            <if test="subject != null">subject = #{subject},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectBankOrderDayList" resultType="com.jky.boot.core.module.gzpt.entity.BankOrderDay">
        <include refid="selectBankOrderDayVo"/>
        <where>
            <if test="stunum != null  and stunum != ''">and stunum = #{stunum}</if>
            <if test="stuname != null  and stuname != ''">and stuname like concat(concat('%', #{stuname}), '%')</if>
            <if test="idcard != null  and idcard != ''">and idcard = #{idcard}</if>
            <if test="inscode != null  and inscode != ''">and inscode in (select LEADER from sys_dept start with
                DEPT_ID=#{inscode} connect by prior DEPT_ID = PARENT_ID AND DEL_FLAG = 0 AND STATUS = 0)
            </if>
            <if test="insname != null  and insname != ''">and insname like concat(concat('%', #{insname}), '%')</if>
            <if test="totaltime != null ">and totaltime = #{totaltime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="freezeAmount != null  and freezeAmount != ''">and freeze_amount = #{freezeAmount}</if>
            <if test="inscodes != null">
                and inscode in
                <foreach collection="inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="calculationAmount != null  and calculationAmount != ''">and calculation_amount =
                #{calculationAmount}
            </if>
            <if test="bankcode != null  and bankcode != ''">and bankcode = #{bankcode}</if>
            <if test="createdate != null and type!=1">
            and createdate between concat( #{createdate},' 00:00:00') and concat(#{createdate},' 23:59:59')
            </if>
            <if test="createdate != null and type==1">and createdate <![CDATA[ < ]]> #{createdate}
            </if>
            <if test="updatedate != null ">
            and updatedate between concat( #{updatedate},' 00:00:00') and concat(#{updatedate},' 23:59:59')</if>
        </where>
        order by createdate desc
    </select>
    <sql id="selectBankOrderDayVo">
        select id,
               stunum,
               stuname,
               idcard,
               inscode,
               insname,
               totaltime,
               status,
               freeze_amount,
               calculation_amount,
               bankcode,
               remark,
               createdate,
               updatedate,
               subject
        from t_m_bank_order_day
    </sql>
</mapper>
