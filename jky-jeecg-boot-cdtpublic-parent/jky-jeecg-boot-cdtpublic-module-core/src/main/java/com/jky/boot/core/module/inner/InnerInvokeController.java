package com.jky.boot.core.module.inner;

import com.alipay.api.AlipayApiException;
import com.alipay.api.response.AlipayEbppIndustrySupervisionOrderfundTransferResponse;
import com.alipay.api.response.AlipayEbppIndustrySupervisionOrderfundUnfreezeResponse;
import com.jky.boot.bank.dto.ParamsDto;
import com.jky.boot.bank.utils.AlipayUtils;
import com.jky.boot.core.module.gzpt.dto.OrderDto;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 限制内部ip调用
 */
@RequestMapping("/inner/invoke")
@RestController
@Slf4j
public class InnerInvokeController {

    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IBankOrderService bankOrderService;

    @AutoLog(value = "支付宝资金划转-手动")
    @PostMapping(value = "/fundTransfer")
    public Result<?> fundTransfer(@Param("amount") String amount,
                                  @Param("outFlowId") String outFlowId,
                                  @Param("openId") String openId,
                                  @Param("inscode") String inscode,
                                  @Param("outOrderNo") String outOrderNo) throws AlipayApiException {
        ParamsDto params = new ParamsDto();
        params.setOpenId(openId).setInscode(inscode);
        params.setOutOrderNo(outOrderNo).setAmount(new BigDecimal(amount)).setOutFlowId(outFlowId);
        AlipayEbppIndustrySupervisionOrderfundTransferResponse response = AlipayUtils.fundTransfer(params);
        return Result.ok(response);
    }

    @AutoLog(value = "支付宝资金解冻-手动")
    @PostMapping(value = "/unfreeze")
    public Result<?> unfreeze(@Param("outOrderNo") String outOrderNo) throws AlipayApiException {
        BankReg byId = bankRegService.getById(outOrderNo);
        if (Objects.isNull(byId)) {
            return Result.error("无效id");
        }
        // 保存订单
        OrderDto orderDto = new OrderDto();
        orderDto.setStunum(byId.getStunum()).setOrderType(8).setTransferAmount(byId.getRemainingAmount());
        BankOrder bankOrder = bankOrderService.genOrder(orderDto);
        //划转对象类型 0驾校 1学员 2教练员
        bankOrder.setUserType(1);
        bankOrderService.save(bankOrder);
        // 发送支付宝解冻指令
        ParamsDto params = new ParamsDto();
        params.setOpenId(byId.getSerialno());
        params.setOutOrderNo(outOrderNo).setOutFlowId(bankOrder.getId());
        AlipayEbppIndustrySupervisionOrderfundUnfreezeResponse unfreeze = AlipayUtils.unfreeze(params);
        return Result.ok(unfreeze);
    }
}
