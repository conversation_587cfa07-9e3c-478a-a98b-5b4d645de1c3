package com.jky.boot.core.module.gzpt.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ClassRecordDetailTDO {
    private String id;

    @JsonFormat(shape = JsonFormat.Shape.STRING,timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date starttime;

    private String abnormalId;
    private Integer abnormalType;
    private String abnormalRnum;


    private String stunum;
    private String km;

    @JsonFormat(shape = JsonFormat.Shape.STRING,timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endTime;
    private String recnums;
}
