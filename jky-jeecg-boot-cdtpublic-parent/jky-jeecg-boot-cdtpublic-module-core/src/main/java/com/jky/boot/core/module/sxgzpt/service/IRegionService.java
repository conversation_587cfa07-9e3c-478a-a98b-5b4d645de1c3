package com.jky.boot.core.module.sxgzpt.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.sxgzpt.entity.Region;
import java.util.List;

/**
 * @Description: 电子围栏对象t_m_region
 * @Author: jeecg-boot
 * @Date:   2023-04-12
 * @Version: V1.0
 */
public interface IRegionService extends IService<Region> {
    /**
     * 查询电子围栏列表
     * @param region 电子围栏
     * @return 电子围栏集合
     */
    List<Region> selectRegionList(Region region);


//    /**
//     * 获取各个地区电子围栏数量
//     *
//     * @return 各个地区电子围栏数量
//     */
//    List<PieCountVO> getRegionCount();


    /**
     * 获取教学区域备案待审核数量
     *
     * @return 教学区域备案待审核数量
     */
    Long getRegionByFlag();
}
