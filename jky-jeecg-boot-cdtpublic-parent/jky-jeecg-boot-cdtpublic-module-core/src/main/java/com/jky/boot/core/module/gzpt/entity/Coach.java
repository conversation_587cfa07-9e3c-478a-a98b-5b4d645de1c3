package com.jky.boot.core.module.gzpt.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: 教练员信息对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Data
@TableName("t_m_coach")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_coach对象", description="教练员信息对象")
public class Coach implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@Excel(name = "id", width = 15)
    @ApiModelProperty(value = "id")
	@JSONField(name = "id")
	@JsonProperty("id")
	@TableId(type = IdType.ASSIGN_ID)
    private java.lang.String coachid;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private java.lang.String inscode;
    /**培训机构名称*/
    @Excel(name = "培训机构名称", width = 15)
    @ApiModelProperty(value = "培训机构名称")
    @TableField(exist = false)
    private java.lang.String insname;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**性别 1:男性;2:女性*/
	@Excel(name = "性别 1:男性;2:女性", width = 15, dicCode = "sys_user_sex")
    @ApiModelProperty(value = "性别 1:男性;2:女性")
	@Dict(dicCode = "sys_user_sex")
    private java.lang.Integer sex;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
	@DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private java.lang.String idcard;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
	@DesensitizedFieldAnno(DesensitizedTypeEnum.MOBILE_PHONE)
    private java.lang.String mobile;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
	@DesensitizedFieldAnno(DesensitizedTypeEnum.ADDRESS)
    private java.lang.String address;
	/**照片文件ID*/
	@Excel(name = "照片文件ID", width = 15)
    @ApiModelProperty(value = "照片文件ID")
    private java.lang.String photo;
	/**照片url*/
	@Excel(name = "照片url", width = 15)
    @ApiModelProperty(value = "照片url")
    private java.lang.String photourl;
	/**指纹图片ID*/
	@Excel(name = "指纹图片ID", width = 15)
    @ApiModelProperty(value = "指纹图片ID")
    private java.lang.String fingerprint;
	/**驾驶证号*/
	@Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
	@DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private java.lang.String drilicence;
	/**驾驶证初领日期*/
	@Excel(name = "驾驶证初领日期", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期")
    private java.lang.String fstdrilicdate;
	/**职业资格证号*/
	@Excel(name = "职业资格证号", width = 15)
    @ApiModelProperty(value = "职业资格证号")
    private java.lang.String occupationno;
	/**职业资格等级
1:一级
2:二级
3:三级
4:四级*/
	@Excel(name = "职业资格等级 1:一级 2:二级 3:三级 4:四级", width = 15, dicCode = "sys_jobLevel")
    @ApiModelProperty(value = "职业资格等级 1:一级 2:二级 3:三级 4:四级")
	@Dict(dicCode = "sys_jobLevel")
    private java.lang.String occupationlevel;
	/**准驾车型 下列编码单选：
A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "准驾车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "准驾车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String dripermitted;
	/**准教车型 下列编码单选：
A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "准教车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "准教车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String teachpermitted;
	/**供职状态 0:在职
1:离职*/
	@Excel(name = "供职状态 0:在职 1:离职", width = 15, dicCode = "sys_Serve_status")
    @ApiModelProperty(value = "供职状态 0:在职 1:离职")
	@Dict(dicCode = "sys_Serve_status")
    private java.lang.String employstatus;
	/**入职日期 YYYYMMDD*/
	@Excel(name = "入职日期 YYYYMMDD", width = 15)
    @ApiModelProperty(value = "入职日期 YYYYMMDD")
    private java.lang.String hiredate;
	/**离职日期  YYYYMMDD*/
	@Excel(name = "离职日期  YYYYMMDD", width = 15)
    @ApiModelProperty(value = "离职日期  YYYYMMDD")
    private java.lang.String leavedate;
	/**0-未审核 1-审核通过 2-审核不通过*/
	@Excel(name = "0-未审核 1-审核通过 2-审核不通过", width = 15, dicCode = "sys_general_audit")
    @ApiModelProperty(value = "0-未审核 1-审核通过 2-审核不通过")
	@Dict(dicCode = "sys_general_audit")
    private java.lang.Long auditstate;
	/**审核时间*/
	@Excel(name = "审核时间", width = 15)
    @ApiModelProperty(value = "审核时间")
    private java.lang.String auditdate;
	/**审核原因*/
	@Excel(name = "审核原因", width = 15)
    @ApiModelProperty(value = "审核原因")
    private java.lang.String auditreason;
	/**停训 1-停训 0-启用*/
	@Dict(dicCode = "coach_stop_train")
	@Excel(name = "停训 1-停训 0-启用", width = 15, dicCode = "coach_stop_train")
    @ApiModelProperty(value = "停训 1-停训 0-启用")
    private java.lang.Long stopTrain;
	/**状态：1.已备案，0.解除备案*/
	@Dict(dicCode = "sys_general_status")
	@Excel(name = "状态：1.已备案，0.解除备案", width = 15, dicCode = "sys_general_status")
    @ApiModelProperty(value = "状态：1.已备案，0.解除备案")
    private java.lang.Long status;
	/**全国统一编号*/
	@Excel(name = "全国统一编号", width = 15)
    @ApiModelProperty(value = "全国统一编号")
    private java.lang.String coachnum;
	/**是否推送计时 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送计时 0未推送,1已推送,2推送失败", width = 15, dicCode = "sys_push")
    @ApiModelProperty(value = "是否推送计时 0未推送,1已推送,2推送失败")
	@Dict(dicCode = "sys_push")
    private java.lang.String istojs;
	/**是否推送全国 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送全国 0未推送,1已推送,2推送失败", width = 15, dicCode = "sys_push")
    @ApiModelProperty(value = "是否推送全国 0未推送,1已推送,2推送失败")
	@Dict(dicCode = "sys_push")
    private java.lang.String istoqg;
	/**有效日期*/
	@Excel(name = "有效日期", width = 15)
    @ApiModelProperty(value = "有效日期")
    private java.lang.String effectivedate;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
	/**政务云路径*/
	@Excel(name = "政务云路径", width = 15)
    @ApiModelProperty(value = "政务云路径")
    private java.lang.String osspotourl;
	/**带教类型：模拟实操理论*/
	@Excel(name = "带教类型：模拟实操理论", width = 15, dicCode = "sys_teach_type")
    @ApiModelProperty(value = "带教类型：模拟实操理论")
	@Dict(dicCode = "sys_teach_type")
    private java.lang.Integer teachtype;
	/**厂商*/
	@Excel(name = "厂商", width = 15)
    @ApiModelProperty(value = "厂商")
    private java.lang.String platform;
	/**厂商名称*/
	@Excel(name = "厂商名称", width = 15)
	@ApiModelProperty(value = "厂商名称")
	@TableField(exist = false)
	private java.lang.String platformName;
	/**remark*/
	@Excel(name = "remark", width = 15)
    @ApiModelProperty(value = "remark")
    private java.lang.String remark;
    /**是否是驾校用户*/
    @TableField(exist = false)
    private Boolean isSchool;
    /**类型*/
    @TableField(exist = false)
    private Integer type;




	/**迁移追加字段*/
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**迁移追加字段*/
}
