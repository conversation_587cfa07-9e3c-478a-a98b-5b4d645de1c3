package com.jky.boot.core.module.gzpt.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.utils.StringUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jky.boot.core.module.gzpt.entity.Bank;
import com.jky.boot.core.module.gzpt.service.IBankService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 资金托管银行管理
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="资金托管银行管理")
@RestController
@RequestMapping("/gzpt/bank")
@Slf4j
public class BankController extends JeecgController<Bank, IBankService> {
	@Autowired
	private IBankService bankService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bank
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "资金托管银行管理-分页列表查询")
	@ApiOperation(value="资金托管银行管理-分页列表查询", notes="资金托管银行管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Bank>> queryPageList(Bank bank,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<Bank> queryWrapper = QueryGenerator.initQueryWrapper(bank, req.getParameterMap());
		queryWrapper.orderByDesc("create_time");
		Page<Bank> page = new Page<Bank>(pageNo, pageSize);
		IPage<Bank> pageList = bankService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bank
	 * @return
	 */
	@AutoLog(value = "资金托管银行管理-添加")
	@ApiOperation(value="资金托管银行管理-添加", notes="资金托管银行管理-添加")
	@RequiresPermissions("gzpt:bank:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Bank bank) {
		LambdaQueryWrapper<Bank> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Bank::getBankCode, bank.getBankCode());
		Bank one = bankService.getOne(wrapper);
		if(one != null){
			return Result.error(bank.getBankName() + "已经添加!");
		}
		bank.setId(StringUtil.getUuid());
		bank.setCrdate(DateUtils.getDate());
		bankService.save(bank);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bank
	 * @return
	 */
	@AutoLog(value = "资金托管银行管理-编辑")
	@ApiOperation(value="资金托管银行管理-编辑", notes="资金托管银行管理-编辑")
	@RequiresPermissions("gzpt:bank:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Bank bank) {
		LambdaQueryWrapper<Bank> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Bank::getBankCode, bank.getBankCode());
		Bank one = bankService.getOne(wrapper);
		if(one != null){
			return Result.error(bank.getBankName() + "已经添加!");
		}
		bankService.updateById(bank);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "资金托管银行管理-通过id删除")
	@ApiOperation(value="资金托管银行管理-通过id删除", notes="资金托管银行管理-通过id删除")
	@RequiresPermissions("gzpt:bank:remove")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bankService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "资金托管银行管理-批量删除")
	@ApiOperation(value="资金托管银行管理-批量删除", notes="资金托管银行管理-批量删除")
	@RequiresPermissions("gzpt:bank:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bankService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "资金托管银行管理-通过id查询")
	@ApiOperation(value="资金托管银行管理-通过id查询", notes="资金托管银行管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Bank> queryById(@RequestParam(name="id",required=true) String id) {
		Bank bank = bankService.getById(id);
		if(bank==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bank);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param bank
//    */
//    @RequiresPermissions("gzpt:bank:export")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, Bank bank) {
//        return super.exportXls(request, bank, Bank.class, "资金托管银行管理");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_bank:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, Bank.class);
//    }
}
