package com.jky.boot.core.module.reservation.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.SchedulingApply;
import com.jky.boot.base.dao.SchedulingImi;
import com.jky.boot.base.dao.TimeTemplateDetails;
import com.jky.boot.base.service.ISchedulingApplyService;
import com.jky.boot.base.service.ISchedulingImiService;
import com.jky.boot.base.service.ITimeTemplateDetailsService;
import com.jky.boot.common.beanCopy.BeanConvertUtils;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.reservation.vo.ZlbImiVo;
import com.jky.boot.core.module.reservation.vo.oprAdd.OprParams;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.boot.core.module.scheduling.entity.SchedulingImiCenter;
import com.jky.boot.core.module.scheduling.service.ISchedulingImiCenterService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模拟课程预约
 *
 * <AUTHOR>
 * @version 2024-09-29
 */
@Slf4j
@RestController
@RequestMapping("/schedule/imi")
public class SchedulingImiController {
    @Autowired
    private ISchedulingImiService schedulingImiService;
    @Autowired
    private ISchedulingApplyService schedulingApplyService;
    @Autowired
    private ITimeTemplateDetailsService timeTemplateDetailsService;
    @Autowired
    private ISchedulingImiCenterService schedulingImiCenterService;

    /**
     * 分页列表查询 包含模拟中心的模拟排班
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SchedulingImi schedulingImi,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(schedulingImi.getInscode());
        String currentInscode = insCodes.get(0);
        schedulingImi.setInscode(null);
        QueryWrapper<SchedulingImi> queryWrapper = QueryGenerator.initQueryWrapper(schedulingImi, req.getParameterMap());
        // 检查用户是否是模拟中心下面的驾校，是的话收集模拟中心编号，可能会有多个记录 查询所有模拟中心记录
        List<SchedulingImiCenter> simulationCenters = schedulingImiCenterService.list();
        // 检查当前驾校是否在任何模拟中心的inscodeList中，如果是则收集该模拟中心的inscode
        List<String> simulationCenterInsCodes = new ArrayList<>();
        for (SchedulingImiCenter center : simulationCenters) {
            String inscodeList = center.getInscodeList();
            if (inscodeList != null && !inscodeList.trim().isEmpty()) {
                // 检查inscodeList中是否包含当前驾校编号
                String[] centerInscodeArray = inscodeList.split(",");
                // 检查当前用户的驾校编号是否在该模拟中心的inscodeList中
                if (Arrays.asList(centerInscodeArray).contains(currentInscode)) {
                    // 如果当前驾校在该模拟中心下，则添加该模拟中心的inscode
                    simulationCenterInsCodes.add(center.getInscode());
                }
            }
        }
        
        // 根据是否找到模拟中心来决定查询逻辑
        if (!simulationCenterInsCodes.isEmpty()) {
            // 如果找到模拟中心，则查询模拟中心的排班
            queryWrapper.in("inscode", simulationCenterInsCodes);
        } else {
            // 否则还是原来的逻辑，查询当前驾校的排班
            queryWrapper.in("inscode", insCodes);
        }
        // 修改排序逻辑：先按日期降序，再按开始时间降序
        queryWrapper.orderByDesc("class_date")
                .orderByAsc("substring(class_time, 1, 5)").orderByDesc("subject").orderByDesc("coachnum");
        Page<SchedulingImi> page = new Page<>(pageNo, pageSize);
        IPage<SchedulingImi> pageList = schedulingImiService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SchedulingImi schedulingImi) {
        schedulingImi.setInscode(JkSecurityUtils.getDeptOrgCode());
        return createSchedulingImi(schedulingImi, "添加成功！");
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody SchedulingImi schedulingImi) {
        // 查看是否已有学员预约
        long count = schedulingApplyService.count(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, schedulingImi.getId())
                        .eq(SchedulingApply::getCancel, 0)
        );
        if (count > 0) {
            return Result.error("请删除预约后操作");
        }
//        schedulingImi.setInscode(JkSecurityUtils.getOrgCodeByUnknown(schedulingImi.getInscode()));

        // 先删除原有的记录
//        schedulingImiService.removeById(schedulingImi.getId());

        // 编辑时需要生成新的ID
//        schedulingImi.setId(null);
        schedulingImiService.updateById(schedulingImi);
        return Result.ok("修改成功");
    }

    /**
     * 创建模拟课程记录的公共方法
     *
     * @param schedulingImi 模拟课程基本信息
     * @param successMessage 成功时的提示信息
     * @return 处理结果
     */
    private Result<?> createSchedulingImi(SchedulingImi schedulingImi, String successMessage) {
        // 获取日期范围
        Date startDate = schedulingImi.getClassDateStart();
        Date endDate = schedulingImi.getClassDateEnd();

        if (startDate == null || endDate == null) {
            return Result.error("请选择开始和结束日期");
        }

        // 检查时间方案ID是否存在
        if (StringUtils.isBlank(schedulingImi.getTemplateId())) {
            return Result.error("请选择时间方案");
        }

        // 获取时间模板详情
        List<TimeTemplateDetails> templateDetails = timeTemplateDetailsService.list(
            Wrappers.<TimeTemplateDetails>lambdaQuery()
                .eq(TimeTemplateDetails::getTemplateId, schedulingImi.getTemplateId())
        );

        if (templateDetails.isEmpty()) {
            return Result.error("所选时间方案没有时间段设置");
        }

        List<SchedulingImi> schedulingImiList = new ArrayList<>();

        // 循环处理日期范围内的每一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        Date currentDate = startDate;

        while (!currentDate.after(endDate)) {
            // 对每一天，处理每个时间段
            for (TimeTemplateDetails detail : templateDetails) {
                SchedulingImi newScheduling = new SchedulingImi();
                BeanUtils.copyProperties(schedulingImi, newScheduling);

                // 设置具体日期和时间
                newScheduling.setClassDate(currentDate);
                newScheduling.setClassTime(detail.getClassTime());
                newScheduling.setStartTime(detail.getStartTime());
                newScheduling.setEndTime(detail.getEndTime());
                newScheduling.setClassHours(detail.getClassHour() != null ? detail.getClassHour().intValue() : 0);
                newScheduling.setTemplateId(detail.getTemplateId());

                schedulingImiList.add(newScheduling);
            }

            // 移动到下一天
            calendar.add(Calendar.DATE, 1);
            currentDate = calendar.getTime();
        }

        // 批量保存
        if (!schedulingImiList.isEmpty()) {
            schedulingImiService.saveBatch(schedulingImiList);
            return Result.ok(successMessage);
        }

        return Result.error("未能创建任何模拟课程记录");
    }

    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        schedulingImiService.removeById(id);
        // 删除相关预约记录
        schedulingApplyService.remove(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, id)
        );
        return Result.ok("删除成功!");
    }
    /**
     * 批量删除
     */
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        List<String> idList = Arrays.asList(ids.split(","));

        // 检查是否存在已预约的记录
        long appointedCount = schedulingApplyService.count(
            Wrappers.<SchedulingApply>lambdaQuery()
                .in(SchedulingApply::getClassId, idList)
                .eq(SchedulingApply::getCancel, 0)
        );

        if (appointedCount > 0) {
            return Result.error("选中记录中包含已预约的课程，不可删除");
        }

        // 检查是否存在已发布的记录
        long publishedCount = schedulingImiService.count(
            Wrappers.<SchedulingImi>lambdaQuery()
                .in(SchedulingImi::getId, idList)
                .eq(SchedulingImi::getPublish, 1)
        );

        if (publishedCount > 0) {
            return Result.error("选中记录中包含已发布的课程，不可删除");
        }

        // 执行批量删除
        schedulingImiService.removeByIds(idList);

        // 删除相关的预约记录（已取消的预约记录）
        schedulingApplyService.remove(
            Wrappers.<SchedulingApply>lambdaQuery()
                .in(SchedulingApply::getClassId, idList)
        );

        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        SchedulingImi schedulingImi = schedulingImiService.getById(id);
        return Result.ok(schedulingImi);
    }

    /**
     * 发布/撤回发布
     */
    @RequestMapping(value = "/pubOrCancel", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> pubOrCancel(@RequestBody OprParams rec) {
        for (String id : rec.getIds().split(",")) {
            schedulingImiService.update(
                    Wrappers.<SchedulingImi>lambdaUpdate()
                            .set(SchedulingImi::getPublish, rec.getPublish())
                            .set(SchedulingImi::getPublishTime, rec.getPublish() == 1 ? new Date() : null)
                            .eq(SchedulingImi::getId, id)
            );
        }
        return Result.ok("操作成功!");
    }

    /**
     * zlb 分页列表查询
     */
    @GetMapping(value = "/zlb/list")
    public Result<?> zlbList(SchedulingImi rec,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                             @RequestParam(name = "classRoomName", required = false) String classRoomName) {
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名！");
        }
        // 是否今天
        String nowHHmm = LocalTime.now().toString().substring(0, 5);
        String today = LocalDate.now().toString();
        String recDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, rec.getClassDate());
        boolean isToday = today.equals(recDate);
        LambdaQueryWrapper<SchedulingImi> queryWrapper = Wrappers.lambdaQuery();

        // --- 根据学员驾校编号查询关联的模拟中心 ---
        // 获取所有模拟中心记录
        List<SchedulingImiCenter> allCenterList = schedulingImiCenterService.list();
        List<String> targetInscodes = new ArrayList<>();
        // 在 Java 代码中循环判断
        if (!allCenterList.isEmpty()) {
            for (SchedulingImiCenter center : allCenterList) {
                // 确保 inscodeList 不为空
                if (StringUtils.isNotBlank(center.getInscodeList())) {
                    // 精确判断 inscodeList 是否包含学员的 inscode
                    List<String> inscodesInCenter = Arrays.asList(center.getInscodeList().split(","));
                    if (inscodesInCenter.contains(stu.getInscode())) {
                        targetInscodes.add(center.getInscode()); // 添加模拟中心自己的 inscode
                    }
                }
            }
        }else{
            //学员所在驾校 没有公共模拟中心 自己在驾校培训
            targetInscodes.add(stu.getInscode());
        }
        queryWrapper.in(SchedulingImi::getInscode, targetInscodes); // 修改为 in 查询
        queryWrapper.eq(StringUtils.isNotBlank(rec.getCoachnum()), SchedulingImi::getCoachnum, rec.getCoachnum());
        //queryWrapper.eq(SchedulingImi::getTraintype, stu.getTraintype());
        //C2 学员可查找 C1,C2排班，否则只能查C1
//        String traintype = Objects.equals(stu.getTraintype(), "C2") ? "C1,C2" : stu.getTraintype();
//        queryWrapper.in(SchedulingImi::getTraintype, Arrays.asList(traintype.split(",")));
        queryWrapper.eq(Objects.nonNull(rec.getSubject()), SchedulingImi::getSubject, rec.getSubject());
        // 添加教室ID筛选条件
        queryWrapper.eq(StringUtils.isNotBlank(classRoomName), SchedulingImi::getClassRoomName, classRoomName);
        queryWrapper.eq(SchedulingImi::getPublish, 1);
        queryWrapper.eq(SchedulingImi::getClassDate, rec.getClassDate());
        queryWrapper.ge(isToday, SchedulingImi::getStartTime, nowHHmm);
        queryWrapper.orderByAsc(SchedulingImi::getStartTime);
        Page<SchedulingImi> page = new Page<>(pageNo, pageSize);
        IPage<SchedulingImi> pageList = schedulingImiService.page(page, queryWrapper);
        if (pageList.getRecords().isEmpty()) {
            return Result.ok(pageList);
        }
        IPage<ZlbImiVo> res = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        List<ZlbImiVo> zlbImiVos = BeanConvertUtils.convertListTo(pageList.getRecords(), ZlbImiVo::new);
        res.setRecords(zlbImiVos);
        // 查看调用人有没有约过这些课程
        List<String> ids = pageList.getRecords().stream().map(SchedulingImi::getId).collect(Collectors.toList());
        List<SchedulingApply> list = schedulingApplyService.list(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getStunum, stu.getStunum())
                        .eq(SchedulingApply::getCancel, 0)
                        .in(SchedulingApply::getClassId, ids)
        );
        if (list.isEmpty()) {
            return Result.ok(res);
        }
        Map<String, Integer> map = list.stream().collect(Collectors.toMap(SchedulingApply::getClassId, SchedulingApply::getCancel));
        for (ZlbImiVo record : zlbImiVos) {
            Integer isCancel = map.get(String.valueOf(record.getId()));
            if (isCancel != null && isCancel == 0) {
                record.setAlready(1);
            }
            if(Objects.equals(record.getTraintype(), "C1")){
                record.setTraintype("C1,C2");
            }
        }
        return Result.ok(res);
    }

    /**
     * 检查当前驾校是否属于模拟中心
     */
    @GetMapping(value = "/checkIfBelongsToSimulationCenter")
    public Result<Boolean> checkIfBelongsToSimulationCenter(HttpServletRequest request) {
        try {
            // 获取当前用户的驾校编号
            List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
            String currentInscode = insCodes.get(0);
            
            // 查询所有模拟中心记录
            List<SchedulingImiCenter> simulationCenters = schedulingImiCenterService.list();
            
            // 检查当前驾校是否在任何模拟中心的inscodeList中
            for (SchedulingImiCenter center : simulationCenters) {
                String inscodeList = center.getInscodeList();
                if (inscodeList != null && !inscodeList.trim().isEmpty()) {
                    // 检查inscodeList中是否包含当前驾校编号
                    String[] centerInscodeArray = inscodeList.split(",");
                    if (Arrays.asList(centerInscodeArray).contains(currentInscode)) {
                        return Result.ok("当前驾校属于模拟中心");
                    }
                }
            }
            
            return Result.error("当前驾校不属于模拟中心");
        } catch (Exception e) {
            log.error("检查驾校是否属于模拟中心时发生错误", e);
            return Result.error("检查失败");
        }
    }
}
