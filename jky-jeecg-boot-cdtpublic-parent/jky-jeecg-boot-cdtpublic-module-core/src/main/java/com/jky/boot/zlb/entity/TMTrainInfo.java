package com.jky.boot.zlb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_train_info
 * @Author: jeecg-boot
 * @Date:   2023-06-26
 * @Version: V1.0
 */
@Data
@TableName("t_m_train_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_train_info对象", description="t_m_train_info")
public class TMTrainInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**序号*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "序号")
    private String id;
	/**培训机构编码*/
	@Excel(name = "培训机构编码", width = 15)
    @ApiModelProperty(value = "培训机构编码")
    private String inscode;
	/**教学区域名称*/
	@Excel(name = "教学区域名称", width = 15)
    @ApiModelProperty(value = "教学区域名称")
    private String name;
	/**教学区域地址*/
	@Excel(name = "教学区域地址", width = 15)
    @ApiModelProperty(value = "教学区域地址")
    private String address;
	/**教学区域面积*/
	@Excel(name = "教学区域面积", width = 15)
    @ApiModelProperty(value = "教学区域面积")
    private String area;
	/**教学区域类型*/
	@Excel(name = "教学区域类型", width = 15)
    @ApiModelProperty(value = "教学区域类型")
    private String type;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新shijian1*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新shijian1")
    private Date updateTime;
}
