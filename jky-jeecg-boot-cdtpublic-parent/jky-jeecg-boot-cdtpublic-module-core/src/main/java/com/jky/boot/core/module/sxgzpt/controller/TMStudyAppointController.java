package com.jky.boot.core.module.sxgzpt.controller;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.TMStudyAppoint;
import com.jky.boot.core.module.sxgzpt.service.ITMStudyAppointService;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * 	学习培训预约
 * <AUTHOR>
 * @since   2023-06-13
 * @version	V1.0
 */
@Api(tags="学习培训预约")
@RestController
@RequestMapping("/gzpt/tMStudyAppoint")
@Slf4j
public class TMStudyAppointController extends JeecgController<TMStudyAppoint, ITMStudyAppointService> {
	@Autowired
	private ITMStudyAppointService tMStudyAppointService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tMStudyAppoint
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "学习培训预约-分页列表查询")
	@ApiOperation(value="学习培训预约-分页列表查询", notes="学习培训预约-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMStudyAppoint>> queryPageList(TMStudyAppoint tMStudyAppoint,
													   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													   HttpServletRequest req) {
		QueryWrapper<TMStudyAppoint> queryWrapper = QueryGenerator.initQueryWrapper(tMStudyAppoint, req.getParameterMap());
		Page<TMStudyAppoint> page = new Page<>(pageNo, pageSize);
		IPage<TMStudyAppoint> pageList = tMStudyAppointService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tMStudyAppoint
	 * @return
	 */
	@AutoLog(value = "学习培训预约-添加")
	@ApiOperation(value="学习培训预约-添加", notes="学习培训预约-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_study_appoint:add")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody TMStudyAppoint tMStudyAppoint) {
		JkySysDepart depart = JkSecurityUtils.getDepartByUnknown(tMStudyAppoint.getInsCode());
		if (ObjectUtils.isEmpty(depart)) {
			return Result.error("培训机构有误！");
		}
		tMStudyAppoint.setInsCode(depart.getOrgCode());
		tMStudyAppointService.save(tMStudyAppoint);
		return Result.ok("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tMStudyAppoint
	 * @return
	 */
	@AutoLog(value = "学习培训预约-编辑")
	@ApiOperation(value="学习培训预约-编辑", notes="学习培训预约-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_study_appoint:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMStudyAppoint tMStudyAppoint) {
		tMStudyAppointService.updateById(tMStudyAppoint);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "学习培训预约-通过id删除")
	@ApiOperation(value="学习培训预约-通过id删除", notes="学习培训预约-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_study_appoint:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		tMStudyAppointService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "学习培训预约-批量删除")
	@ApiOperation(value="学习培训预约-批量删除", notes="学习培训预约-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_study_appoint:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
		this.tMStudyAppointService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "学习培训预约-通过id查询")
	@ApiOperation(value="学习培训预约-通过id查询", notes="学习培训预约-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMStudyAppoint> queryById(@RequestParam(name="id") String id) {
		TMStudyAppoint tMStudyAppoint = tMStudyAppointService.getById(id);
		if(tMStudyAppoint==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMStudyAppoint);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMStudyAppoint
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_study_appoint:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMStudyAppoint tMStudyAppoint) {
//        return super.exportXls(request, tMStudyAppoint, TMStudyAppoint.class, "学习培训预约");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_study_appoint:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMStudyAppoint.class);
//    }

}
