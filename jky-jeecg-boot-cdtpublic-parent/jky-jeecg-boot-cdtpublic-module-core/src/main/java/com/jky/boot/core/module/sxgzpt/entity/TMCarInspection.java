package com.jky.boot.core.module.sxgzpt.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_car_inspection
 * @Author: jeecg-boot
 * @Date:   2023-07-31
 * @Version: V1.0
 */
@Data
@TableName("t_m_car_inspection")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_car_inspection对象", description="t_m_car_inspection")
public class TMCarInspection implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**车辆牌号*/
	@Excel(name = "车辆牌号", width = 15)
    @ApiModelProperty(value = "车辆牌号")
    private String licnum;

    private java.lang.String franum;
	/**车辆编号*/
	@Excel(name = "车辆编号", width = 15)
    @ApiModelProperty(value = "车辆编号")
    private String carnum;
	/**车辆型号*/
	@Excel(name = "车辆型号", width = 15)
    @ApiModelProperty(value = "车辆型号")
    private String model;
	/**车牌颜色 1=蓝色,2=黄色,3=黑色,4=白色,5=绿色,9=其他*/
	@Excel(name = "车牌颜色 1=蓝色,2=黄色,3=黑色,4=白色,5=绿色,9=其他", width = 15)
    @ApiModelProperty(value = "车牌颜色 1=蓝色,2=黄色,3=黑色,4=白色,5=绿色,9=其他")
    private Integer platecolor;
	/**车辆年检状态（1正常、2逾期未检验 、3无法判断）*/
	@Excel(name = "车辆年检状态（1正常、2逾期未检验 、3无法判断）", width = 15)
    @ApiModelProperty(value = "车辆年检状态（1正常、2逾期未检验 、3无法判断）")
    private String inspectionStatus;
	/**年检到期时间*/
	@Excel(name = "年检到期时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "年检到期时间")
    private Date inspectionDate;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private Integer removed;
}
