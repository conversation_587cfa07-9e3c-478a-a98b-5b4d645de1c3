package com.jky.boot.core.module.gzpt.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.mapper.PlatformMapper;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 厂商管理对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class PlatformServiceImpl extends ServiceImpl<PlatformMapper, Platform> implements IPlatformService {

    /**
     * 通过厂商编号查询
     *
     * @param serialNumber
     * @return
     */
    @Override
    public Platform getByPlatformSerialNumber(String serialNumber) {
        return baseMapper.selectOne(Wrappers.<Platform>lambdaQuery()
                .select(Platform::getId,Platform::getSerialNumber,Platform::getPlatformName,Platform::getApi,Platform::getIp,Platform::getCrdate)
                .eq(Platform::getSerialNumber,serialNumber));
    }

    @Override
    public Platform selectBySerialNumber(String platform) {
        return baseMapper.selectOne(Wrappers.<Platform>lambdaQuery()
                .eq(Platform::getSerialNumber,platform));
    }
}
