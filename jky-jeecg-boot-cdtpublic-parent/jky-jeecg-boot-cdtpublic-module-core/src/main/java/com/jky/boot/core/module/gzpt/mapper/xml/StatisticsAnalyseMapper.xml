<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.StatisticsAnalyseMapper">

    <select id="analyseSensitive" resultType="java.util.Map">
        select   username , log_type, count(1) total from  t_m_mr890ch_log

        <where>
            <if test="createTimeStart != null and createTimeStart != ''">
                and create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        GROUP BY username , log_type
    </select>

</mapper>
