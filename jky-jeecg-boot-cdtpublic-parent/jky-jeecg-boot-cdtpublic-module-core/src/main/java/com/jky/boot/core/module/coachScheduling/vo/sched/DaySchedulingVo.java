package com.jky.boot.core.module.coachScheduling.vo.sched;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @version 2023-10-18  15:11
 */
@Data
public class DaySchedulingVo {
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;
    private List<SchedulingVo> amSchedulingVo;
    private List<SchedulingVo> pmSchedulingVo;
}
