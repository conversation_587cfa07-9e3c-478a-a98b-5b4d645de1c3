package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.jky.boot.core.module.gzpt.entity.InstitutionDetails;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 培训机构附加信息对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface InstitutionDetailsMapper extends BaseMapper<InstitutionDetails> {
    /**
     * 查询培训机构附加信息
     *
     * @param inscode 培训机构附加信息主键
     * @return 培训机构附加信息
     */
    InstitutionDetails selectInstitutionDetailsByInscode(String inscode);

    /**
     * 查询培训机构附加信息列表
     *
     * @param institutionDetails 培训机构附加信息
     * @return 培训机构附加信息集合
     */
    List<InstitutionDetails> selectInstitutionDetailsList(InstitutionDetails institutionDetails);

    /**
     * 新增培训机构附加信息
     *
     * @param institutionDetails 培训机构附加信息
     * @return 结果
     */
    int insertInstitutionDetails(InstitutionDetails institutionDetails);

    /**
     * 修改培训机构附加信息
     *
     * @param institutionDetails 培训机构附加信息
     * @return 结果
     */
    int updateInstitutionDetails(InstitutionDetails institutionDetails);

    /**
     * 删除培训机构附加信息
     *
     * @param inscode 培训机构附加信息主键
     * @return 结果
     */
    int deleteInstitutionDetailsByInscode(String inscode);

    /**
     * 批量删除培训机构附加信息
     *
     * @param inscodes 需要删除的数据主键集合
     * @return 结果
     */
    int deleteInstitutionDetailsByInscodes(String[] inscodes);
}
