package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.TrainSubjectCredit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 额定学时对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface TrainSubjectCreditMapper extends BaseMapper<TrainSubjectCredit> {
    /**
     * 查询额定学时
     *
     * @param id 额定学时主键
     * @return 额定学时
     */
    TrainSubjectCredit selectTrainSubjectCreditById(String id);

    /**
     * 查询额定学时列表
     *
     * @param trainSubjectCredit 额定学时
     * @return 额定学时集合
     */
    List<TrainSubjectCredit> selectTrainSubjectCreditList(TrainSubjectCredit trainSubjectCredit);

    /**
     * 新增额定学时
     *
     * @param trainSubjectCredit 额定学时
     * @return 结果
     */
    int insertTrainSubjectCredit(TrainSubjectCredit trainSubjectCredit);

    /**
     * 修改额定学时
     *
     * @param trainSubjectCredit 额定学时
     * @return 结果
     */
    int updateTrainSubjectCredit(TrainSubjectCredit trainSubjectCredit);

    /**
     * 删除额定学时
     *
     * @param id 额定学时主键
     * @return 结果
     */
    int deleteTrainSubjectCreditById(String id);

    /**
     * 批量删除额定学时
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTrainSubjectCreditByIds(String[] ids);

    /**
     * 查询指定大纲，车型，学习类型的总学时
     */
    Double queryTotalTime(@Param("outline") Integer outline,
                          @Param("trainType") String trainType,
                          @Param("classType") String classType);
}
