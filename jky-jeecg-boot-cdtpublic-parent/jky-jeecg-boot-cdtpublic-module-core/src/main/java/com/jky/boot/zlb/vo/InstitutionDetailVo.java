package com.jky.boot.zlb.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InstitutionDetailVo  implements Serializable {

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private Integer id;
    /**区县行政区划代码*/
    @Excel(name = "区县行政区划代码", width = 15)
    @ApiModelProperty(value = "区县行政区划代码")
    private String district;
    /**培训机构全称*/
    @Excel(name = "培训机构全称", width = 15)
    @ApiModelProperty(value = "培训机构全称")
    private String name;
    /**培训机构简称*/
    @Excel(name = "培训机构简称", width = 15)
    @ApiModelProperty(value = "培训机构简称")
    private String shortname;
    /**培训机构编号*/
    @Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;
    /**经营许可证编号*/
    @Excel(name = "经营许可证编号", width = 15)
    @ApiModelProperty(value = "经营许可证编号")
    private String licnum;
    /**经营许可日期*/
    @Excel(name = "经营许可日期", width = 15)
    @ApiModelProperty(value = "经营许可日期")
    private String licetime;
    /**营业执照注册号 (营业执照号与社会信用代码至少有1个不为空)*/
    @Excel(name = "营业执照注册号 (营业执照号与社会信用代码至少有1个不为空)", width = 15)
    @ApiModelProperty(value = "营业执照注册号 (营业执照号与社会信用代码至少有1个不为空)")
    private String business;
    /**统一社会信用代码*/
    @Excel(name = "统一社会信用代码", width = 15)
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditcode;
    /**培训机构地址*/
    @Excel(name = "培训机构地址", width = 15)
    @ApiModelProperty(value = "培训机构地址")
    private String address;
    /**邮政编码*/
    @Excel(name = "邮政编码", width = 15)
    @ApiModelProperty(value = "邮政编码")
    private String postcode;
    /**法人代表*/
    @Excel(name = "法人代表", width = 15)
    @ApiModelProperty(value = "法人代表")
    private String legal;
    /**联系人*/
    @Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    private String contact;
    /**联系电话*/
    @Excel(name = "联系电话", width = 15)
    @ApiModelProperty(value = "联系电话")
    private String phone;
    /**经营范围 (A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P)*/
    @Excel(name = "经营范围 (A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P)", width = 15)
    @ApiModelProperty(value = "经营范围 (A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P)")
    private String busiscope;
    /**经营状态 (1:营业
     2:停业
     3:整改
     4:停业整顿
     5:歇业
     6:注销
     9:其他)*/
    @Excel(name = "经营状态 (1:营业 2:停业 3:整改 4:停业整顿 5:歇业 6:注销 9:其他)", width = 15)
    @ApiModelProperty(value = "经营状态 (1:营业 2:停业 3:整改 4:停业整顿 5:歇业 6:注销 9:其他)")
    private String busistatus;
    /**分类等级 (1:一级2:二级3:三级)*/
    @Excel(name = "分类等级 (1:一级 2:二级 3:三级)", width = 15)
    @ApiModelProperty(value = "分类等级 (1:一级 2:二级 3:三级)")
    private String levels;
    /**教练员总数*/
    @Excel(name = "教练员总数", width = 15)
    @ApiModelProperty(value = "教练员总数")
    private Integer coachnumber;
    /**考核员总数*/
    @Excel(name = "考核员总数", width = 15)
    @ApiModelProperty(value = "考核员总数")
    private Integer grasupvnum;
    /**安全员总数*/
    @Excel(name = "安全员总数", width = 15)
    @ApiModelProperty(value = "安全员总数")
    private Integer safmngnum;
    /**教练车总数*/
    @Excel(name = "教练车总数", width = 15)
    @ApiModelProperty(value = "教练车总数")
    private Integer tracarnum;
    /**教室总面积 (单位：m2)*/
    @Excel(name = "教室总面积 (单位：m2)", width = 15)
    @ApiModelProperty(value = "教室总面积 (单位：m2)")
    private Integer classroom;
    /**理论教室面积 (单位：m2)*/
    @Excel(name = "理论教室面积 (单位：m2)", width = 15)
    @ApiModelProperty(value = "理论教室面积 (单位：m2)")
    private Integer thclassroom;
    /**教练场总面积 (单位：m2)*/
    @Excel(name = "教练场总面积 (单位：m2)", width = 15)
    @ApiModelProperty(value = "教练场总面积 (单位：m2)")
    private Integer praticefield;
    /**驾校锁定、解锁*/
    @Excel(name = "驾校锁定、解锁", width = 15)
    @ApiModelProperty(value = "驾校锁定、解锁")
    private Integer islock;
    /**驾校锁定、解锁的原因*/
    @Excel(name = "驾校锁定、解锁的原因", width = 15)
    @ApiModelProperty(value = "驾校锁定、解锁的原因")
    private String islocktxt;
    /**厂商编号*/
    @Excel(name = "厂商编号", width = 15)
    @ApiModelProperty(value = "厂商编号")
    private String platform;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @TableField(exist = false)
    private String insTotalFraction;

    /**经度*/
    @Excel(name = "经度", width = 15)
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**纬度*/
    @Excel(name = "纬度", width = 15)
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**简介*/
    @Excel(name = "简介", width = 15)
    @ApiModelProperty(value = "简介")
    private String intro;

    /**评价*/
    @Excel(name = "评价", width = 15)
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**评价*/
    @Excel(name = "考试通过率", width = 15)
    @ApiModelProperty(value = "考试通过率")
    private String rate;


    /**评价*/
    @Excel(name = "行政处罚条数", width = 15)
    @ApiModelProperty(value = "行政处罚条数")
    private String penalize;

    /**评分*/
    @Excel(name = "评分", width = 15)
    @ApiModelProperty(value = "评分")
    private String mark;

    /**轮播图 图片组*/
    @Excel(name = "轮播图 图片组", width = 15)
    @ApiModelProperty(value = "轮播图 图片组")
    private List<String> logoList;

    @ApiModelProperty(value = "驾校照片")
    private String logo;
}
