package com.jky.boot.zlb.controller;

import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.zlb.event.BankEvent;
import com.jky.boot.zlb.vo.OrderVo;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * zlb 订单
 *
 * <AUTHOR>
 * @version 2023-10-19  13:43
 */
@RestController
@RequestMapping("/zlb/order")
@Slf4j
public class ZlbBankOrderController {

    private final IStudentinfoService stuService;
    private final IBankOrderService bankOrderService;
    private final ApplicationEventPublisher applicationEventPublisher;

    public ZlbBankOrderController(IStudentinfoService stuService,
                                  IBankOrderService bankOrderService,
                                  ApplicationEventPublisher applicationEventPublisher) {
        this.stuService = stuService;
        this.bankOrderService = bankOrderService;
        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * 学员订单确认
     */
//    @AutoLog(value = "学员订单确认")
//    @PostMapping(value = "/confirm")
//    @RedissonLock(key = "#orderVo.id", keyPrefix = "order:confirm:", waitTime = 1, lockTime = 10)
//    public Result<?> record(@RequestBody OrderVo orderVo) {
//        BankOrder order = bankOrderService.getById(orderVo.getId());
//        if (Objects.isNull(order)) {
//            return Result.error("该订单不存在！");
//        }
//        if (!order.getStunum().equals(orderVo.getStunum())) {
//            return Result.error("非法操作！");
//        }
//        if (order.getConfirm() == 1) {
//            return Result.error("该订单已确认，请勿重复操作！");
//        }
//        order.setConfirm(1);
//        BankOrder orderBak = new BankOrder();
//        BeanUtils.copyProperties(order, orderBak);
//        bankOrderService.updateById(order);
//        Studentinfo stu = stuService.getOneByStuNum(order.getStunum());
//        if (stu.getBaseType() == 1 && order.getStatus() == 0 &&
//                (order.getOrdertype() == 1 || order.getOrdertype() == 2 || order.getOrdertype() == 3)) {
//            BankEvent event = new BankEvent(this, orderBak);
//            applicationEventPublisher.publishEvent(event);
//        }
//        return Result.ok("确认成功！");
//    }
}
