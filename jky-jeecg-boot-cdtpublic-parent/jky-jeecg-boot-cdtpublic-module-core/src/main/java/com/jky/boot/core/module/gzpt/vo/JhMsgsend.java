package com.jky.boot.core.module.gzpt.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @TableName T_M_JH_MSGSEND
 */

@Data
public class JhMsgsend implements Serializable {
    /**
     *
     */
    private String id;

    /**
     *
     */
    private Integer msgtype;

    /**
     *
     */
    private String data;

    /**
     *
     */
    private Date createDate;

    /**
     *
     */
    private Integer status;

    /**
     *
     */
    private Date sendBankDate;

    /**
     *
     */
    private String bankMsg;

    private Integer bankStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        JhMsgsend other = (JhMsgsend) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getMsgtype() == null ? other.getMsgtype() == null : this.getMsgtype().equals(other.getMsgtype()))
                && (this.getData() == null ? other.getData() == null : this.getData().equals(other.getData()))
                && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getSendBankDate() == null ? other.getSendBankDate() == null : this.getSendBankDate().equals(other.getSendBankDate()))
                && (this.getBankMsg() == null ? other.getBankMsg() == null : this.getBankMsg().equals(other.getBankMsg()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMsgtype() == null) ? 0 : getMsgtype().hashCode());
        result = prime * result + ((getData() == null) ? 0 : getData().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getSendBankDate() == null) ? 0 : getSendBankDate().hashCode());
        result = prime * result + ((getBankMsg() == null) ? 0 : getBankMsg().hashCode());
        return result;
    }

    @Override
    public String toString() {
        String sb = getClass().getSimpleName() +
                " [" +
                "Hash = " + hashCode() +
                ", id=" + id +
                ", msgtype=" + msgtype +
                ", data=" + data +
                ", createDate=" + createDate +
                ", status=" + status +
                ", sendBankDate=" + sendBankDate +
                ", bankMsg=" + bankMsg +
                ", serialVersionUID=" + serialVersionUID +
                "]";
        return sb;
    }
}

