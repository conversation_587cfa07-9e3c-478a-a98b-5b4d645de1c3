package com.jky.boot.core.module.gzpt.service;

import org.jeecg.common.api.vo.Result;

public interface IStuTransferAndDropOutService {

    /**
     * 学员转校 1、遍历同步监管转校审核状态，
     */
    void searchJgTransfer(String stunum);

    /**
     * 学员转校 2、监管转校审核通过，先进行学员资金解冻
     */
    void unFreezeStuTransfer(String stunum);

    /**
     * 学员转校 3、监管转校审核通过，进行学员资金解冻之后 检测解冻订单是否完成，如果完成则将学员转校
     */
    void checkUnfreezeTransfer(String stunum);

    /**
     * 学员转车型 1、先进行学员资金解冻
     */
    void unFreezeStuCarType(String stunum);

    /**
     * 学员转车型 2、学员资金解冻之后 检测解冻订单是否完成，如果完成则将学员转校
     */
    void checkUnfreezeCarType(String stunum);

}
