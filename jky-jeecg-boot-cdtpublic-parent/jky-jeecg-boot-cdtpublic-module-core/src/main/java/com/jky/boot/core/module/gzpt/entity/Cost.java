package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同费用表
 *
 * <AUTHOR>
 * @version 2024-11-11
 */
@Data
@TableName("t_m_cost")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class Cost implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 机构编号
     */
    private String inscode;
    /**
     * 机构名称
     */
    private String insname;
    /**
     * 培训车型
     */
    private String traintype;

    /**
     * 教材费用
     */
    private BigDecimal bookCost;

    /**
     * 档案费用
     */
    private BigDecimal archiveCost;

    /**
     * 科二培训费（元/小时）
     */
    private BigDecimal sub2CostPer;

    /**
     * 科二培训费总额
     */
    private BigDecimal sub2Cost;

    /**
     * 科三培训费（元/小时）
     */
    private BigDecimal sub3CostPer;

    /**
     * 科三培训费总额
     */
    private BigDecimal sub3Cost;

    /**
     * 理论知识培训单价
     */
    private BigDecimal theoryCostPer;

    /**
     * 理论知识培训费总额
     */
    private BigDecimal theoryCost;

    /**
     * 模拟培训单价
     */
    private BigDecimal imiCostPer;
    /**
     * 模拟驾驶培训费
     */
    private BigDecimal imiCost;

    /**
     * 远程培训单价
     */
    private BigDecimal remoteCostPer;

    /**
     * 远程培训费
     */
    private BigDecimal remoteCost;

    /**
     * 是否模拟替代实操（0-否，1-是）
     */
    private Integer imiFlag;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 保险费
     */
    private BigDecimal insuranceCost;

    /**
     * 支付后几日入学
     */
    private Integer workday;

    /**
     * 驾校违约金比例
     */
    private Integer insToStuCost;

    /**
     * 学员违约金比例
     */
    private Integer stuToInsCost;

    /**
     * 学员退学违约金
     */
    private BigDecimal damages;

    /**
     * 支付违约金后几日办理转校/退学
     */
    private Integer damageDays;

    /**
     * 课堂培训地址
     */
    private String classPlace;

    /**
     * 远程培训地址
     */
    private String remotePlace;

    /**
     * 模拟培训地址
     */
    private String imiPlace;

    /**
     * 约定时间（单位： /天）
     */
    private String appointtime;

    private String appointnetwork;

    private String appointphone;

    /**
     * 其余约定
     */
    private String otherAppoint;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;


    @TableField(exist = false)
    private String classTuitionFees;
}
