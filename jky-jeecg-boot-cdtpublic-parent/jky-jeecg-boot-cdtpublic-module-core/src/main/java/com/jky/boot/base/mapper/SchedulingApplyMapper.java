package com.jky.boot.base.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.SchedulingApply;
import com.jky.boot.core.module.reservation.vo.ZlbSchApplyVo;
import org.apache.ibatis.annotations.Param;

/**
 * 课堂排班学员预约
 *
 * <AUTHOR>
 * @version 2024-09-25
 */
public interface SchedulingApplyMapper extends BaseMapper<SchedulingApply> {

    IPage<ZlbSchApplyVo> oprPage(Page<ZlbSchApplyVo> page, @Param("rec") SchedulingApply rec);

    IPage<ZlbSchApplyVo> classPage(Page<ZlbSchApplyVo> page, @Param("rec") SchedulingApply rec);

    IPage<ZlbSchApplyVo> imiPage(Page<ZlbSchApplyVo> page, @Param("rec") SchedulingApply rec);
}
