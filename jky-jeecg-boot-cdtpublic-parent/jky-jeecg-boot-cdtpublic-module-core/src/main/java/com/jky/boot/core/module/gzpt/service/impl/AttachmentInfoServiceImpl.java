package com.jky.boot.core.module.gzpt.service.impl;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.dto.AttachmentDto;
import com.jky.boot.core.module.gzpt.entity.AttachmentInfo;
import com.jky.boot.core.module.gzpt.mapper.AttachmentInfoMapper;
import com.jky.boot.core.module.gzpt.service.AttachmentInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class AttachmentInfoServiceImpl implements AttachmentInfoService {

    @Resource
    private AttachmentInfoMapper attachmentInfoMapper;

    @Override
    public Object findTypeList(String associateId,String type, String subType) {

        List<AttachmentInfo> attachmentInfos = this.attachmentInfoMapper.selectList(new LambdaQueryWrapper<AttachmentInfo>().eq(AttachmentInfo::getLinkId,associateId).eq(AttachmentInfo::getType, type).eq(StringUtils.isNotBlank(subType), AttachmentInfo::getSubType, subType).eq(AttachmentInfo::getIsDelete, 0));
        Map<String, List<AttachmentInfo>> collect = attachmentInfos.stream().collect(Collectors.groupingBy(AttachmentInfo::getSubType));

        HashMap<String, Object> parentMap = new HashMap<>();
         collect.forEach((key, value) -> {

             /**
              * 这里包裹了一层分类作为外层里面是内容
              * {
              *     安全经费提取：{
              *         content：[json]
              *     }
              *
              * }
              */

             if (CollectionUtils.isNotEmpty(value)) {
                 if (value.size() > 1) {
                     List<JSONObject> collect1 = value.stream().map(attachmentInfo ->
                     {
                         JSONObject jsonObject = JSONObject.parseObject(Base64.decodeStr(attachmentInfo.getContent()));
                         jsonObject.put("id", attachmentInfo.getId());
                         return jsonObject;
                     }).collect(Collectors.toList());
                     parentMap.put(key, collect1);
                 } else {
                     JSONObject jsonObject = JSONObject.parseObject(Base64.decodeStr(value.get(0).getContent()));
                     jsonObject.put("id", value.get(0).getId());
                     parentMap.put(key,jsonObject );
                 }
             } else {
                 parentMap.put(key, "");
             }


         });
         return parentMap;

    }

    @Override
    public void saveAttachment(AttachmentDto attachmentDto) {

        String decodeStr = Base64.decodeStr(attachmentDto.getContent());
        JSONObject jsonObject = JSONObject.parseObject(decodeStr);
        jsonObject.put("uploadTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String jsonString = jsonObject.toJSONString();
        String encodeStr = Base64.encodeStr(jsonString.getBytes(StandardCharsets.UTF_8), false, false);

        //如果附件id存在则操作相关附件
        if (StringUtils.isNotBlank(attachmentDto.getId())) {
            AttachmentInfo  attachmentInfo = this.attachmentInfoMapper.selectById(attachmentDto.getId());

            if (Objects.isNull(attachmentInfo)) {
              throw new RuntimeException("未查询到附件信息");
            }
            attachmentInfo.setContent(encodeStr);
            attachmentInfo.setUpdateTime(new Date());
            this.attachmentInfoMapper.updateById(attachmentInfo);

        }else {

            AttachmentInfo attachmentInfo = new AttachmentInfo();
            attachmentInfo.setLinkId(attachmentDto.getAssociateId());
            attachmentInfo.setType(attachmentDto.getType());
            attachmentInfo.setSubType(attachmentDto.getSubType());
            attachmentInfo.setContent(encodeStr);
            attachmentInfo.setCreateTime(new Date());
            attachmentInfo.setUpdateTime(new Date());
            attachmentInfo.setIsDelete("0");
            this.attachmentInfoMapper.insert(attachmentInfo);


        }

    }

    @Override
    public void delAttachment(String id) {
        this.attachmentInfoMapper.deleteById(id);
    }


}
