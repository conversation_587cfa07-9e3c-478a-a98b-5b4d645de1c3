package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.entity.TMExaminer;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import com.jky.boot.core.module.gzpt.service.ITMExaminerService;
import com.jky.boot.core.module.gzpt.utils.NationwideUtil;
import com.jky.boot.core.module.gzpt.utils.PlatformUtils;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import com.jky.crypto.utils.JkyCryptorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

 /**
 * @Description: t_m_examiner
 * @Author: jeecg-boot
 * @Date:   2023-04-14
 * @Version: V1.0
 */
@Api(tags="t_m_examiner")
@RestController
@RequestMapping("/gzpt/tMExaminer")
@Slf4j
public class TMExaminerController extends JeecgController<TMExaminer, ITMExaminerService> {
	@Autowired
	private ITMExaminerService examinerService;
	@Autowired
	private IInstitutionService institutionService;
	@Autowired
	private IPlatformService platformService;
	@Autowired
	private OssZWYUtils ossZWYUtils;

	/**
	 * 分页列表查询
	 *
	 * @param examiner
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_examiner-分页列表查询")
	@ApiOperation(value="t_m_examiner-分页列表查询", notes="t_m_examiner-分页列表查询")
	@DesensitizedAnno
	@GetMapping(value = "/list")
	public Result<IPage<TMExaminer>> queryPageList(TMExaminer examiner,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(examiner.getInscode());
		examiner.setInscode(null);
		QueryWrapper<TMExaminer> queryWrapper = QueryGenerator.initQueryWrapper(examiner, req.getParameterMap());
		queryWrapper.in("inscode",inscode);
		queryWrapper.orderByDesc("create_time");
		queryWrapper.setEntityClass(TMExaminer.class);
		Page<TMExaminer> page = new Page<>(pageNo, pageSize);
		IPage<TMExaminer> pageList = examinerService.page(page, queryWrapper);

		// 提取 insCodes
		List<TMExaminer> records = pageList.getRecords();
		Set<String> insCodes = records.stream().map(TMExaminer::getInscode).collect(Collectors.toSet());

		if(!insCodes.isEmpty()){
			// 根据 insCodes，查询 institutions
			List<Institution> institutions = institutionService.list(Wrappers.lambdaQuery(Institution.class).in(Institution::getInscode, insCodes));

			// 构造 insCode 和 insName 的映射关系
			Map<String, String> map = institutions.stream().collect(Collectors.toMap(Institution::getInscode, Institution::getName));

			// records 中添加 insName
			records.forEach(e -> {
				e.setInsname(map.get(e.getInscode()));
			});
		}

		records.forEach(e -> {
			if(!StringUtils.isEmpty(e.getPhoto())){
				e.setPhoto(ossZWYUtils.getPhotoUrl(e.getPhoto()));
			}
		});
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param tMExaminer
	 * @return
	 */
	@AutoLog(value = "t_m_examiner-添加")
	@ApiOperation(value="t_m_examiner-添加", notes="t_m_examiner-添加")
	@RequiresPermissions("gzpt:examiner:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMExaminer tMExaminer) {
		JkySysDepart depart = JkSecurityUtils.getDepartByUnknown(tMExaminer.getInscode());
		if (Objects.nonNull(depart)) {
			tMExaminer.setInscode(depart.getOrgCode());
		}

		// 添加计时厂商
		Institution institutionServiceOne = institutionService.getOne(
				new LambdaQueryWrapper<Institution>().eq(Institution::getInscode, tMExaminer.getInscode())
		);
		if (Objects.nonNull(institutionServiceOne)){
			tMExaminer.setPlatform(institutionServiceOne.getPlatform());
		}
		tMExaminer.setPhoto(OssZWYUtils.subStr(tMExaminer.getPhoto()));
		tMExaminer.setCrdate(new Date());
		examinerService.save(tMExaminer);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param examiner
	 * @return
	 */
	@AutoLog(value = "t_m_examiner-编辑")
	@ApiOperation(value="t_m_examiner-编辑", notes="t_m_examiner-编辑")
	@RequiresPermissions("gzpt:examiner:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMExaminer examiner) throws Exception {
		TMExaminer examiners = examinerService.selectExaminerByExamid(Long.valueOf(examiner.getExamid()));
		System.out.println(examiner.getInscode());
		if (StringUtils.isNotEmpty(examiner.getInscode())) {
			System.out.println(examiner.getInscode());
			JkySysDepart sysDept = JkSecurityUtils.getDepartByUnknown(examiner.getInscode());
			examiner.setInscode(sysDept.getOrgCode());
		}
		if (StringUtils.isEmpty(examiner.getPlatform())) {
			String platform = institutionService.getPlatform(examiner.getInscode());
			examiner.setPlatform(platform);
		}
		if (StringUtils.isNotEmpty(examiner.getExamnum())) {
			if (!examiners.getInscode().equals(examiner.getInscode())) {
				Institution inscode = institutionService.getByInscode(examiner.getInscode());
				Platform platform = platformService.getByPlatformSerialNumber(inscode.getPlatform());
				CommonResponse response = PushJsUtil.push(examiner, platform.getApi() + "gzexaminer");
				JSONObject jsonObject = JSON.parseObject(response.getResult());
				if (!"0".equals(jsonObject.get("errorcode").toString())) {
					return Result.error(jsonObject.get("message").toString());
				}
			}
		}
		examiner.setPhoto(OssZWYUtils.subStr(examiner.getPhoto()));
		examinerService.updateById(examiner);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_examiner-通过id删除")
	@ApiOperation(value="t_m_examiner-通过id删除", notes="t_m_examiner-通过id删除")
	@RequiresPermissions("gzpt:examiner:remove")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		examinerService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_examiner-批量删除")
	@ApiOperation(value="t_m_examiner-批量删除", notes="t_m_examiner-批量删除")
	@RequiresPermissions("gzpt:examiner:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.examinerService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param examid
	 * @return
	 */
	//@AutoLog(value = "t_m_examiner-通过id查询")
	@ApiOperation(value="t_m_examiner-通过id查询", notes="t_m_examiner-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMExaminer> queryById(@RequestParam(name="id",required=true) String examid) {
		TMExaminer examiner = examinerService.selectExaminerByExamid(Long.parseLong(examid));
		if(examiner==null) {
			return Result.error("未找到对应数据");
		}
		Institution ins = institutionService.getByInscode(examiner.getInscode());
		examiner.setInsname(ins.getName());
		examiner.setPhoto(ossZWYUtils.getPhotoUrl(examiner.getPhoto()));
		return Result.ok(examiner);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMExaminer
//    */
//    @RequiresPermissions("gzpt:examiner:export")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMExaminer tMExaminer) {
//		String title = "考核员信息";
//		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
//
//		// 权限控制
//		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMExaminer.getInscode());
//		tMExaminer.setInscode(null);
//		QueryWrapper<TMExaminer> queryWrapper = QueryGenerator.initQueryWrapper(tMExaminer, request.getParameterMap());
//		queryWrapper.in("inscode",inscode);
//
//		// 处理勾选
//		String selections = request.getParameter("selections");
//		if (oConvertUtils.isNotEmpty(selections)) {
//			queryWrapper.in("examid", Arrays.asList(selections.split(",")));
//		}
//		queryWrapper.orderByDesc("create_time");
//
//		// 插入培训机构名称
//		List<TMExaminer> records = this.service.list(queryWrapper);
//		Set<String> insCodes = records.stream().map(TMExaminer::getInscode).collect(Collectors.toSet());
//		if(!insCodes.isEmpty()){
//			List<Institution> institutions = institutionService.list(Wrappers.lambdaQuery(Institution.class).in(Institution::getInscode, insCodes));
//			Map<String, String> map = institutions.stream().collect(Collectors.toMap(Institution::getInscode, Institution::getName));
//			records.forEach(e -> {
//				e.setInsname(map.get(e.getInscode()));
//			});
//		}
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject("fileName", title);
//		mv.addObject("entity", TMExaminer.class);
//		ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
//		mv.addObject("params", exportParams);
//		mv.addObject("data", records);
//		return mv;
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_examiner:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMExaminer.class);
//    }

	 /**
	  * 推送计时
	  */
	 @GetMapping("/pushJs/{examid}")
	 @RequiresPermissions("gzpt:examiner:pushjs")
	 public Result<?> PushJs(@PathVariable("examid")  Long examid) throws Exception {
		 TMExaminer examiner = examinerService.selectExaminerByExamid(examid);
		 Institution ins = institutionService.getByInscode(examiner.getInscode());
		 examiner.setPlatform(ins.getPlatform());

		 Platform platform = platformService.getByPlatformSerialNumber(examiner.getPlatform());
		 String photo = examiner.getPhoto();
		 examiner.setPhoto(StringUtils.isNotEmpty(photo) ? ossZWYUtils.getPhotoUrl(photo) : "");
		 CommonResponse response = PushJsUtil.pushExaminerOrSecurityguard(examiner, platform.getApi() + "gzexaminer");
		 /* JSONObject json = JSON.parseObject(response.getResult());*/
		 if (response.getErrorcode() == 0) {
			 examiner.setPhoto(photo);
			 examinerService.updateExaminer(examiner);
			 return Result.ok(response.getMessage());
		 }
		 return Result.error(PlatformUtils.errMsg(examiner.getPlatform(), response.getMessage()));
	 }

	 /**
	  * 推送全国
	  */
	 @GetMapping("/pushQg/{examid}")
	 @RequiresPermissions("gzpt:examiner:pushQg")
	 public Result<?> PushQg(@PathVariable("examid") Long examid) throws Exception {
		 TMExaminer examiner = examinerService.selectExaminerByExamid(examid);
		 CommonResponse response = NationwideUtil.pushCar(examiner, "examiner", null);
		 if (response == null) {
			 return Result.error("图片上传失败！");
		 }
		 if (response.getErrorcode() != 0) {
			 return Result.error(response.getMessage());
		 }
		 JSONObject jsonObject = JSON.parseObject(response.getData().toString());
		 String exanum = jsonObject.get("examnum").toString();
		 examiner.setExamnum(exanum);
		 examinerService.updateById(examiner);
		 return Result.ok(response.getMessage());
	 }

	 /**
	  * 加密接口
	  */
	 @GetMapping("/encrypt")
	 public Result<?> encryptData() {
		 QueryWrapper<TMExaminer> wrapper = new QueryWrapper<>();
		 wrapper.setEntityClass(TMExaminer.class);
		 List<TMExaminer> list = examinerService.list(wrapper);
		 examinerService.updateBatchById(list);
		 return Result.ok();
	 }


	 public static void main(String[] args) {
		 String key = "Jkyjppt@20231001";
		 //解密
		 System.out.println(JkyCryptorUtils.decrypt("HCRmlWYzRNoxLIOreaD/gg==", key));
		 //加密
		 System.out.println(JkyCryptorUtils.encrypt("330482199803280637", key));
	 }
}
