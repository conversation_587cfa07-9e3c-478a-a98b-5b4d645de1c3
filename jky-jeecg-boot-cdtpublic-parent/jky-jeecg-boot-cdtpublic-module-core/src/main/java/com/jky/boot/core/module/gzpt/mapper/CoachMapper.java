package com.jky.boot.core.module.gzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.boot.core.module.gzpt.entity.Coach;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 教练员信息对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface CoachMapper extends BaseMapper<Coach> {
    /**
     * 查询教练员
     *
     * @param coachid 教练员主键
     * @return 教练员
     */
    Coach selectCoachByCoachid(Long coachid);

    @Select("select * from t_m_coach where coachnum = #{coachnum}")
    Coach selectCoachByCoachnum(String coachnum);


    /**
     * 查询教练员信息列表
     *
     * @param coach 教练员信息
     * @return 教练员信息集合
     */
    List<Coach> selectCoachList(Coach coach);

    List<Coach> selectCoachList2(Coach coach);

    /**
     * 修改教练员信息
     *
     * @param coach 教练员信息
     * @return 结果
     */
    int updateCoach(Coach coach);

    /**
     * 删除教练员
     *
     * @param coachid 教练员主键
     * @return 结果
     */
    int deleteCoachByCoachid(Long coachid);

    /**
     * 批量删除教练员
     *
     * @param coachids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCoachByCoachids(Long[] coachids);

    //转校
    int updateTransfer(String[] ids, String inscode, String insname);


    //根据id查询教练员
    List<Coach> findCoachByIds(Long[] ids);

    @Select("select coachnum,photourl from T_M_COACH where coachnum = #{coachnum}")
    Coach findCoachByCoachnum(String coachnum);

    int updatePhotourlAndPhoto(String coachnum, String photourl, String photo);

    List<Coach> queryCoachByInsDevice(@Param("inscodes") List<String> inscodes, @Param("type") Integer type);

    List<Coach> queryCoachByClassRoom(@Param("classRoomId") String classRoomId, @Param("type") Integer type);
}
