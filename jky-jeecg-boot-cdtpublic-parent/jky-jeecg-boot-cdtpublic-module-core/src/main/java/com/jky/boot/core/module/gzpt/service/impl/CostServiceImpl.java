package com.jky.boot.core.module.gzpt.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.core.module.gzpt.entity.Cost;
import com.jky.boot.core.module.gzpt.mapper.CostMapper;
import com.jky.boot.core.module.gzpt.service.ICostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 费用表
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class CostServiceImpl extends ServiceImpl<CostMapper, Cost> implements ICostService {

    @Autowired
    private CostMapper costMapper;

    @Override
    public Cost getOneByInsTrainType(String inscode, String trainType) {
        return costMapper.selectOne(
                Wrappers.<Cost>lambdaQuery()
                        .eq(Cost::getInscode, inscode)
                        .eq(Cost::getTraintype, trainType)
        );
    }
}
