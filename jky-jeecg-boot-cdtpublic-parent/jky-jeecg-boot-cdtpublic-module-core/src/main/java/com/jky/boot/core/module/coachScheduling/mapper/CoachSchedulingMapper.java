package com.jky.boot.core.module.coachScheduling.mapper;

import com.jky.boot.core.module.coachScheduling.vo.sched.ImitateSchedVo;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.coachScheduling.dto.CoachSchedulingDto;
import com.jky.boot.core.module.coachScheduling.entity.CoachSchedulingTitle;
import com.jky.boot.core.module.coachScheduling.entity.SchedulingConfig;
import com.jky.boot.core.module.coachScheduling.vo.sched.CoachSchedulingVo;
import com.jky.boot.core.module.coachScheduling.vo.DisOperationCoachSchedulingVo;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.coachScheduling.entity.CoachScheduling;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: coach_scheduling
 * @Author: jeecg-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
public interface CoachSchedulingMapper extends BaseMapper<CoachScheduling> {

    IPage<CoachSchedulingVo> queryList(Page<CoachSchedulingVo> page, @Param("coachSchedulingDto") CoachSchedulingDto coachSchedulingDto, @Param("inscodes") List<String> inscodes);

    List<CoachSchedulingTitle> queryTitle(@Param("coachSchedulingDto") CoachSchedulingDto coachScheduling,@Param("inscodes") List<String> inscodes);

    List<SchedulingConfig> getList(String sql);

    void deleteOldScheduling(@Param("inscode") String inscode, @Param("startDay") Date startDay, @Param("endDay") Date endDay);

    void deleteOldSchedulingByCoach(@Param("coachnum") String coachnum, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<CoachScheduling> ifOrder(@Param("classRoomId") String classRoomId,  @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    IPage<DisOperationCoachSchedulingVo> alllist(Page<DisOperationCoachSchedulingVo> page, @Param("coachSchedulingDto") CoachSchedulingDto coachScheduling, @Param("inscodes") List<String> inscodes);


    /**
     * 查询模拟排班集合
     */
    IPage<ImitateSchedVo> imiList(Page<ImitateSchedVo> page, @Param("coachSchedulingDto") CoachSchedulingDto coachScheduling);

    List<CoachScheduling> isTeache(@Param("coachnum") String coachnum, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
