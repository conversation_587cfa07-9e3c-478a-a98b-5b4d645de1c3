package com.jky.boot.zlb.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.zlb.entity.TMInstitution;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.zlb.entity.TMCoach;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: t_m_coach
 * @Author: jeecg-boot
 * @Date:   2023-06-14
 * @Version: V1.0
 */
public interface TMCoachMapper extends BaseMapper<TMCoach> {

    Page<TMCoach> driver(@Param("page") IPage page, @Param("inscode") String inscode);

    Page<TMCoach> coach(@Param("page") IPage page, @Param("idcard") String idcard,@Param("name") String name);

    Page<TMCoach> selectcoach(@Param("page") IPage page, @Param("inscode") String inscode,@Param("teachpermitted") String teachpermitted);

}
