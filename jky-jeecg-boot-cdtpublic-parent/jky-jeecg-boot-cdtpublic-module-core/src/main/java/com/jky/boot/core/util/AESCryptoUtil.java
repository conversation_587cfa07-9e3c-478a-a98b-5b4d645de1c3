package com.jky.boot.core.util;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AESCryptoUtil {
    private final static byte[] ZLB_KEY="8923MGS32A14B13C".getBytes();
    private final static byte[] ZLB_IV="G9812C12E34A1CD3".getBytes();
    private final static AES aes = new AES(Mode.CTR, Padding.ZeroPadding, ZLB_KEY, ZLB_IV);

    public static String encrypt(String encryptString){
        return aes.encryptBase64(encryptString.getBytes());
    }

    public static <T> T decrypt(String decryptString, Class<T> clazz){
        try {
            return JSONObject.parseObject(aes.decryptStr(decryptString),clazz);
        }catch (Exception e){
            log.error("解密失败",e);
        }
        return null;
    }

    public static void main(String[] args) {
        String a="{\"claprice\":\"3658\",\"classname\":\"C1手动挡\",\"classno\":null,\"inscode\":\"3154646751071652\",\"name\":\"绍兴柯桥区安捷汽车驾驶培训有限公司\",\"traintype\":\"C1\",\"insname\":\"绍兴柯桥区安捷汽车驾驶培训有限公司\",\"coachname\":\"\",\"busitype\":0,\"iscrosscity\":\"2\",\"headpicture\":\"\",\"id\":\"1721713505327464449\"}";
        final String en = aes.encryptBase64("1721713505327464449");
        System.out.println("en=========="+en);
//        final StudentInfoSaveDto de = decrypt(a
//                , StudentInfoSaveDto.class);
//        System.out.println("de=========="+de);
        System.out.println(aes.decryptStr("tdyITJuHWM7x/JQqbiVwgy7AqsqqCNyq5d1pyVJm8s4="));
    }
}
