package com.jky.boot.core.module.sxgzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.TMReplaceRecord;
import com.jky.boot.core.module.sxgzpt.entity.TMStuCollection;
import com.jky.boot.core.module.sxgzpt.service.ITMReplaceRecordService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: t_m_replace_record
 * @Author: jeecg-boot
 * @Date: 2023-08-04
 * @Version: V1.0
 */
@Api(tags = "t_m_replace_record")
@RestController
@RequestMapping("/gzpt/tMReplaceRecord")
@Slf4j
public class TMReplaceRecordController extends JeecgController<TMReplaceRecord, ITMReplaceRecordService> {
    @Autowired
    private ITMReplaceRecordService tMReplaceRecordService;

    /**
     * 分页列表查询
     *
     * @param tMReplaceRecord
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "t_m_replace_record-分页列表查询")
    @ApiOperation(value = "t_m_replace_record-分页列表查询", notes = "t_m_replace_record-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<TMReplaceRecord>> queryPageList(TMReplaceRecord tMReplaceRecord,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMReplaceRecord.getInscode());
        tMReplaceRecord.setInscode(null);
        QueryWrapper<TMReplaceRecord> queryWrapper = QueryGenerator.initQueryWrapper(tMReplaceRecord, req.getParameterMap());
        queryWrapper.in("inscode", insCodes);
        queryWrapper.orderByDesc("create_time");
        Page<TMReplaceRecord> page = new Page<TMReplaceRecord>(pageNo, pageSize);
        IPage<TMReplaceRecord> pageList = tMReplaceRecordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param tMReplaceRecord
     * @return
     */
    @AutoLog(value = "t_m_replace_record-添加")
    @ApiOperation(value = "t_m_replace_record-添加", notes = "t_m_replace_record-添加")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_replace_record:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody TMReplaceRecord tMReplaceRecord) {
        tMReplaceRecordService.save(tMReplaceRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param tMReplaceRecord
     * @return
     */
    @AutoLog(value = "t_m_replace_record-编辑")
    @ApiOperation(value = "t_m_replace_record-编辑", notes = "t_m_replace_record-编辑")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_replace_record:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody TMReplaceRecord tMReplaceRecord) {
        tMReplaceRecordService.updateById(tMReplaceRecord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "t_m_replace_record-通过id删除")
    @ApiOperation(value = "t_m_replace_record-通过id删除", notes = "t_m_replace_record-通过id删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_replace_record:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        tMReplaceRecordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "t_m_replace_record-批量删除")
    @ApiOperation(value = "t_m_replace_record-批量删除", notes = "t_m_replace_record-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_replace_record:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.tMReplaceRecordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "t_m_replace_record-通过id查询")
    @ApiOperation(value = "t_m_replace_record-通过id查询", notes = "t_m_replace_record-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<TMReplaceRecord> queryById(@RequestParam(name = "id", required = true) String id) {
        TMReplaceRecord tMReplaceRecord = tMReplaceRecordService.getById(id);
        if (tMReplaceRecord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(tMReplaceRecord);
    }
//
//    /**
//     * 导出excel
//     *
//     * @param request
//     * @param tMReplaceRecord
//     */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_replace_record:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMReplaceRecord tMReplaceRecord) {
//        return super.exportXls(request, tMReplaceRecord, TMReplaceRecord.class, "t_m_replace_record");
//    }
//
//    /**
//     * 通过excel导入数据
//     *
//     * @param request
//     * @param response
//     * @return
//     */
//    //@RequiresPermissions("t_m_replace_record:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMReplaceRecord.class);
//    }

}
