package com.jky.boot.zlb.service.impl;

import com.jky.boot.zlb.entity.TMTransfer;
import com.jky.boot.zlb.mapper.TMTransferMapper;
import com.jky.boot.zlb.service.ITMTransferService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: t_m_transfer
 * @Author: jeecg-boot
 * @Date:   2023-06-19
 * @Version: V1.0
 */
@Service
public class TMTransferServiceImpl extends ServiceImpl<TMTransferMapper, TMTransfer> implements ITMTransferService {

    @Override
    public int updateEntitie(TMTransfer transfer) {
        return baseMapper.updateById(transfer);
    }
}
