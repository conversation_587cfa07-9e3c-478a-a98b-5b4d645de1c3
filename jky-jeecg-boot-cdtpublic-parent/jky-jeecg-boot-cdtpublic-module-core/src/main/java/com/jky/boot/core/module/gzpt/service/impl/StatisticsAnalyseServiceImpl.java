package com.jky.boot.core.module.gzpt.service.impl;

import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.dto.StatisticsSensitiveDTO;
import com.jky.boot.core.module.gzpt.dto.StatisticsYear2YearDTO;
import com.jky.boot.core.module.gzpt.entity.TMMr890chLog;
import com.jky.boot.core.module.gzpt.mapper.StatisticsAnalyseMapper;
import com.jky.boot.core.module.gzpt.mapper.StudentinfoMapper;
import com.jky.boot.core.module.gzpt.service.StatisticsAnalyseService;
import com.jky.boot.core.module.gzpt.vo.StatisticsSensitiveVO;
import com.jky.boot.core.module.gzpt.vo.StatisticsYear2YearVO;
import com.jky.boot.core.module.gzpt.vo.StudentCountVO;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class StatisticsAnalyseServiceImpl implements StatisticsAnalyseService {

    @Autowired
    StatisticsAnalyseMapper statisticsAnalyseMapper;

    @Autowired
    StudentinfoMapper studentinfoMapper;

    @Override
    public List<StatisticsSensitiveVO> analyseSensitive(StatisticsSensitiveDTO statisticsAnalyseDTO) {
        if (StringUtils.isNotBlank(statisticsAnalyseDTO.getCreateTimeEnd())) {
            statisticsAnalyseDTO.setCreateTimeEnd(statisticsAnalyseDTO.getCreateTimeEnd() + " 23:59:59");
        }
        Map<String, StatisticsSensitiveVO> sensitiveVOMap = new HashMap<>();
        List<Map<String, Object>> list = statisticsAnalyseMapper.analyseSensitive(statisticsAnalyseDTO);
        for (Map<String, Object> a : list) {
            StatisticsSensitiveVO vo = sensitiveVOMap.computeIfAbsent(a.get("username").toString(), key -> new StatisticsSensitiveVO());
            vo.setUsername(a.get("username").toString());
            if (Objects.equals(a.get("log_type"), TMMr890chLog.LogTypeEnum.FACE_AUTH.getCode())) {
                vo.setFaceScanCount(Long.valueOf(a.get("total").toString()));
            } else if (Objects.equals(a.get("log_type"), TMMr890chLog.LogTypeEnum.BLACKLIST_FILL.getCode())) {
                vo.setBlacklistCount(Long.valueOf(a.get("total").toString()));
            } else if (Objects.equals(a.get("log_type"), TMMr890chLog.LogTypeEnum.STU_LOGOUT.getCode())) {
                vo.setLogoutCount(Long.valueOf(a.get("total").toString()));
            } else if (Objects.equals(a.get("log_type"), TMMr890chLog.LogTypeEnum.ENABEL.getCode())) {
                vo.setEnableCount(Long.valueOf(a.get("total").toString()));
            } else if (Objects.equals(a.get("log_type"), TMMr890chLog.LogTypeEnum.DISABEL.getCode())) {
                vo.setDisableCount(Long.valueOf(a.get("total").toString()));
            }
        }
        List<StatisticsSensitiveVO> result = new ArrayList<>(sensitiveVOMap.values());
        result = result.stream().peek(a -> a.setTotalCount(a.getFaceScanCount()
                + a.getBlacklistCount() + a.getLogoutCount()+a.getEnableCount()+a.getDisableCount()))
                .sorted(Comparator.comparing(StatisticsSensitiveVO::getTotalCount).reversed())
                .collect(Collectors.toList());
        return result;
    }

    @SneakyThrows
    @Override
    public StatisticsYear2YearVO analyseYear2Year(StatisticsYear2YearDTO statisticsYear2YearDTO) {

        StatisticsYear2YearVO statisticsYear2YearVO = new StatisticsYear2YearVO();
        //x轴
        String point = statisticsYear2YearDTO.getCreateTimeStart();
        List<String> dateList = new ArrayList<>();
        while (true) {
            dateList.add(point);
            if (Objects.equals(point, statisticsYear2YearDTO.getCreateTimeEnd())) {
                break;
            }
            point = DateUtils.parseDateToStr("yyyyMM", DateUtils.addMonths(DateUtils.parseDate(point, "yyyyMM"), 1));
        }

        List<String> inscodeList = JkSecurityUtils.getOrgCodeStartWithPriorIdByDeptId(statisticsYear2YearDTO.getInscode());
        String createTimeStart = statisticsYear2YearDTO.getCreateTimeStart();
        String createTimeEnd = statisticsYear2YearDTO.getCreateTimeEnd();
        //当年
        List<StudentCountVO> nowList = studentinfoMapper.analyseYear2Year(createTimeStart + "01", createTimeEnd + "31", inscodeList);
        Map<String, Long> nowMap = nowList.stream()
                .collect(Collectors.groupingBy(a -> StringUtils.substring(a.getApplydate(), 0, 6), Collectors.summingLong(StudentCountVO::getCount)));
        dateList.forEach(a -> nowMap.computeIfAbsent(a, key -> 0L));
        statisticsYear2YearVO.setNow(new ArrayList<>(new TreeMap<>(nowMap).values()));

        //上年
        createTimeStart = DateUtils.parseDateToStr("yyyyMM", DateUtils.addYears(DateUtils.parseDate(createTimeStart, "yyyyMM"), -1));
        createTimeEnd = DateUtils.parseDateToStr("yyyyMM", DateUtils.addYears(DateUtils.parseDate(createTimeEnd, "yyyyMM"), -1));
        List<StudentCountVO> beforeList = studentinfoMapper.analyseYear2Year(createTimeStart + "01", createTimeEnd + "31", inscodeList);
        Map<String, Long> beforeMap = beforeList.stream()
                .collect(Collectors.groupingBy(a -> StringUtils.substring(a.getApplydate(), 0, 6), Collectors.summingLong(StudentCountVO::getCount)));
        List<String> beforeDateList = dateList.stream()
                .map(a -> DateUtils.parseDateToStr("yyyyMM", DateUtils.addYears(DateUtils.parseDate(a), -1)))
                .collect(Collectors.toList());
        beforeDateList.forEach(a -> beforeMap.computeIfAbsent(a, key -> 0L));
        statisticsYear2YearVO.setBefore(new ArrayList<>(new TreeMap<>(beforeMap).values()));

        statisticsYear2YearVO.setX(dateList);
        return statisticsYear2YearVO;
    }
}
