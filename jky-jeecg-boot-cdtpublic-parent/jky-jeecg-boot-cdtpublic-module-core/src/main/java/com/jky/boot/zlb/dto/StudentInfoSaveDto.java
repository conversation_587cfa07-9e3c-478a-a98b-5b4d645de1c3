package com.jky.boot.zlb.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class StudentInfoSaveDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学员登录浙里办后保存浙里办信息的id")
    private String id;

    @NotBlank(message="培训机构编号不能为空")
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;
    /**业务类型  0:初领 1:增领9:其他*/
    @ApiModelProperty(value = "业务类型 0:初领 1:增领 9:其他")
    private String busitype;
    /**培训车型 	下列编码单选：
     A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
    @Excel(name = "培训车型", width = 15)
    @ApiModelProperty(value = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private String traintype;


    @NotBlank(message="课程编号不能为空")
    @Excel(name = "课程编号", width = 15)
    @ApiModelProperty(value = "课程编号")
    private String classno;
    /**驾校名称*/
    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
    /**学员统一编号 推送全国平台/计时平台获取*/
//    @Excel(name = "学员统一编号", width = 15)
//    @ApiModelProperty(value = "学员统一编号")
//    private String stunum;
    /**冻结金额*/
    @ApiModelProperty(value = "金额")
    private String amount;
    /**教练编号*/
    @ApiModelProperty(value = "教练编号")
    private String coachnum;

    /**教练姓名*/
    @ApiModelProperty(value = "教练姓名")
    private String coachname;
    /**教练姓名*/
    @ApiModelProperty(value = "头像")
    private String headpicture;
    /**教练姓名*/
    @ApiModelProperty(value = "地址")
    private String homeaddress;
    /**驾驶证号（增领时传）*/
    private String drilicnum;

    /**
     * 是否为跨地市转校 1:是  2:否
     */
    private String iscrosscity;

    /**
     * 外地转入前所在地市
     */
    private String fromarea;

    /**
     * 跨地市转校学员学习类型（0-非跨地市学员默认值，1-全学，2-只学科目二，3-只学科目三）
     */
    private Integer crossType;
}
