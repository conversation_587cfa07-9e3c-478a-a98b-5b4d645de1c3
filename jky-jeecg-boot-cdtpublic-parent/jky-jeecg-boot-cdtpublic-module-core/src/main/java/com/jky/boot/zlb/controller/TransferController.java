package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.core.module.gzpt.entity.Coach;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.ICoachService;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.service.ITransferCarTypeService;
import com.jky.boot.zlb.entity.TDCity;
import com.jky.boot.zlb.entity.TMCoachTransfer;
import com.jky.boot.zlb.entity.TMTransfer;
import com.jky.boot.zlb.entity.ZzTransferInscode;
import com.jky.boot.zlb.service.ITDCityService;
import com.jky.boot.zlb.service.ITMCoachTransferService;
import com.jky.boot.zlb.service.ITMStudentinfoLogoutService;
import com.jky.boot.zlb.service.ITMTransferService;
import com.jky.jdata.common.module.R;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 *学员转校
 *<pre>
 * 学员转校    /student/transfer
 * 教练员转校    /coach/transfer
 * 学员跨地市转校 /student/area/transfer
 * 学员车型变更申请 /stu/carEdit
 * </pre>
 */
@RestController
@RequestMapping("/zlb")
@Slf4j
public class TransferController {
    @Autowired
    private IInstitutionService iInstitutionService;
    @Autowired
    private IStudentinfoService iStudentinfoService;
    @Autowired
    private ITMTransferService itmTransferService;
    @Autowired
    private ICoachService coachService;
    @Autowired
    private ITMCoachTransferService itmCoachTransferService;
    @Autowired
    private ITMStudentinfoLogoutService itmStudentinfoLogoutService;
    @Autowired
    private ITDCityService itdCityService;

    @Autowired
    private ITransferCarTypeService transferCarTypeService;

    @Autowired
    private RedissonLockClient redissonLock;

    /**
     * 学员转校 (学员跨培训机构备案)
     *
     * @param tmTransfer
     * @return
     */
    @AutoLog(value = "学员转校")
    @ApiOperation(value = "学员转校", notes = "学员转校")
    @PostMapping(value = "/student/transfer")
    public Result<String> studentTransfer(@RequestBody TMTransfer tmTransfer) {
        if (StringUtils.isBlank(tmTransfer.getInscode())) {
            return Result.error("新培训机构编号不能为空");
        }
        if (tmTransfer.getStunum() == null) {
            return Result.error("学员编号不能为空");
        }
        Institution institution = this.iInstitutionService.getOne(
                Wrappers.<Institution>lambdaQuery()
                        .eq(Institution::getInscode,tmTransfer.getInscode()));
        if (institution == null) {
            return Result.error("培训机构未备案");
        }
        Studentinfo student = this.iStudentinfoService.getOne(
                Wrappers.<Studentinfo>lambdaQuery().eq(Studentinfo::getStunum,tmTransfer.getStunum())
        );
        if (student == null) {
            return Result.error("学员未备案");
        }
        if (student.getInscode().equals(institution.getInscode())) {
            return Result.error(institution.getName() + "学员已备案");
        }
        QueryWrapper<TMTransfer> qc = new QueryWrapper<>();
        qc.lambda().eq(TMTransfer::getStunum, tmTransfer.getStunum());
        qc.lambda().eq(TMTransfer::getStatus, 0);
        List<TMTransfer> list = this.itmTransferService.list(qc);
        //如果存在转校记录
        if (list != null && list.size() > 0) {
            TMTransfer transfer = list.get(0);
            transfer.setInscode(institution.getInscode());
            transfer.setInsname(institution.getName());
            transfer.setStatus(0);
            transfer.setCrdate(new Date());
//            transfer.setPlatfrom();//备案平台
            transfer.setDistrict(institution.getDistrict());
            this.itmTransferService.updateEntitie(transfer);
        } else {
            TMTransfer transfer = new TMTransfer();
            transfer.setStunum(student.getStunum());
            transfer.setName(student.getName());
            transfer.setIdcard(student.getIdcard());
            transfer.setOldInscode(student.getInscode());
            Institution oldInstitution = this.iInstitutionService.getOne(
                    Wrappers.<Institution>lambdaQuery()
                            .eq(Institution::getInscode,tmTransfer.getInscode()));
            transfer.setOldInsname(oldInstitution.getName());
            transfer.setInscode(institution.getInscode());
            transfer.setInsname(institution.getName());
            transfer.setStatus(0);
            transfer.setCrdate(new Date());
//            transfer.setPlatfrom();//备案平台
            transfer.setDistrict(institution.getDistrict());
            this.itmTransferService.save(transfer);
        }
        return Result.OK("成功！");
    }


    /**
     * 教练员跨培训机构备案
     *
     * @param
     * @param tmCoachTransfer
     * @return
     */
    @AutoLog(value = "教练员转校")
    @ApiOperation(value = "教练员转校", notes = "教练员转校")
    @PostMapping(value = "/coach/transfer")
    public Result<String> coachTransfer(@RequestBody TMCoachTransfer tmCoachTransfer) {
        if (StringUtils.isEmpty(tmCoachTransfer.getInscode())) {
            return Result.error("新培训机构编号不能为空");
        }
        if (StringUtils.isEmpty(tmCoachTransfer.getCoachnum())) {
            return Result.error("教练员编号不能为空");
        }
        Institution institution = iInstitutionService.getOne(
                Wrappers.<Institution>lambdaQuery()
                        .eq(Institution::getInscode,tmCoachTransfer.getInscode()));
        if (institution == null) {
            return Result.error("培训机构未备案");
        }
        Coach coach = coachService.getOne(
                Wrappers.<Coach>lambdaQuery()
                        .eq(Coach::getCoachnum,tmCoachTransfer.getCoachnum()));
        if (coach == null || coach.getStatus() == 0) {
            return Result.error("教练员未备案");
        }
        if (coach.getInscode().equals(institution.getInscode())) {
            return Result.error(institution.getName() + "教练员已备案");
        }
        List<TMCoachTransfer> list = itmCoachTransferService.list(
                Wrappers.<TMCoachTransfer>lambdaQuery()
                        .eq(TMCoachTransfer::getCoachnum, tmCoachTransfer.getCoachnum()).eq(TMCoachTransfer::getStatus, 0)
        );
        if (list != null && list.size() > 0) {
            TMCoachTransfer transfer = list.get(0);
            transfer.setInscode(institution.getInscode());
            transfer.setInsname(institution.getName());
            transfer.setStatus(0);
            transfer.setCrdate(new Date());
//            transfer.setPlatfrom();//备案平台
            transfer.setDistrict(institution.getDistrict());
            itmCoachTransferService.updateEntitie(transfer);
        } else {
            TMCoachTransfer transfer = new TMCoachTransfer();
            transfer.setCoachnum(coach.getCoachnum());
            transfer.setName(coach.getName());
            transfer.setIdcard(coach.getIdcard());
            transfer.setOldInscode(coach.getInscode());
            Institution oldInstitution = iInstitutionService.getOne(
                    Wrappers.<Institution>lambdaQuery()
                            .eq(Institution::getInscode,tmCoachTransfer.getInscode()));
            transfer.setOldInsname(oldInstitution.getName());
            transfer.setInscode(institution.getInscode());
            transfer.setInsname(institution.getName());
            transfer.setStatus(0);
            transfer.setCrdate(new Date());
//            transfer.setPlatfrom();//备案平台
            transfer.setDistrict(institution.getDistrict());
            itmCoachTransferService.save(transfer);
        }
        return Result.OK("成功！");
    }

    /**
     * 学员跨地市转校
     *
     * @param
     * @param zzTransferInscode
     * @return
     */
    @AutoLog(value = "学员跨地市转校")
    @ApiOperation(value = "学员跨地市转校", notes = "学员跨地市转校")
    @PostMapping(value = "/student/area/transfer")
    public Result<String> studentAreaTransfer(@RequestBody ZzTransferInscode zzTransferInscode) {
        Studentinfo student = iStudentinfoService.getOne(
                Wrappers.<Studentinfo>lambdaQuery()
                        .eq(Studentinfo::getStunum,zzTransferInscode.getStunum()));
        Institution institution = iInstitutionService.getOne(
                Wrappers.<Institution>lambdaQuery()
                        .eq(Institution::getInscode,student.getInscode()));
        if (student.getFromarea() != null && student.getFromarea().length() > 0) {
            Studentinfo info = iStudentinfoService.getById(
                    Wrappers.<Studentinfo>lambdaQuery()
                            .eq(Studentinfo::getStunum,zzTransferInscode.getStunum()));
            if (info != null) {
                if (!student.getIdcard().equals(info.getIdcard())) {
                    return Result.error("学员编号重复，已存在身份证为【"+info.getIdcard()+"】的学员信息记录。");
                }
            }

            long count = itdCityService.count(Wrappers.<TDCity>lambdaQuery().like(TDCity::getDcName,student.getFromarea()));
            if (count > 0) {
                return Result.error("本地市学员不能进行跨地市转校操作");
            } else {
                iStudentinfoService.saveTransfer(student, institution.getName());
            }
        }
        return Result.OK("成功！");
    }

    /**
     * 修改车型
     *
     * @param studentinfo
     * @return
     */
    @AutoLog(value = "学员信息-修改车型")
    @ApiOperation(value = "学员信息-修改车型", notes = "学员信息-修改车型")
    @PostMapping(value = "/stu/carEdit")
    public Result carEdit(@RequestBody Studentinfo studentinfo) {
        String key = "stucarEdit-" + studentinfo.getId();
        if (redissonLock.existKey(key)) {
            return Result.error("学员转车型申请提交中，请稍后再试！！");
        }
        try {
            if (redissonLock.tryLock(key, -1, 10000)) {
                return transferCarTypeService.saveCarType(studentinfo);
            }
        } catch (Exception e) {
            log.error("提交失败",e);
            return Result.error("提交失败");
        } finally {
            redissonLock.unlock(key);
        }
        return  Result.OK("提交失败!");
    }




}

