package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.core.module.gzpt.dto.ResultDto;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.core.module.gzpt.vo.RecordVO;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.common.util.StringUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: t_m_class_record_detail
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Api(tags = "学时管理对象")
@RestController
@RequestMapping("/gzpt/classRecordDetail")
@Slf4j
public class ClassRecordDetailController extends JeecgController<ClassRecordDetail, IClassRecordDetailService> {
    @Autowired
    private IClassRecordDetailService classRecordDetailService;
    @Autowired
    private IInstitutionService InsService;
    @Autowired
    private IPlatformService platformService;
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private ITotalTimeService totalTimeService;
    @Autowired
    private IStageTrainningTimeService stageTrainingTimeService;

    /**
     * 分页列表查询
     *
     * @param classRecordDetail
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
//	@RequiresPermissions("gzpt:detail:list")
    @AutoLog(value = "学时管理-分页列表查询")
    @ApiOperation(value = "学时管理-分页列表查询", notes = "学时管理-分页列表查询")
    @DesensitizedAnno
    @GetMapping(value = "/list")
    public Result<IPage<ClassRecordDetail>> queryPageList(ClassRecordDetail classRecordDetail,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(classRecordDetail.getInscode(), classRecordDetail.getInscodes());
        classRecordDetail.setInscode(null);

        //查询时间区间
        String starttime_begin = classRecordDetail.getStarttime();
        String starttime_end = classRecordDetail.getEndtime();
        classRecordDetail.setStarttime(null);
        classRecordDetail.setEndtime(null);
        QueryWrapper<ClassRecordDetail> queryWrapper = QueryGenerator.initQueryWrapper(classRecordDetail, req.getParameterMap());
        queryWrapper.setEntityClass(ClassRecordDetail.class);
        queryWrapper.in("inscode", inscode);
        if (classRecordDetail != null) {
            if (StringUtils.isNotBlank(classRecordDetail.getKm())) {
                queryWrapper.likeRight("subjcode", "___" + classRecordDetail.getKm());
            }
            if (StringUtils.isNotBlank(classRecordDetail.getType())) {
                queryWrapper.likeRight("subjcode", classRecordDetail.getType());
            }
            if (StringUtils.isNotBlank(starttime_begin)) {
                queryWrapper.ge("starttime", starttime_begin.replace("-", "") + "000000");
            }
            if (StringUtils.isNotBlank(starttime_end)) {
                queryWrapper.le("starttime", starttime_end.replace("-", "") + "235959");
            }
        }
        if (StringUtils.isNotBlank(classRecordDetail.getStuname()) || StringUtils.isNotBlank(classRecordDetail.getStuidcard())) {
            LambdaQueryWrapper<Studentinfo> eq = Wrappers.<Studentinfo>lambdaQuery(Studentinfo.class)
                    .eq(StringUtils.isNotBlank(classRecordDetail.getStuname()), Studentinfo::getName, classRecordDetail.getStuname())
                    .eq(StringUtils.isNotBlank(classRecordDetail.getStuidcard()), Studentinfo::getIdcard, classRecordDetail.getStuidcard())
                    .last("limit 1");
            List<Studentinfo> list = studentinfoService.list(eq);
            if (CollectionUtils.isEmpty(list)) {
                return Result.OK(new Page<>());
            }
            queryWrapper.eq("stunum", list.get(0).getStunum());
        }
        queryWrapper.orderByDesc("starttime");
        Page<ClassRecordDetail> page = new Page<ClassRecordDetail>(pageNo, pageSize);
        IPage<ClassRecordDetail> pageList = classRecordDetailService.page(page, queryWrapper);

        for (ClassRecordDetail detail : pageList.getRecords()) {
            detail.setKm(detail.getSubjcode().substring(3, 4));
            detail.setType(detail.getSubjcode().substring(0, 1));
        }

        // 填充车型
        if (!pageList.getRecords().isEmpty()) {
            List<ClassRecordDetail> origin = pageList.getRecords();
            List<String> stuNums = pageList.getRecords().stream().map(ClassRecordDetail::getStunum).distinct().collect(Collectors.toList());
            List<Studentinfo> students = studentinfoService.list(
                    Wrappers.lambdaQuery(Studentinfo.class)
                            .select(Studentinfo::getTraintype, Studentinfo::getStunum)
                            .in(Studentinfo::getStunum, stuNums)
            );
            Map<String, String> map = students.stream()
                    .collect(Collectors.toMap(Studentinfo::getStunum, Studentinfo::getTraintype));
            origin.forEach(e -> e.setTrainCarType(map.get(e.getStunum())));
        }
        return Result.OK(pageList);
    }

    @ApiOperation(value = "风险预警表-分页列表查询-人员相关预警", notes = "风险预警表-分页列表查询-人员相关预警")
    @GetMapping(value = "/selectkc")
    public Result<?> selectkc(ClassRecordDetail classRecordDetail) {

        List<ClassRecordDetail> warnMessage1;

        warnMessage1 = classRecordDetailService.selectkc(classRecordDetail.getSubjcode(), classRecordDetail.getId(), classRecordDetail.getType());
        List<ClassRecordDetail> warnMessage2 = classRecordDetailService.selectyc(classRecordDetail.getSubjcode(), classRecordDetail.getId(), classRecordDetail.getType());
        List<ClassRecordDetail> warnMessage3 = classRecordDetailService.selectsc(classRecordDetail.getSubjcode(), classRecordDetail.getId(), classRecordDetail.getType());
        List<ClassRecordDetail> warnMessage4 = classRecordDetailService.selectmn(classRecordDetail.getSubjcode(), classRecordDetail.getId(), classRecordDetail.getType());
        if (CollectionUtils.isEmpty(warnMessage1) && CollectionUtils.isEmpty(warnMessage2) && CollectionUtils.isEmpty(warnMessage3) && CollectionUtils.isEmpty(warnMessage4)) {
            return Result.error("未找到对应数据");
        }

        List<RecordVO> newList1 = new ArrayList<>();
        for (ClassRecordDetail c :
                warnMessage1) {
            RecordVO entity = new RecordVO();
            BeanUtils.copyProperties(c, entity);
            newList1.add(entity);
        }

        //如果warnMessage2不空
        if (CollectionUtils.isNotEmpty(warnMessage2)) {
            for (ClassRecordDetail o : warnMessage2) {
                RecordVO entity = new RecordVO();
                BeanUtils.copyProperties(o, entity);
                newList1.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(warnMessage3)) {
            for (ClassRecordDetail o : warnMessage3) {
                RecordVO entity = new RecordVO();
                BeanUtils.copyProperties(o, entity);
                newList1.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(warnMessage4)) {
            for (ClassRecordDetail o : warnMessage4) {
                RecordVO entity = new RecordVO();
                BeanUtils.copyProperties(o, entity);
                newList1.add(entity);
            }
        }


//		 if (CollectionUtils.isEmpty(warnMessage.getRecords())){
//		 	return Result.error("未找到对应数据");
//		 }
        return Result.OK(newList1);
    }

//	 @ApiOperation(value="风险预警表-分页列表查询-人员相关预警", notes="风险预警表-分页列表查询-人员相关预警")
//	 @GetMapping(value = "/selectyc")
//	 public Result<?> selectyc(ClassRecordDetail classRecordDetail,
//							   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//							   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//							   HttpServletRequest req) {
//
//		 Page<ClassRecordDetail> page = new Page<ClassRecordDetail>(pageNo, pageSize);
//		 IPage<ClassRecordDetail> warnMessage  = classRecordDetailService.selectyc(page,classRecordDetail.getSubjcode(),classRecordDetail.getId(),classRecordDetail.getType());
//		 if (CollectionUtils.isEmpty(warnMessage.getRecords())){
//			 return Result.error("未找到对应数据");
//		 }
//		 return Result.OK(warnMessage);
//	 }

//	 @ApiOperation(value="风险预警表-分页列表查询-人员相关预警", notes="风险预警表-分页列表查询-人员相关预警")
//	 @GetMapping(value = "/selectmn")
//	 public Result<?> selectmn(ClassRecordDetail classRecordDetail,
//							   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//							   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//							   HttpServletRequest req) {
//
//		 Page<ClassRecordDetail> page = new Page<ClassRecordDetail>(pageNo, pageSize);
//		 IPage<ClassRecordDetail> warnMessage  = classRecordDetailService.selectmn(page,classRecordDetail.getSubjcode(),classRecordDetail.getId(),classRecordDetail.getType());
//		 if (CollectionUtils.isEmpty(warnMessage.getRecords())){
//			 return Result.error("未找到对应数据");
//		 }
//		 return Result.OK(warnMessage);
//	 }

//	 @ApiOperation(value="风险预警表-分页列表查询-人员相关预警", notes="风险预警表-分页列表查询-人员相关预警")
//	 @GetMapping(value = "/selectsc")
//	 public Result<?> selectsc(ClassRecordDetail classRecordDetail,
//							   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
//							   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
//							   HttpServletRequest req) {
//
//		 Page<ClassRecordDetail> page = new Page<ClassRecordDetail>(pageNo, pageSize);
//		 IPage<ClassRecordDetail> warnMessage  = classRecordDetailService.selectsc(page,classRecordDetail.getSubjcode(),classRecordDetail.getId(),classRecordDetail.getType());
//		 if (CollectionUtils.isEmpty(warnMessage.getRecords())){
//			 return Result.error("未找到对应数据");
//		 }
//		 return Result.OK(warnMessage);
//	 }


    @ApiOperation(value = "风险预警表-分页列表查询-人员相关预警", notes = "风险预警表-分页列表查询-人员相关预警")
    @GetMapping(value = "/selectqb")
    public Result<?> selectqb(ClassRecordDetail classRecordDetail,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<ClassRecordDetail> page = new Page<>(pageNo, pageSize);
        IPage<ClassRecordDetail> warnMessage = classRecordDetailService.selectqb(page, classRecordDetail.getSubjcode(), classRecordDetail.getId(), classRecordDetail.getType());
        if (CollectionUtils.isEmpty(warnMessage.getRecords())) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(warnMessage);
    }

    /**
     * 添加
     *
     * @param classRecordDetail
     * @return
     */
    @AutoLog(value = "学时管理数据-添加")
    @ApiOperation(value = "学时管理数据-添加", notes = "学时管理数据-添加")
//	@RequiresPermissions("gzpt:detail:add")
    @PostMapping(value = "/add")
    public ResultDto add(@RequestBody ClassRecordDetail classRecordDetail) {
//		log.info("[监管端推送-学时管理数据],{}",classRecordDetail);
        int i = classRecordDetailService.insertClassRecordDetail(classRecordDetail);
        ResultDto resultDto = new ResultDto();
        if (i == 1) {
            resultDto.setMsg("添加成功");
            resultDto.setCode("200");
        } else if (i == 2) {
            resultDto.setMsg("添加失败");
            resultDto.setCode("1");
        } else if (i == 3) {
            resultDto.setMsg("当前学员不存在");
            resultDto.setCode("3");
        } else if (i == 4) {
            resultDto.setMsg("当前电子教学日志已存在");
            resultDto.setCode("4");
        }

        // 修改 totalTime 和 stage 的驾校
        Studentinfo stu = studentinfoService.getOneByStuNum(classRecordDetail.getStunum());
        LambdaUpdateWrapper<TotalTime> totalWrapper = Wrappers.lambdaUpdate(TotalTime.class);
        totalWrapper.set(TotalTime::getInsname, stu.getInsname());
        totalWrapper.set(TotalTime::getInscode, stu.getInscode());
        totalWrapper.eq(TotalTime::getStunum, stu.getStunum());
        totalTimeService.update(totalWrapper);

        LambdaUpdateWrapper<StageTrainningTime> stageWrapper = Wrappers.lambdaUpdate(StageTrainningTime.class);
        stageWrapper.set(StageTrainningTime::getInscode, stu.getInscode());
        stageWrapper.set(StageTrainningTime::getInsname, stu.getInsname());
        stageWrapper.eq(StageTrainningTime::getStunum, stu.getStunum());
        stageWrapper.ne(StageTrainningTime::getAuditstate, 1);
        stageTrainingTimeService.update(stageWrapper);
        return resultDto;
    }

    /**
     * 编辑
     *
     * @param classRecordDetail
     * @return
     */
    @AutoLog(value = "学时管理数据-编辑")
    @ApiOperation(value = "学时管理数据-编辑", notes = "学时管理数据-编辑")
    @RequiresPermissions("gzpt:detail:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ClassRecordDetail classRecordDetail) {
        classRecordDetailService.updateClassRecordDetail(classRecordDetail);
        return Result.OK("编辑成功!");
    }


    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学时管理数据-批量删除")
    @ApiOperation(value = "学时管理数据-批量删除", notes = "学时管理数据-批量删除")
    @RequiresPermissions("gzpt:detail:remove")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.classRecordDetailService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学时管理数据-通过id删除")
    @ApiOperation(value = "学时管理数据-通过id删除", notes = "学时管理数据-通过id删除")
    @RequiresPermissions("gzpt:detail:remove")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        classRecordDetailService.removeById(id);
        return Result.OK("删除成功!");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学时管理数据-通过id查询")
    @ApiOperation(value = "学时管理数据-通过id查询", notes = "学时管理数据-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ClassRecordDetail> queryById(@RequestParam(name = "id", required = true) String id) {
        ClassRecordDetail classRecordDetail = classRecordDetailService.getById(id);
        if (classRecordDetail == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(classRecordDetail);
    }

//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param classRecordDetail
//    */
//	@AutoLog(value = "导出选中学时管理数据-导出", operateType = CommonConstant.OPERATE_TYPE_6)
//	@ApiOperation(value="导出选中学时管理数据-导出", notes="导出选中学时管理数据-导出")
//	@RequiresPermissions("gzpt:detail:export")
//	@RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, ClassRecordDetail classRecordDetail) {
//		//查询时间区间
//		String starttime_begin = classRecordDetail.getStarttime();
//		String starttime_end = classRecordDetail.getEndtime();
//		classRecordDetail.setStarttime(null);
//		classRecordDetail.setEndtime(null);
//
//		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(classRecordDetail.getInscode(),classRecordDetail.getInscodes());
//		classRecordDetail.setInscode(null);
//		QueryWrapper<ClassRecordDetail> queryWrapper = QueryGenerator.initQueryWrapper(classRecordDetail, request.getParameterMap());
//		queryWrapper.in("inscode",inscode);
//		if(classRecordDetail != null){
//			if(StringUtils.isNotBlank(classRecordDetail.getKm())){
//				queryWrapper.likeRight("subjcode","___"+classRecordDetail.getKm());
//			}
//			if(StringUtils.isNotBlank(classRecordDetail.getType())){
//				queryWrapper.likeRight("subjcode",classRecordDetail.getType());
//			}
//			if(StringUtils.isNotBlank(starttime_begin)){
//				queryWrapper.ge("starttime",starttime_begin.replace("-","")+"000000");
//			}
//			if(StringUtils.isNotBlank(starttime_end)){
//				queryWrapper.le("starttime",starttime_end.replace("-","")+"235959");
//			}
//		}
//		List<ClassRecordDetail> list = classRecordDetailService.list(queryWrapper);
//		for (ClassRecordDetail detail : list) {
//			detail.setKm(detail.getSubjcode().substring(3, 4));
//			detail.setType(detail.getSubjcode().substring(0, 1));
//		}
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject(NormalExcelConstants.FILE_NAME, "学时管理数据");
//		mv.addObject(NormalExcelConstants.CLASS, ClassRecordDetail.class);
//		mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学时管理数据", "导出人:" + JkSecurityUtils.getRealname(), "学时管理数据"));
//		mv.addObject(NormalExcelConstants.DATA_LIST, list);
//		return mv;
//    }
//
//
//	 /**
//	  * 导出excel
//	  *
//	  * @param request
//	  * @param classRecordDetail
//	  */
//	 @AutoLog(value = "导出选中学时管理数据-勾选导出", operateType = CommonConstant.OPERATE_TYPE_6)
//	 @ApiOperation(value="导出选中学时管理数据-勾选导出", notes="导出选中学时管理数据-勾选导出")
//	 @RequiresPermissions("gzpt:detail:export")
//	 @RequestMapping(value = "/exportByIds")
//	 public ModelAndView exportByIds(HttpServletRequest request, ClassRecordDetail classRecordDetail) {
//		 //查询时间区间
//		 String starttime_begin = classRecordDetail.getStarttime();
//		 String starttime_end = classRecordDetail.getEndtime();
//		 classRecordDetail.setStarttime(null);
//		 classRecordDetail.setEndtime(null);
//
//		 List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(classRecordDetail.getInscode(),classRecordDetail.getInscodes());
//		 classRecordDetail.setInscode(null);
//		 QueryWrapper<ClassRecordDetail> queryWrapper = QueryGenerator.initQueryWrapper(classRecordDetail, request.getParameterMap());
//		 queryWrapper.in("inscode",inscode);
//		 if(classRecordDetail != null){
//			 if(StringUtils.isNotBlank(classRecordDetail.getKm())){
//				 queryWrapper.likeRight("subjcode","___"+classRecordDetail.getKm());
//			 }
//			 if(StringUtils.isNotBlank(classRecordDetail.getType())){
//				 queryWrapper.likeRight("subjcode",classRecordDetail.getType());
//			 }
//			 if(StringUtils.isNotBlank(starttime_begin)){
//				 queryWrapper.ge("starttime",starttime_begin.replace("-","")+"000000");
//			 }
//			 if(StringUtils.isNotBlank(starttime_end)){
//				 queryWrapper.le("starttime",starttime_end.replace("-","")+"235959");
//			 }
//		 }
//
//		 // 过滤选中数据
//		 List<ClassRecordDetail> list = null;
//		 String selections = request.getParameter("selections");
//		 if (org.apache.commons.lang3.StringUtils.isNotBlank(selections)) {
//			 queryWrapper.in("id", Arrays.asList(selections.split(",")));
//			 list = classRecordDetailService.list(queryWrapper);
//		 }
//
//		 for (ClassRecordDetail detail : list) {
//			 detail.setKm(detail.getSubjcode().substring(3, 4));
//			 detail.setType(detail.getSubjcode().substring(0, 1));
//		 }
//
//		 ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		 mv.addObject(NormalExcelConstants.FILE_NAME, "学时管理数据");
//		 mv.addObject(NormalExcelConstants.CLASS, ClassRecordDetail.class);
//		 mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学时管理数据", "导出人:" + JkSecurityUtils.getRealname(), "学时管理数据"));
//		 mv.addObject(NormalExcelConstants.DATA_LIST, list);
//		 return mv;
//	 }


    /**
     * 推送计时
     *
     * @param id
     * @return
     */
    @RequiresPermissions("gzpt:detail:PushJs")
    @AutoLog(value = "电子教学日志推送计时")
    @ApiOperation(value = "电子教学日志推送计时", notes = "电子教学日志推送计时")
    @GetMapping(value = "pushDetailToJs/{id}")
    public Result<?> pushDetailToJs(@PathVariable String id) {
        if (StringUtils.isBlank(id)) {
            return Result.error("id参数不能为空");
        }
        ClassRecordDetail detail = classRecordDetailService.selectClassRecordDetailById(id);
        if (detail == null) {
            return Result.error("未找到相关的电子教学日志");
        }
        if (StringUtils.isBlank(detail.getStunum())) {
            return Result.error("该条教学日志没有所属的学员编号");
        }
        if (StringUtils.isBlank(detail.getPlatnum())) {
            Institution ins = InsService.getByInscode(detail.getInscode());
            if (ins == null) {
                return Result.error("未找到该条电子教学日志所属的驾校");
            }
            detail.setPlatnum(ins.getPlatform());
        } else {
            if (detail.getPlatnum().length() <= 5) {
                Map<String, String> platformMap = new HashMap<>();
                platformMap.put("A0002", "163AB4F0E6C");
                platformMap.put("A0016", "157C1C2BAF4");
                platformMap.put("A0188", "882076CA688");
                platformMap.put("A0010", "15886BD9C39");
                String platnum = platformMap.get(detail.getPlatnum());
                if (StringUtils.isBlank(platnum)) {
                    return Result.error("未找到该条电子教学日志相关的计时厂商");
                }
                detail.setPlatnum(platnum);
            }
        }
        Platform platform = platformService.getByPlatformSerialNumber(detail.getPlatnum());
        if (platform == null) {
            return Result.error("未找到该条电子教学日志相关的计时厂商");
        }
        String result = "";
        detail.setPlatnum(platform.getPlatnum());
        try {
            CommonResponse push = PushJsUtil.push(detail, platform.getApi() + "gzdetail");
            result = push.getMessage();
            if (push.getErrorcode() == 0) {
                detail.setIstojs(1);
                classRecordDetailService.updateById(detail);
                return Result.OK(result);
            }
        } catch (Exception e) {
            e.printStackTrace();
            detail.setIstojs(2);
            classRecordDetailService.updateById(detail);
            return Result.error("推送时出现了异常" + e);
        }
        return Result.error(result);
    }

    @RequiresPermissions("gzpt:detail:BatchPushJs")
    @AutoLog(value = "电子教学日志批量推送计时")
    @ApiOperation(value = "电子教学日志批量推送计时", notes = "电子教学日志批量推送计时")
    @GetMapping(value = "batchPushDetailToJs/{id}")
    public Result<?> batchPushDetailToJs(@PathVariable String id) {
        if (StringUtils.isBlank(id)) {
            return Result.error("id参数不能为空");
        }
        String[] ids = id.split(",");
        List<String> errorMsgList = new ArrayList<>();
        List<String> successMsg = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            ClassRecordDetail detail = classRecordDetailService.selectClassRecordDetailById(ids[i]);
            if (detail == null) {
                errorMsgList.add("未找到相关的电子教学日志");
                continue;
            }
            if (StringUtils.isBlank(detail.getStunum())) {
                errorMsgList.add("该条电子教学日志[" + detail.getRecnum() + "]没有所属的学员编号");
                continue;
            }
            if (StringUtils.isBlank(detail.getPlatnum())) {
                Institution ins = InsService.getByInscode(detail.getInscode());
                if (ins == null) {
                    errorMsgList.add("未找到该条电子教学日志[" + detail.getRecnum() + "]所属的驾校");
                    continue;
                }
                detail.setPlatnum(ins.getPlatform());
            } else {
                if (detail.getPlatnum().length() <= 5) {
                    Map<String, String> platformMap = new HashMap<>();
                    platformMap.put("A0002", "163AB4F0E6C");
                    platformMap.put("A0016", "157C1C2BAF4");
                    platformMap.put("A0188", "882076CA688");
                    platformMap.put("A0010", "15886BD9C39");
                    String platnum = platformMap.get(detail.getPlatnum());
                    if (StringUtils.isBlank(platnum)) {
                        return Result.error("未找到该条电子教学日志相关的计时厂商");
                    }
                    detail.setPlatnum(platnum);
                }
            }
            Platform platform = platformService.getByPlatformSerialNumber(detail.getPlatnum());
            if (platform == null) {
                errorMsgList.add("未找到该条电子教学日志[" + detail.getRecnum() + "]相关的计时厂商");
                continue;
            }
            String result = "";
            try {
                CommonResponse push = PushJsUtil.push(detail, platform.getApi() + "gzdetail");
                result = push.getMessage();
                if (push.getErrorcode() == 0) {
                    detail.setIstojs(1);
                    classRecordDetailService.updateById(detail);
                    successMsg.add(result);
                } else {
                    // 推送失败
                    detail.setIstojs(2);
                    classRecordDetailService.updateById(detail);
                    errorMsgList.add(result);
                }
            } catch (Exception e) {
                errorMsgList.add("出异常了:" + e);
                e.printStackTrace();
            }
        }

        if (errorMsgList.isEmpty()) {
            StringBuilder successBuilder = new StringBuilder();
            for (String success : successMsg) {
                successBuilder.append(success);
            }
            return Result.OK(successBuilder.toString());
        } else {
            StringBuilder errorBuilder = new StringBuilder();
            for (String error : errorMsgList) {
                errorBuilder.append(error);
            }
            return Result.error(errorBuilder.toString());
        }

    }

    /**
     * 加密接口
     */
    @GetMapping("/encrypt")
    public Result<?> encryptData() {
        QueryWrapper<ClassRecordDetail> wrapper = new QueryWrapper<>();
        wrapper.setEntityClass(ClassRecordDetail.class);
        wrapper.eq("LENGTH(stuidcard)", 18);
        wrapper.last("limit 200");
        List<ClassRecordDetail> list = classRecordDetailService.list(wrapper);
        classRecordDetailService.updateBatchById(list);
        return Result.ok();
    }
}
