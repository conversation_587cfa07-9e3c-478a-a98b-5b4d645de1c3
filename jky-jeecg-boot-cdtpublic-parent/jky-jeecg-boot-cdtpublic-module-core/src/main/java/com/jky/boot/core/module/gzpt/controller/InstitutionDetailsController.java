package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.InsDetailsEdit;
import com.jky.boot.base.service.IInsDetailsEditService;
import com.jky.boot.common.beanCopy.BeanConvertUtils;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.InstitutionDetails;
import com.jky.boot.core.module.gzpt.service.IInstitutionDetailsService;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.utils.SearchHttpAK;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 培训机构附加信息对象
 */
@Api(tags = "培训机构附加信息对象")
@RestController
@RequestMapping("/gzpt/institutionDetails")
@Slf4j
public class InstitutionDetailsController extends JeecgController<InstitutionDetails, IInstitutionDetailsService> {
    @Autowired
    private IInstitutionDetailsService institutionDetailsService;
    @Autowired
    private JkySysDepartService sysDeptService;

    @Autowired
    private JkySysDepartService jkySysDepartService;

    @Autowired
    private IInstitutionService institutionService;

    @Autowired
    private IInsDetailsEditService insDetailsEditService;

    @Autowired
    private OssZWYUtils ossZWYUtils;

    /**
     * 分页列表查询
     *
     * @param institutionDetails
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "培训机构附加信息对象-分页列表查询")
    @ApiOperation(value = "培训机构附加信息对象-分页列表查询", notes = "培训机构附加信息对象-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InstitutionDetails>> queryPageList(InstitutionDetails institutionDetails,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                           HttpServletRequest req) {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(institutionDetails.getInscode());
        institutionDetails.setInscode(null);
        QueryWrapper<InstitutionDetails> queryWrapper = QueryGenerator.initQueryWrapper(institutionDetails, req.getParameterMap());
        queryWrapper.in("inscode", inscode);
        queryWrapper.orderByDesc("create_time");
        Page<InstitutionDetails> page = new Page<>(pageNo, pageSize);
        IPage<InstitutionDetails> pageList = institutionDetailsService.page(page, queryWrapper);
        List<InstitutionDetails> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Result.OK(pageList);
        }
        photoConvert(records);
        pageList.setRecords(records);

        // 填充手机号
        List<String> insCodes = pageList.getRecords().stream().map(InstitutionDetails::getInscode).collect(Collectors.toList());
        List<Institution> list = institutionService.list(
                Wrappers.lambdaQuery(Institution.class)
                        .in(Institution::getInscode, insCodes)
        );
        Map<String, String> phoneMap = list.stream().collect(Collectors.toMap(Institution::getInscode, Institution::getPhone));
        Map<String, String> addrMap = list.stream().collect(Collectors.toMap(Institution::getInscode, Institution::getAddress));
        for (InstitutionDetails record : pageList.getRecords()) {
            record.setPhone(phoneMap.get(record.getInscode()));
            record.setAddress(addrMap.get(record.getInscode()));
        }
        return Result.OK(pageList);
    }

    @ApiOperation(value = "培训机构附加信息对象-分页列表查询", notes = "培训机构附加信息对象-分页列表查询")
    @GetMapping(value = "/append")
    public Result<IPage<InstitutionDetails>> queryList(InstitutionDetails institutionDetails,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        QueryWrapper<InstitutionDetails> queryWrapper = QueryGenerator.initQueryWrapper(institutionDetails, req.getParameterMap());
        Page<InstitutionDetails> page = new Page<InstitutionDetails>(pageNo, pageSize);
        IPage<InstitutionDetails> pageList = institutionDetailsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    private void photoConvert(List<InstitutionDetails> records) {
        records.forEach(e -> {
            e.setId(e.getInscode());
            if (Objects.nonNull(e.getCarouselIds())) {
                String carouselIds = e.getCarouselIds();
                String[] split = carouselIds.split(",");
                StringBuilder tempCar = new StringBuilder();
                for (String s : split) {
                    s = ossZWYUtils.getPhotoUrl(s);
                    tempCar.append(s).append(",");
                }
                tempCar.deleteCharAt(tempCar.length() - 1);
                e.setCarouselIds(tempCar.toString());
            }

            if (Objects.nonNull(e.getLogo())) {
                e.setLogo(ossZWYUtils.getPhotoUrl(e.getLogo()));
            }
            if (Objects.nonNull(e.getBusinessImgIds())) {
                e.setBusinessImgIds(ossZWYUtils.getPhotoUrl(e.getBusinessImgIds()));
            }
            if (Objects.nonNull(e.getBillboardImgIds())) {
                e.setBillboardImgIds(ossZWYUtils.getPhotoUrl(e.getBillboardImgIds()));
            }
            if (Objects.nonNull(e.getLicenceImgIds())) {
                e.setLicenceImgIds(ossZWYUtils.getPhotoUrl(e.getLicenceImgIds()));
            }
        });
    }


    @GetMapping(value = "/getLongitudeAndLatitude")
    public Result getLongitudeAndLatitude(@RequestParam String address) {

        Map params = new LinkedHashMap<String, String>();
        params.put("address", address);
        params.put("output", "json");
        params.put("ak", SearchHttpAK.AK);
        params.put("callback", "showLocation");
        SearchHttpAK snCal = new SearchHttpAK();
        try {
            String s = snCal.requestGetAK(SearchHttpAK.URL, params);
            String replace = s.replace("showLocation&&showLocation(", "");
            String substring = replace.substring(0, replace.length() - 1);
            JSONObject jsonObject = JSONObject.parseObject(substring);
            Object result = jsonObject.get("result");
            JSONObject jsonObject1 = JSONObject.parseObject(result.toString());
            Object location = jsonObject1.get("location");
            return Result.ok(location);
        } catch (Exception e) {
            log.error("获取经纬度异常", e);
            return Result.error("获取经纬度异常");
        }
    }

    /**
     * 添加
     */
    @AutoLog(value = "培训机构附加信息对象-添加")
    @RequiresPermissions("gzpt:institutionDetails:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody InstitutionDetails institutionDetails) {
        JkySysDepart depart = JkSecurityUtils.getDepartByUnknown(institutionDetails.getInscode());
        if (depart == null) {
            return Result.error("该培训机构尚未推送全国");
        }
        InstitutionDetails old = institutionDetailsService.getById(depart.getOrgCode());
        if (old != null) {
            return Result.error(depart.getDepartName() + "已添加,无需重复添加");
        }
        institutionDetails.setCradate(new Date());
        JkySysDepart sysDept = sysDeptService.selectDeptById(institutionDetails.getInscode());
        institutionDetails.setInscode(sysDept.getOrgCode());
        institutionDetails.setInsname(sysDept.getDepartName());
        if (Objects.nonNull(institutionDetails.getLogo())) {
            institutionDetails.setLogo(substrPhotoPath(institutionDetails.getLogo()));
        }
        if (Objects.nonNull(institutionDetails.getBusinessImgIds())) {
            institutionDetails.setBusinessImgIds(
                    substrPhotoPath(institutionDetails.getBusinessImgIds()));
        }
        if (Objects.nonNull(institutionDetails.getLicenceImgIds())) {
            institutionDetails.setLicenceImgIds(
                    substrPhotoPath(institutionDetails.getLicenceImgIds()));
        }
        if (Objects.nonNull(institutionDetails.getBillboardImgIds())) {
            institutionDetails.setBillboardImgIds(
                    substrPhotoPath(institutionDetails.getBillboardImgIds()));
        }
        if (StringUtils.isNotBlank(institutionDetails.getCarouselIds())) {
            String[] strings = institutionDetails.getCarouselIds().split(",");
            StringBuilder CarouselIds = new StringBuilder();
            for (String CarouselId : strings) {
                String path = substrPhotoPath(CarouselId);
                CarouselIds.append(path).append(",");
            }
            CarouselIds.deleteCharAt(CarouselIds.length() - 1);
            institutionDetails.setCarouselIds(CarouselIds.toString());
        }
        institutionDetailsService.save(institutionDetails);
        InsDetailsEdit insDetailsEdit = BeanConvertUtils.convertTo(institutionDetails, InsDetailsEdit::new);
        Institution ins = institutionService.getByInscode(insDetailsEdit.getInscode());
        insDetailsEdit.setPhone(ins.getPhone()).setAddress(ins.getAddress());
        insDetailsEditService.save(insDetailsEdit);
        return Result.ok("添加成功，请等待审核发布");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "培训机构附加信息对象-编辑")
    @ApiOperation(value = "培训机构附加信息对象-编辑", notes = "培训机构附加信息对象-编辑")
    @RequiresPermissions("gzpt:institutionDetails:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InstitutionDetails institutionDetails) {
        List<InsDetailsEdit> editList = insDetailsEditService.list(
                Wrappers.<InsDetailsEdit>lambdaQuery()
                        .eq(InsDetailsEdit::getInscode, institutionDetails.getInscode())
                        .eq(InsDetailsEdit::getAuditStatus, 0)
        );
        if (!CollectionUtils.isEmpty(editList)) {
            return Result.error("存在未审核的修改申请, 请等待审核后再试!");
        }
        JkySysDepart sysDept = jkySysDepartService.selectDeptByleader(institutionDetails.getInscode());
        institutionDetails.setInscode(sysDept.getOrgCode());
        if (Objects.nonNull(institutionDetails.getLogo())) {
            institutionDetails.setLogo(substrPhotoPath(institutionDetails.getLogo()));
        }
        if (Objects.nonNull(institutionDetails.getBusinessImgIds())) {
            institutionDetails.setBusinessImgIds(
                    substrPhotoPath(institutionDetails.getBusinessImgIds()));
        }
        if (Objects.nonNull(institutionDetails.getLicenceImgIds())) {
            institutionDetails.setLicenceImgIds(
                    substrPhotoPath(institutionDetails.getLicenceImgIds()));
        }
        if (Objects.nonNull(institutionDetails.getBillboardImgIds())) {
            institutionDetails.setBillboardImgIds(
                    substrPhotoPath(institutionDetails.getBillboardImgIds()));
        }
        if (StringUtils.isNotBlank(institutionDetails.getCarouselIds())) {
            String[] strings = institutionDetails.getCarouselIds().split(",");
            StringBuilder CarouselIds = new StringBuilder();
            for (String CarouselId : strings) {
                String path = substrPhotoPath(CarouselId);
                CarouselIds.append(path).append(",");
            }
            CarouselIds.deleteCharAt(CarouselIds.length() - 1);
            institutionDetails.setCarouselIds(CarouselIds.toString());
        }
        InsDetailsEdit res = new InsDetailsEdit();
        BeanUtils.copyProperties(institutionDetails, res);
        res.setId(null);
        res.setCreateTime(null);
        insDetailsEditService.save(res);
        return Result.ok("修改申请提交成功, 等待管理部门审核, 请稍后查看!");
    }

    /**
     * 通过id删除
     *
     * @param inscode
     * @return
     */
    @AutoLog(value = "培训机构附加信息对象-通过id删除")
    @ApiOperation(value = "培训机构附加信息对象-通过id删除", notes = "培训机构附加信息对象-通过id删除")
    @RequiresPermissions("gzpt:institutionDetails:remove")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String inscode) {
        institutionDetailsService.deleteInstitutionDetailsByInscode(inscode);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param inscodes
     * @return
     */
    @AutoLog(value = "培训机构附加信息对象-批量删除")
    @ApiOperation(value = "培训机构附加信息对象-批量删除", notes = "培训机构附加信息对象-批量删除")
    @RequiresPermissions("gzpt:institutionDetails:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String inscodes) {
        this.institutionDetailsService.deleteInstitutionDetailsByInscodes(inscodes.split(","));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过 inscode 查询
     *
     * @param inscode
     * @return
     */
    //@AutoLog(value = "培训机构附加信息对象-通过id查询")
    @ApiOperation(value = "培训机构附加信息对象-通过id查询", notes = "培训机构附加信息对象-通过id查询")
    @GetMapping(value = "/queryByInscode")
    public Result<InstitutionDetails> queryById(@RequestParam(name = "inscode") String inscode) {
        InstitutionDetails institutionDetails = institutionDetailsService.selectInstitutionDetailsByInscode(inscode);
        if (institutionDetails == null) {
            return Result.error("未找到对应数据");
        }
        JkySysDepart depart = jkySysDepartService.selectDeptByleader(institutionDetails.getInscode());
        institutionDetails.setInscode(depart.getId());
        return Result.OK(institutionDetails);
    }
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param institutionDetails
//    */
//    @RequiresPermissions("gzpt:institutionDetails:export")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, InstitutionDetails institutionDetails) {
//		String title = "培训机构附加信息对象";
//		LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
//		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(institutionDetails.getInscode());
//		institutionDetails.setInscode(null);
//		QueryWrapper<InstitutionDetails> queryWrapper = QueryGenerator.initQueryWrapper(institutionDetails, request.getParameterMap());
//		queryWrapper.in("inscode",inscode);
//		String selections = request.getParameter("selections");
//		if (oConvertUtils.isNotEmpty(selections)) {
//			List<String> arr = Arrays.asList(selections.split(","));
//			queryWrapper.in("inscode", arr);
//		}
//		queryWrapper.orderByDesc("create_time");
//		List<InstitutionDetails> records = this.service.list(queryWrapper);
//
//		// 提取 insCodes
//		Set<String> insCodes = records.stream().map(InstitutionDetails::getInscode).collect(Collectors.toSet());
//
//		if(!insCodes.isEmpty()){
//			// 根据 insCodes，查询 institutions
//			List<Institution> institutions = institutionService.list(Wrappers.lambdaQuery(Institution.class).in(Institution::getInscode, insCodes));
//
//			// 构造 insCode 和 insName 的映射关系
//			Map<String, String> map = institutions.stream().collect(Collectors.toMap(Institution::getInscode, Institution::getName));
//
//			// records 中添加 insName
//			records.forEach(e -> {
//				e.setInsname(map.get(e.getInscode()));
//			});
//		}
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject("fileName", title);
//		mv.addObject("entity", InstitutionDetails.class);
//		ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
//		exportParams.setImageBasePath(this.upLoadPath);
//		mv.addObject("params", exportParams);
//		mv.addObject("data", records);
//		return mv;
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_institution_details:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, InstitutionDetails.class);
//    }


    private String substrPhotoPath(String url) {
        if (StringUtils.isNotBlank(url) && url.contains(OssZWYUtils.UPLOAD_PATH) && url.contains("?")) {
            String subPath = StringUtils.substringBetween(url, OssZWYUtils.UPLOAD_PATH, "?");
            url = "/" + OssZWYUtils.UPLOAD_PATH + subPath;
            log.info("[照片路径] photopath:{} ", url);
        }
        return url;
    }

    /**
     * 查指定驾校的详细信息
     */
    @RequestMapping(value = "/hotline", method = RequestMethod.GET)
    public Result<?> hotline(@RequestParam String inscode) {
        InstitutionDetails byId = institutionDetailsService.getById(inscode);
        return Result.ok(byId);
    }

    /**
     * 是否发布
     */
    @PostMapping(value = "/publish")
    public Result<?> publish(@RequestBody InstitutionDetails rec) {
        institutionDetailsService.update(
                Wrappers.<InstitutionDetails>lambdaUpdate()
                        .set(InstitutionDetails::getPublish, rec.getPublish())
                        .eq(InstitutionDetails::getInscode, rec.getInscode())
        );
        return Result.ok("操作成功");
    }
}
