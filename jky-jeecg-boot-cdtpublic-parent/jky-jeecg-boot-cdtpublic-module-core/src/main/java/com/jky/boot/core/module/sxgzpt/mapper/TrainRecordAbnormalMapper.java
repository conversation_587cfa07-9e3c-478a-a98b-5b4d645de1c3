package com.jky.boot.core.module.sxgzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.sxgzpt.entity.TrainRecordAbnormal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 异常组管理Mapper接口
 *
 * <AUTHOR>
 * @date 2022-04-21
 */

@Mapper
public interface TrainRecordAbnormalMapper extends BaseMapper<TrainRecordAbnormal> {

    /**
     * 查询异常组管理列表
     *
     * @param trainRecordAbnormal 异常组管理
     * @return 异常组管理集合
     */
    public List<TrainRecordAbnormal> selectTrainRecordAbnormalList(TrainRecordAbnormal trainRecordAbnormal);


    @Select("SELECT datail.*,abnormal.status as abnormalStatus,abnormal.id as  abnormalId,abnormal.type as  abnormalType ,abnormal.rnum as abnormalRnum from ${tablename} abnormal JOIN sxgzpt.t_m_class_record_detail  datail " +
            " on abnormal.rnum=concat(datail.stunum,datail.recnum) and  datail.starttime_date = abnormal.starttime" +
            " WHERE abnormal.group_id = #{groupId} and abnormal.crdate >= #{crdate}")
    public List<ClassRecordDetail> getClassRecordDetailList(@Param("groupId") String groupId, @Param("crdate") Date crdate, @Param("tablename") String tablename);

    public List<ClassRecordDetail> getClassRecordDetailListByGroupIds(@Param("groupIds") List<String> groupIds, @Param("crdate") Date crdate, @Param("starttime") Date starttime);

    @Select("SELECT datail.*,abnormal.status as abnormalStatus,abnormal.id as  abnormalId ,abnormal.type as  abnormalType,abnormal.rnum as abnormalRnum from ${tablename} abnormal LEFT JOIN sxgzpt.t_m_class_record_detail  datail " +
            " on abnormal.rnum=concat(datail.stunum,datail.recnum) " +
            " WHERE abnormal.group_id = #{groupId} and abnormal.crdate >= #{crdate}  " +
            "and  datail.starttime_date BETWEEN #{starttime} and #{crdate} ")
    public List<ClassRecordDetail> getoldClassRecordDetailList(@Param("groupId") String groupId, @Param("starttime") Date starttime, @Param("crdate") Date crdate, @Param("tablename") String tablename);

//    @Select("SELECT record.* from ${tablename} abnormal RIGHT JOIN sxgzpt.t_m_train_record  record " +
//            " on abnormal.rnum=record.rnum and  record.traintime = abnormal.starttime" +
//            " WHERE abnormal.group_id = #{groupId} and abnormal.crdate >= #{crdate}")
//    public List<TrainRecord> getTrainRecordList(@Param("groupId") String groupId, @Param("crdate") Date crdate, @Param("tablename") String tablename);


}
