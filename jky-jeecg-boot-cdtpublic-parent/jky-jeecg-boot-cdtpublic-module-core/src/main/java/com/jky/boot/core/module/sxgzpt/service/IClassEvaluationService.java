package com.jky.boot.core.module.sxgzpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.sxgzpt.entity.ClassEvaluation;
import com.jky.boot.core.module.sxgzpt.vo.ClassEvaluationVo;
import com.jky.boot.core.module.sxgzpt.vo.CoachEvaluationVo;

import java.util.List;

public interface IClassEvaluationService extends IService<ClassEvaluation> {

    /**
     * 企业端签退评价展示list
     */
    List<ClassEvaluationVo> getClassVos(List<ClassEvaluation> temp);

    /**
     * 企业端教练评价展示list
     */
    List<CoachEvaluationVo> getCoachVos(List<ClassEvaluation> temp);
}
