package com.jky.boot.core.module.gzpt.utils.pdf;

import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

@Slf4j
public class PdfUtils {

    /**
     * pdf 压缩并生成压缩后文件, 压缩后文件在原文件同目录
     *
     * @param oriFilePath 原 pdf 路径
     */
    public static void compress(String oriFilePath) {
        try (PDDocument document = PDDocument.load(new File(oriFilePath))) {
            PDDocumentCatalog catalog = document.getDocumentCatalog();
            // 获取所有页面并压缩
            for (PDPage page : catalog.getPages()) {
                PDRectangle mediaBox = page.getMediaBox();
                float width = mediaBox.getWidth() * 0.5f; // 设置压缩率，例如：压缩到原来的一半
                float height = mediaBox.getHeight() * 0.5f; // 设置压缩率，例如：压缩到原来的一半

                PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true);
                contentStream.beginText();
                contentStream.newLineAtOffset(width, height);
                contentStream.endText();
                contentStream.close();
            }
            // 保存压缩后的PDF文档
            document.save(oriFilePath);
        } catch (Exception e) {
            log.error("压缩文件出错:", e);
        }
    }
}
