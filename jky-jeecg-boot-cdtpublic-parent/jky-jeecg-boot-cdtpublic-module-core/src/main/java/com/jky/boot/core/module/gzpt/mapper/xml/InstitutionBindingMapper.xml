<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.InstitutionBindingMapper">

    <resultMap type="com.jky.boot.core.module.gzpt.entity.InstitutionBinding" id="InstitutionBindingResult">
        <result property="inscode" column="inscode"/>
        <result property="bankcode" column="bankcode"/>
        <result property="bankname" column="bankname"/>
        <result property="cradate" column="cradate"/>
        <result property="insname" column="insname"/>
    </resultMap>

    <sql id="selectInstitutionBindingVo">
        select inscode, bankcode, bankname, cradate
        from t_m_institution_binding
    </sql>

    <select id="selectInstitutionBindingList" parameterType="com.jky.boot.core.module.gzpt.entity.InstitutionBinding" resultMap="InstitutionBindingResult">
        select bind.inscode, bind.bankcode, bind.bankname, bind.cradate,ins.name as insname from t_m_institution_binding
        bind left join t_m_institution ins
        on ins.inscode=bind.inscode
        <where>
            <if test="bankcode != null  and bankcode != ''">and bankcode = #{bankcode}</if>
            <if test="bankname != null  and bankname != ''">and bankname like concat(concat('%', #{bankname}), '%')</if>
            <if test="cradate != null ">and cradate = #{cradate}</if>
        </where>
    </select>

    <select id="selectInstitutionBindingByInscode" parameterType="String" resultMap="InstitutionBindingResult">
        <include refid="selectInstitutionBindingVo"/>
        where inscode = #{inscode}
    </select>

    <insert id="insertInstitutionBinding" parameterType="com.jky.boot.core.module.gzpt.entity.InstitutionBinding">
        insert into t_m_institution_binding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inscode != null">inscode,</if>
            <if test="bankcode != null">bankcode,</if>
            <if test="bankname != null">bankname,</if>
            <if test="cradate != null">cradate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inscode != null">#{inscode},</if>
            <if test="bankcode != null">#{bankcode},</if>
            <if test="bankname != null">#{bankname},</if>
            <if test="cradate != null">#{cradate},</if>
        </trim>
    </insert>

    <update id="updateInstitutionBinding" parameterType="com.jky.boot.core.module.gzpt.entity.InstitutionBinding">
        update t_m_institution_binding
        <trim prefix="SET" suffixOverrides=",">
            <if test="bankcode != null">bankcode = #{bankcode},</if>
            <if test="bankname != null">bankname = #{bankname},</if>
            <if test="cradate != null">cradate = #{cradate},</if>
        </trim>
        where inscode = #{inscode}
    </update>

    <delete id="deleteInstitutionBindingByInscode" parameterType="String">
        delete
        from t_m_institution_binding
        where inscode = #{inscode}
    </delete>

    <delete id="deleteInstitutionBindingByInscodes" parameterType="String">
        delete from t_m_institution_binding where inscode in
        <foreach item="inscode" collection="array" open="(" separator="," close=")">
            #{inscode}
        </foreach>
    </delete>
</mapper>