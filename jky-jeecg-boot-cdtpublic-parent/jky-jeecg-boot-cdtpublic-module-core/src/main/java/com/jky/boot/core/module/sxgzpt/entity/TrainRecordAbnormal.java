package com.jky.boot.core.module.sxgzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 异常组管理对象 t_m_train_record_abnormal
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@TableName(value = "t_m_train_record_abnormal")
@Data
public class TrainRecordAbnormal implements Serializable {
    private static final long serialVersionUID = 1L;


    @Excel(name = "主键")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;


    @Excel(name = "学员编号")
    private String stunum;


    @Excel(name = "类型 1-分钟学时 2-电子教学日志")
    private Integer type;


    @Excel(name = "s=tunum+recnum")
    private String rnum;


    @Excel(name = "异常编号")
    private String abnormalSeq;


    @Excel(name = "异常名称")
    private String abnormalName;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生成时间", width = 30, databaseFormat = "yyyy-MM-dd HH:mm:ss")
    private Date crdate;


    @Excel(name = "异常组id")
    private String groupId;


    @Excel(name = "状态 0-未处理 1-有效 2-无效")
    private Integer status;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处理时间", width = 30, databaseFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposeDate;


    @Excel(name = "处理人")
    private Integer operator;


    @Excel(name = "处理人姓名")
    private String operatorName;


    @Excel(name = "处理原因")
    private String reason;
    //培训时间
    private Date starttime;


}
