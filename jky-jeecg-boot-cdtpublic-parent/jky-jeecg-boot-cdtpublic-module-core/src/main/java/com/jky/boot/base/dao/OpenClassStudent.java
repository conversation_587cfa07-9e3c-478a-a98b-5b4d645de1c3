package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 开班学员
 *
 * <AUTHOR>
 * @version 2024-09-25
 */
@Data
@TableName("open_class_student")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class OpenClassStudent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 课堂id
     */
    private String openClassId;
    /**
     * 驾校编号
     */
    private String inscode;
    /**
     * 学员编号
     */
    private String stunum;
    /**
     * 学员名称
     */
    private String stuname;
    /**
     * 学员手机号
     */
    private String phone;
    /**
     * 是否推送计时（0-未推送，1-已推送，2-推送失败）
     */
    private Integer pushJs;
    /**
     * 审核状态
     */
    @Dict(dicCode = "sys_general_audit")
    private Integer auditStatus;
    /**
     * 审核人
     */
    private String auditPerson;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;
    /**
     * 预约状态
     */
    private Integer reservation;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    //1删除，0正常
    private Integer deleted;
}
