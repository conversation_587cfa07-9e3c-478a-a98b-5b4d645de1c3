package com.jky.boot.core.module.sxgzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.sxgzpt.entity.TMEvaluation;
import com.jky.boot.core.module.sxgzpt.vo.EvaluationCoach;

/**
 * @Description: t_m_evaluation
 * @Author: jeecg-boot
 * @Date: 2023-08-11
 * @Version: V1.0
 */
public interface ITMEvaluationService extends IService<TMEvaluation> {

    EvaluationCoach getEvaluationByCoach(TMEvaluation tMEvaluation);
    IPage<TMEvaluation> selectpj(Page<TMEvaluation> page, String id, String name);
}
