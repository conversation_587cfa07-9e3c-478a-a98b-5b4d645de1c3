package com.jky.boot.core.module.gzpt.entity;

import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.annotation.IgnoreScanAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_total_time
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Data
@TableName("t_m_total_time")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_total_time对象", description="学时汇总对象")
public class TotalTime implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private String stunum;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
	@ApiModelProperty(value = "姓名")
	@CryptoFieldAnno
	private String stuname;
	/**身份证*/
	@Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
	@DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
	@CryptoFieldAnno
    private String idcard;
	/**科目*/
	@Excel(name = "科目", width = 15 , dicCode = "sys_km")
    @ApiModelProperty(value = "科目")
	@Dict(dicCode = "sys_km")
    private Long subject;
	/**实操学时*/
	@Excel(name = "实操学时", width = 15)
    @ApiModelProperty(value = "实操学时")
    private Long vehicletime;
	/**课程*/
	@Excel(name = "课程", width = 15)
    @ApiModelProperty(value = "课程")
    private Long classtime;
	/**模拟*/
	@Excel(name = "模拟", width = 15)
    @ApiModelProperty(value = "模拟")
    private Long simulatortime;
	/**远程*/
	@Excel(name = "远程", width = 15)
    @ApiModelProperty(value = "远程")
    private Long networktime;
	/**是否报审*/
	@Excel(name = "是否报审", width = 15,dicCode = "sys_baoshen")
    @ApiModelProperty(value = "IS_BAOSHEN")
	@Dict(dicCode = "sys_baoshen")
    private String isBaoshen;
	/**里程*/
	@Excel(name = "里程", width = 15)
    @ApiModelProperty(value = "里程")
    private BigDecimal mileage;
	/**创建时间*/
	@Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date crdate;
	/**车型*/
	@Excel(name = "车型", width = 15)
    @ApiModelProperty(value = "车型")
    private String traintype;
	/**培训机构*/
	@Excel(name = "培训机构", width = 15)
    @ApiModelProperty(value = "培训机构")
    private String inscode;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private String insname;
	/**市区*/
	@Excel(name = "市区", width = 15)
    @ApiModelProperty(value = "市区")
    private String district;


    @Excel(name = "报名时间")
    @TableField(exist = false)
    private String applydate;
    @Excel(name = "教练员编号")
    @TableField(exist = false)
    private String coachnum;
    @Excel(name = "教练员")
    @TableField(exist = false)
	@CryptoFieldAnno
    private String coachname;

    @TableField(exist = false)
	@IgnoreScanAnno
    private List<String> inscodes;

	/**课堂额定学时*/
	@ApiModelProperty(value = "课堂额定学时")
	@TableField(exist = false)
	private java.lang.Long classCreditration;

	/**远程额定学时*/
	@ApiModelProperty(value = "远程额定学时")
	@TableField(exist = false)
	private java.lang.Long networkCreditration;

	/**实操额定学时*/
	@ApiModelProperty(value = "实操额定学时")
	@TableField(exist = false)
	private java.lang.Long vehicleCreditration;

	/**模拟额定学时*/
	@ApiModelProperty(value = "模拟额定学时")
	@TableField(exist = false)
	private java.lang.Long simulatorCreditration;

	/**额定额定里程*/
	@ApiModelProperty(value = "额定额定里程")
	@TableField(exist = false)
	private java.lang.Long ratedmileage;

	public Long getClassCreditration() {
		if (Objects.isNull(this.classCreditration))
		return 0L;
		else {
			return this.classCreditration;
		}
	}

	public Long getNetworkCreditration() {
		if (Objects.isNull(this.networkCreditration))
			return 0L;
		else {
			return this.networkCreditration;
		}
	}


	public Long getVehicleCreditration() {
		if (Objects.isNull(this.vehicleCreditration))
			return 0L;
		else {
			return this.vehicleCreditration;
		}
	}


	public Long getSimulatorCreditration() {
		if (Objects.isNull(this.simulatorCreditration))
			return 0L;
		else {
			return this.simulatorCreditration;
		}
	}


	public Long getRatedmileage() {
		if (Objects.isNull(this.ratedmileage))
			return 0L;
		else {
			return this.ratedmileage;
		}
	}


/**迁移追加字段*/
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**迁移追加字段*/
}
