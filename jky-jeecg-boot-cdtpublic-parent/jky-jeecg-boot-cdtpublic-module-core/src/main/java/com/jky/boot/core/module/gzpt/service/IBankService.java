package com.jky.boot.core.module.gzpt.service;

import com.jky.boot.core.module.gzpt.entity.Bank;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.vo.JhMsgsend;

/**
 * @Description: 资金托管银行管理
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IBankService extends IService<Bank> {

    int saveJhMegsend(JhMsgsend jhMsgsend);
}
