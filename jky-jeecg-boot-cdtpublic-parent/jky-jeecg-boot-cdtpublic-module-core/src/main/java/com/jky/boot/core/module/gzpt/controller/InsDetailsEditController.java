package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.InsDetailsEdit;
import com.jky.boot.base.service.IInsDetailsEditService;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.InstitutionDetails;
import com.jky.boot.core.module.gzpt.service.IInstitutionDetailsService;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 驾校详细信息编辑
 *
 * <AUTHOR>
 * @version 2024-03-21
 */
@Api(tags = "驾校详细信息编辑")
@RestController
@RequestMapping("/gzpt/insDetailsEdit")
@Slf4j
public class InsDetailsEditController extends JeecgController<InsDetailsEdit, IInsDetailsEditService> {
    private final OssZWYUtils ossZWYUtils;
	private final IInstitutionService institutionService;
    private final IInsDetailsEditService insDetailsEditService;
	private final IInstitutionDetailsService institutionDetailsService;

    public InsDetailsEditController(OssZWYUtils ossZWYUtils,
                                    IInstitutionService institutionService,
									IInsDetailsEditService insDetailsEditService,
									IInstitutionDetailsService institutionDetailsService) {
        this.ossZWYUtils = ossZWYUtils;
		this.institutionService = institutionService;
        this.insDetailsEditService = insDetailsEditService;
		this.institutionDetailsService = institutionDetailsService;
    }

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "驾校详细信息编辑-分页列表查询", notes = "驾校详细信息编辑-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InsDetailsEdit>> queryPageList(InsDetailsEdit insDetailsEdit,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(insDetailsEdit.getInscode());
        insDetailsEdit.setInscode(null);
        QueryWrapper<InsDetailsEdit> queryWrapper = QueryGenerator.initQueryWrapper(insDetailsEdit, req.getParameterMap());
        queryWrapper.in("inscode", insCodes);
        queryWrapper.orderByDesc("create_time");
        Page<InsDetailsEdit> page = new Page<>(pageNo, pageSize);
        IPage<InsDetailsEdit> pageList = insDetailsEditService.page(page, queryWrapper);
        pageList.getRecords().forEach(e -> {
            if (Objects.nonNull(e.getCarouselIds())) {
                String carouselIds = e.getCarouselIds();
                String[] split = carouselIds.split(",");
                StringBuilder tempCar = new StringBuilder();
                for (String s : split) {
                    s = ossZWYUtils.getPhotoUrl(s);
                    tempCar.append(s).append(",");
                }
                tempCar.deleteCharAt(tempCar.length() - 1);
                e.setCarouselIds(tempCar.toString());
            }
            if (Objects.nonNull(e.getLogo())) {
                e.setLogo(ossZWYUtils.getPhotoUrl(e.getLogo()));
            }
        });
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "驾校详细信息编辑-添加", notes = "驾校详细信息编辑-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InsDetailsEdit insDetailsEdit) {
        insDetailsEditService.save(insDetailsEdit);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "驾校详细信息编辑-编辑", notes = "驾校详细信息编辑-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InsDetailsEdit insDetailsEdit) {
        InsDetailsEdit byId = insDetailsEditService.getById(insDetailsEdit.getId());
        if (byId.getAuditStatus() != 0) {
            return Result.error("已审核的数据，不可编辑");
        }
        if (StringUtils.isNotBlank(insDetailsEdit.getCarouselIds())) {
            String[] strings = insDetailsEdit.getCarouselIds().split(",");
            StringBuilder CarouselIds = new StringBuilder();
            for (String CarouselId : strings) {
                String path = OssZWYUtils.subStr(CarouselId);
                CarouselIds.append(path).append(",");
            }
            CarouselIds.deleteCharAt(CarouselIds.length() - 1);
            insDetailsEdit.setCarouselIds(CarouselIds.toString());
        }
        insDetailsEdit.setLogo(OssZWYUtils.subStr(insDetailsEdit.getLogo()));
        insDetailsEditService.updateById(insDetailsEdit);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "驾校详细信息编辑-通过id删除", notes = "驾校详细信息编辑-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        insDetailsEditService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "驾校详细信息编辑-批量删除", notes = "驾校详细信息编辑-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.insDetailsEditService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "驾校详细信息编辑-通过id查询", notes = "驾校详细信息编辑-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InsDetailsEdit> queryById(@RequestParam(name = "id") String id) {
        InsDetailsEdit insDetailsEdit = insDetailsEditService.getById(id);
        if (insDetailsEdit == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(insDetailsEdit);
    }


	/**
	 * 审核
	 */
	@PostMapping(value = "/audit")
	public Result<?> audit(@RequestBody InsDetailsEdit receive) {
		InsDetailsEdit insEdit = insDetailsEditService.getById(receive.getId());
        if (insEdit.getAuditStatus() != 0) {
            return Result.error("请选择未审核的数据!");
        }
		insEdit.setAuditStatus(receive.getAuditStatus());
		insEdit.setAuditDate(new Date());
		insEdit.setAuditUser(JkSecurityUtils.getRealname());
		insEdit.setAuditReason(receive.getAuditReason());
		insDetailsEditService.updateById(insEdit);

		if (receive.getAuditStatus() == 1) {
			InstitutionDetails insDetails = institutionDetailsService.getById(insEdit.getInscode());
			BeanUtils.copyProperties(insEdit, insDetails);
            insDetails.setPublish(1);
			institutionDetailsService.updateById(insDetails);

			// 更改手机号
			institutionService.update(
				Wrappers.lambdaUpdate(Institution.class)
					.set(Institution::getPhone, insDetails.getPhone())
					.set(Institution::getAddress, insDetails.getAddress())
					.eq(Institution::getInscode, insDetails.getInscode())
			);
		}
		return Result.ok("审核成功!");
	}
}
