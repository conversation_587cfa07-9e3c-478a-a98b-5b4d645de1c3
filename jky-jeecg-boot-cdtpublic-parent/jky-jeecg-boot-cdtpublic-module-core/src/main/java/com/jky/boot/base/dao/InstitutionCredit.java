package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.io.Serializable;
import java.util.Date;

/**
 * 驾校信用管理对象
 *
 * <AUTHOR>
 * @version 2024-05-09
 */
@TableName(value = "t_m_institution_credit")
@Data
public class InstitutionCredit implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Dict(dictTable = "t_m_institution", dicCode = "inscode", dicText = "name")
    private String inscode;


    private String district;


    private Double fraction;


    private String grade;


    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crdate;

    private String year;

    private Integer season;

    private Integer flag;
}
