package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.CostLimit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 机构资金监管上下限管理对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface CostLimitMapper extends BaseMapper<CostLimit> {
    /**
     * 查询机构资金监管上下限管理
     *
     * @param id 机构资金监管上下限管理主键
     * @return 机构资金监管上下限管理
     */
    CostLimit selectCostLimitById(String id);

    /**
     * 查询机构资金监管上下限管理
     *
     * @param inscode 机构资金监管上下限管理 机构编号
     * @return 机构资金监管上下限管理
     */
    List<CostLimit> selectCostLimitListByInscode(String inscode);

    /**
     * 查询机构资金监管上下限管理列表
     *
     * @param costLimit 机构资金监管上下限管理
     * @return 机构资金监管上下限管理集合
     */
    List<CostLimit> selectCostLimitList(CostLimit costLimit);

    /**
     * 新增机构资金监管上下限管理
     *
     * @param costLimit 机构资金监管上下限管理
     * @return 结果
     */
    int insertCostLimit(CostLimit costLimit);

    /**
     * 修改机构资金监管上下限管理
     *
     * @param costLimit 机构资金监管上下限管理
     * @return 结果
     */
    int updateCostLimit(CostLimit costLimit);

    /**
     * 删除机构资金监管上下限管理
     *
     * @param id 机构资金监管上下限管理主键
     * @return 结果
     */
    int deleteCostLimitById(String id);

    /**
     * 批量删除机构资金监管上下限管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCostLimitByIds(String[] ids);
}
