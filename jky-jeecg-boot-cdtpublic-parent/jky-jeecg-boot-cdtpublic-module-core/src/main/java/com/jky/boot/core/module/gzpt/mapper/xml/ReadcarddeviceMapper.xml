<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.ReadcarddeviceMapper">
    <resultMap type="com.jky.boot.core.module.gzpt.entity.Readcarddevice" id="TLReadcarddeviceResult">
        <result property="id" column="id"/>
        <result property="deviceid" column="deviceid"/>
        <result property="schoolname" column="schoolname"/>
        <result property="inscode" column="inscode"/>
        <result property="applyaddress" column="applyaddress"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectTLReadcarddeviceVo">
        select id, deviceid, schoolname, inscode, applyaddress, create_time
        from t_l_readcarddevice
    </sql>

    <select id="selectTLReadcarddeviceList" parameterType="com.jky.boot.core.module.gzpt.entity.Readcarddevice" resultMap="TLReadcarddeviceResult">
        <include refid="selectTLReadcarddeviceVo"/>
        <where>
            <if test="deviceid != null  and deviceid != ''">and deviceid = #{deviceid}</if>
            <if test="schoolname != null  and schoolname != ''">and schoolname like concat(concat('%', #{schoolname}),
                '%')
            </if>
            <if test="inscodes != null ">
                and inscode in
                <foreach collection="inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="applyaddress != null  and applyaddress != ''">and applyaddress = #{applyaddress}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
        </where>
    </select>

    <select id="selectTLReadcarddeviceById" parameterType="Long" resultMap="TLReadcarddeviceResult">
        <include refid="selectTLReadcarddeviceVo"/>
        where id = #{id}
    </select>

    <insert id="insertTLReadcarddevice" parameterType="com.jky.boot.core.module.gzpt.entity.Readcarddevice">
        insert into t_l_readcarddevice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceid != null and deviceid != ''">deviceid,</if>
            <if test="schoolname != null">schoolname,</if>
            <if test="inscode != null">inscode,</if>
            <if test="applyaddress != null">applyaddress,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceid != null and deviceid != ''">#{deviceid},</if>
            <if test="schoolname != null">#{schoolname},</if>
            <if test="inscode != null">#{inscode},</if>
            <if test="applyaddress != null">#{applyaddress},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateTLReadcarddevice" parameterType="com.jky.boot.core.module.gzpt.entity.Readcarddevice">
        update t_l_readcarddevice
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceid != null and deviceid != ''">deviceid = #{deviceid},</if>
            <if test="schoolname != null">schoolname = #{schoolname},</if>
            <if test="inscode != null">inscode = #{inscode},</if>
            <if test="applyaddress != null">applyaddress = #{applyaddress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTLReadcarddeviceById" parameterType="Long">
        delete
        from t_l_readcarddevice
        where id = #{id}
    </delete>

    <delete id="deleteTLReadcarddeviceByIds" parameterType="String">
        delete from t_l_readcarddevice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>