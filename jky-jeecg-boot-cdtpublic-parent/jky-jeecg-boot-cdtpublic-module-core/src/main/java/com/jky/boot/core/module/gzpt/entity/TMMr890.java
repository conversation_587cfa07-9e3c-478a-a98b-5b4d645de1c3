package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * mr890 设备
 * <AUTHOR>
 * @since   2023-06-02
 * @version V1.0
 */
@Data
@TableName("t_m_mr890")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_mr890对象", description="t_m_mr890")
public class TMMr890 implements Serializable {
    private static final long serialVersionUID = 1L;

	/**设备编号*/
	@TableId(type = IdType.ASSIGN_ID)
	@Excel(name = "设备编号", width = 15)
    @ApiModelProperty(value = "设备编号")
    @JsonProperty(value = "id")
    private String deviceId;
	/**理论或模拟中心名称*/
	@Excel(name = "理论或模拟中心名称", width = 15)
    @ApiModelProperty(value = "理论或模拟中心名称")
    private String centerName;
	/**状态 0离线 1在线 2升级中*/
	@Excel(name = "状态 0离线 1在线 2升级中", width = 15, dicCode = "sys_mr890_device_status")
    @ApiModelProperty(value = "状态 0离线 1在线 2升级中")
    @Dict(dicCode = "sys_mr890_device_status")
    private Integer status;
	/**版本号*/
	@Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号")
    private String version;
    /**最后在线时间*/
    @Excel(name = "最后在线时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后在线时间")
    private Date lastOnlineTime;


	/**????*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "????")
    private Date createTime;
	/**????*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "????")
    private Date updateTime;

	/**是否使用人脸*/
	@Excel(name = "是否使用人脸", width = 15, dicCode = "sys_mr890_use_face")
    @ApiModelProperty(value = "是否使用人脸")
    @Dict(dicCode = "sys_mr890_use_face")
    private Integer useFace;
	/**insDeviceId*/
	@Excel(name = "insDeviceId", width = 15)
    @ApiModelProperty(value = "insDeviceId")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long insDeviceId;
	/**insname*/
	@Excel(name = "insname", width = 15)
    @ApiModelProperty(value = "insname")
    private String insname;
	/**inscode*/
	@Excel(name = "inscode", width = 15)
    @ApiModelProperty(value = "inscode")
    private String inscode;

    @Excel(name = "")
    @TableField(exist = false)
    @Dict(dicCode = "sys_status")
    private Integer enable;
}
