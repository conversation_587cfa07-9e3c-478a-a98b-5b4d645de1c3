package com.jky.boot.bank.vo;

import lombok.Data;

/**
 * 订单状态回调接收参数
 *
 * <AUTHOR>
 * @version 2024-12-12 16:42
 */
@Data
public class OrderStatusVo {

    /**
     * 外部流水号（业务订单号）
     */
    private String outOrderNo;

    /**
     * 支付宝订单号
     */
    private String alipayOrderNo;

    /**
     * 业务场景（默认 GENERAL_USER_FUND_CUSTODY）
     */
    private String bizScene;

    /**
     * 订单状态
     */
    private String orderStatus;
}
