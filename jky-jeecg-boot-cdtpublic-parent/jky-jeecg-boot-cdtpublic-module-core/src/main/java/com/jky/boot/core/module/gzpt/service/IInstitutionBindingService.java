package com.jky.boot.core.module.gzpt.service;

import com.jky.boot.core.module.gzpt.entity.InstitutionBinding;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 驾校绑定银行对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IInstitutionBindingService extends IService<InstitutionBinding> {
    /**
     * 查询驾校绑定银行
     *
     * @param inscode 驾校绑定银行主键
     * @return 驾校绑定银行
     */
    InstitutionBinding selectInstitutionBindingByInscode(String inscode);

    /**
     * 查询驾校绑定银行列表
     *
     * @param institutionBinding 驾校绑定银行
     * @return 驾校绑定银行集合
     */
    List<InstitutionBinding> selectInstitutionBindingList(InstitutionBinding institutionBinding);

    /**
     * 新增驾校绑定银行
     *
     * @param institutionBinding 驾校绑定银行
     * @return 结果
     */
    int insertInstitutionBinding(InstitutionBinding institutionBinding);

    /**
     * 修改驾校绑定银行
     *
     * @param institutionBinding 驾校绑定银行
     * @return 结果
     */
    int updateInstitutionBinding(InstitutionBinding institutionBinding);

    /**
     * 批量删除驾校绑定银行
     *
     * @param inscodes 需要删除的驾校绑定银行主键集合
     * @return 结果
     */
    int deleteInstitutionBindingByInscodes(String[] inscodes);

    /**
     * 删除驾校绑定银行信息
     *
     * @param inscode 驾校绑定银行主键
     * @return 结果
     */
    int deleteInstitutionBindingByInscode(String inscode);


}
