<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.base.mapper.QuestionMapper">
    <select id="getRandom100" parameterType="com.jky.boot.base.dao.Question" resultType="String">
        select id from question
        where chapter in (select id from chapter where car_type like concat('%', #{question.carType}, '%') AND subject =
        #{question.subject} )
        order by RANDOM() limit 100
    </select>
</mapper>
