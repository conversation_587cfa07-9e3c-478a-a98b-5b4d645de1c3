package com.jky.boot.zlb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jky.boot.common.sensitive.Sensitive;
import com.jky.boot.common.sensitive.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * t_m_coach
 *
 * <AUTHOR>
 * @version 2023-06-14
 */
@Data
@TableName("t_m_coach")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_coach对象", description="t_m_coach")
public class TMCoach implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@Excel(name = "id", width = 15)
    @ApiModelProperty(value = "id")
    private String coachid;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;
	/**姓名*/
//    @Sensitive(type = SensitiveTypeEnum.CHINESE_NAME_ZLB)
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;
	/**性别 1:男性;2:女性*/
	@Excel(name = "性别 1:男性;2:女性", width = 15)
    @ApiModelProperty(value = "性别 1:男性;2:女性")
    private Integer sex;
	/**身份证号*/
    @Sensitive(type = SensitiveTypeEnum.ID_CARD_ZLB)
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private String idcard;
	/**手机号码*/
    @Sensitive(type = SensitiveTypeEnum.MOBILE_PHONE)
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private String mobile;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
    private String address;
	/**照片文件ID*/
	@Excel(name = "照片文件ID", width = 15)
    @ApiModelProperty(value = "照片文件ID")
    private String photo;
	/**照片url*/
	@Excel(name = "照片url", width = 15)
    @ApiModelProperty(value = "照片url")
    private String photourl;
	/**指纹图片ID*/
	@Excel(name = "指纹图片ID", width = 15)
    @ApiModelProperty(value = "指纹图片ID")
    private String fingerprint;
	/**驾驶证号*/
	@Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
    private String drilicence;
	/**驾驶证初领日期*/
	@Excel(name = "驾驶证初领日期", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期")
    private String fstdrilicdate;
	/**职业资格证号*/
	@Excel(name = "职业资格证号", width = 15)
    @ApiModelProperty(value = "职业资格证号")
    private String occupationno;
	/**职业资格等级
1:一级
2:二级
3:三级
4:四级*/
	@Excel(name = "职业资格等级 1:一级 2:二级 3:三级 4:四级", width = 15)
    @ApiModelProperty(value = "职业资格等级 1:一级 2:二级 3:三级 4:四级")
    private java.lang.String occupationlevel;
	/**准驾车型 下列编码单选：
A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "准驾车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "准驾车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String dripermitted;
	/**准教车型 下列编码单选：A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "准教车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "准教车型 下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String teachpermitted;
	/**供职状态 0:在职1:离职*/
	@Excel(name = "供职状态 0:在职 1:离职", width = 15)
    @ApiModelProperty(value = "供职状态 0:在职 1:离职")
    private java.lang.String employstatus;
	/**入职日期 YYYYMMDD*/
	@Excel(name = "入职日期 YYYYMMDD", width = 15)
    @ApiModelProperty(value = "入职日期 YYYYMMDD")
    private java.lang.String hiredate;
	/**离职日期  YYYYMMDD*/
	@Excel(name = "离职日期  YYYYMMDD", width = 15)
    @ApiModelProperty(value = "离职日期  YYYYMMDD")
    private java.lang.String leavedate;
	/**0-未审核 1-审核通过 2-审核不通过*/
	@Excel(name = "0-未审核 1-审核通过 2-审核不通过", width = 15)
    @ApiModelProperty(value = "0-未审核 1-审核通过 2-审核不通过")
    private java.lang.Integer auditstate;
	/**审核时间*/
	@Excel(name = "审核时间", width = 15)
    @ApiModelProperty(value = "审核时间")
    private java.lang.String auditdate;
	/**审核原因*/
	@Excel(name = "审核原因", width = 15)
    @ApiModelProperty(value = "审核原因")
    private java.lang.String auditreason;
	/**停训 1-停训 0-启用*/
	@Excel(name = "停训 1-停训 0-启用", width = 15)
    @ApiModelProperty(value = "停训 1-停训 0-启用")
    private java.lang.Integer stopTrain;
	/**状态：1.已备案，0.解除备案*/
	@Excel(name = "状态：1.已备案，0.解除备案", width = 15)
    @ApiModelProperty(value = "状态：1.已备案，0.解除备案")
    private java.lang.Integer status;
	/**全国统一编号*/
	@Excel(name = "全国统一编号", width = 15)
    @ApiModelProperty(value = "全国统一编号")
    private java.lang.String coachnum;
	/**是否推送计时 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送计时 0未推送,1已推送,2推送失败", width = 15)
    @ApiModelProperty(value = "是否推送计时 0未推送,1已推送,2推送失败")
    private java.lang.String istojs;
	/**是否推送全国 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送全国 0未推送,1已推送,2推送失败", width = 15)
    @ApiModelProperty(value = "是否推送全国 0未推送,1已推送,2推送失败")
    private java.lang.String istoqg;
	/**有效日期*/
	@Excel(name = "有效日期", width = 15)
    @ApiModelProperty(value = "有效日期")
    private java.lang.String effectivedate;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remarks;
	/**政务云路径*/
	@Excel(name = "政务云路径", width = 15)
    @ApiModelProperty(value = "政务云路径")
    private java.lang.String osspotourl;
	/**带教类型：模拟实操理论*/
	@Excel(name = "带教类型：模拟实操理论", width = 15)
    @ApiModelProperty(value = "带教类型：模拟实操理论")
    private java.lang.Integer teachtype;
	/**厂商*/
	@Excel(name = "厂商", width = 15)
    @ApiModelProperty(value = "厂商")
    private java.lang.String platform;
	/**remark*/
	@Excel(name = "remark", width = 15)
    @ApiModelProperty(value = "remark")
    private java.lang.String remark;

    @Excel(name = "年龄", width = 15)
    @ApiModelProperty(value = "年龄")
    private java.lang.String age;

    @Excel(name = "评分", width = 15)
    @ApiModelProperty(value = "评分")
    private java.lang.String mark;

    @Excel(name = "处罚条数", width = 15)
    @ApiModelProperty(value = "处罚条数")
    private java.lang.String penalize;

    @Excel(name = "评价条数", width = 15)
    @ApiModelProperty(value = "评价条数")
    private java.lang.String evaluate;

    @TableField(exist = false)
    private String label;


    @TableField(exist = false)
    private String num;

    @TableField(exist = false)
    private String year;

    /**
     * 是否处于重点关注名单
     */
    @TableField(exist = false)
    private Integer auth;
}
