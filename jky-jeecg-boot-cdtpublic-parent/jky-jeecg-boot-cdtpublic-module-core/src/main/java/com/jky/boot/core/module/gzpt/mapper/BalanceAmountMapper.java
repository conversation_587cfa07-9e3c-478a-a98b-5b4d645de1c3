package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.BalanceAmount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 冻结余额查询对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface BalanceAmountMapper extends BaseMapper<BalanceAmount> {


    /**
     * 查询冻结余额查询列表
     *
     * @param balanceAmount 冻结余额查询
     * @return 冻结余额查询集合
     */
    List<BalanceAmount> selectBalanceAmountList(BalanceAmount balanceAmount);
    IPage<BalanceAmount> selectBalanceAmountPage(Page<BalanceAmount> page, @Param("balanceAmout") BalanceAmount balanceAmount);
    /**
     * 新增冻结余额查询
     *
     * @param balanceAmount 冻结余额查询
     * @return 结果
     */
    int insertBalanceAmount(BalanceAmount balanceAmount);

    /**
     * 修改冻结余额查询
     *
     * @param balanceAmount 冻结余额查询
     * @return 结果
     */
    int updateBalanceAmount(BalanceAmount balanceAmount);
}
