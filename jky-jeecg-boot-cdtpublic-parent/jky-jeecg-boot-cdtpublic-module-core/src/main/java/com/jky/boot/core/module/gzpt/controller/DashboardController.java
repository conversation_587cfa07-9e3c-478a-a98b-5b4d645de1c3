package com.jky.boot.core.module.gzpt.controller;

import com.jky.boot.core.module.gzpt.dto.DashboardDto;
import com.jky.boot.core.module.gzpt.service.IDashboardService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页数据展示
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    private final IDashboardService dashboardService;

    @Autowired
    private RedisTemplate redisTemplate;

    public DashboardController(IDashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    @GetMapping("/total")
    public Result<DashboardDto> total() {
        String key = "homePageData";
        String leader = JkSecurityUtils.getLoginUser().getOrgCode();
        if (leader.length()==4||leader.length()==6){
            Boolean aBoolean = redisTemplate.hasKey(key + leader);
            if (aBoolean){
                DashboardDto o = (DashboardDto)redisTemplate.opsForValue().get(key + leader);
                return Result.OK(o);
            }
        }
        return Result.ok(dashboardService.total(leader));
    }
}
