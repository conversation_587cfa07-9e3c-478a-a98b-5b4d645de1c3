package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.annotation.IgnoreScanAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 学员开户冻结对象
 */
@Data
@TableName("t_m_bank_reg")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_bank_reg对象", description="学员开户冻结对象")
public class BankReg implements Serializable {
    private static final long serialVersionUID = 1L;

	/**开户表 id */
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "开户表 id ")
    private java.lang.String id;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private java.lang.String stunum;
	/**驾校编号*/
	@Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private java.lang.String inscode;
	/**驾校名称*/
	@Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private java.lang.String insname;
	/**圈存id 银行冻结后返回*/
//	@Excel(name = "圈存id 银行冻结后返回", width = 15)
    @ApiModelProperty(value = "圈存id 银行冻结后返回")
    private java.lang.String serialno;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**订单状态 0 开户 1 开户成功  2 开户失败 3 冻结成功 4冻结中   5冻结失败*/
	@Excel(name = "订单状态", width = 15)
    @ApiModelProperty(value = "订单状态 0 开户 1 开户成功  2 开户失败 3 冻结成功 4冻结中   5冻结失败")
    private java.lang.Integer status;
	/**对应银行编号*/
	@Excel(name = "对应银行编号", width = 15)
    @ApiModelProperty(value = "对应银行编号")
    private java.lang.String bankcode;
	/**银行操作时间*/
	@Excel(name = "银行操作时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "银行操作时间")
    private java.util.Date bankdate;
	/**对应银行名称*/
	@Excel(name = "对应银行名称", width = 15)
    @ApiModelProperty(value = "对应银行名称")
    private java.lang.String bankname;
	/**冻结金额余额*/
	@Excel(name = "总冻结金额", width = 15)
    @ApiModelProperty(value = "总冻结金额")
    private java.math.BigDecimal balanceAmount;
	/**学员名字*/
	@Excel(name = "学员名字", width = 15)
    @ApiModelProperty(value = "学员名字")
    @CryptoFieldAnno
    private java.lang.String stuname;
    /**冻结编号*/
	@Excel(name = "银行冻结编号", width = 15)
    @ApiModelProperty(value = "银行冻结编号")
    private java.lang.String blockid;
	/**企业流水号*/
//	@Excel(name = "企业流水号", width = 15)
    @ApiModelProperty(value = "企业流水号")
    private java.lang.String corpseqno;
	/**错误原因*/
//	@Excel(name = "错误原因", width = 15)
    @ApiModelProperty(value = "错误原因")
    private java.lang.String bankmsg;

    @Excel(name = "计时学员编号", width = 15)
    private String oldStunum;

    @Excel(name = "计时驾校编号", width = 15)
    private String oldInscode;

    @Excel(name = "计时厂商", width = 15)
    private String platform;

    /**剩余冻结金额*/
    @Excel(name = "冻结余额", width = 15)
    private java.math.BigDecimal remainingAmount;

    //报名号
    private String enrollId;

    /**
     * 支付宝订单页面路径
     */
    private String alipayUrl;

    /**
     * 运管发起的划转金额
     */
    @TableField(exist = false)
    private BigDecimal transAmount;

    @TableField(exist = false)
    @IgnoreScanAnno
    private String[] ids;

    /**学员身份证号码*/
//    @Excel(name = "学员身份证号码", width = 20)
//    @TableField(exist = false)
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    @CryptoFieldAnno
    private java.lang.String idcard;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    @IgnoreScanAnno
    private List<String> insnames;

    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    @IgnoreScanAnno
    private List<String> inscodes;

    public BankReg() {
    }

    public BankReg(String stunum) {
        this.stunum = stunum;
    }

    public BankReg(String stunum, Integer status) {
        this.stunum = stunum;
        this.status = status;
    }


    /**
     * 当前冻结阶段
     * 1 单笔冻结
     * 21二笔冻结第一笔（课堂+模拟）  22二笔冻结第二笔（科二+科三实操）
     * 31三笔冻结第一笔（课堂+模拟）  32三笔冻结第二笔（科二实操）   33三笔冻结第三笔（科三实操）
     * 9 额外多笔冻结（与免单对应）
     */
    private String regState;


	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**迁移追加字段*/

    @TableField(exist = false)
    private String traintype;
}
