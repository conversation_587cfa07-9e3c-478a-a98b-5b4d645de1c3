package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Param;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.StudentinfoEnter;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.simpleframework.xml.core.Validate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 学员预录入
 */
@Api(tags = "在线报名学员对象")
@RestController
@RequestMapping("/gzpt/studentinfoEnter")
@Slf4j
public class StudentinfoEnterController extends JeecgController<StudentinfoEnter, IStudentinfoEnterService> {
    @Autowired
    private IStudentinfoEnterService studentinfoEnterService;
    @Autowired
    private JkySysDepartService sysDeptService;
    @Autowired
    private IInstitutionService iInstitutionService;
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @Autowired
    private IParamService paramService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "在线报名学员-分页列表查询", notes = "在线报名学员-分页列表查询")
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<IPage<StudentinfoEnter>> queryPageList(StudentinfoEnter studentinfoEnter,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfoEnter.getInscode());
        studentinfoEnter.setInscode(null);
        QueryWrapper<StudentinfoEnter> queryWrapper = QueryGenerator.initQueryWrapper(studentinfoEnter, req.getParameterMap());
        queryWrapper.in("inscode", inscode);
        queryWrapper.orderByDesc("create_time");
        Page<StudentinfoEnter> page = new Page<>(pageNo, pageSize);
        IPage<StudentinfoEnter> pageList = studentinfoEnterService.page(page, queryWrapper);
        pageList.getRecords().forEach(e ->
                e.setPhotopath(ossZWYUtils.getUrl(e.getPhotopath()))
        );
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "在线报名学员-添加")
    @ApiOperation(value = "在线报名学员-添加", notes = "在线报名学员-添加")
    @RequiresPermissions("gzpt:studentinfoEnter:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody StudentinfoEnter studentinfoEnter) {
        JkySysDepart sysDept = sysDeptService.selectDeptByleader(studentinfoEnter.getInscode());

        studentinfoEnter.setInscode(sysDept.getOrgCode());
        studentinfoEnter.setInsname(sysDept.getDepartName());
        studentinfoEnterService.save(studentinfoEnter);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "在线报名学员-编辑")
    @RequiresPermissions("gzpt:studentinfoEnter:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody StudentinfoEnter studentinfoEnter) {
        StudentinfoEnter studentinfoEnterById = studentinfoEnterService.getById(studentinfoEnter.getId());
        if (Objects.equals(studentinfoEnterById.getIsEnter(), 1)) {
            return Result.error("学员已录入不允许编辑");
        }
        if (!Objects.equals(studentinfoEnter.getTraintype(), studentinfoEnterById.getTraintype())) {
            return Result.error("车型不允许编辑");
        }

		studentinfoEnter.setPhotopath(OssZWYUtils.subStr(studentinfoEnter.getPhotopath()));
        // 非跨地市学员校验资金上下限
        if ("2".equals(studentinfoEnter.getIscrosscity()) && (studentinfoEnter.getTraintype().equals("C1") || studentinfoEnter.getTraintype().equals("C2"))) {
            studentinfoEnter.setFromarea("");
            studentinfoEnter.setCrossType(0);
        }
        // 基本信息禁止修改
        studentinfoEnter.setApplydate(null);
        studentinfoEnter.setName(null);
        studentinfoEnter.setCardtype(null);
        studentinfoEnter.setIdcard(null);
        studentinfoEnter.setApplydate(null);
        studentinfoEnter.setInscode(null);
        studentinfoEnter.setInsname(null);
        studentinfoEnter.setNationality(null);
        studentinfoEnter.setTraintype(null);
        studentinfoEnterService.updateById(studentinfoEnter);
        return Result.ok("编辑成功!");
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete")
    @RequiresPermissions("gzpt:studentinfoEnter:remove")
    public Result<?> delete(@RequestParam("id") String id) {
        studentinfoEnterService.removeById(id);
        return Result.ok("删除成功！");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "在线报名学员-批量删除")
    @ApiOperation(value = "在线报名学员-批量删除", notes = "在线报名学员-批量删除")
    @RequiresPermissions("gzpt:studentinfoEnter:remove")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.studentinfoEnterService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "在线报名学员-通过id查询")
    @ApiOperation(value = "在线报名学员-通过id查询", notes = "在线报名学员-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<StudentinfoEnter> queryById(@RequestParam(name = "id") String id) {
        StudentinfoEnter studentinfoEnter = studentinfoEnterService.getById(id);
        if (studentinfoEnter == null) {
            return Result.error("未找到对应数据");
        }
        if (StringUtils.isNotBlank(studentinfoEnter.getPhotopath())) {
            studentinfoEnter.setPhotopath(ossZWYUtils.getPhotoUrl(studentinfoEnter.getPhotopath()));
        }
        return Result.OK(studentinfoEnter);
    }


    @AutoLog(value = "录入学员")
    @RequiresPermissions("gzpt:studentinfoEnter:pushStudent")
    @PostMapping("/pushStudent")
    public Result<?> pushStudent(@RequestBody StudentinfoEnter enter) {
        StudentinfoEnter studentinfoEnter = studentinfoEnterService.getById(enter.getId());
        if (studentinfoEnter == null) {
            return Result.error("学员不存在");
        }
        // 录入前，部分驾校校验面签单
//        Param sysParam = paramService.getOne(
//                Wrappers.<Param>lambdaQuery()
//                        .eq(Param::getSpKey, "pushStuIns")
//        );
//        List<String> insCodes = Arrays.asList(sysParam.getSpValue().split(","));
//        if (insCodes.contains(studentinfoEnter.getInscode()) && StringUtils.isBlank(enter.getFileUrl())) {
//            return Result.error("请上传面签截图");
//        }
        if (StringUtils.isBlank(enter.getFileUrl())) {
            return Result.error("请上传面签截图");
        }
        if (StringUtils.isBlank(studentinfoEnter.getCoachnum())) {
            return Result.error("当前学员还未绑定教练员，请先编辑选择实操教练!");
        }
        if ("1".equals(studentinfoEnter.getIscrosscity()) && Objects.equals(studentinfoEnter.getCrossType(), 0)) {
            return Result.error("学员为跨地市转校学员，跨地市转校学员学习类型选择有误，请检查后重新录入");
        }
        Studentinfo one = studentinfoService.getOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, studentinfoEnter.getIdcard())
                        .eq(Studentinfo::getTraintype, studentinfoEnter.getTraintype())
        );
        if (Objects.nonNull(one)) {
            return Result.error("该学员已录入，请勿重复操作！");
        }
        if (EnvControl.JG_SWITCH) {
            JSONObject json = new JSONObject();
            json.put("idCard", studentinfoEnter.getIdcard());
            json.put("traintype", studentinfoEnter.getTraintype());
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.jgptUrl + "/sx/studentApplySearch", json.toString());
            int errorcode = commonResponse.getErrorcode();
            JSONObject json1 = (JSONObject) JSON.toJSON(commonResponse.getData());
            log.info("[errorcode]-------------------------------------------" + errorcode);
            log.info("[json1]-------------------------------------------" + json1);
            if (errorcode == 1) {
                String insname = json1 != null ? json1.getString("insname") : null;
                return Result.error("该学员已在" + insname + "报名，不能重复报名");
            }
        }
        Studentinfo studentinfo = new Studentinfo();
        BeanUtils.copyProperties(studentinfoEnter, studentinfo);
        Institution byInscode = iInstitutionService.getByInscode(studentinfoEnter.getInscode());
        if (byInscode == null) {
            return Result.error(studentinfoEnter.getInsname() + ":不存在!");
        }
        studentinfo.setId(null);
        studentinfo.setPlatform(byInscode.getPlatform());
        studentinfo.setDistrict(byInscode.getDistrict());

        // 补充字段
        studentinfo.setIstoqg(0);
        studentinfo.setIstojs(0);
        studentinfo.setIstobank(0);
        studentinfo.setIsold("0");
        studentinfo.setIssignature(0);
        studentinfo.setIstoapp(0);
        studentinfo.setBaseType(1);
        if ("1".equals(studentinfo.getCardtype())) {
            studentinfo.setBirthday(studentinfo.getIdcard().substring(6, 14));
            studentinfo.setBirthplace(studentinfo.getIdcard().substring(0, 6));
        }
        if ("0".equals(studentinfoEnter.getBusitype())) {
            studentinfo.setDrilicnum(null);
            studentinfo.setFstdrilicdate(null);
            studentinfo.setPerdritype(null);
        } else {
            studentinfo.setDrilicnum(studentinfoEnter.getIdcard());
        }

        studentinfoService.save(studentinfo);
        studentinfoEnter.setIsEnter(1);
        //切割一下 防止链接有效期过期 http://jkyqstes.oss-cn-hangzhou.aliyuncs.com/quzgzpt/upload/2025/04/01/*************.pdf?Expires=**********&OSSAccessKeyId=LTAI4GDArYcxPrYXqbVMjXKc&Signature=ASCxll2XTX37xIiPOG3LT67p92o%3D
        //生成 quzgzpt/upload/2025/04/01/1743475459637.pdf
        studentinfoEnter.setFileUrl(OssZWYUtils.subStr(enter.getFileUrl()));
        studentinfoEnterService.updateById(studentinfoEnter);
        return Result.ok("录入成功！");
    }


    /**
     * 接收公安报名学员
     *
     * @param stu 在线报名学员
     */
    @RequestMapping(value = "/stuadd", method = RequestMethod.POST)
    public CommonResponse stuAdd(@Validate @RequestBody StudentinfoEnter stu) {
        try {
            studentinfoEnterService.save(stu);
            return new CommonResponse(0, "执行成功！");
        } catch (Exception e) {
            return new CommonResponse(1, "执行失败！");
        }
    }


    @AutoLog(value = "在线报名学员-添加")
    @ApiOperation(value = "在线报名学员-添加", notes = "在线报名学员-添加")
    @PostMapping(value = "/addst")
    public Result<String> addst(@RequestParam(name = "idcard", required = false) String idcard) {
        studentinfoEnterService.insertStudent(idcard);
        return Result.OK("添加成功！");
    }

    /**
     * 获取学员驾驶证申请表
     */
    @AutoLog(value = "获取驾驶证申请表")
    @ApiOperation(value = "获取驾驶证申请表", notes = "获取驾驶证申请表")
    @GetMapping("/viewApplicationForm")
    public Result<String> viewApplicationForm(@RequestParam(name = "id") String id) {
        // 先通过id查询正式学员信息
        Studentinfo studentinfo = studentinfoService.getById(id);
        if (studentinfo == null) {
            return Result.error("未找到学员信息");
        }

        // 根据身份证号和车型查询在线报名表
        StudentinfoEnter studentinfoEnter = studentinfoEnterService.getOne(
                Wrappers.lambdaQuery(StudentinfoEnter.class)
                        .eq(StudentinfoEnter::getIdcard, studentinfo.getIdcard())
                        .eq(StudentinfoEnter::getTraintype, studentinfo.getTraintype())
                        .orderByDesc(StudentinfoEnter::getCreateTime)
        );

        if (studentinfoEnter != null && StringUtils.isNotBlank(studentinfoEnter.getFileUrl())) {
            return Result.OK(ossZWYUtils.getPhotoUrl(studentinfoEnter.getFileUrl()));
        }

        return Result.error("未找到驾驶证申请表文件");
    }
}
