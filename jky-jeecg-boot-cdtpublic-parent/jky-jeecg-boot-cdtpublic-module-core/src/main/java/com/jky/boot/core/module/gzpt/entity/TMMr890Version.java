package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: t_m_mr890_version
 * @Author: jeecg-boot
 * @Date:   2023-06-02
 * @Version: V1.0
 */
@Data
@TableName("t_m_mr890_version")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_mr890_version对象", description="version对象")
public class TMMr890Version implements Serializable {
    private static final long serialVersionUID = 1L;

	/**版本ID*/
	@Excel(name = "版本ID", width = 15)
    @ApiModelProperty(value = "版本ID")
    @JsonSerialize(using= ToStringSerializer.class)
    @JsonProperty(value = "id")
    @TableId(value = "version_id", type = IdType.ASSIGN_ID)
    private String versionId;
	/**版本名称*/
	@Excel(name = "版本名称", width = 15)
    @ApiModelProperty(value = "版本名称")
    @NotBlank(message = "版本名称不能为空")
    private String versionName;
	/**??url*/
	@Excel(name = "版本url", width = 15)
    @ApiModelProperty(value = "版本url")
    @NotBlank(message = "请先上传apk文件")
    private String versionUrl;
	/**???*/
    @ApiModelProperty(value = "???")
    private String createBy;
	/**????*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "????")
    @Excel(name = "创建时间", width = 15)
    private Date createTime;
	/**???*/
    @ApiModelProperty(value = "???")
    private String updateBy;
	/**????*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "????")
    private Date updateTime;
}
