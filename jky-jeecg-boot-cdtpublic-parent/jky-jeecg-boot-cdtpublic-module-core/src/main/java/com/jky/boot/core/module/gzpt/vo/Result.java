package com.jky.boot.core.module.gzpt.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "Result", description = "通用请求返回数据")
public class Result {

    /**
     * 返回编码code
     */
    @ApiModelProperty(value = "返回编码code")
    private String code;

    /**
     * 返回数据data
     */
    @ApiModelProperty(value = "返回数据data")
    private Object data;

    /**
     * 错误数据
     */
    @ApiModelProperty(value = "错误数据")
    private Object errorData;

    /**
     * 返回信息
     */
    @ApiModelProperty(value = "返回信息")
    private String message;

    /**
     * 请求用户名
     */
    private String name;

    /**
     * 请求描述
     */
    private String describe;

    /**
     * 请求参数json
     */
    private String json;

    /**
     * @param result
     */
    private String result;

    public Result(String result) {
        JSONObject jb = JSONObject.parseObject(result);
        this.code = jb.getString("code");
        this.data = jb.get("data");
        this.errorData = jb.get("errorData");
        this.message = jb.getString("message");
        this.result = result;
    }

    public Result() {

    }

}

