package com.jky.boot.zlb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: t_m_institution_class
 * @Author: jeecg-boot
 * @Date:   2023-06-16
 * @Version: V1.0
 */
@Data
@TableName("t_m_institution_class")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_institution_class对象", description="t_m_institution_class")
public class TMInstitutionClass implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
	/**培训机构编码*/
	@Excel(name = "培训机构编码", width = 15)
    @ApiModelProperty(value = "培训机构编码")
    @Dict(dicText = "name",dicCode = "inscode",dictTable = "t_m_institution")
    private String inscode;
    /**培训机构名称*/
    @Excel(name = "培训机构名称", width = 15)
    @ApiModelProperty(value = "培训机构名称")
    private java.lang.String name;
    /**班级名称*/
    @Excel(name = "课程编号", width = 15)
    @ApiModelProperty(value = "课程编号")
    private String classno;
	/**班级名称*/
	@Excel(name = "班级名称", width = 15)
    @ApiModelProperty(value = "班级名称")
    private String classname;
    /**培训车型 	下列编码单选：
     A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
    @Excel(name = "培训车型", width = 15)
    @ApiModelProperty(value = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    @Dict(dicCode = "sys_stu_traintype")
    private java.lang.String traintype;
	/**班级简介*/
	@Excel(name = "班级简介", width = 15)
    @ApiModelProperty(value = "班级简介")
    private String classintro;
	/**班级价格*/
	@Excel(name = "班级价格", width = 15)
    @ApiModelProperty(value = "班级价格")
    private String claprice;
	/**班级备注*/
	@Excel(name = "班级备注", width = 15)
    @ApiModelProperty(value = "班级备注")
    private String claremark;
//	/**班级教练员*/
//	@Excel(name = "班级教练员", width = 15)
//    @ApiModelProperty(value = "班级教练员")
//    private String coach;
    /**图片路径*/
    @ApiModelProperty(value = "图片路径")
    private java.lang.String photopath;

    /**基础费用*/
    @ApiModelProperty(value = "基础费用")
    private java.lang.String basicCost;
    /**第二部分费用*/
    @ApiModelProperty(value = "第二部分费用")
    private java.lang.String secondCost;
    /**第三部分费用*/
    @ApiModelProperty(value = "第三部分费用")
    private java.lang.String thirdCoast;

    /**
     * 一个月平均费用（仅限C1，C2）
     */
    @Excel(name = "一个月平均费用（仅限C1，C2）", width = 15)
    @ApiModelProperty(value = "一个月平均费用（仅限C1，C2）")
    private BigDecimal averageCost;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
}
