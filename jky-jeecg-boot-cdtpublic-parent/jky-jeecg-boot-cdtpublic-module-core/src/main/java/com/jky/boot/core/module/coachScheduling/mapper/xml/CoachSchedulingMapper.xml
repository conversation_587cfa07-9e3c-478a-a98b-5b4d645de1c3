<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.coachScheduling.mapper.CoachSchedulingMapper">

    <resultMap id="coachSchedulingResultMap" type="com.jky.boot.core.module.coachScheduling.vo.sched.CoachSchedulingVo">
        <id column="coach_name" property="coachName"></id>
        <id column="coach_num" property="coachNum"></id>
        <collection property="schedulings" resultMap="dayScheduling">
        </collection>
    </resultMap>

    <resultMap id="imitateSchedResultMap" type="com.jky.boot.core.module.coachScheduling.vo.sched.ImitateSchedVo">
        <id column="company_name" property="companyName"></id>
        <collection property="schedulings" resultMap="dayScheduling">
        </collection>
    </resultMap>

    <resultMap id="dayScheduling" type="com.jky.boot.core.module.coachScheduling.vo.sched.DaySchedulingVo">
        <id column="date" property="date"></id>
        <collection property="amSchedulingVo" resultMap="amSchedulingResultMap"></collection>
        <collection property="pmSchedulingVo" resultMap="pmSchedulingResultMap"></collection>
    </resultMap>

    <resultMap id="pmSchedulingResultMap" type="com.jky.boot.core.module.coachScheduling.vo.sched.SchedulingVo">
        <discriminator javaType="java.lang.Integer" column="time">
            <case value="2">
                <id property="id" column="id"></id>
                <result column="course" property="course"></result>
                <result column="order_num" property="orderNum"></result>
                <result column="order_cap" property="orderCap"></result>
                <result column="date" property="date"></result>
                <result column="today_date" property="todayDate"></result>
                <result column="company_name" property="companyName"></result>
                <result column="address" property="address"></result>
            </case>
        </discriminator>
    </resultMap>

    <resultMap id="amSchedulingResultMap" type="com.jky.boot.core.module.coachScheduling.vo.sched.SchedulingVo">
        <discriminator javaType="java.lang.Integer" column="time">
            <case value="1">
                <id property="id" column="id"></id>
                <result column="course" property="course"></result>
                <result column="order_num" property="orderNum"></result>
                <result column="order_cap" property="orderCap"></result>
                <result column="date" property="date"></result>
                <result column="today_date" property="todayDate"></result>
                <result column="company_name" property="companyName"></result>
                <result column="address" property="address"></result>
            </case>
        </discriminator>
    </resultMap>



    <select id="queryList" resultMap="coachSchedulingResultMap" parameterType="com.jky.boot.core.module.coachScheduling.dto.CoachSchedulingDto">
        select f3.*, if(SUBSTRING_INDEX(today_date, ':', 1)  > 12,2,1) time
        from t_m_coach f2
        left join scheduling_config f1
        on f1.inscode = f2.inscode
        join coach_scheduling f3
        on f3.coach_num = f2.coachnum
        where date between  DATE_FORMAT(#{coachSchedulingDto.startDay}, '%Y-%m-%d') and DATE_FORMAT(#{coachSchedulingDto.endDay}, '%Y-%m-%d')
        <if test="coachSchedulingDto.coachName != null and coachSchedulingDto.coachName != ''">
            and f3.coach_name = #{coachSchedulingDto.coachName}
        </if>
        <if test="coachSchedulingDto.coachNum != null and coachSchedulingDto.coachNum != ''">
            and f3.coach_num = #{coachSchedulingDto.coachNum}
        </if>
        <if test="coachSchedulingDto.idCard != null and coachSchedulingDto.idCard != ''">
            and f3.id_card = #{coachSchedulingDto.idCard}
        </if>
        <if test="inscodes != null">
            and f2.inscode in
            <foreach collection="inscodes" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        and f3.course not in (1,2)
        and f2.teachtype = 4
        order by f3.coach_num,date, CAST(SUBSTRING_INDEX(today_date, ':', 1) AS UNSIGNED)
    </select>

    <select id="queryTitle" resultType="com.jky.boot.core.module.coachScheduling.entity.CoachSchedulingTitle">
        select date,sum(order_num) order_num from t_m_coach f2
        left join scheduling_config f1
        on f1.inscode = f2.inscode
        join coach_scheduling f3
        on f3.coach_num = f2.coachnum
        where date between DATE_FORMAT(#{coachSchedulingDto.startDay}, '%Y-%m-%d') and DATE_FORMAT(#{coachSchedulingDto.endDay}, '%Y-%m-%d')
        and f2.inscode in
        <foreach collection="inscodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="coachSchedulingDto.coachName != null and coachSchedulingDto.coachName != ''">
            and f3.coach_name = #{coachSchedulingDto.coachName}
        </if>
        <if test="coachSchedulingDto.coachNum != null and coachSchedulingDto.coachNum != ''">
            and f3.coach_num = #{coachSchedulingDto.coachNum}
        </if>
        <if test="coachSchedulingDto.idCard != null and coachSchedulingDto.idCard != ''">
            and f3.id_card = #{coachSchedulingDto.idCard}
        </if>
        and f3.course not in (1,2)
        and f2.teachtype = 4
        group by date
        order by date
    </select>

    <select id="getList" resultType="com.jky.boot.core.module.coachScheduling.entity.SchedulingConfig">
        select ifnull(f2.inscode, f1.inscode) inscode,worker_start_time,rest_start_time,rest_end_time,worker_end_time,
        week_rest_days,operation_max,imitate_max
        from t_m_institution f1 left join scheduling_config f2 on f1.INSCODE = f2.INSCODE
        where 1 = 1
        <if test="sql != null">
            ${sql}
        </if>
    </select>

    <update id="deleteOldScheduling">
        delete f1 from coach_scheduling f1 join t_m_coach f2 on f1.coach_num = f2.coachnum
        where f2.inscode = #{inscode} and date &gt;= DATE_FORMAT(#{startDay}, '%Y-%m-%d') and date &lt;= DATE_FORMAT(#{endDay}, '%Y-%m-%d')
    </update>

    <select id="ifOrder" resultType="com.jky.boot.core.module.coachScheduling.entity.CoachScheduling">
        select  * from coach_scheduling where date &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d') and date &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d') and class_room_id = #{classRoomId}
    </select>

    <update id="deleteOldSchedulingByCoach">
        delete from coach_scheduling
        where coach_num = #{coachnum} and date &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d') and date &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d')
    </update>
<!--加上教练员只有课堂和模拟的-->
    <select id="alllist" resultType="com.jky.boot.core.module.coachScheduling.vo.DisOperationCoachSchedulingVo">
        select f1.*,f2.COMPANY_NAME,f2.ADDRESS from coach_scheduling f1
        join t_m_ins_device f2 on f2.INS_DEVICE_ID = f1.class_room_id
        join t_m_coach f3 on f3.coachnum = f1.coach_num
        where 1 = 1
        and f3.inscode in
        <foreach collection="inscodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="coachSchedulingDto.coachName != null and coachSchedulingDto.coachName != ''">
            and f1.coach_name = #{coachSchedulingDto.coachName}
        </if>
        <if test="coachSchedulingDto.coachNum != null and coachSchedulingDto.coachNum != ''">
            and f1.coach_num = #{coachSchedulingDto.coachNum}
        </if>
        <if test="coachSchedulingDto.idCard != null and coachSchedulingDto.idCard != ''">
            and f1.id_card = #{coachSchedulingDto.idCard}
        </if>
        <if test="coachSchedulingDto.classRoomId != null and coachSchedulingDto.classRoomId != ''">
            and f1.class_room_id = #{coachSchedulingDto.classRoomId}
        </if>
        <if test="coachSchedulingDto.course != null and coachSchedulingDto.course != ''">
            and f1.course = #{coachSchedulingDto.course}
        </if>
        <if test="coachSchedulingDto.startDay != null">
            and f1.date BETWEEN #{coachSchedulingDto.startDay} and #{coachSchedulingDto.endDay}
        </if>
        and course != 4
        order by date, coach_num
    </select>

    <select id="imiList" resultMap="imitateSchedResultMap">
        select f1.*, if(SUBSTRING_INDEX(today_date, ':', 1)  > 12,2,1) time, f2.COMPANY_NAME, f2.ADDRESS
        from coach_scheduling f1
        join t_m_ins_device f2 on f2.INS_DEVICE_ID = f1.class_room_id
        where f2.INSCODE_LIST like concat('%', #{coachSchedulingDto.inscode}, '%')
        <if test="coachSchedulingDto.coachName != null and coachSchedulingDto.coachName != ''">
            and f1.coach_name = #{coachSchedulingDto.coachName}
        </if>
        <if test="coachSchedulingDto.coachNum != null and coachSchedulingDto.coachNum != ''">
            and f1.coach_num = #{coachSchedulingDto.coachNum}
        </if>
        <if test="coachSchedulingDto.idCard != null and coachSchedulingDto.idCard != ''">
            and f1.id_card = #{coachSchedulingDto.idCard}
        </if>
        <if test="coachSchedulingDto.classRoomId != null and coachSchedulingDto.classRoomId != ''">
            and f1.class_room_id = #{coachSchedulingDto.classRoomId}
        </if>
        <if test="coachSchedulingDto.course != null and coachSchedulingDto.course != ''">
            and f1.course = #{coachSchedulingDto.course}
        </if>
        <if test="coachSchedulingDto.startDay != null and coachSchedulingDto.endDay != null">
            and f1.date BETWEEN DATE_FORMAT(#{coachSchedulingDto.startDay}, '%Y-%m-%d') and DATE_FORMAT(#{coachSchedulingDto.endDay}, '%Y-%m-%d')
        </if>
        and course != 4
        order by date, coach_num
    </select>

    <select id="isTeache" resultType="com.jky.boot.core.module.coachScheduling.entity.CoachScheduling">
        select  * from coach_scheduling where date &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d') and date &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d') and coach_num = #{coachnum}
    </select>


</mapper>
