package com.jky.boot.core.module.gzpt.utils;

import org.apache.commons.lang.StringUtils;

import java.util.UUID;

public class StringUtil {
    /*uuid*/
    public static String getUuid() {
        String s = UUID.randomUUID().toString();
        // 去掉"-"符号的UUID
        return s.replace("-", "").trim();
    }

    public static String getUUID16(){
        //1.UUID生成32位数
        String uuid32 = UUID.randomUUID().toString().replace("-", "");
        //2.然后截取前面或后面16位
        String uuid16 = uuid32.substring(0, 16);
        return uuid16;
    }

    //将1,2,3 转换成 ‘1’,‘2’,‘3’
    public static String joinString(String str) {
        String result = "";
        if (str.indexOf(",") == -1) {
            return "'" + str + "'";
        }
        String[] split = str.split(",");
        for (String str1 : split) {
            if (StringUtils.isEmpty(result)) {
                result = "'" + str1 + "'";
            } else {
                result += ",'" + str1 + "'";
            }
        }
        return result;
    }
}
