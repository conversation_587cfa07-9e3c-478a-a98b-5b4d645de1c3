package com.jky.boot.zlb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_transfer
 * @Author: jeecg-boot
 * @Date:   2023-06-19
 * @Version: V1.0
 */
@Data
@TableName("t_m_transfer")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_transfer对象", description="t_m_transfer")
public class TMTransfer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private java.lang.String stunum;
	/**学员姓名*/
	@Excel(name = "学员姓名", width = 15)
    @ApiModelProperty(value = "学员姓名")
    private java.lang.String name;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private java.lang.String idcard;
	/**原驾校*/
	@Excel(name = "原驾校", width = 15)
    @ApiModelProperty(value = "原驾校")
    private java.lang.String oldInscode;
	/**oldInsname*/
	@Excel(name = "oldInsname", width = 15)
    @ApiModelProperty(value = "oldInsname")
    private java.lang.String oldInsname;
	/**待转入驾校*/
	@Excel(name = "待转入驾校", width = 15)
    @ApiModelProperty(value = "待转入驾校")
    private java.lang.String inscode;
	/**insname*/
	@Excel(name = "insname", width = 15)
    @ApiModelProperty(value = "insname")
    private java.lang.String insname;
	/**状态 0-未审核 1-同意转入 2-不同意*/
	@Excel(name = "状态 0-未审核 1-同意转入 2-不同意", width = 15)
    @ApiModelProperty(value = "状态 0-未审核 1-同意转入 2-不同意")
    private Integer status;
	/**审核时间*/
	@Excel(name = "审核时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date auditDate;
	/**不同意原因*/
	@Excel(name = "不同意原因", width = 15)
    @ApiModelProperty(value = "不同意原因")
    private java.lang.String reason;
	/**申请时间*/
	@Excel(name = "申请时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "申请时间")
    private java.util.Date crdate;
	/**备案平台*/
	@Excel(name = "备案平台", width = 15)
    @ApiModelProperty(value = "备案平台")
    private java.lang.String platfrom;
	/**县区编号*/
	@Excel(name = "县区编号", width = 15)
    @ApiModelProperty(value = "县区编号")
    private java.lang.String district;
	/**审核人id*/
	@Excel(name = "审核人id", width = 15)
    @ApiModelProperty(value = "审核人id")
    private java.lang.String operator;
	/**operatorName*/
	@Excel(name = "operatorName", width = 15)
    @ApiModelProperty(value = "operatorName")
    private java.lang.String operatorName;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private java.lang.String createBy;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private java.util.Date createTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private java.lang.String updateBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private java.util.Date updateTime;
}
