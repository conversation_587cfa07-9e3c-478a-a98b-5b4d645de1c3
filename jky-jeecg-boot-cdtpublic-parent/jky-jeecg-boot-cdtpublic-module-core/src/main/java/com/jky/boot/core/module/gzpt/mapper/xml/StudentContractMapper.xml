<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.StudentContractMapper">

    <resultMap type="com.jky.boot.core.module.gzpt.entity.StudentContract" id="StudentContractResult">
        <result property="stunum" column="stunum"/>
        <result property="pdfurl" column="pdfurl"/>
        <result property="signtime" column="signtime"/>
        <result property="isSign" column="is_sign"/>
        <result property="basicCost" column="basic_cost"/>
        <result property="secondCost" column="second_cost"/>
        <result property="thirdCoast" column="third_coast"/>
        <result property="frozenAmount" column="frozen_amount"/>
    </resultMap>

    <sql id="selectStudentContractVo">
        select stunum, pdfurl, signtime,is_sign, basic_cost, second_cost, third_coast, frozen_amount
        from t_m_student_contract
    </sql>

    <select id="selectStudentContractByStunum" parameterType="String" resultMap="StudentContractResult">
        <include refid="selectStudentContractVo"/>
        where stunum = #{stunum}
    </select>

    <select id="selectConById" resultType="com.jky.boot.core.module.gzpt.entity.StudentContract">
        SELECT PDFURL from t_m_student_contract where STUNUM in
                                                     (SELECT STUNUM from t_m_studentinfo where IDCARD in
                                                                                                     (SELECT idnum FROM `base_zlb_person_info`where id = #{id}))
    </select>



</mapper>
