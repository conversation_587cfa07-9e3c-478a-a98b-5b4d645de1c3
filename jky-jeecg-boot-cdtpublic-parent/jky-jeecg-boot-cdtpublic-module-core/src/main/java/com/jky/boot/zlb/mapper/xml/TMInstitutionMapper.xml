<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.zlb.mapper.TMInstitutionMapper">
    <select id="instlist" resultType="com.jky.boot.zlb.entity.TMInstitution">
        select t.*,f.ins_total_fraction from
                (select * from t_m_institution where DISTRICT  like CONCAT(#{district},'%') ) t
                    left join t_m_institution_fraction f
                              on t.INSCODE = f.inscode
            where t.shelve = 1
        order by f.ins_total_fraction desc
    </select>


    <select id="inlist" resultType="com.jky.boot.zlb.entity.TMInstitution">
        select NAME,ADDRESS,mark ,rate ,evaluate ,penalize,intro  from t_m_institution where INSCODE = #{inscode}
    </select>

</mapper>