package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.TMMr890chLog;
import com.jky.boot.core.module.gzpt.service.ITMMr890chLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

/**
 * @Description: t_m_mr890ch_log
 * @Author: jeecg-boot
 * @Date:   2024-01-18
 * @Version: V1.0
 */
@Api(tags="t_m_mr890ch_log")
@RestController
@RequestMapping("/gzpt/tMMr890chLog")
@Slf4j
public class TMMr890chLogController extends JeecgController<TMMr890chLog, ITMMr890chLogService> {
	@Autowired
	private ITMMr890chLogService tMMr890chLogService;

	/**
	 * 分页列表查询
	 *
	 * @param tMMr890chLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_mr890ch_log-分页列表查询")
	@ApiOperation(value="t_m_mr890ch_log-分页列表查询", notes="t_m_mr890ch_log-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMMr890chLog>> queryPageList(TMMr890chLog tMMr890chLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		if (Objects.nonNull(tMMr890chLog.getLogContent())){
			tMMr890chLog.setLogContent("*"+tMMr890chLog.getLogContent()+"*");
		}
		QueryWrapper<TMMr890chLog> queryWrapper = QueryGenerator.initQueryWrapper(tMMr890chLog, req.getParameterMap());
		if (Objects.nonNull(tMMr890chLog.getCreateTimeBegin())) {
			queryWrapper.ge("create_time", tMMr890chLog.getCreateTimeBegin()+" 00:00:00");
		}
		if (Objects.nonNull(tMMr890chLog.getCreateTimeEnd())) {
			queryWrapper.le("create_time", tMMr890chLog.getCreateTimeEnd()+" 23:59:59");
		}
		queryWrapper.orderByDesc("create_time");
		Page<TMMr890chLog> page = new Page<TMMr890chLog>(pageNo, pageSize);
		IPage<TMMr890chLog> pageList = tMMr890chLogService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param tMMr890chLog
	 * @return
	 */
	@AutoLog(value = "t_m_mr890ch_log-添加")
	@ApiOperation(value="t_m_mr890ch_log-添加", notes="t_m_mr890ch_log-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_mr890ch_log:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMMr890chLog tMMr890chLog) {
		tMMr890chLogService.save(tMMr890chLog);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param tMMr890chLog
	 * @return
	 */
	@AutoLog(value = "t_m_mr890ch_log-编辑")
	@ApiOperation(value="t_m_mr890ch_log-编辑", notes="t_m_mr890ch_log-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_mr890ch_log:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMMr890chLog tMMr890chLog) {
		tMMr890chLogService.updateById(tMMr890chLog);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_mr890ch_log-通过id删除")
	@ApiOperation(value="t_m_mr890ch_log-通过id删除", notes="t_m_mr890ch_log-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_mr890ch_log:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tMMr890chLogService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_mr890ch_log-批量删除")
	@ApiOperation(value="t_m_mr890ch_log-批量删除", notes="t_m_mr890ch_log-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_mr890ch_log:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tMMr890chLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "t_m_mr890ch_log-通过id查询")
	@ApiOperation(value="t_m_mr890ch_log-通过id查询", notes="t_m_mr890ch_log-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMMr890chLog> queryById(@RequestParam(name="id",required=true) String id) {
		TMMr890chLog tMMr890chLog = tMMr890chLogService.getById(id);
		if(tMMr890chLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMMr890chLog);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMMr890chLog
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_mr890ch_log:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMMr890chLog tMMr890chLog) {
//        return super.exportXls(request, tMMr890chLog, TMMr890chLog.class, "t_m_mr890ch_log");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_mr890ch_log:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMMr890chLog.class);
//    }

}
