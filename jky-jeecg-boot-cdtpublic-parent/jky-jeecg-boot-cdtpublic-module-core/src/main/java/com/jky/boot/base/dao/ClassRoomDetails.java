package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;

import java.util.Date;

/**
 * 教室信息维护
 *
 * <AUTHOR>
 * @version 2025-01-21
 */
@Data
@TableName("class_room_details")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ClassRoomDetails {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 驾校编号
     */
    @Dict(dictTable = "t_m_institution", dicText = "NAME", dicCode = "INSCODE")
    private String inscode;
    /**
     * 教室类型
     */
    private Integer classRoomType;
    /**
     * 教室名称
     */
    private String classRoomName;
    /**
     * 地址
     */
    private String address;
    /**
     * 学院容量
     */
    private Integer stuAmount;
    /**
     * 创建时间
     */
    private Date createTime;
}
