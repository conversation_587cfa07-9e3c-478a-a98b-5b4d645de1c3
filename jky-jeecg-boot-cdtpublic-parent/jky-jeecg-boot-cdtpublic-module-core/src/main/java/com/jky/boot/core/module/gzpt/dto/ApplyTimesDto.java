package com.jky.boot.core.module.gzpt.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 学员各部分预约次数
 *
 * <AUTHOR>
 * @version 2024-11-22 11:17
 */
@Data
@Accessors(chain = true)
public class ApplyTimesDto {

    /**
     * 科二实操预约次数
     */
    private Integer sub2OprApplyTimes;

    /**
     * 科三实操预约次数
     */
    private Integer sub3OprApplyTimes;

    /**
     * 课堂预约次数
     */
    private Integer classApplyTimes;

    /**
     * 模拟预约次数
     */
    private Integer imiApplyTimes;
}
