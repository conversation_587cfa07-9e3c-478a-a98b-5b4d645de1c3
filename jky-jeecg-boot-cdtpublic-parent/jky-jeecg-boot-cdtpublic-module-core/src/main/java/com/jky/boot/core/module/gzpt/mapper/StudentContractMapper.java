package com.jky.boot.core.module.gzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.boot.core.module.gzpt.entity.StudentContract;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Description: 学员合同表
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface StudentContractMapper extends BaseMapper<StudentContract> {

    @Select("select * from t_m_student_contract where stunum = #{stunum}")
    StudentContract selectOneByStunum(@Param("stunum") String stunum);

    IPage<StudentContract> selectConById(IPage<?> page, @Param("id")String id);
}
