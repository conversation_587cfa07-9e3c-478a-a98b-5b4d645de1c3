package com.jky.boot.core.module.gzpt.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;

import java.math.BigDecimal;
import java.util.List;

@Data
public class InstitutionVo {
    /**主键*/
    @ApiModelProperty(value = "主键")
    private String id;
    /**区县行政区划代码*/
    @ApiModelProperty(value = "区县行政区划代码")
    @Dict(dictTable = "sys_area_new",dicCode = "AREA_ID",dicText = "AREA_NAME")
    private String district;
    /**培训机构全称*/
    @ApiModelProperty(value = "培训机构全称")
    private String name;
    /**培训机构简称*/
    @ApiModelProperty(value = "培训机构简称")
    private String shortname;
    /**培训机构编号*/
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private Double longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private Double latitude;
    /**距离*/
    @ApiModelProperty(value = "距离(单位：KM)")
    private Double distance;

    /**累计评分*/
    @ApiModelProperty(value = "累计评分")
    private BigDecimal totalFraction;
    /**驾校信用分*/
    @ApiModelProperty(value = "驾校信用分")
    private BigDecimal insTotalFraction;
    /**信用等级*/
    @ApiModelProperty(value = "信用等级")
    private String creditLevel;
    /** 评价*/
    @ApiModelProperty(value = "评价")
    private String evaluates;
    /** 最低套餐名称*/
    @ApiModelProperty(value = "最低套餐名称")
    private String className;
    /** 最低套餐价格*/
    @ApiModelProperty(value = "最低套餐价格")
    private String price;

    /** 驾校图片*/
    private String logo;
    //0重点关注，1重点监管,2 重点关注且重点监管
    private Integer supervise;

    private Double examPassRate;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 风险提示
     */
    private String riskNotice;

    private List<AverageCostVo> average;

    //驾校锁定 0不锁定 1锁定
    private Integer islock;

    private Integer flag;
}
