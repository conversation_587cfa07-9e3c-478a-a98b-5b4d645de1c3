package com.jky.boot.core.module.gzpt.utils;

import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class PlatformUtils {
    public static Map<String, String> MAP = new HashMap<>();
    private final IPlatformService platformService;

    public PlatformUtils(IPlatformService platformService) {
        this.platformService = platformService;
    }

    @PostConstruct
    public void init() {
        List<Platform> list = platformService.list();
        for (Platform one : list) {
            MAP.put(one.getSerialNumber(), one.getPlatformName());
        }
    }

    public static String errMsg(String serialNum, String res) {
        String platForm = PlatformUtils.MAP.get(serialNum);
        if (StringUtils.isBlank(res)) {
            return String.format("推送%s计时异常，请稍后再试或者联系相关计时人员!", platForm);
        }
        return String.format("推送%s计时异常，请稍后再试或者联系相关计时人员! 计时平台提示:[%s]", platForm, res);
    }
}
