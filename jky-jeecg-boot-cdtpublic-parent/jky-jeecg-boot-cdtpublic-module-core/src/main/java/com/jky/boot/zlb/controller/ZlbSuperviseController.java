package com.jky.boot.zlb.controller;

import com.jky.boot.core.module.gzpt.service.ICdtManageService;
import com.jky.boot.zlb.entity.TMSupervise;
import com.jky.boot.zlb.service.ITMSuperviseService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "t_m_supervise")
@RestController
@RequestMapping("/zlb/supervise")
@Slf4j
public class ZlbSuperviseController extends JeecgController<TMSupervise, ITMSuperviseService> {

    @Autowired
    private ICdtManageService cdtManageService;

//    @GetMapping(value = "/getSuperviseAndAuthrecord")
//    public Result<Map<String, Object>> getSuperviseAndAuthrecord() {
//        Map<String, Object> superviseAndAuthrecordMap = cdtManageService.getSuperviseAndAuthrecord();
//        return Result.OK(superviseAndAuthrecordMap);
//    }
}
