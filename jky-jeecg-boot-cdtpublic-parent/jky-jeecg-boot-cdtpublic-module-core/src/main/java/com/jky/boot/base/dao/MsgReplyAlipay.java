package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 支付宝消息表
 *
 * <AUTHOR>
 * @version 2024-12-31
 */
@Data
@TableName("t_m_msg_reply_alipay")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MsgReplyAlipay {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 学员编号
     */
    private String stunum;
    /**
     * 消息类型
     */
    private String msgApi;
    /**
     * 消息id
     */
    private String msgId;
    /**
     * 消息内容
     */
    private String bizContent;
    /**
     * 接收时间
     */
    private Date createTime;
}
