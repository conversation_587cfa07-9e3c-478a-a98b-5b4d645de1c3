<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.base.mapper.SchedulingApplyMapper">

    <select id="oprPage" resultType="com.jky.boot.core.module.reservation.vo.ZlbSchApplyVo">
        SELECT a.*, opr.inscode, opr.coachnum, opr.traintype, opr.subject, opr.class_date, opr.class_time
        from scheduling_apply as a
        inner join scheduling_opr as opr
        on a.class_id = opr.id
        <where>
            <if test="rec.stunum != null and rec.stunum != ''">and a.stunum = #{rec.stunum}</if>
            <if test="rec.inscodes != null ">
                and opr.inscode in
                <foreach collection="rec.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="rec.coachnum != null and rec.coachnum != ''">and opr.coachnum = #{rec.coachnum}</if>
            <if test="rec.classDateBegin != null">and opr.class_date >= #{rec.classDateBegin}</if>
            <if test="rec.classDateEnd != null">and #{rec.classDateEnd} >= opr.class_date</if>
            <if test="rec.traintype != null and rec.traintype != ''">and opr.traintype = #{rec.traintype}</if>
            <if test="rec.cancel != null">and a.cancel = #{rec.cancel}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="classPage" resultType="com.jky.boot.core.module.reservation.vo.ZlbSchApplyVo">
        SELECT a.*, class.inscode, class.coachnum, class.traintype, class.subject, class.class_date, class.class_time, class.class_hours
        from scheduling_apply as a
        inner join scheduling_class as class
        on a.class_id = class.id
        <where>
            <if test="rec.stunum != null and rec.stunum != ''">and a.stunum = #{rec.stunum}</if>
            <if test="rec.inscodes != null ">
                and class.inscode in
                <foreach collection="rec.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="rec.coachnum != null and rec.coachnum != ''">and class.coachnum = #{rec.coachnum}</if>
            <if test="rec.classDateBegin != null">and class.class_date >= #{rec.classDateBegin}</if>
            <if test="rec.classDateEnd != null">and #{rec.classDateEnd} >= class.class_date</if>
            <if test="rec.traintype != null and rec.traintype != ''">and class.traintype = #{rec.traintype}</if>
            <if test="rec.cancel != null">and a.cancel = #{rec.cancel}</if>
        </where>
        order by a.create_time desc
    </select>

    <select id="imiPage" resultType="com.jky.boot.core.module.reservation.vo.ZlbSchApplyVo">
        SELECT a.*, imi.inscode, imi.coachnum, imi.traintype, imi.subject, imi.class_date, imi.class_time, imi.class_hours
        from scheduling_apply as a
        inner join scheduling_imi as imi
        on a.class_id = imi.id
        <where>
            <if test="rec.stunum != null and rec.stunum != ''">and a.stunum = #{rec.stunum}</if>
            <if test="rec.inscodes != null ">
                and imi.inscode in
                <foreach collection="rec.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="rec.coachnum != null and rec.coachnum != ''">and imi.coachnum = #{rec.coachnum}</if>
            <if test="rec.classDateBegin != null">and imi.class_date >= #{rec.classDateBegin}</if>
            <if test="rec.classDateEnd != null">and #{rec.classDateEnd} >= imi.class_date</if>
            <if test="rec.traintype != null and rec.traintype != ''">and imi.traintype = #{rec.traintype}</if>
            <if test="rec.cancel != null">and a.cancel = #{rec.cancel}</if>
        </where>
        order by a.create_time desc
    </select>
</mapper>
