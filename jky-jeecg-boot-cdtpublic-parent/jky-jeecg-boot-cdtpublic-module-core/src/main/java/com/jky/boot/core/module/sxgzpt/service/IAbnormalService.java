package com.jky.boot.core.module.sxgzpt.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.sxgzpt.entity.Abnormal;

import java.util.List;
import java.util.Map;

/**
 * 异常组管理Service接口
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
public interface IAbnormalService extends IService<Abnormal> {

    /**
     * 查询异常组管理列表
     *
     * @param abnormal 异常组管理
     * @return 异常组管理集合
     */
    public List<Abnormal> selectAbnormalList(Abnormal abnormal);


    public List<Map<String, Object>> getAbnormal();
}
