package com.jky.boot.bank.handler;

import com.alipay.api.AlipayConfig;
import com.alipay.api.msg.AlipayMsgClient;
import com.jky.boot.bank.utils.AlipayUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 消息接收配置
 *
 * <AUTHOR>
 * @version 2024-12-12 16:02
 */
@Configuration
@Profile("prod")
@Slf4j
public class AlipayMsgConfig {
    @Bean
    @ConditionalOnProperty(prefix = "alipay", name = "subscribe", havingValue = "true")
    public AlipayMsgClient alipayMsgClient(CallbackHandler handler) throws Exception {
        AlipayConfig config = AlipayUtils.getAlipayConfig();
        String appId = config.getAppId();
        // 目标支付宝服务端地址
        String serverHost = "openchannel.alipay.com";
        // 数据签名方式，请与应用设置的默认签名方式保持一致
        String signType = config.getSignType();
        // 应用私钥
        String appPrivateKey = config.getPrivateKey();
        // 支付宝公钥
        String alipayPublicKey = config.getAlipayPublicKey();
        // 获取client对象，一个appId对应一个实例
        final AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(appId);
        alipayMsgClient.setConnector(serverHost);
        alipayMsgClient.setSecurityConfig(signType, appPrivateKey, alipayPublicKey);
        alipayMsgClient.setMessageHandler(handler);
        alipayMsgClient.connect();
        log.info("开始订阅alipay");
        return alipayMsgClient;
    }
}
