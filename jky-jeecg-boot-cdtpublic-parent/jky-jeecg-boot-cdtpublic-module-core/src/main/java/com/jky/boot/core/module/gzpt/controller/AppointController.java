package com.jky.boot.core.module.gzpt.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.entity.Carinfo;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jky.boot.core.module.gzpt.entity.Appoint;
import com.jky.boot.core.module.gzpt.service.IAppointService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: t_m_appoint
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="t_m_appoint")
@RestController
@RequestMapping("/gzpt/appoint")
@Slf4j
public class AppointController extends JeecgController<Appoint, IAppointService> {
	@Autowired
	private IAppointService appointService;

	@Autowired
	private JkySysDepartService jkySysDepartService;
	
	/**
	 * 分页列表查询
	 *
	 * @param appoint
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_appoint-分页列表查询")
	@ApiOperation(value="t_m_appoint-分页列表查询", notes="t_m_appoint-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<Appoint>> queryPageList(Appoint appoint,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(appoint.getInscode());
		appoint.setInscode(null);
		QueryWrapper<Appoint> queryWrapper = QueryGenerator.initQueryWrapper(appoint, req.getParameterMap());
		queryWrapper.in("inscode",inscode);
		queryWrapper.orderByDesc("create_time");
		Page<Appoint> page = new Page<>(pageNo, pageSize);
		IPage<Appoint> pageList = appointService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param appoint
	 * @return
	 */
	@AutoLog(value = "t_m_appoint-添加")
	@ApiOperation(value="t_m_appoint-添加", notes="t_m_appoint-添加")
	@RequiresPermissions("gzpt:appoint:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Appoint appoint) {
		JkySysDepart depart = jkySysDepartService.getById(appoint.getInscode());
		LambdaQueryWrapper<Appoint> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(Appoint::getInscode, depart.getOrgCode());
		List<Appoint> list = appointService.list(wrapper);
		if(CollectionUtils.isNotEmpty(list)){
			return Result.error("该驾校合同其它内容已添加！");
		}
		appoint.setInscode(depart.getOrgCode());
		appoint.setInsname(depart.getDepartName());
		appointService.insertAppoint(appoint);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param appoint
	 * @return
	 */
	@AutoLog(value = "t_m_appoint-编辑")
	@ApiOperation(value="t_m_appoint-编辑", notes="t_m_appoint-编辑")
	@RequiresPermissions("gzpt:appoint:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Appoint appoint) {
		JkySysDepart depart = jkySysDepartService.selectDeptByleader(appoint.getInscode());
		appoint.setInscode(depart.getOrgCode());
		appointService.updateAppoint(appoint);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_appoint-通过id删除")
	@ApiOperation(value="t_m_appoint-通过id删除", notes="t_m_appoint-通过id删除")
	@RequiresPermissions("gzpt:appoint:remove")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		appointService.deleteAppointById(Long.parseLong(id));
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_appoint-批量删除")
	@ApiOperation(value="t_m_appoint-批量删除", notes="t_m_appoint-批量删除")
	@RequiresPermissions("gzpt:appoint:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.appointService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "t_m_appoint-通过id查询")
	@ApiOperation(value="t_m_appoint-通过id查询", notes="t_m_appoint-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<Appoint> queryById(@RequestParam(name="id",required=true) String id) {
		Appoint appoint = appointService.selectAppointById(Long.parseLong(id));
		if(appoint==null) {
			return Result.error("未找到对应数据");
		}
		JkySysDepart depart = jkySysDepartService.selectDeptByleader(appoint.getInscode());
		appoint.setInscode(depart.getOrgCode());
		return Result.ok(appoint);
	}
}
