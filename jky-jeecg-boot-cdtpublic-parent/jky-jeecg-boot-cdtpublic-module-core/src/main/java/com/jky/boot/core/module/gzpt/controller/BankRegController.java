package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.common.utils.DateIdUtil;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.PushBankUtil;
import com.jky.boot.system.module.manage.entity.JkySysDictData;
import com.jky.boot.system.module.manage.mapper.JkySysDictDataMapper;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.common.util.StringUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 学员开户冻结对象
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Api(tags = "学员开户冻结对象")
@RestController
@RequestMapping("/gzpt/bankReg")
@Slf4j
public class BankRegController extends JeecgController<BankReg, IBankRegService> {
    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IStudentinfoService iStudentinfoService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private JkySysDictDataMapper sysDictMapper;
    @Autowired
    private RedissonLockClient redissonLock;
    @Autowired
    private IStuTransferService stuTransferService;

    private Map<String, String> bankcode = new HashMap<>();

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<IPage<BankReg>> queryPageList(BankReg bankReg,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = sysUser.getUsername();
        bankcode.put("x!Jq4Rj2ct4#", "SZDBCNBS");
        bankcode.put("Lsjs6D!9v&cN", "9IC3C55B");
        bankcode.put("t2^aP#2#D$N2", "06C3CRCB");
        String s = bankcode.get(username);
        if (StringUtils.isNotBlank(s)) {
            bankReg.setBankcode(s);
        }
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(bankReg.getInscode(), bankReg.getInscodes());
        bankReg.setInscode(null);
        bankReg.setInscodes(inscode);

        if (StringUtils.isNotBlank(bankReg.getIdcard())) {
            LambdaQueryWrapper<Studentinfo> eq = Wrappers.<Studentinfo>lambdaQuery(Studentinfo.class)
                    .eq(StringUtils.isNotBlank(bankReg.getIdcard()), Studentinfo::getIdcard, bankReg.getIdcard())
                    .last("limit 1");
            List<Studentinfo> list = iStudentinfoService.list(eq);
            if (CollectionUtils.isEmpty(list)) {
                return Result.OK(new Page<>());
            }
            bankReg.setStunum(list.get(0).getStunum());
        }
        Page<BankReg> page = new Page<BankReg>(pageNo, pageSize);
        IPage<BankReg> list = bankRegService.selectBankRegPage(page, bankReg);
        if(CollectionUtils.isEmpty(list.getRecords()))return Result.OK(new Page<>());

        List<String> stuList = list.getRecords().stream().map(BankReg::getStunum).collect(Collectors.toList());
        List<Studentinfo> supplier = iStudentinfoService.list(Wrappers.<Studentinfo>lambdaQuery(Studentinfo.class)
                .in(Studentinfo::getStunum, stuList));
        Map<String, Studentinfo> supplierMap = supplier.stream().collect(Collectors.toMap(Studentinfo::getStunum, a -> a, (v1, v2) -> v1));
        list.getRecords().forEach(a->{
            if(supplierMap.containsKey(a.getStunum())){
                a.setIdcard(supplierMap.get(a.getStunum()).getIdcard());
            }
        });
        return Result.OK(list);
    }

    /**
     * 添加
     *
     * @param bankReg
     * @return
     */
    @AutoLog(value = "学员开户冻结对象-添加")
    @ApiOperation(value = "学员开户冻结对象-添加", notes = "学员开户冻结对象-添加")
    @RequiresPermissions("gzpt:reg:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody BankReg bankReg) {
        bankRegService.save(bankReg);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param bankReg
     * @return
     */
    @AutoLog(value = "学员开户冻结对象-编辑")
    @ApiOperation(value = "学员开户冻结对象-编辑", notes = "学员开户冻结对象-编辑")
    @RequiresPermissions("gzpt:reg:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody BankReg bankReg) {
        bankRegService.updateById(bankReg);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "学员开户冻结对象-通过id删除")
    @ApiOperation(value = "学员开户冻结对象-通过id删除", notes = "学员开户冻结对象-通过id删除")
    @RequiresPermissions("gzpt:reg:remove")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        bankRegService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "学员开户冻结对象-批量删除")
    @ApiOperation(value = "学员开户冻结对象-批量删除", notes = "学员开户冻结对象-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_reg:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.bankRegService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "学员开户冻结对象-通过id查询")
    @ApiOperation(value = "学员开户冻结对象-通过id查询", notes = "学员开户冻结对象-通过id查询")
    @GetMapping(value = "/details")
    @RequiresPermissions("gzpt:reg:query")
    public Result<BankReg> queryById(@RequestParam(name = "id", required = true) String id) {
        BankReg bankReg = bankRegService.getById(id);
        if (bankReg == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(bankReg);
    }

    @ApiOperation(value = "查询余额")
    @GetMapping("/queryAmount")
    @RequiresPermissions("gzpt:reg:queryAmount")
    public Result queryAmount(@RequestParam(name = "ids", required = true) String ids) {
        try {
            bankRegService.queryAmount(ids.split(","));
            return Result.OK();
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("查询失败");
        }

    }

    @ApiOperation(value = "查询交易记录")
    @GetMapping("/queryTransaction")
    @RequiresPermissions("gzpt:reg:queryTransaction")
    public Result queryTransaction(@RequestParam(name = "ids", required = true) String ids) {
        try {
            bankRegService.queryTransaction(ids.split(","));
            return Result.OK();
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error("查询失败");
        }

    }

    @ApiOperation(value = "建行冻结金额")
    @GetMapping("/frozen")
    @RequiresPermissions("gzpt:reg:jhfrozen")
    public Result frozen(@RequestParam(name = "id") String id) {
        String key = "jhfrozen-" + id;
        try {
            if (redissonLock.existKey(key)) {
                return Result.error("冻结中，请稍后再试！！");
            }
            if (redissonLock.tryLock(key, -1, 10000)) {
                BankReg bankReg = bankRegService.getById(id);
                if (!"9IC3C55B".equals(bankReg.getBankcode())) {
                    return Result.error("只有建行学员才能使用该功能");
                }
                if (bankReg.getStatus() != 1 && bankReg.getStatus() != 5) {
                    return Result.error("只有开户成功或者冻结失败才重新冻结！");
                }

                JSONObject json = new JSONObject();
                Studentinfo studentinfo = new Studentinfo();
                studentinfo.setStunum(bankReg.getStunum());
                LambdaQueryWrapper<Studentinfo> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(Studentinfo::getStunum, bankReg.getStunum());
                List<Studentinfo> studentinfos = iStudentinfoService.list(queryWrapper);
                if (studentinfos.size() == 0) {
                    return Result.error("学员信息不存在!");
                }
                String corpseqno = DateIdUtil.getSeq();
                json.put("corpseqno", corpseqno);
                json.put("amt", studentinfos.get(0).getFrozenAmount());
                if (Objects.isNull(studentinfos.get(0).getFrozenAmount()) || Double.parseDouble(studentinfos.get(0).getFrozenAmount()) == 0) {
                    return Result.error("冻结失败,请确认学员冻结金额是否为0,或是空");
                }
                json.put("stunum", bankReg.getStunum());
                String s = PushBankUtil.pushFundTransfer(2, json, bankReg.getBankcode());
                JSONObject jsonObject = JSONObject.parseObject(s);
                if (Integer.valueOf(jsonObject.get("errorcode").toString()) == 0) {
                    bankReg.setStatus(4);
                    bankRegService.updateById(bankReg);
                } else {
                    return Result.error("冻结信息推送银行失败");
                }
                return Result.OK();
            } else {
                return Result.error("冻结中，请稍后再试！！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("冻结失败");
        } finally {
            redissonLock.unlock(key);
        }
    }

    @ApiOperation(value = "嘉兴冻结金额")
    @GetMapping("/jxfrozen")
    @RequiresPermissions("gzpt:reg:jxfrozen")
    public Result jxfrozen(@RequestParam(name = "id") String id,Integer frozenamount) throws IOException {
        String key = "jxfrozen-" + id;
        if (redissonLock.existKey(key)) {
            return Result.error("冻结中，请稍后再试！！");
        }
        if (frozenamount == null){
            return Result.error("冻结金额不能为空");
        }
        if (redissonLock.tryLock(key, -1, 10000)) {
            try {
                BankReg bankReg = bankRegService.getById(id);

                if (bankReg.getStatus() == 3 || bankReg.getStatus() == 4 || bankReg.getStatus() == 0) {
                    return Result.error("请先确认是否开户成功！开户成功后，再操作冻结，或确认冻结状态是否重复冻结");
                }
                bankRegService.capitalFreeze(bankReg,frozenamount);
            } catch (Exception e) {
                e.printStackTrace();
                return Result.error("查询失败");
            } finally {
                redissonLock.unlock(key);
            }
        }
        return Result.OK();
    }

    //建行解冻资金
    //@PostMapping("/unFreeze")
    //@RequiresPermissions("gzpt:reg:unFreeze")
    //@RedissonLock(key = "#rec.id", keyPrefix = "bankReg:unfreeze:", waitTime = 1, lockTime = 10)
    public Result<?> unFreeze(@RequestBody BankReg rec) {
        BankReg bankReg = bankRegService.getById(rec.getId());
        if (Objects.isNull(bankReg)) {
            return Result.error("未查询到资金冻结信息");
        }
        Studentinfo stu = iStudentinfoService.getOneByStuNum(bankReg.getStunum());
        bankReg.setIdcard(Objects.isNull(stu) ? null : stu.getIdcard());
        if (bankReg.getStatus() != 3) {
            return Result.error("未完成资金冻结");
        }
        if (!"9IC3C55B".equals(bankReg.getBankcode())) {
            return Result.error("非建行学员，不可操作解冻");
        }
        long count = bankOrderService.count(
                Wrappers.lambdaQuery(BankOrder.class)
                        .eq(BankOrder::getStunum, bankReg.getStunum())
                        .ne(BankOrder::getStatus, 1)
        );
        if (count > 0) {
            return Result.error("有划转中的订单，清稍后解冻");
        }
        try {
            int i = bankRegService.transUnFreeze(bankReg, 3);
            if (i != 0) {
                return Result.error("解冻失败");
            }
        } catch (Exception e) {
            log.error("解冻出错：", e);
            return Result.error("解冻失败");
        }
        return Result.ok("解冻成功");
    }


    @ApiOperation(value = "资金解冻退费")
    @PostMapping("/transfer")
    @RequiresPermissions(value = {"gzpt:reg:unFreeze"})
    public Result transfer(@RequestBody Map<String, String> params) {
//        Integer type = Integer.valueOf(params.get("type"));
        String stunum = params.get("stunum");
//        JSONObject json = new JSONObject();
//        Studentinfo studentinfo1 = null;

        String key = "transfer-" + stunum;
        if (redissonLock.existKey(key)) {
            return Result.error("资金划转中，请稍后再试！！");
        }
        if (redissonLock.tryLock(key, -1, 10000)) {
            try {
                return iStudentinfoService.unfreeze(stunum, 1, null);
            } catch (Exception e) {
                e.printStackTrace();
                return Result.error("资金划转失败");
            } finally {
                redissonLock.unlock(key);
            }
        }
        return Result.ok();
    }

    @ApiOperation(value = "签约确定")
    @PostMapping("/sign")
    @RequiresPermissions("gzpt:reg:sign")
    public Result sign(@RequestBody BankReg bankReg) {
        BankReg bankReg1 = bankRegService.getById(bankReg.getId());
        if (bankRegService.sign(bankReg1)) {
            return Result.ok();
        } else {
            return Result.error("签约失败");
        }
    }

    @ApiOperation(value = "取消签约")
    @PostMapping("/signCancel")
    @RequiresPermissions("gzpt:reg:signCancel")
    public Result signCancel(@RequestBody BankReg bankReg) {
        BankReg bankReg1 = bankRegService.getById(bankReg.getId());
        if (bankRegService.signCancel(bankReg1)) {
            return Result.OK();
        } else {
            return Result.error("取消失败");
        }
    }

    @ApiOperation(value = "签约查询")
    @PostMapping("/signQuery")
    @RequiresPermissions("gzpt:reg:signQuery")
    public Result signQuery(@RequestBody BankReg bankReg) {
        BankReg bankReg1 = bankRegService.getById(bankReg.getId());
        if (bankRegService.signQuery(bankReg1)) {
            return Result.ok();
        }
        return Result.error("查询失败");
    }

    @ApiOperation(value = "修改银行")
    @PostMapping("/updateBank")
    @RequiresPermissions("gzpt:reg:updateBank")
    public Result updateBank(@RequestBody BankReg bankReg) {
        LambdaQueryWrapper<JkySysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JkySysDictData::getDictValue, bankReg.getBankcode());
        JkySysDictData bank = sysDictMapper.selectOne(queryWrapper);
        if (StringUtils.isBlank(bank.getDictLabel())) {
            return Result.error("银行名字为空");
        }
        String sys_bank = bank.getDictLabel();
        bankReg.setBankname(sys_bank);
        bankRegService.updateBankRegByStunum(bankReg);
        BankOrder bankOrder = new BankOrder();
        bankOrder.setStunum(bankReg.getStunum());
        bankOrder.setBankcode(bankReg.getBankcode());
        bankOrder.setBankname(sys_bank);
        bankOrderService.updateBankOrderByStunum(bankOrder);

        Studentinfo studentByStunum = iStudentinfoService.getStudentByStunum(bankReg.getStunum());
        if (studentByStunum != null) {
            studentByStunum.setBankcode(bankReg.getBankcode());
            studentByStunum.setBankname(sys_bank);
            iStudentinfoService.updateStudentinfo(studentByStunum);
        }
        return Result.OK();
    }
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param bankReg
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_reg:exportXls")
//    @RequestMapping(value = "/exportXls")
////	@RequiresPermissions("gzpt:reg:export")
//    public ModelAndView exportXls(HttpServletRequest request, BankReg bankReg) {
//		List<BankReg> list = bankRegService.list();
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject(NormalExcelConstants.FILE_NAME, "学员开户冻结信息");
//		mv.addObject(NormalExcelConstants.CLASS, BankReg.class);
//		mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学员开户冻结信息", "导出人:" + JkSecurityUtils.getRealname(), "学员开户冻结信息"));
//		mv.addObject(NormalExcelConstants.DATA_LIST, list);
//		return mv;
//    }


    /**
     * 通过excel导入数据
     */
    @RequiresPermissions("gzpt:reg:dr")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        Iterator<Map.Entry<String, MultipartFile>> var6 = fileMap.entrySet().iterator();
        if (!var6.hasNext()) {
            return Result.error("文件为空");
        }
        Map.Entry<String, MultipartFile> entity = var6.next();
        MultipartFile file = entity.getValue();
        ImportParams params = new ImportParams();
        params.setTitleRows(2);
        params.setHeadRows(1);
        try {
            List<BankReg> list = ExcelImportUtil.importExcel(file.getInputStream(), BankReg.class, params);
            long start = System.currentTimeMillis();
            this.service.saveBatch(list);
            log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
            return Result.ok("文件导入成功！数据行数：" + list.size());
        } catch (Exception var24) {
            String msg = var24.getMessage();
            log.error(msg, var24);
            Result<String> var12;
            if (msg != null && msg.contains("Duplicate entry")) {
                var12 = Result.error("文件导入失败:有重复数据！");
                return var12;
            } else {
                var12 = Result.error("文件导入失败:" + var24.getMessage());
                return var12;
            }
        } finally {
            try {
                file.getInputStream().close();
            } catch (IOException var23) {
                log.error("导入文件流关闭失败：", var23);
            }

        }
    }

    /**
     * 加密接口
     */
    @GetMapping("/encrypt")
    public Result<?> encryptData() {
        QueryWrapper<BankReg> wrapper = new QueryWrapper<>();
        wrapper.setEntityClass(BankReg.class);
        wrapper.eq("LENGTH(idcard)", 18);
        wrapper.last("limit 200");
        List<BankReg> list = bankRegService.list(wrapper);
        bankRegService.updateBatchById(list);
        return Result.ok();
    }

    @ApiOperation(value = "查询订单详情", notes = "查询订单详情")
    @GetMapping(value = "/selectdd")
    public Result<?> selectByid(BankReg bankReg,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<BankReg> page = new Page<>(pageNo, pageSize);
        IPage<BankReg> warnMessage = bankRegService.selectBankById(page, bankReg.getId());
        if (CollectionUtils.isEmpty(warnMessage.getRecords())) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(warnMessage);
    }
}
