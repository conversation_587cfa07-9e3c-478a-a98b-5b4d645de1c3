package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 资金划转对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface BankOrderMapper extends BaseMapper<BankOrder> {


    int updateBankOrderByStunum(BankOrder bankOrder);

    IPage<BankOrder> selectBankOrderPage(Page<BankOrder> page, @Param("bankOrder") BankOrder bankOrder);
    BankOrder selectTotalTransferAmount(@Param("bankOrder")BankOrder bankOrder);
    /**
     * 冻结资金
     */
    List<Map<String, Object>> frozenMoneyTotal(BankOrder bankOrder);

    /**
     * 学员金额
     */
    List<Map<String, Object>> transferMoneyTotal(BankOrder bankOrder);

    /**
     * 当前冻结金额
     *
     * @return
     */
    List<Map<String, Double>> currentFrozenMoneyTotal(BankOrder bankOrder);

    List<BankOrder> selectBankOrderList(@Param("bankOrder")BankOrder bankOrder);

    int updateBankOrder(BankOrder bankOrder);

    int insertBankOrder(BankOrder bankOrder);

    @Delete("delete from t_m_bank_order where stunum = #{stunum}")
    int deleteBankOrderByStunum(String stunum);

    List<String> selectstunum();

    List<BankOrder> selectZLBorder(@Param("bankOrder") BankOrder bankOrder);

    List<BankOrder> selectfalse();
}
