package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.BankOrderDay;
import com.jky.boot.core.module.gzpt.service.IBankOrderDayService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

 /**
 * @Description: 每日资金划转对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="每日资金划转对象")
@RestController
@RequestMapping("/gzpt/bankOrderDay")
@Slf4j
public class BankOrderDayController extends JeecgController<BankOrderDay, IBankOrderDayService> {
	@Autowired
	private IBankOrderDayService bankOrderDayService;

	/**
	 * 分页列表查询
	 *
	 * @param bankOrderDay
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "每日资金划转对象-分页列表查询")
	@ApiOperation(value="每日资金划转对象-分页列表查询", notes="每日资金划转对象-分页列表查询")
	@GetMapping(value = "/list")
//	@RequiresPermissions("gzpt:bankOrderDay:list")
	public Result<IPage<BankOrderDay>> queryPageList(BankOrderDay bankOrderDay,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		List<String> inscode = new ArrayList<>();
		if (Objects.nonNull(bankOrderDay.getInscode())){
			inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(bankOrderDay.getInscode(),bankOrderDay.getInscodes());
		}else {
			inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
		}
		bankOrderDay.setInscode(null);
		Date updatedate = bankOrderDay.getUpdatedate();
		Date createTime = bankOrderDay.getCreateTime();
		bankOrderDay.setUpdatedate(null);
		QueryWrapper<BankOrderDay> queryWrapper = QueryGenerator.initQueryWrapper(bankOrderDay, req.getParameterMap());
		if (StringUtils.isNotBlank(bankOrderDay.getInsname())){
			queryWrapper.like("insname",bankOrderDay.getInsname());
		}
		if (StringUtils.isNotBlank(bankOrderDay.getStuname())){
			queryWrapper.like("stuname",bankOrderDay.getStuname());
		}

			queryWrapper.in("inscode",inscode);

		if (Objects.nonNull(bankOrderDay.getType())){
			if (Objects.nonNull(bankOrderDay.getCreatedate())){
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String format = sdf.format(bankOrderDay.getCreatedate());
				if (bankOrderDay.getType()!=1){
					queryWrapper.between("createdate",format+" 00:00:00",format+" 23:59:59");
				}else if (bankOrderDay.getType()==1){
					queryWrapper.lt("createdate",format);
				}
			}
		}
		if (Objects.nonNull(updatedate)){
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String format = sdf.format(updatedate);
			queryWrapper.between("updatedate",format+" 00:00:00",format+" 23:59:59");
		}
		if (Objects.nonNull(createTime)){
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String format = sdf.format(createTime);
			queryWrapper.between("create_time",format+" 00:00:00",format+" 23:59:59");
		}
		queryWrapper.orderByDesc("create_time");
		Page<BankOrderDay> page = new Page<BankOrderDay>(pageNo, pageSize);
		IPage<BankOrderDay> pageList = bankOrderDayService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param bankOrderDay
	 * @return
	 */
	@AutoLog(value = "每日资金划转对象-添加")
	@ApiOperation(value="每日资金划转对象-添加", notes="每日资金划转对象-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_order_day:add")
	@PostMapping(value = "/add")
	@RequiresPermissions("gzpt:bankOrderDay:add")
	public Result<String> add(@RequestBody BankOrderDay bankOrderDay) {
		bankOrderDayService.save(bankOrderDay);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param bankOrderDay
	 * @return
	 */
	@AutoLog(value = "每日资金划转对象-编辑")
	@ApiOperation(value="每日资金划转对象-编辑", notes="每日资金划转对象-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_order_day:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	@RequiresPermissions("gzpt:bankOrderDay:edit")
	public Result<String> edit(@RequestBody BankOrderDay bankOrderDay) {
		bankOrderDayService.updateById(bankOrderDay);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "每日资金划转对象-通过id删除")
	@ApiOperation(value="每日资金划转对象-通过id删除", notes="每日资金划转对象-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_order_day:delete")
	@DeleteMapping(value = "/delete")
	@RequiresPermissions("gzpt:bankOrderDay:remove")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bankOrderDayService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "每日资金划转对象-批量删除")
	@ApiOperation(value="每日资金划转对象-批量删除", notes="每日资金划转对象-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_bank_order_day:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bankOrderDayService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value="每日资金划转对象-通过id查询", notes="每日资金划转对象-通过id查询")
	@GetMapping(value = "/queryById")
	@RequiresPermissions("gzpt:bankOrderDay:query")
	public Result<BankOrderDay> queryById(@RequestParam(name="id") String id) {
		BankOrderDay bankOrderDay = bankOrderDayService.getById(id);
		if(bankOrderDay==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bankOrderDay);
	}
}
