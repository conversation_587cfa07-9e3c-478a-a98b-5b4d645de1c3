package com.jky.boot.core.module.sxgzpt.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_stu_collection
 * @Author: jeecg-boot
 * @Date:   2023-08-07
 * @Version: V1.0
 */
@Data
@TableName("t_m_stu_collection")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_stu_collection对象", description="t_m_stu_collection")
public class TMStuCollection implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.Integer id;
    /**驾校编号*/
    @Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private java.lang.String inscode;
    /**驾校名称*/
    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private java.lang.String insname;
    /**学员编号*/
    @Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private java.lang.String stunum;
    /**学员身份证号*/
    @Excel(name = "学员身份证号", width = 15)
    @ApiModelProperty(value = "学员身份证号")
    private java.lang.String idcard;
    /**学员姓名*/
    @Excel(name = "学员姓名", width = 15)
    @ApiModelProperty(value = "学员姓名")
    private java.lang.String name;
    /**学员手机号*/
    @Excel(name = "学员手机号", width = 15)
    @ApiModelProperty(value = "学员手机号")
    private java.lang.String phone;
    /**学员性别*/
    @Excel(name = "学员性别", width = 15)
    @ApiModelProperty(value = "学员性别")
    private java.lang.String sex;
    /**学员照片*/
    @Excel(name = "学员照片", width = 15)
    @ApiModelProperty(value = "学员照片")
    private java.lang.String photo;
    /**驾驶证号*/
    @Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
    private java.lang.String drilicnum;
    /**驾驶证初领日期   YYYYMMDD*/
    @Excel(name = "驾驶证初领日期   YYYYMMDD", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期   YYYYMMDD")
    private java.lang.String fstdrilicdate;
    /**培训车型*/
    @Excel(name = "培训车型", width = 15)
    @ApiModelProperty(value = "培训车型")
    private java.lang.String traintype;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
}
