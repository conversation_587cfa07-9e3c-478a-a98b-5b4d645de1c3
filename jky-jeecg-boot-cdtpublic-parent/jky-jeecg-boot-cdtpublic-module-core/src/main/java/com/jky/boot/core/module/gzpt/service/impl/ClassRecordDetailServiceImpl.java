package com.jky.boot.core.module.gzpt.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.common.utils.*;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.mapper.*;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.boot.zlb.vo.BrProVo;
import com.jky.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * t_m_class_record_detail
 */
@Service
@Slf4j
public class ClassRecordDetailServiceImpl extends ServiceImpl<ClassRecordDetailMapper, ClassRecordDetail> implements IClassRecordDetailService {
    @Autowired
    private TotalTimeMapper totalTimeMapper;
    @Autowired
    private ITrainSubjectCreditService trainSubjectCreditService;
    @Autowired
    private StageTrainningTimeMapper stageTrainningTimeMapper;
    @Autowired
    private StudentinfoMapper studentinfoMapper;
    @Autowired
    private IBankOrderDayService bankOrderDayService;
    @Autowired
    private IStageTrainningTimeLogService iStageTrainningTimeLogService;
    @Autowired
    private StageTrainningTimeLogMapper stageTrainningTimeLogMapper;
    @Autowired
    private IStageTrainningTimeService stageTrainningTimeService;
    @Autowired
    private BankRegMapper bankRegMapper;
    @Autowired
    private IStuTransferService stuTransferService;


    /**
     * 查询学时管理
     *
     * @param id 学时管理主键
     * @return 学时管理
     */
    @Override
    public ClassRecordDetail selectClassRecordDetailById(String id) {
        return baseMapper.selectClassRecordDetailById(id);
    }

    /**
     * 查询学时管理列表
     *
     * @param classRecordDetail 学时管理
     * @return 学时管理
     */
    @Override
    public List<ClassRecordDetail> selectClassRecordDetailList(ClassRecordDetail classRecordDetail) {
        return baseMapper.selectClassRecordDetailList(classRecordDetail);
    }

    /**
     * 新增学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    @Override
    public int insertClassRecordDetail(ClassRecordDetail classRecordDetail) {

        ClassRecordDetail classRecordDetailServiceOne = baseMapper.selectOne(
                new LambdaQueryWrapper<>(ClassRecordDetail.class)
                        .eq(ClassRecordDetail::getRecnum, classRecordDetail.getRecnum())
                        .eq(ClassRecordDetail::getStunum, classRecordDetail.getStunum()));

        //科目
        int km = Integer.parseInt(classRecordDetail.getSubjcode().substring(3, 4));
        //培训类型
        int type = Integer.parseInt(classRecordDetail.getSubjcode().substring(0, 1));
        Studentinfo studentinfo = studentinfoMapper.findDetailByStunum(classRecordDetail.getStunum());
        if (studentinfo == null) {
            return 3;
        }
        TotalTime totalTime = totalTimeMapper.selectByStunumAndSubject(classRecordDetail.getStunum(), km);
        boolean isupdate = false;
        //添加电子教学日志
//        ClassRecordDetail classRecordDetail1 = baseMapper.selectClassRecordDetailById(classRecordDetail.getId());

        // 保存加密前的 totalTime
        TotalTime bak = new TotalTime();

        //学时汇总
        if (totalTime == null) {
            totalTime = new TotalTime();
            totalTime = getTotalTime(classRecordDetail, 1, totalTime);
            totalTime.setStunum(classRecordDetail.getStunum());
            totalTime.setStuname(classRecordDetail.getStuname());
            totalTime.setSubject((long) km);
            totalTime.setIdcard(studentinfo.getIdcard());
            totalTime.setIsBaoshen("0");
            totalTime.setCrdate(new Date());
            totalTime.setTraintype(studentinfo.getTraintype());
            totalTime.setInscode(studentinfo.getInscode());
            totalTime.setInsname(studentinfo.getInsname());
            totalTime.setDistrict(studentinfo.getDistrict());
            BeanUtils.copyProperties(totalTime, bak);
            totalTimeMapper.insert(totalTime);
        } else {
            if (type == 1) {//按照实操车型汇总
                totalTime.setTraintype(classRecordDetail.getTrainCarType());
            }
            totalTime = getTotalTime(classRecordDetail, 2, totalTime);
            BeanUtils.copyProperties(totalTime, bak);
            isupdate = true;
        }
        if (Objects.isNull(classRecordDetailServiceOne)) {
            if (isupdate) {
                //判断新学员是否生成报审
                if (!"1".equals(studentinfo.getIsold())) {
                    if (isbaosen(bak)) bak.setIsBaoshen("1");
                    else bak.setIsBaoshen("0");
                }
                List<StageTrainningTime> stageTrainingTimeList = stageTrainningTimeMapper.selectList(
                        new LambdaQueryWrapper<>(StageTrainningTime.class)
                                .eq(StageTrainningTime::getStunum, bak.getStunum())
                                .eq(StageTrainningTime::getSubject, km)
                );
                if (CollectionUtils.isNotEmpty(stageTrainingTimeList)) {
                    bak.setIsBaoshen("1");
                } else {
                    bak.setIsBaoshen("0");
                }
                totalTimeMapper.updateTotalTime(bak);
            }
            classRecordDetail.setCrdate(new Date());
            classRecordDetail.setKeeponTime(new Date());
            if (Objects.isNull(classRecordDetail.getStuidcard())) {
                classRecordDetail.setStuidcard(studentinfo.getIdcard());
            }
            return baseMapper.insert(classRecordDetail);
        } else if (Objects.nonNull(classRecordDetailServiceOne)) {//如果学时汇总过需要减掉旧的学时
            bak = getTotalTime(classRecordDetailServiceOne, 3, bak);
            //判断是否生成报审
            if (isbaosen(bak)) bak.setIsBaoshen("1");
            List<StageTrainningTime> stageTrainingTimeList = stageTrainningTimeMapper.selectList(
                    new LambdaQueryWrapper<>(StageTrainningTime.class)
                            .eq(StageTrainningTime::getStunum, bak.getStunum())
                            .eq(StageTrainningTime::getSubject, km)
            );
            if (CollectionUtils.isNotEmpty(stageTrainingTimeList)) {
                bak.setIsBaoshen("1");
            } else {
                bak.setIsBaoshen("0");
            }
            totalTimeMapper.updateTotalTime(bak);
            classRecordDetail.setUpdateTime(new Date());
            return baseMapper.updateClassRecordDetail(classRecordDetail);
        }
        return 4;
    }

    public boolean isbaosen(TotalTime totalTime) {
        String isBaoShen = CrStageTrainningTime(totalTime);
        return isBaoShen == null;
    }


    public boolean bankOrderDay(ClassRecordDetail classRecordDetail) {
        Long duration = classRecordDetail.getDuration();
        BankOrderDay bankOrderDay = new BankOrderDay();
        bankOrderDay.setStunum(classRecordDetail.getStunum());
        bankOrderDay.setStatus(0);
        bankOrderDay.setSubject(Integer.valueOf(classRecordDetail.getSubjcode().substring(3, 4)));
        bankOrderDay.setCreatedate(DateUtils.parseDate(DateUtils.getDate()));
        List<BankOrderDay> bankOrderDays = bankOrderDayService.selectBankOrderDayList(bankOrderDay);

        if (bankOrderDays.size() == 0) {
            Studentinfo studentinfo = studentinfoMapper.findDetailByStunum(classRecordDetail.getStunum());
            String id = DateIdUtil.getDateId();
            bankOrderDay.setId(id);
            bankOrderDay.setIdcard(studentinfo.getIdcard());
            bankOrderDay.setStuname(studentinfo.getName());
            bankOrderDay.setInscode(studentinfo.getInscode());
            bankOrderDay.setInsname(studentinfo.getInsname());
            bankOrderDay.setTotaltime(duration);

            bankOrderDayService.insertBankOrderDay(bankOrderDay);
        } else {
            BankOrderDay bankOrderDay1 = bankOrderDays.get(0);
            bankOrderDay.setTotaltime(bankOrderDay1.getTotaltime() + duration);
            bankOrderDay.setId(bankOrderDay1.getId());
            bankOrderDayService.updateBankOrderDay(bankOrderDay);
        }
        return true;
    }

    /*
     * type 1 添加
     * type 2 添加新学时 修改
     * type 3 减掉旧的学时 修改
     * */
    public TotalTime getTotalTime(ClassRecordDetail classRecordDetail, int type, TotalTime totalTime) {
//        log.error("-------------getTotalTime开始--------------"+new Date());
        Map<String, Object> map = getMap(classRecordDetail);
        if (type == 1) {
            totalTime.setNetworktime(Long.valueOf(map.get("networktime").toString()));
            totalTime.setVehicletime(Long.valueOf(map.get("vehicletime").toString()));
            totalTime.setSimulatortime(Long.valueOf(map.get("simulatortime").toString()));
            totalTime.setClasstime(Long.valueOf(map.get("classtime").toString()));
            totalTime.setMileage(new BigDecimal(map.get("mileage").toString()));
        } else if (type == 2) {
            totalTime.setNetworktime(totalTime.getNetworktime() + Long.valueOf(map.get("networktime").toString()));
            totalTime.setVehicletime(totalTime.getVehicletime() + Long.valueOf(map.get("vehicletime").toString()));
            totalTime.setSimulatortime(totalTime.getSimulatortime() + Long.valueOf(map.get("simulatortime").toString()));
            totalTime.setClasstime(totalTime.getClasstime() + Long.valueOf(map.get("classtime").toString()));
            totalTime.setMileage(totalTime.getMileage().add(new BigDecimal(map.get("mileage").toString())));
        } else if (type == 3) {
            totalTime.setNetworktime(totalTime.getNetworktime() == 0 ? 0 : totalTime.getNetworktime() - Long.valueOf(map.get("networktime").toString()));
            totalTime.setVehicletime(totalTime.getVehicletime() == 0 ? 0 : totalTime.getVehicletime() - Long.valueOf(map.get("vehicletime").toString()));
            totalTime.setSimulatortime(totalTime.getSimulatortime() == 0 ? 0 : totalTime.getSimulatortime() - Long.valueOf(map.get("simulatortime").toString()));
            totalTime.setClasstime(totalTime.getClasstime() == 0 ? 0 : totalTime.getClasstime() - Long.valueOf(map.get("classtime").toString()));
            totalTime.setMileage(totalTime.getMileage().compareTo(BigDecimal.ZERO) == 0 ? new BigDecimal(0) : totalTime.getMileage().subtract(new BigDecimal(map.get("mileage").toString())));
        }

//        log.error("-------------getTotalTime开始--------------"+new Date());
        return totalTime;
    }

    //学时汇总
    public Map<String, Object> getMap(ClassRecordDetail classRecordDetail) {
        //培训类型
        int type = Integer.valueOf(classRecordDetail.getSubjcode().substring(0, 1));
        Long vehicletime = 0L;//实操
        Long classtime = 0L;//课堂
        Long simulatortime = 0L;//模拟
        Long networktime = 0L;//远程

        double mileage = classRecordDetail.getMileageY();
        if (type == 1) {
         /*   if(classRecordDetail.getDuration_y()!=null){
                vehicletime=classRecordDetail.getDuration_y();
            }else{

            }*/

            if (Objects.nonNull(classRecordDetail.getDurationY())) {
                vehicletime = Double.valueOf(classRecordDetail.getDurationY().toString()).longValue();
            } else {
                vehicletime = 0L;
            }
           /* if(classRecordDetail.getMileage_y()!=null){
                mileage=classRecordDetail.getMileage_y();
            }*/
        } else if (type == 2) {
            if (Objects.nonNull(classRecordDetail.getDurationY())) {
                classtime = Double.valueOf(classRecordDetail.getDurationY().toString()).longValue();
            } else {
                classtime = 0L;
            }
        } else if (type == 3) {
            if (Objects.nonNull(classRecordDetail.getDurationY())) {
                simulatortime = Double.valueOf(classRecordDetail.getDurationY().toString()).longValue();
            } else {
                simulatortime = 0L;
            }
        } else if (type == 4) {
            if (Objects.nonNull(classRecordDetail.getDurationY())) {
                networktime = Double.valueOf(classRecordDetail.getDurationY().toString()).longValue();
            } else {
                networktime = 0L;
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("vehicletime", vehicletime);
        map.put("classtime", classtime);
        map.put("simulatortime", simulatortime);
        map.put("networktime", networktime);
        map.put("mileage", mileage);
        return map;
    }

    /**
     * 修改学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    @Override
    public int updateClassRecordDetail(ClassRecordDetail classRecordDetail) {
        classRecordDetail.setUpdateTime(new Date());
        return baseMapper.updateClassRecordDetail(classRecordDetail);
    }

    /**
     * 批量删除学时管理
     *
     * @param ids 需要删除的学时管理主键
     * @return 结果
     */
    @Override
    public int deleteClassRecordDetailByIds(String[] ids) {
        return baseMapper.deleteClassRecordDetailByIds(ids);
    }

    /**
     * 删除学时管理信息
     *
     * @param id 学时管理主键
     * @return 结果
     */
    @Override
    public int deleteClassRecordDetailById(String id) {
        return baseMapper.deleteClassRecordDetailById(id);
    }

    @Override
    public int deleteClassRecordDetailByStunum(String id) {
        return baseMapper.deleteClassRecordDetailByStunum(id);
    }

    @Override
    public boolean findClassRecord(ClassRecordDetail classRecordDetail) {
        ClassRecordDetail detail = baseMapper.selectCLassRecordDetail(classRecordDetail);
        return detail == null;
    }


    @Override
    public String CrStageTrainningTime(TotalTime totalTime) {
        //学员信息查询
        if (totalTime == null) {
            return "学时汇总不存在";
        }
        Studentinfo studentinfo = studentinfoMapper.findDetailByStunum(totalTime.getStunum());
        if (studentinfo == null) {
            return "学员信息不存在";
        }
        long count = stuTransferService.count(
                Wrappers.lambdaQuery(StuTransfer.class)
                        .eq(StuTransfer::getStunum, totalTime.getStunum())
        );
        if (studentinfo.getStutype() != 2 && (Integer.parseInt(studentinfo.getApplydate()) >= ******** || count > 0) &&
            ("C1".equals(studentinfo.getTraintype()) || "C2".equals(studentinfo.getTraintype()))) {
            List<BankReg> bankRegs = bankRegMapper.selectList(
                    Wrappers.lambdaQuery(BankReg.class)
                            .eq(BankReg::getStunum, totalTime.getStunum())
                            .eq(BankReg::getStatus, 3)
            );
            if (CollectionUtils.isEmpty(bankRegs)) {
                return "未资金监管，不能生成报审！";
            }
        }
        //type 1:报审 0:电子教学日志汇总并报审
        totalTime.setTraintype(studentinfo.getTraintype());
        if (StringUtils.isBlank(totalTime.getApplydate())) {
            totalTime.setApplydate(studentinfo.getApplydate().trim());
        }
        //判断是否查询模拟学时
        boolean subject2Or3 = totalTime.getSubject() == 2 || totalTime.getSubject() == 3;
        //是否查询报审记录
//        List<StageTrainningTime> lastStageTrainningTimes = new ArrayList<>();
        StageTrainningTime stageParam = new StageTrainningTime();
        stageParam.setStunum(totalTime.getStunum());
        //查询是否报审科目一
        //stageParam.setSubject(1L);
        List<StageTrainningTime> oldStageTrainningTimeList = stageTrainningTimeMapper.selectStageTrainningTimeList(stageParam);

        if (subject2Or3) {
            //新学员
            if (studentinfo.getIsold() == null || "0".equals(studentinfo.getIsold())) {
                List<StageTrainningTime> collect = oldStageTrainningTimeList.stream().filter(item -> item.getSubject() == 1L && item.getAuditstate().equals("1")).collect(Collectors.toList());
                if (collect.size() == 0) {
                    //判断是否是转校学员
                    if (studentinfo.getIscrosscity().equals("2")) {
                        return "请先报审科目一";
                    }
                }
            }
            //查询科目二和科目三的公里数
//            Long lastSubject = subject;
//            lastStageTrainningTimes = oldStageTrainningTimeList.stream().filter(item -> item.getSubject().equals(lastSubject)).collect(Collectors.toList());

        }
        //额定学时查询
        String applydate = totalTime.getApplydate().replaceAll("-", "");
        TrainSubjectCredit param = new TrainSubjectCredit();
        param.setSubject(totalTime.getSubject());
        param.setType(CityUtil.getSubjectCreditType(applydate));//大纲类型
        param.setTrainCarType(totalTime.getTraintype());
        List<TrainSubjectCredit> trainSubjectCreditList = trainSubjectCreditService.list();
        trainSubjectCreditList = trainSubjectCreditList.stream().filter(item -> item.getType().equals(param.getType()) && item.getTrainCarType().equals(param.getTrainCarType()) && item.getSubject().equals(param.getSubject())).collect(Collectors.toList());
        long vehicleSimulatorTime = trainSubjectCreditList.stream().filter(a -> a.getClasstype() == 1L || a.getClasstype() == 3L).mapToLong(TrainSubjectCredit::getCreditration).sum();
        long classTime = trainSubjectCreditList.stream().filter(a -> a.getClasstype() == 2L).mapToLong(TrainSubjectCredit::getCreditration).sum();
        long networkTime = trainSubjectCreditList.stream().filter(a -> a.getClasstype() == 4L).mapToLong(TrainSubjectCredit::getCreditration).sum();
        List<String> idList = trainSubjectCreditList.stream().map(TrainSubjectCredit::getId).collect(Collectors.toList());
        log.info("生成报审 stunum:{},大纲idList：{},实操+模拟{},课堂:{},远程:{}", studentinfo.getStunum(), idList, vehicleSimulatorTime, classTime, networkTime);

        //汇总学时与额定学时对比
        for (TrainSubjectCredit trainSubjectCredit : trainSubjectCreditList) {
            Long creditration = (trainSubjectCredit.getCreditration() * 3) / 4;
            if (trainSubjectCredit.getClasstype() == 1) {//实操
                if (totalTime.getVehicletime() < creditration) {
                    return "实操学时未达标,额定学时:" + creditration + "/报审学时:" + totalTime.getVehicletime();
                }
            } else if (trainSubjectCredit.getClasstype() == 2) {
                if (totalTime.getClasstime() < creditration) {
                    if (Integer.parseInt(applydate) < 20230701) {//课堂+远程》manzu
                        if (classTime + networkTime > totalTime.getClasstime() + totalTime.getNetworktime()) {
                            return "课堂+远程未达标,额定学时:" + (classTime + networkTime) + "/报审学时:" + (totalTime.getClasstime() + totalTime.getNetworktime());
                        }
                    } else {
                        return "课堂学时未达标,额定学时:" + creditration + "/报审学时:" + totalTime.getClasstime();
                    }
                }

            } else if (trainSubjectCredit.getClasstype() == 3) {//模拟
                if (totalTime.getSimulatortime() < creditration) {
                    //不达标时,检查模拟+实操>额定总学时 即达标
                    boolean canRePlace = true;
                    String othercity = "330624,330683";
                    String types = "C1,C2";
                    int Applydate = Integer.parseInt(studentinfo.getApplydate());
                    //新昌、嵊州 3月1号开始 小车 实操不能代替模拟 Applydate>20220301
                    if (types.contains(studentinfo.getTraintype()) && othercity.contains(studentinfo.getDistrict()) && Applydate > 20220301) {
                        canRePlace = false;
                    }
                    //上虞 1月10号报名的学员 小车实操不能代替模拟 Applydate>20220109
                    if (types.contains(studentinfo.getTraintype()) && "330604".equals(studentinfo.getDistrict()) && Applydate > 20220109) {
                        canRePlace = false;
                    }
                    //市区,诸暨、越城，柯桥 3月10号开始  实操不能代替模拟
                    String othercity2 = "330601,330681,330602,330603";
                    if (types.contains(studentinfo.getTraintype()) && othercity2.contains(studentinfo.getDistrict()) && Applydate > 20220309) {
                        canRePlace = false;
                    }
                    if (!canRePlace) {
                        return "模拟学时未达标,额定学时:" + creditration + "/报审学时:" + totalTime.getSimulatortime();
                    } else if (totalTime.getSimulatortime() + totalTime.getVehicletime() < vehicleSimulatorTime * 3 / 4) {
                        return "模拟+实操未达标,额定学时:" + vehicleSimulatorTime * 3 / 4 + "/报审学时:" + (totalTime.getSimulatortime() + totalTime.getVehicletime());
                    }

                }
            } else if (trainSubjectCredit.getClasstype() == 4) {//远程
                if (totalTime.getNetworktime() < trainSubjectCredit.getCreditration()) {
                    return "远程学时未达标,额定学时:" + trainSubjectCredit.getCreditration() + "/报审学时:" + totalTime.getNetworktime();
                }
            }

        }
        //判断是否生成重复报审
        List<StageTrainningTime> repeatList = oldStageTrainningTimeList.stream().filter(item -> item.getSubject().equals(totalTime.getSubject()) && !item.getAuditstate().equals("2")).collect(Collectors.toList());
        StageTrainningTime stageTrainningTime = null;
        if (repeatList.size() > 0) {
            stageTrainningTime = repeatList.get(0);
            buildStageTrainningTime(stageTrainningTime, totalTime, applydate);
            stageTrainningTimeMapper.updateById(stageTrainningTime);
        } else {
            stageTrainningTime = buildStageTrainningTime(null, totalTime, applydate);
            stageTrainningTimeMapper.insert(stageTrainningTime);
        }
        log.info("[生成报审结束]:" + totalTime.getStunum() + "。:科目:" + totalTime.getSubject());
        return null;
    }

    private StageTrainningTime buildStageTrainningTime(StageTrainningTime stageTrainningTime, TotalTime totalTime, String applydate) {
        if (stageTrainningTime == null) {
            stageTrainningTime = new StageTrainningTime();
            stageTrainningTime.setCrdate(new Date());
            stageTrainningTime.setAuditstate("0");
            stageTrainningTime.setIspushsj(0);
            stageTrainningTime.setPushsjdate(null);
        }
        stageTrainningTime.setInscode(totalTime.getInscode());
        stageTrainningTime.setInsname(totalTime.getInsname());
        stageTrainningTime.setStunum(totalTime.getStunum());
        stageTrainningTime.setIdcard(totalTime.getIdcard());
        stageTrainningTime.setStuname(totalTime.getStuname());
        stageTrainningTime.setSubject(totalTime.getSubject());
        stageTrainningTime.setTotaltime(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
        stageTrainningTime.setDuration(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
        stageTrainningTime.setVehicletime(totalTime.getVehicletime());
        stageTrainningTime.setClasstime(totalTime.getClasstime());
        stageTrainningTime.setSimulatortime(totalTime.getSimulatortime());
        stageTrainningTime.setNetworktime(totalTime.getNetworktime());
        stageTrainningTime.setMileage(totalTime.getMileage());
        stageTrainningTime.setTraintype(totalTime.getTraintype());
        stageTrainningTime.setApplydate(applydate);
        //默认人脸对比
        stageTrainningTime.setIsface(1);
        stageTrainningTime.setDistrict(totalTime.getDistrict());
            /*stageTrainningTime.setIspushjs(0L);
            stageTrainningTime.setIspushsj(0L);*/
        //生成阶段报审时就直接去校验阶段报审--处理老数据
        stageTrainningTime.setUpdateTime(new Date());
        totalTime.setIsBaoshen("1");
        return stageTrainningTime;
    }


    @Override
    public String CrOrUpStageTrainningTime(TotalTime totalTime) {
        //学员信息查询
        if (totalTime == null) {
            return "学员不存在";
        }
        //type 1:报审 0:电子教学日志汇总并报审
        int type = 1;
        if (StringUtils.isBlank(totalTime.getApplydate())) {
            Studentinfo studentinfo = studentinfoMapper.findDetailByStunum(totalTime.getStunum());
            if (studentinfo == null) {
                return "学员信息不存在";
            }
            totalTime.setApplydate(studentinfo.getApplydate().trim());
            type = 0;
        }

        boolean mnselcet = Long.parseLong(totalTime.getApplydate()) < Long.parseLong("20230101");
        boolean outlineFig = false;
        //判断是否查询模拟学时
        if (CityUtil.cityCoode.equals("3307") && totalTime.getSubject() == 2 && mnselcet) {
            ClassRecordDetail classRecordDetail = new ClassRecordDetail();
            classRecordDetail.setStunum(totalTime.getStunum());
            classRecordDetail.setStuidcard(totalTime.getIdcard());
            classRecordDetail.setKm(totalTime.getSubject() + "");
            classRecordDetail.setType("3");
            List<ClassRecordDetail> classRecordDetails = this.selectClassRecordDetailList(classRecordDetail);
            if (classRecordDetails.size() > 0) {
                outlineFig = Long.parseLong(classRecordDetails.get(0).getStarttime().substring(0, 8)) < Long.parseLong("20230101");
            }
        }
        boolean vehicletime = true;
        boolean classtime = true;
        boolean simulatortime = true;
        boolean networktime = true;
        boolean mileage = true;
        boolean isMileage = false;
        Long subject = 2L;
        if (totalTime.getSubject() == 2) {
            subject = 3L;
            isMileage = true;
        } else if (totalTime.getSubject() == 3) {
            isMileage = true;
        }
        //是否查询报审记录
        List<StageTrainningTime> stageTrainningTimes = new ArrayList<>();
        StageTrainningTime stageParam = new StageTrainningTime();
        stageParam.setStunum(totalTime.getStunum());
        //查询是否报审科目一
        //stageParam.setSubject(1L);
        List<StageTrainningTime> stageTrainningTimes1 = stageTrainningTimeMapper.selectStageTrainningTimeList(stageParam);

        if (isMileage) {
            Studentinfo detailByStunum = studentinfoMapper.findDetailByStunum(totalTime.getStunum());
            //老学员无需判断是否审核
            if (detailByStunum.getIsold() == null || "2".equals(detailByStunum.getIsold()) || Integer.parseInt(detailByStunum.getApplydate())>********) {
                List<StageTrainningTime> collect = stageTrainningTimes1.stream().filter(item -> item.getSubject() == 1L && item.getAuditstate().equals("1")).collect(Collectors.toList());

                if (collect.size() == 0) {
                    //判断是否是转校学员
                    if (detailByStunum.getIscrosscity().equals("2")) {
                        return "请先报审科目一";
                    }
                }
            }
            //查询科目二和科目三的公里数
            Long finalSubject = subject;
            stageTrainningTimes = stageTrainningTimes1.stream().filter(item -> item.getSubject() == finalSubject).collect(Collectors.toList());

        }

        //判断是否更更新重复报审
        List<StageTrainningTime> collect1 = stageTrainningTimes1.stream().filter(item -> item.getSubject() == totalTime.getSubject() && !item.getAuditstate().equals("2")).collect(Collectors.toList());
        if (collect1.size() > 0) {

            List<StageTrainningTimeLog> list = iStageTrainningTimeLogService.list(new LambdaQueryWrapper<StageTrainningTimeLog>().eq(StageTrainningTimeLog::getSubject, totalTime.getSubject()).eq(StageTrainningTimeLog::getStunum, totalTime.getStunum()));
            if (!list.isEmpty()) {
                StageTrainningTimeLog stageTrainningTimeLog = list.get(0);
                stageTrainningTimeLog.setMessage(null);
                iStageTrainningTimeLogService.updateById(stageTrainningTimeLog);
            }
            StageTrainningTime stageTrainningTime1 = stageTrainningTimeMapper.selectOne(new LambdaQueryWrapper<StageTrainningTime>().eq(StageTrainningTime::getStunum, totalTime.getStunum()).eq(StageTrainningTime::getSubject, totalTime.getSubject()));

            //额定学时查询
            String applydate = totalTime.getApplydate().replaceAll("-", "");
            TrainSubjectCredit param = new TrainSubjectCredit();
            param.setSubject(totalTime.getSubject());
            param.setType(CityUtil.getSubjectCreditType(applydate));//大纲类型
            param.setTrainCarType(totalTime.getTraintype());
            List<TrainSubjectCredit> trainSubjectCreditList = trainSubjectCreditService.list();
            List<TrainSubjectCredit> trainSubjectCredits = trainSubjectCreditList.stream().filter(item -> item.getType() == param.getType() && item.getTrainCarType().equals(param.getTrainCarType()) && item.getSubject().equals(param.getSubject())).collect(Collectors.toList());

            //汇总学时与额定学时对比
            for (TrainSubjectCredit trainSubjectCredit : trainSubjectCredits) {

                if (trainSubjectCredit.getClasstype() == 1) {//实操
                    //判断里程
                    if (stageTrainningTimes.size() > 0) {

                        BigDecimal b1 = stageTrainningTimes.get(0).getMileage();
                        BigDecimal b2 = totalTime.getMileage();
                        double v = b2.add(b1).doubleValue();
                        mileage = (v >= trainSubjectCredit.getMileageration());
//                    log.error("公里数是大于300:"+(v>=300));
                    }
                    Long creditration = trainSubjectCredit.getCreditration();
                    if (outlineFig) {
                        creditration = creditration + 120L;
                    }
                    vehicletime = totalTime.getVehicletime() >= creditration;
                } else if (trainSubjectCredit.getClasstype() == 2) {//课堂
                    classtime = totalTime.getClasstime() >= trainSubjectCredit.getCreditration();
                } else if (trainSubjectCredit.getClasstype() == 3) {//模拟
                    Long creditration = trainSubjectCredit.getCreditration();
                    if (outlineFig) {
                        creditration = 120L;
                    }
                    simulatortime = totalTime.getSimulatortime() >= creditration;
                } else if (trainSubjectCredit.getClasstype() == 4) {//远程
                    networktime = totalTime.getNetworktime() >= trainSubjectCredit.getCreditration();
                }

            }

            //3月26号开始课堂改成远程教学
            String yyyyMMdd = DateUtil.format(new Date(), "yyyyMMdd");
            boolean b = Long.parseLong(yyyyMMdd) >= Long.parseLong("20220326");
            /* //8.3号起报审必须要课堂学时*/
            /* boolean ktfig = Long.parseLong(yyyyMMdd) < Long.parseLong("20220803");*/
            /*   if (b && ((CityUtil.cityCoode.equals("3307") &&  ktfig) || CityUtil.cityCoode.equals("3304"))) {*/
            if (b) {
                //课堂+远程
                long calssTime = totalTime.getClasstime() + totalTime.getNetworktime();
                //额定学时 课堂+远程
                Long totime = trainSubjectCredits.stream().filter(item -> item.getClasstype() == 2 || item.getClasstype() == 4).collect(Collectors.summingLong(TrainSubjectCredit::getCreditration));

                classtime = calssTime >= totime;
            }
            //判断学时/里程 是否满足 --->生成报审记录
            if (vehicletime && classtime && simulatortime && networktime && mileage) {
                stageTrainningTime1.setInscode(totalTime.getInscode());
                stageTrainningTime1.setInsname(totalTime.getInsname());
                stageTrainningTime1.setStunum(totalTime.getStunum());
                stageTrainningTime1.setIdcard(totalTime.getIdcard());
                stageTrainningTime1.setStuname(totalTime.getStuname());
                stageTrainningTime1.setSubject(totalTime.getSubject());
//            log.error("总学时:"+(totalTime.getVehicletime()+totalTime.getClasstime()+totalTime.getSimulatortime()+totalTime.getNetworktime()));
                stageTrainningTime1.setTotaltime(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
                stageTrainningTime1.setDuration(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
                stageTrainningTime1.setVehicletime(totalTime.getVehicletime());
                stageTrainningTime1.setClasstime(totalTime.getClasstime());
                stageTrainningTime1.setSimulatortime(totalTime.getSimulatortime());
                stageTrainningTime1.setNetworktime(totalTime.getNetworktime());
                stageTrainningTime1.setMileage(totalTime.getMileage());
                stageTrainningTime1.setTraintype(totalTime.getTraintype());
                stageTrainningTime1.setApplydate(applydate);
                //默认人脸对比
                stageTrainningTime1.setIsface(1);
                // stageTrainningTime.setAuditstate("0");
                stageTrainningTime1.setDistrict(totalTime.getDistrict());
            /*stageTrainningTime.setIspushjs(0L);
            stageTrainningTime.setIspushsj(0L);*/
                stageTrainningTime1.setCrdate(new Date());

                //生成阶段报审时就直接去校验阶段报审--处理老数据

                stageTrainningTime1.setIspushsj(checkStage(stageTrainningTime1));
                stageTrainningTimeMapper.updateById(stageTrainningTime1);

                log.error("[CrOrUpStageTrainningTime-updateById]:" + totalTime.getStunum() + "。:总学时:" + stageTrainningTime1.getTotaltime());

                //已申请报审 手动报审更新汇总表
                if (type == 1) {
                    totalTime.setIsBaoshen("1");
                }

            } else {
                return "学时不足！无法更新或生成阶段报审";
            }
            return "阶段报审更新成功";

        }

        //额定学时查询
        String applydate = totalTime.getApplydate().replaceAll("-", "");
        TrainSubjectCredit param = new TrainSubjectCredit();
        param.setSubject(totalTime.getSubject());
        param.setType(CityUtil.getSubjectCreditType(applydate));//大纲类型
        param.setTrainCarType(totalTime.getTraintype());
        List<TrainSubjectCredit> trainSubjectCreditList = trainSubjectCreditService.list();
        List<TrainSubjectCredit> trainSubjectCredits = trainSubjectCreditList.stream().filter(item -> item.getType() == param.getType() && item.getTrainCarType().equals(param.getTrainCarType()) && item.getSubject().equals(param.getSubject())).collect(Collectors.toList());

        //汇总学时与额定学时对比
        for (TrainSubjectCredit trainSubjectCredit : trainSubjectCredits) {

            if (trainSubjectCredit.getClasstype() == 1) {//实操
                //判断里程
                if (stageTrainningTimes.size() > 0) {

                    BigDecimal b1 = stageTrainningTimes.get(0).getMileage();
                    BigDecimal b2 = totalTime.getMileage();
                    if (Objects.isNull(b1)) {
                        b1 = new BigDecimal(0);
                    }
                    double v = b2.add(b1).doubleValue();
                    mileage = (v >= trainSubjectCredit.getMileageration());
//                    log.error("公里数是大于300:"+(v>=300));
                }
                Long creditration = trainSubjectCredit.getCreditration();
                if (outlineFig) {
                    creditration = creditration + 120L;
                }
                vehicletime = totalTime.getVehicletime() >= creditration;
            } else if (trainSubjectCredit.getClasstype() == 2) {//课堂
                classtime = totalTime.getClasstime() >= trainSubjectCredit.getCreditration();
            } else if (trainSubjectCredit.getClasstype() == 3) {//模拟
                Long creditration = trainSubjectCredit.getCreditration();
                if (outlineFig) {
                    creditration = 120L;
                }
                simulatortime = totalTime.getSimulatortime() >= creditration;
            } else if (trainSubjectCredit.getClasstype() == 4) {//远程
                networktime = totalTime.getNetworktime() >= trainSubjectCredit.getCreditration();
            }

        }

        //3月26号开始课堂改成远程教学
        String yyyyMMdd = DateUtil.format(new Date(), "yyyyMMdd");
        boolean b = Long.parseLong(yyyyMMdd) >= Long.parseLong("20220326");
        /* //8.3号起报审必须要课堂学时*/
        /* boolean ktfig = Long.parseLong(yyyyMMdd) < Long.parseLong("20220803");*/
        /*   if (b && ((CityUtil.cityCoode.equals("3307") &&  ktfig) || CityUtil.cityCoode.equals("3304"))) {*/
        if (b) {
            //课堂+远程
            long calssTime = totalTime.getClasstime() + totalTime.getNetworktime();
            //额定学时 课堂+远程
            Long totime = trainSubjectCredits.stream().filter(item -> item.getClasstype() == 2 || item.getClasstype() == 4).collect(Collectors.summingLong(TrainSubjectCredit::getCreditration));

            classtime = calssTime >= totime;
        }
        //判断学时/里程 是否满足 --->生成报审记录
        if (vehicletime && classtime && simulatortime && networktime && mileage) {
            StageTrainningTime stageTrainningTime = new StageTrainningTime();
            stageTrainningTime.setInscode(totalTime.getInscode());
            stageTrainningTime.setInsname(totalTime.getInsname());
            stageTrainningTime.setStunum(totalTime.getStunum());
            stageTrainningTime.setIdcard(totalTime.getIdcard());
            stageTrainningTime.setStuname(totalTime.getStuname());
            stageTrainningTime.setSubject(totalTime.getSubject());
//            log.error("总学时:"+(totalTime.getVehicletime()+totalTime.getClasstime()+totalTime.getSimulatortime()+totalTime.getNetworktime()));
            stageTrainningTime.setTotaltime(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
            stageTrainningTime.setDuration(totalTime.getVehicletime() + totalTime.getClasstime() + totalTime.getSimulatortime() + totalTime.getNetworktime());
            stageTrainningTime.setVehicletime(totalTime.getVehicletime());
            stageTrainningTime.setClasstime(totalTime.getClasstime());
            stageTrainningTime.setSimulatortime(totalTime.getSimulatortime());
            stageTrainningTime.setNetworktime(totalTime.getNetworktime());
            stageTrainningTime.setMileage(totalTime.getMileage());
            stageTrainningTime.setTraintype(totalTime.getTraintype());
            stageTrainningTime.setApplydate(applydate);
            //默认人脸对比
            stageTrainningTime.setIsface(1);
            // stageTrainningTime.setAuditstate("0");
            stageTrainningTime.setDistrict(totalTime.getDistrict());
            /*stageTrainningTime.setIspushjs(0L);
            stageTrainningTime.setIspushsj(0L);*/
            stageTrainningTime.setCrdate(new Date());

            //生成阶段报审时就直接去校验阶段报审--处理老数据

            stageTrainningTime.setIspushsj(checkStage(stageTrainningTime));
            stageTrainningTimeMapper.insert(stageTrainningTime);

            log.error("[CrOrUpStageTrainningTime-insertStageTrainningTime]:" + totalTime.getStunum() + "。:总学时:" + stageTrainningTime.getTotaltime());

            //已申请报审 手动报审更新汇总表
            if (type == 1) {
                totalTime.setIsBaoshen("1");
            }

        } else {
            return "学时不足！无法生成阶段报审";
        }
        return null;
    }

    public int checkStage(StageTrainningTime stageTrainningTime) {
        Studentinfo studentinfo = studentinfoMapper.selectOne(new LambdaQueryWrapper<Studentinfo>().eq(Studentinfo::getStunum, stageTrainningTime.getStunum()));
        if (Objects.nonNull(studentinfo) && Long.parseLong(studentinfo.getApplydate()) <= 20230601) {
            JSONObject json = new JSONObject();
            json.put("stunum", stageTrainningTime.getStunum());
            json.put("subject", stageTrainningTime.getSubject());
            json.put("vehicletime", stageTrainningTime.getVehicletime());
            json.put("classtime", stageTrainningTime.getClasstime());
            json.put("simulatortime", stageTrainningTime.getSimulatortime());
            json.put("networktime", stageTrainningTime.getNetworktime());
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.querysubject, json.toString());
            StageTrainningTimeLog timeLog = new StageTrainningTimeLog();
            timeLog.setStageid(stageTrainningTime.getId());
            timeLog.setMessage(commonResponse.getMessage());
            JSONObject o = (JSONObject) JSON.toJSON(commonResponse.getData());
            if (commonResponse.getErrorcode() == 0) {
                System.out.println(o.get("recarray"));
                timeLog.setRecarray(o.get("recarray").toString());

                timeLog.setInscode(stageTrainningTime.getInscode());
                timeLog.setStunum(stageTrainningTime.getStunum());
                timeLog.setSubject(o.get("subject").toString());
            }
            if ("本部分已审核通过，无需查询".equals(commonResponse.getMessage())) {
                stageTrainningTime.setIspushjs(1);
                stageTrainningTime.setAuditstate("1");
            }

            long logcount = stageTrainningTimeLogMapper.selectCount(
                    Wrappers.<StageTrainningTimeLog>lambdaQuery().eq(StageTrainningTimeLog::getStageid, stageTrainningTime.getId())
            );
            if (logcount == 0) {
                timeLog.setCrdate(new Date());
                stageTrainningTimeLogMapper.insert(timeLog);
            } else {
                timeLog.setUpdateTime(new Date());
                stageTrainningTimeLogMapper.updateStageTrainningTimeLog(timeLog);
            }

            return 1;
        }
        return 0;
    }

    @Override
    public List<ClassRecordDetail> findDetailByIds(String[] ids) {
        return baseMapper.findDetailByIds(ids);
    }


    @Override
    public List<ClassRecordDetail> selectkc(String subjcode, String id, String type) {
        List<ClassRecordDetail> list1 = new ArrayList<>();

        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (ObjectUtils.isEmpty(stu)) {
            return list1;
        }
        String stunum = stu.getStunum();
        String applydate = stu.getApplydate().replaceAll("-", "");
        Integer ty = CityUtil.getSubjectCreditType(applydate);
        QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();

        queryWrappeer.eq("stunum", stunum);
        queryWrappeer.apply("SUBSTR(SUBJCODE,4,1) = {0}  and SUBSTR(SUBJCODE,1,1) = '2'", subjcode);
        queryWrappeer.select("sum(duration_y) durationY");
        ClassRecordDetail lsae = null;
        lsae = this.getOne(queryWrappeer);
        ClassRecordDetail selectkc = this.baseMapper.selectkc(subjcode, type, ty);


        if (ObjectUtils.isEmpty(lsae)) {
            lsae = new ClassRecordDetail();
            lsae.setState("课堂");
        }
        if (ObjectUtils.isNotEmpty(selectkc) && selectkc.getMileageY() != null) {
            lsae.setMileageY(selectkc.getMileageY());
        }
        lsae.setState("课堂");
        if (lsae.getMileageY() == null) {
            List<ClassRecordDetail> classRecordDetails = new ArrayList<>();
            return classRecordDetails;
        }
        list1.add(lsae);
        return list1;
    }

    @Override
    public List<ClassRecordDetail> selectyc(String subjcode, String id, String type) {
        List<ClassRecordDetail> list1 = new ArrayList<>();
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (ObjectUtils.isEmpty(stu)) {
            return list1;
        }
        String stunum = stu.getStunum();
        String applydate = stu.getApplydate().replaceAll("-", "");
        Integer ty = CityUtil.getSubjectCreditType(applydate);
        QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();
        queryWrappeer.eq("stunum", stunum);
        queryWrappeer.apply("SUBSTR(SUBJCODE,4,1) = {0}  and SUBSTR(SUBJCODE,1,1) = '4'", subjcode);
        queryWrappeer.select("sum(duration_y) durationY");
        ClassRecordDetail lsae = null;
        lsae = this.getOne(queryWrappeer);
        ClassRecordDetail selectkc = this.baseMapper.selectyc(subjcode, type, ty);


        if (ObjectUtils.isEmpty(lsae)) {
            lsae = new ClassRecordDetail();
            lsae.setState("远程");
        }
        if (ObjectUtils.isNotEmpty(selectkc) && selectkc.getMileageY() != null) {
            lsae.setMileageY(selectkc.getMileageY());
        }
        lsae.setState("远程");
        if (lsae.getMileageY() == null) {
            return new ArrayList<>();
        }
        list1.add(lsae);
        return list1;
    }

    @Override
    public List<ClassRecordDetail> selectmn(String subjcode, String id, String type) {
        List<ClassRecordDetail> list1 = new ArrayList<>();
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (ObjectUtils.isEmpty(stu)) {
            return list1;
        }
        String stunum = stu.getStunum();
        String applydate = stu.getApplydate().replaceAll("-", "");
        Integer ty = CityUtil.getSubjectCreditType(applydate);
        QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();

        queryWrappeer.eq("stunum", stunum);
        queryWrappeer.apply("SUBSTR(SUBJCODE,4,1) = {0}  and SUBSTR(SUBJCODE,1,1) = '3'", subjcode);
        queryWrappeer.select("sum(duration_y) durationY");
        ClassRecordDetail lsae;
        lsae = this.getOne(queryWrappeer);
        ClassRecordDetail selectkc = this.baseMapper.selectmn(subjcode, type, ty);

        if (ObjectUtils.isEmpty(lsae)) {
            lsae = new ClassRecordDetail();
            lsae.setState("模拟");
        }
        if (ObjectUtils.isNotEmpty(selectkc) && selectkc.getMileageY() != null) {
            lsae.setMileageY(selectkc.getMileageY());
        }
        lsae.setState("模拟");
        if (lsae.getMileageY() == null) {
            return new ArrayList<>();
        }
        list1.add(lsae);
        return list1;
    }

    @Override
    public List<ClassRecordDetail> selectsc(String subjcode, String id, String type) {
        List<ClassRecordDetail> list1 = new ArrayList<>();
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (ObjectUtils.isEmpty(stu)) {
            return list1;
        }
        String stunum = stu.getStunum();
        String applydate = stu.getApplydate().replaceAll("-", "");
        Integer ty = CityUtil.getSubjectCreditType(applydate);
        QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();

        queryWrappeer.eq("stunum", stunum);
        queryWrappeer.apply("SUBSTR(SUBJCODE,4,1) = {0}  and SUBSTR(SUBJCODE,1,1) = '1'", subjcode);
        queryWrappeer.select("sum(duration_y) durationY");
        ClassRecordDetail lsae;
        lsae = this.getOne(queryWrappeer);
        ClassRecordDetail selectkc = this.baseMapper.selectsc(subjcode, type, ty);
        if (ObjectUtils.isEmpty(lsae)) {
            lsae = new ClassRecordDetail();
            lsae.setState("实操");
        }
        if (ObjectUtils.isNotEmpty(selectkc) && selectkc.getMileageY() != null) {
            lsae.setMileageY(selectkc.getMileageY());
        }
        lsae.setState("实操");
        if (lsae.getMileageY() == null) {
            return new ArrayList<>();
        }
        list1.add(lsae);
        return list1;
    }

    @Override
    public List<BrProVo> getBrType(Integer outlineType, String subCode, String carType) {
        return baseMapper.getBrType(outlineType, subCode, carType);
    }

    @Override
    public List<BrProVo> queryBrPro(String stuNum, String subCode) {
        return baseMapper.queryBrPro(stuNum, subCode);
    }


    @Override
    public IPage<ClassRecordDetail> selectqb(Page<ClassRecordDetail> page, String subjcode, String id, String type) {
        IPage<ClassRecordDetail> iPage = new Page<>();
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (Objects.isNull(stu)) {
            return iPage;
        }
        String stunum = stu.getStunum();
        String applyDate = stu.getApplydate().replaceAll("-", "");
        Integer ty = CityUtil.getSubjectCreditType(applyDate);

        QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();
        queryWrappeer.eq("stunum", stunum);
        queryWrappeer.apply("SUBSTR(SUBJCODE,4,1) = {0} ", subjcode);
        queryWrappeer.select("sum(duration_y) durationY");
        ClassRecordDetail lsae = this.getOne(queryWrappeer);
        ClassRecordDetail selectkc = this.baseMapper.selectqb(subjcode, type, ty);
        List<ClassRecordDetail> list1 = new ArrayList<>();
        if (ObjectUtils.isEmpty(lsae)) {
            list1.add(new ClassRecordDetail());
            list1.add(selectkc);
            iPage.setRecords(list1);
            return iPage;
        }

        QueryWrapper<StageTrainningTime> queryWrappseer = new QueryWrapper<>();
        queryWrappseer.eq("stunum", stunum);
        queryWrappseer.eq("subject", subjcode);
        queryWrappseer.last("limit 1");
        StageTrainningTime lres = stageTrainningTimeService.getOne(queryWrappseer);
        if (ObjectUtils.isEmpty(lres)) {
            lsae.setState("0");
            lsae.setAuditdate(null);
        } else {
            lsae.setState(lres.getAuditstate());
            lsae.setAuditdate(lres.getAuditdate());
        }

        list1.add(lsae);
        list1.add(selectkc);
        iPage.setRecords(list1);
        return iPage;
    }
}
