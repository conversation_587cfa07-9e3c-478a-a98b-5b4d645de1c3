package com.jky.boot.core.util;

import com.jky.boot.common.cas.entity.BaseZlbPersonInfo;
import com.jky.boot.common.cas.service.IBaseZlbPersonInfoService;
import com.jky.boot.common.exception.JkyCdtpublicException;
import com.jky.boot.core.module.gzpt.entity.Coach;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.ICoachService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.Constant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2024-02-22  09:57
 */
@Component
public class ZlbStuUtils {
    @Autowired
    private IBaseZlbPersonInfoService baseZlbPersonInfoService;
    @Autowired
    private IStudentinfoService studentInfoService;
    @Autowired
    private ICoachService coachOriService;
    @Autowired
    private RedisUtil redisUtil;

    private static IBaseZlbPersonInfoService zlbPersonInfoService;
    private static IStudentinfoService stuInfoService;
    private static ICoachService coachService;
    private static RedisUtil redis;

    @PostConstruct
    public void init() {
        zlbPersonInfoService = baseZlbPersonInfoService;
        stuInfoService = studentInfoService;
        coachService = coachOriService;
        redis = redisUtil;
    }

    /**
     * 浙里办临时 id 转身份证
     *
     * @param certId 登录临时凭证
     */
    public static BaseZlbPersonInfo getZlbPerson(String certId) {
        String id = certIdToId(certId);
        return zlbPersonInfoService.getById(id);
    }

    /**
     * 浙里办临时 id 转学员信息
     */
    public static Studentinfo getStuByZlbId(String certId) {
        BaseZlbPersonInfo zlbPerson = getZlbPerson(certId);
        if (Objects.isNull(zlbPerson)) {
            return null;
        }
        return stuInfoService.getOneByIdCard(zlbPerson.getIdnum());
    }

    /**
     * 浙里办临时 id 转教练信息
     */
    public static Coach getCoachByZlbId(String certId) {
        BaseZlbPersonInfo personInfo = getZlbPerson(certId);
        if (Objects.isNull(personInfo)) {
            return null;
        }
        return coachService.getOneByIdCard(personInfo.getIdnum());
    }

    /**
     * 临时凭证转 id
     *
     * @param certId 临时凭证
     * @return id
     */
    private static String certIdToId(String certId) {
        String key = Constant.YXC_REDIS_ZLB_ID_BY_UUID + certId;
        if (!redis.hasKey(key)) {
            throw new JkyCdtpublicException("非法操作");
        }
        return redis.get(key).toString();
    }
}
