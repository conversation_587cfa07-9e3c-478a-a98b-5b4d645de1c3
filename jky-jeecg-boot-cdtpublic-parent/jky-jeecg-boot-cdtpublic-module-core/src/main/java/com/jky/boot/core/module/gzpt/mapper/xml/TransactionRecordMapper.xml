<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.TransactionRecordMapper">

    <select id="selectTransactionRecordList"
            resultType="com.jky.boot.core.module.gzpt.entity.TransactionRecord">
        select t1.id,
        t1.codenum,
        t1.tra_type,
        t1.name,
        t1.idcard,
        t1.startdate,
        t1.enddate,
        t1.counts,
        t1.orderarray,
        t1.status,
        t1.message,
        t1.crdate
        from t_m_transaction_record t1
        <choose>
            <when test="traType!=null and traType == 1">left join t_m_institution t2 on t1.codenum = t2.inscode</when>
            <otherwise>
                left join t_m_studentinfo t2 on t1.codenum = t2.stunum
            </otherwise>
        </choose>
        <where>
            <if test="inscodes != null ">
                and t2.inscode in
                <foreach collection="inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="codenum != null  and codenum != ''">and t1.codenum = #{codenum}</if>
            <if test="traType != null ">and t1.tra_type = #{traType}</if>
            <if test="name != null  and name != ''">and t1.name like concat(concat('%', #{name}), '%')</if>
            <if test="idcard != null  and idcard != ''">and t1.idcard = #{idcard}</if>
            <if test="startdate != null  and startdate != ''">and t1.startdate = #{startdate}</if>
            <if test="enddate != null  and enddate != ''">and t1.enddate = #{enddate}</if>
            <if test="counts != null ">and t1.counts = #{counts}</if>
            <if test="orderarray != null  and orderarray != ''">and t1.orderarray = #{orderarray}</if>
            <if test="status != null ">and t1.status = #{status}</if>
            <if test="message != null  and message != ''">and t1.message = #{message}</if>
            <if test="crdate != null ">and t1.crdate between #{crdate} and replace(#{crdate},"00:00:00","23:59:59")</if>
        </where>
        order by t1.create_time
    </select>
    <select id="selectTransactionRecordPage"
            resultType="com.jky.boot.core.module.gzpt.entity.TransactionRecord">
        select t1.id,
        t1.codenum,
        t1.tra_type,
        t1.name,
        t1.idcard,
        t1.startdate,
        t1.enddate,
        t1.counts,
        t1.orderarray,
        t1.status,
        t1.message,
        t1.crdate
        from t_m_transaction_record t1
        <choose>
            <when test="transactionRecord.traType!=null and transactionRecord.traType == 1">left join t_m_institution t2 on t1.codenum = t2.inscode</when>
            <otherwise>
                left join t_m_studentinfo t2 on t1.codenum = t2.stunum
            </otherwise>
        </choose>
        <where>
            <if test="transactionRecord.inscodes != null ">
                and t2.inscode in
                <foreach collection="transactionRecord.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="transactionRecord.codenum != null  and transactionRecord.codenum != ''">and t1.codenum = #{transactionRecord.codenum}</if>
            <if test="transactionRecord.traType != null ">and t1.tra_type = #{transactionRecord.traType}</if>
            <if test="transactionRecord.name != null  and transactionRecord.name != ''">and t1.name like concat(concat('%', #{transactionRecord.name}), '%')</if>
            <if test="transactionRecord.idcard != null  and transactionRecord.idcard != ''">and t1.idcard = #{transactionRecord.idcard}</if>
            <if test="transactionRecord.startdate != null  and transactionRecord.startdate != ''">and t1.startdate = #{transactionRecord.startdate}</if>
            <if test="transactionRecord.enddate != null  and transactionRecord.enddate != ''">and t1.enddate = #{transactionRecord.enddate}</if>
            <if test="transactionRecord.counts != null ">and t1.counts = #{transactionRecord.counts}</if>
            <if test="transactionRecord.orderarray != null  and transactionRecord.orderarray != ''">and t1.orderarray = #{transactionRecord.orderarray}</if>
            <if test="transactionRecord.status != null ">and t1.status = #{transactionRecord.status}</if>
            <if test="transactionRecord.message != null  and transactionRecord.message != ''">and t1.message = #{transactionRecord.message}</if>
            <if test="transactionRecord.crdate != null ">and t1.crdate between #{transactionRecord.crdate} and replace(#{transactionRecord.crdate},"00:00:00","23:59:59")</if>
        </where>
        order by t1.create_time
    </select>

</mapper>
