package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.TotalTime;
import com.jky.boot.core.module.gzpt.mapper.TotalTimeMapper;
import com.jky.boot.core.module.gzpt.service.IClassRecordDetailService;
import com.jky.boot.core.module.gzpt.service.IStageTrainningTimeService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.service.ITotalTimeService;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

 /**
 * @Description: t_m_total_time
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="学时汇总对象")
@RestController
@RequestMapping("/gzpt/totalTime")
@Slf4j
public class TotalTimeController extends JeecgController<TotalTime, ITotalTimeService> {
	@Autowired
	private ITotalTimeService totalTimeService;
	@Autowired
	private JkySysDepartService sysDeptService;
	@Autowired
	private IClassRecordDetailService iClassRecordDetailService;
	@Autowired
	private TotalTimeMapper totalTimeMapper;
	 @Autowired
	 private IStageTrainningTimeService stageTrainningTimeService;
	 @Autowired
	 private IStudentinfoService studentinfoService;
	 @Autowired
	 private RedissonLockClient redissonLock;


	/**
	 * 分页列表查询
	 *
	 * @param totalTime
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "学时汇总-分页列表查询", operateType = CommonConstant.OPERATE_TYPE_1)
	@ApiOperation(value = "学时汇总-分页列表查询", notes = "学时汇总-分页列表查询")
	@GetMapping(value = "/list")
	@DesensitizedAnno
//	@RequiresPermissions("gzpt:totaltime:list")
//	@PermissionData(pageComponent="base/BaseCompInfoList")
	public Result<IPage<TotalTime>> queryPageList(TotalTime totalTime,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
//		QueryWrapper<TotalTime> queryWrapper = QueryGenerator.initQueryWrapper(totalTime, req.getParameterMap());

		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(totalTime.getInscode(),totalTime.getInscodes());
		totalTime.setInscodes(inscode);

		Page<TotalTime> page = new Page<TotalTime>(pageNo, pageSize);
		IPage<TotalTime> pageList = totalTimeService.selectTotalTimeListPage(page,totalTime);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param totalTime
	 * @return
	 */
	@AutoLog(value = "学时汇总列表-添加")
	@ApiOperation(value="学时汇总列表-添加", notes="学时汇总列表-添加")
	@RequiresPermissions("gzpt:totaltime:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TotalTime totalTime) {
		totalTimeService.save(totalTime);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param totalTime
	 * @return
	 */
	@AutoLog(value = "学时汇总列表-编辑")
	@ApiOperation(value="学时汇总列表-编辑", notes="学时汇总列表-编辑")
	@RequiresPermissions("gzpt:totaltime:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TotalTime totalTime) {
		totalTimeService.updateById(totalTime);
		return Result.OK("编辑成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "学时汇总列表-批量删除")
	@ApiOperation(value="学时汇总列表-批量删除", notes="学时汇总列表-批量删除")
	@RequiresPermissions("gzpt:totaltime:remove")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.totalTimeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}


	 /**
	  *   通过id删除
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "学时汇总列表-通过id删除")
	 @ApiOperation(value="学时汇总列表-通过id删除", notes="学时汇总列表-通过id删除")
	 @RequiresPermissions("gzpt:totaltime:remove")
	 @DeleteMapping(value = "/delete")
	 public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		 totalTimeService.removeById(id);
		 return Result.OK("删除成功!");
	 }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "学时汇总列表-通过id查询")
	@ApiOperation(value="学时汇总列表-通过id查询", notes="学时汇总列表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TotalTime> queryById(@RequestParam(name="id",required=true) String id) {
		TotalTime totalTime = totalTimeService.getById(id);
		if(totalTime==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(totalTime);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param totalTime
//    */
//	@AutoLog(value = "导出学时汇总列表", operateType = CommonConstant.OPERATE_TYPE_6)
//	@ApiOperation(value = "导出学时汇总列表", notes = "导出学时汇总列表")
//    @RequiresPermissions("gzpt:totaltime:export")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TotalTime totalTime) {
//		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(totalTime.getInscode(),totalTime.getInscodes());
//		totalTime.setInscodes(inscode);
//		List<TotalTime> list = totalTimeService.selectTotalTimeList(totalTime);
//
//
//
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject(NormalExcelConstants.FILE_NAME, "学时汇总");
//		mv.addObject(NormalExcelConstants.CLASS, TotalTime.class);
//		mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学时汇总", "导出人:" + JkSecurityUtils.getRealname(), "学时汇总"));
//		mv.addObject(NormalExcelConstants.DATA_LIST, list);
//		return mv;
//    }


	 @AutoLog(value = "生成报审")
	 @ApiOperation(value = "生成报审", notes = "生成报审")
	 @RequiresPermissions("gzpt:totaltime:craStage")
	 @GetMapping(value = "/craStage/{id}")
	 public Result<?> craStage(@PathVariable("id") Long id) {
		 String key = "craStage-"+id;
		 if(redissonLock.existKey(key)){
			 return Result.error("生成报审中，请稍后再试！！");
		 }
		 try {

			 if (redissonLock.tryLock(key, -1, 10000)) {
				 TotalTime totalTime = totalTimeService.selectByid(id);
				 String result = iClassRecordDetailService.CrStageTrainningTime(totalTime);
				 if (result != null) {
					 return Result.error(result);
				 }
				 totalTime.setIsBaoshen("1");
				 totalTimeMapper.updateTotalTime(totalTime);
				 return Result.OK("操作成功");
			 }
		 }catch (Exception e){
		 	log.error("生成报审失败",e);
			 return Result.error("生成报审失败");
		 }finally {
			 redissonLock.unlock(key);
		 }
		 return Result.OK();
	 }

	 /**
	  * 重新汇总学时
	  *
	  * @return
	  */
//	 @RequiresPermissions("gzpt:totaltime:summaryTotalTime")
	 @RequestMapping(value = "/summaryTotalTime", method = RequestMethod.POST)
	 public Result<?> summaryTotalTime(@RequestBody Map<String,Object> map) {
		 String key = "summaryTotalTime-"+map.get("stunum").toString();
		 if(redissonLock.existKey(key)){
			 return Result.error("学时汇总中，请稍后再试！！");
		 }
		 try {
			 if (redissonLock.tryLock(key, -1, 10000)) {
				 if (Objects.isNull(map.get("stunum"))) {
					 return Result.error("学员为空！");
				 }
				 if (Objects.isNull(map.get("subject"))) {
					 return Result.error("科目为空！");
				 }
				 String stunum = map.get("stunum").toString();
				 int km = Integer.parseInt(map.get("subject").toString());
				 Studentinfo studentInfo = studentinfoService.getStudentByStunum(stunum);
				 boolean isFirst = true;
				 TotalTime totalTime = totalTimeMapper.selectByStunumAndSubject(stunum, km);
				 List<ClassRecordDetail> classRecordDetails = iClassRecordDetailService.list(new LambdaQueryWrapper<ClassRecordDetail>().eq(ClassRecordDetail::getStunum, stunum).apply("substring(subjcode,4,1) = {0}", km));
				 if (classRecordDetails.isEmpty()) {
					 return Result.error("该阶段电子教学日志不存在");
				 }
				 int i = 0;
				 for (ClassRecordDetail classRecordDetail : classRecordDetails) {

					 if (Objects.isNull(totalTime)) {
						 totalTime = new TotalTime();
						 totalTime = iClassRecordDetailService.getTotalTime(classRecordDetail, 1, totalTime);
						 totalTime.setStunum(classRecordDetail.getStunum());
						 totalTime.setStuname(classRecordDetail.getStuname());
						 totalTime.setSubject((long) km);
						 totalTime.setIdcard(studentInfo.getIdcard());
						 totalTime.setIsBaoshen("0");
						 totalTime.setCrdate(new Date());
						 totalTime.setTraintype(classRecordDetail.getTrainCarType());
						 totalTime.setInscode(studentInfo.getInscode());
						 totalTime.setInsname(studentInfo.getInsname());
						 totalTime.setDistrict(studentInfo.getDistrict());
						 isFirst = false;
						 i = 1;
					 } else {
						 if (isFirst) {
							 totalTime.setNetworktime(0L);
							 totalTime.setVehicletime(0L);
							 totalTime.setSimulatortime(0L);
							 totalTime.setClasstime(0L);
							 totalTime.setMileage(new BigDecimal(0));
							 isFirst = false;
						 }
						 totalTime = iClassRecordDetailService.getTotalTime(classRecordDetail, 2, totalTime);
					 }
				 }
				 iClassRecordDetailService.CrStageTrainningTime(totalTime);
				 if (i ==1){
					 totalTimeService.save(totalTime);
				 }else {
					 totalTimeService.updateById(totalTime);
				 }
				 return Result.ok("汇总学时成功");
			 }
		 }catch (Exception e){
			 log.error(e.getMessage());
			 return Result.error("学时汇总失败");
		 }finally {
			 redissonLock.unlock(key);
		 }
		 return Result.OK();
	 }

	 /**
	  * 加密接口
	  */
	 @GetMapping("/encrypt")
	 public Result<?> encryptData() {
		 QueryWrapper<TotalTime> wrapper = new QueryWrapper<>();
		 wrapper.setEntityClass(TotalTime.class);
		 wrapper.eq("LENGTH(idcard)", 18);
		 wrapper.last("limit 200");
		 List<TotalTime> list = totalTimeService.list(wrapper);
		 totalTimeService.updateBatchById(list);
		 return Result.ok();
	 }
}
