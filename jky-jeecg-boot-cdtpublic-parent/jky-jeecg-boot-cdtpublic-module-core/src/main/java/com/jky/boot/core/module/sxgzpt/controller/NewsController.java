package com.jky.boot.core.module.sxgzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.News;
import com.jky.boot.core.module.sxgzpt.service.INewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * 通知管理
 *
 * <AUTHOR>
 */
@Api(tags = "通知管理")
@RestController
@RequestMapping("/news")
@Slf4j
public class NewsController extends JeecgController<News, INewsService> {
	private final INewsService newsService;

	public NewsController(INewsService newsService) {
		this.newsService = newsService;
	}

	/**
	 * 分页列表查询
	 */
	@ApiOperation(value = "通知管理-分页列表查询", notes = "通知管理-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<News>> queryPageList(News news,
											 @RequestParam(name = "pageNo", defaultValue = "1")
											 Integer pageNo,
											 @RequestParam(name = "pageSize", defaultValue = "10")
											 Integer pageSize,
											 HttpServletRequest req) {
		QueryWrapper<News> queryWrapper = QueryGenerator.initQueryWrapper(news, req.getParameterMap());
		queryWrapper.orderByDesc("create_time");
		Page<News> page = new Page<>(pageNo, pageSize);
		IPage<News> pageList = newsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *	zlb list (only pub)
	 */
	@GetMapping(value = "/pubList")
	public Result<IPage<News>> queryPubList(News news,
											@RequestParam(name = "pageNo", defaultValue = "1")
											Integer pageNo,
											@RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize,
											HttpServletRequest req) {
		QueryWrapper<News> queryWrapper = QueryGenerator.initQueryWrapper(news, req.getParameterMap());
		queryWrapper.eq("status", 1);
		queryWrapper.orderByDesc("create_time");
		Page<News> page = new Page<>(pageNo, pageSize);
		IPage<News> pageList = newsService.page(page, queryWrapper);
		return Result.ok(pageList);
	}


	/**
	 * 分页列表查询
	 */
	@ApiOperation(value = "通知管理-分页列表查询", notes = "通知管理-分页列表查询")
	@GetMapping(value = "/hege")
	public Result<IPage<News>> queryZlbList(News news,
											@RequestParam(name = "pageNo", defaultValue = "1")
											Integer pageNo,
											@RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize,
											HttpServletRequest req) {
		QueryWrapper<News> queryWrapper = QueryGenerator.initQueryWrapper(news, req.getParameterMap());
		queryWrapper.eq("status", 1);
		queryWrapper.eq("type", 0).or().eq("type", 1);
		queryWrapper.orderByDesc("create_time");
		Page<News> page = new Page<>(pageNo, pageSize);
		IPage<News> pageList = newsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	@ApiOperation(value = "通知管理-分页列表查询", notes = "通知管理-分页列表查询")
	@GetMapping(value = "/tongzhi")
	public Result<IPage<News>> queryNoticeList(News news,
											   @RequestParam(name = "pageNo", defaultValue = "1")
											   Integer pageNo,
											   @RequestParam(name = "pageSize", defaultValue = "10")
											   Integer pageSize,
											   HttpServletRequest req) {
		QueryWrapper<News> queryWrapper =
				QueryGenerator.initQueryWrapper(news, req.getParameterMap());
		queryWrapper.eq("status", 1);
		queryWrapper.eq("type", 2);
		queryWrapper.orderByDesc("create_time");
		Page<News> page = new Page<>(pageNo, pageSize);
		IPage<News> pageList = newsService.page(page, queryWrapper);

		return Result.OK(pageList);
	}

	/**
	 * 添加
	 */
	@AutoLog(value = "通知管理-添加")
	@ApiOperation(value = "通知管理-添加", notes = "通知管理-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody News news) {
		newsService.save(news);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 */
	@AutoLog(value = "通知管理-编辑")
	@ApiOperation(value = "通知管理-编辑", notes = "通知管理-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	public Result<String> edit(@RequestBody News news) {
		newsService.updateById(news);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 */
	@AutoLog(value = "通知管理-通过id删除")
	@ApiOperation(value = "通知管理-通过id删除", notes = "通知管理-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id") String id) {
		newsService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 */
	@AutoLog(value = "通知管理-批量删除")
	@ApiOperation(value = "通知管理-批量删除", notes = "通知管理-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
		this.newsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 */
	@ApiOperation(value = "通知管理-通过id查询", notes = "通知管理-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<News> queryById(@RequestParam(name = "id") String id) {
		News news = newsService.getById(id);
		if (news == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(news);
	}
//
//	/**
//	 * 导出excel
//	 */
//	@RequestMapping(value = "/exportXls")
//	public ModelAndView exportXls(HttpServletRequest request, News news) {
//		return super.exportXls(request, news, News.class, "通知管理");
//	}
//
//	/**
//	 * 通过excel导入数据
//	 */
//	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//		return super.importExcel(request, response, News.class);
//	}

	/**
	 * 发布 / 撤回
	 */
	@RequestMapping(value = "/pubOrCancel", method = RequestMethod.POST)
	public Result<?> pubNews(String ids, Integer status) {
		List<String> list = Arrays.asList(ids.split(","));
		newsService.update(
			Wrappers.<News>lambdaUpdate()
				.set(News::getStatus, status)
				.in(News::getId, list)
		);
		return Result.ok("操作成功！");
	}
}
