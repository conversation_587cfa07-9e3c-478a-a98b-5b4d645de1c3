package com.jky.boot.core.module.gzpt.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监管学员转校记录返回
 */
@Data
@ApiModel(value = "t_m_transfer对象", description = "监管学员转校记录返回")
public class JgTransferVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    //学员编号
    private String stunum;
    //学员姓名
    private String name;
    //身份证号
    private String idcard;
    //原驾校
    private String oldInscode;
    private String oldInsname;
    //待转入驾校
    private String inscode;
    private String insname;
    //状态 0-未审核 1-同意转入 2-不同意
    private Integer status;
    //审核时间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditDate;
    //不同意原因
    private String reason;
    //申请时间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crdate;

    private Date updateTime;

    //备案平台
    private String platfrom;
    //县区
    private String district;

    private String operatorName;

    //转校技能证明图片ID
    private Long imgid;

}
