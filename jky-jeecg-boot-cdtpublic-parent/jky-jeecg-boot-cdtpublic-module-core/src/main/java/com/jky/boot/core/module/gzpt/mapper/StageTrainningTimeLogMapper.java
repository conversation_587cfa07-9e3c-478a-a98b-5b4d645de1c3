package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.StageTrainningTimeLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 校验结果对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface StageTrainningTimeLogMapper extends BaseMapper<StageTrainningTimeLog> {

    /**
     * 查询校验结果
     *
     * @param stageid 校验结果主键
     * @return 校验结果
     */
    StageTrainningTimeLog selectStageTrainningTimeLogByStageid(Long stageid);

    /**
     * 查询校验结果列表
     *
     * @param stageTrainningTimeLog 校验结果
     * @return 校验结果集合
     */
    List<StageTrainningTimeLog> selectStageTrainningTimeLogList(StageTrainningTimeLog stageTrainningTimeLog);

    /**
     * 新增校验结果
     *
     * @param stageTrainningTimeLog 校验结果
     * @return 结果
     */
    int insertStageTrainningTimeLog(StageTrainningTimeLog stageTrainningTimeLog);

    /**
     * 修改校验结果
     *
     * @param stageTrainningTimeLog 校验结果
     * @return 结果
     */
    int updateStageTrainningTimeLog(StageTrainningTimeLog stageTrainningTimeLog);

    /**
     * 删除校验结果
     *
     * @param stageid 校验结果主键
     * @return 结果
     */
    int deleteStageTrainningTimeLogByStageid(Long stageid);

    /**
     * 批量删除校验结果
     *
     * @param stageids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteStageTrainningTimeLogByStageids(Long[] stageids);
}
