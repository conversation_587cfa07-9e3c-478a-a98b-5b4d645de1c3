package com.jky.boot.core.module.coachScheduling.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.coachScheduling.entity.CoachRest;
import com.jky.boot.core.module.coachScheduling.entity.SchedulingConfig;
import com.jky.boot.core.module.coachScheduling.mapper.CoachRestMapper;
import com.jky.boot.core.module.coachScheduling.service.ICoachRestService;
import com.jky.boot.core.module.coachScheduling.service.ISchedulingConfigService;
import com.jky.boot.core.module.gzpt.entity.Coach;
import com.jky.boot.core.module.gzpt.service.ICoachService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: coach_rest
 * @Author: jeecg-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
@Service
public class CoachRestServiceImpl extends ServiceImpl<CoachRestMapper, CoachRest> implements ICoachRestService {

    @Autowired
    private ICoachService coachService;

    @Autowired
    private ISchedulingConfigService schedulingConfigService;

    @Override
    @Transactional
    public void add(CoachRest coachRest) {
        LambdaQueryWrapper<Coach> wrapper = Wrappers.lambdaQuery(Coach.class)
                .eq(Coach::getCoachnum, coachRest.getCoachNum());
        Coach coach = coachService.getOne(wrapper);
        coachRest.setCoachName(coach.getName());
        coachRest.setInscode(coach.getInscode());
        List<CoachRest> list = baseMapper.queryCoachRest(coachRest);
        if(list.size() > 0){
            throw new RuntimeException("该教练已存在这段时间的请假或休息");
        }
        coachRest.setIdCard(coach.getIdcard());
        this.save(coachRest);
        alterScheduling(coachRest);
    }

    @Override
    public void alterScheduling(CoachRest coachRest) {
        LambdaQueryWrapper<SchedulingConfig> schedulingConfigLambdaQueryWrapper = Wrappers.lambdaQuery(SchedulingConfig.class)
                .eq(SchedulingConfig::getInscode, coachRest.getInscode());
        SchedulingConfig schedulingConfig = schedulingConfigService.getOne(schedulingConfigLambdaQueryWrapper);
        if(schedulingConfig != null){
            if(coachRest.getStartDate().getHours() == 12){
                coachRest.setStartDate(DateUtils.setMinutesHour(coachRest.getStartDate(),schedulingConfig.getRestEndTime() == null ? 720 + (schedulingConfig.getWorkerStartTime() % 60) : schedulingConfig.getRestEndTime()));
            }else{
                coachRest.setStartDate(DateUtils.setMinutesHour(coachRest.getStartDate(),schedulingConfig.getWorkerStartTime()));
            }
            if(coachRest.getEndDate().getHours() == 12){
                coachRest.setEndDate(DateUtils.setMinutesHour(coachRest.getEndDate(),schedulingConfig.getRestEndTime() == null ? 780 + (schedulingConfig.getWorkerStartTime() % 60) : schedulingConfig.getRestEndTime()));
            }else{
                coachRest.setEndDate(DateUtils.setMinutesHour(coachRest.getEndDate(),schedulingConfig.getWorkerEndTime()));
            }
        }
        baseMapper.alterScheduling(coachRest);
    }



}
