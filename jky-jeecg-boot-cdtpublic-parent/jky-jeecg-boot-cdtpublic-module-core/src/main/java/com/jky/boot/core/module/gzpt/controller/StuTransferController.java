package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.StuTransfer;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IStuTransferService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.CancelAccountUtils;
import com.jky.boot.core.module.gzpt.utils.PushJgUtils;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import com.jky.boot.core.util.AESCryptoUtil;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.boot.zlb.entity.TMSupervise;
import com.jky.boot.zlb.service.ITMSuperviseService;
import com.jky.boot.zlb.vo.StuTransferProcessProgressVo;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学员转校
 *
 * <AUTHOR>
 * @version 2023-12-18
 */
@Api(tags = "学员转校")
@RestController
@RequestMapping("/gzpt/stuTransfer")
@Slf4j
public class StuTransferController extends JeecgController<StuTransfer, IStuTransferService> {

	@Autowired
	private IInstitutionService insService;
	@Autowired
	private IStudentinfoService stuInfoService;
	@Autowired
	private IStuTransferService stuTransferService;
	@Autowired
	private ITMSuperviseService superviseService;

	/**
	 * 分页列表查询-转出驾校
	 */
	@ApiOperation(value = "学员转校-分页列表查询", notes = "学员转校-分页列表查询")
	@GetMapping(value = "/list")
	@DesensitizedAnno
	public Result<IPage<StuTransfer>> queryPageList(StuTransfer stuTransfer,
													@RequestParam(name = "pageNo", defaultValue = "1")
													Integer pageNo,
													@RequestParam(name = "pageSize", defaultValue = "10")
													Integer pageSize,
													HttpServletRequest req) {
		List<String> oldInsCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
		QueryWrapper<StuTransfer> queryWrapper = QueryGenerator.initQueryWrapper(stuTransfer, req.getParameterMap());
		queryWrapper.in(CollectionUtils.isNotEmpty(oldInsCodes), "old_inscode", oldInsCodes);
		queryWrapper.orderByDesc("crdate");
		Page<StuTransfer> page = new Page<>(pageNo, pageSize);
		IPage<StuTransfer> pageList = stuTransferService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 分页列表查询-转入驾校
	 */
	@ApiOperation(value = "学员转校-分页列表查询", notes = "学员转校-分页列表查询")
	@GetMapping(value = "/list/new")
	@DesensitizedAnno
	public Result<IPage<StuTransfer>> queryPageListNew(StuTransfer stuTransfer,
													   @RequestParam(name = "pageNo", defaultValue = "1")
													   Integer pageNo,
													   @RequestParam(name = "pageSize", defaultValue = "10")
													   Integer pageSize,
													   HttpServletRequest req) {
		List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
		QueryWrapper<StuTransfer> queryWrapper = QueryGenerator.initQueryWrapper(stuTransfer, req.getParameterMap());
		queryWrapper.in(CollectionUtils.isNotEmpty(insCodes), "inscode", insCodes);
		queryWrapper.eq("ins_audit", 1);
		queryWrapper.orderByDesc("crdate");
		Page<StuTransfer> page = new Page<>(pageNo, pageSize);
		IPage<StuTransfer> pageList = stuTransferService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 */
	@AutoLog(value = "学员转校-添加")
	@ApiOperation(value = "学员转校-添加", notes = "学员转校-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody StuTransfer stuTransfer) {
		stuTransferService.save(stuTransfer);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 */
	@AutoLog(value = "学员转校-编辑")
	@ApiOperation(value = "学员转校-编辑", notes = "学员转校-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	public Result<String> edit(@RequestBody StuTransfer stuTransfer) {
		stuTransferService.updateById(stuTransfer);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 */
	@AutoLog(value = "学员转校-通过id删除")
	@ApiOperation(value = "学员转校-通过id删除", notes = "学员转校-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id") String id) {
		stuTransferService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 */
	@AutoLog(value = "学员转校-批量删除")
	@ApiOperation(value = "学员转校-批量删除", notes = "学员转校-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
		this.stuTransferService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 */
	@ApiOperation(value = "学员转校-通过id查询", notes = "学员转校-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<StuTransfer> queryById(@RequestParam(name = "id") String id) {
		StuTransfer stuTransfer = stuTransferService.getById(id);
		if (stuTransfer == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(stuTransfer);
	}

	/**
	 * 提交转校申请（公众页面）
	 */
	@PostMapping("/apply")
	public Result<?> transferApply(@RequestBody StuTransfer transfer) {
		return stuTransferService.transferApply(transfer);
	}

	/**
	 * 审核-转出驾校
	 */
	@PostMapping("/audit")
	public Result<?> audit(@RequestBody StuTransfer rec) {
		StuTransfer transfer = stuTransferService.getById(rec.getId());
		if (transfer.getInsAudit() != 0) {
			return Result.error("转出驾校已审核，请勿重复操作！");
		}
		// 学员申请转校后，驾校如有变更，审核时作废当前转校记录
		Result<?> result = CancelAccountUtils.stuInfoCheck(transfer);
		if (!result.isSuccess()) {
			transfer.setFlow(4);
			transfer.setInsReason(result.getMessage());
			stuTransferService.updateById(transfer);
			return result;
		}
		transfer.setInsAudit(rec.getInsAudit());
		transfer.setInsAuditDate(new Date());
		transfer.setInsReason(rec.getInsReason());
		// 如果审核不通过，转校流程结束
		if (transfer.getInsAudit() == 2) {
			transfer.setFlow(4);
		}
		stuTransferService.updateById(transfer);
		return Result.ok("审核成功！");
	}

	/**
	 * 审核-转入驾校
	 */
	@PostMapping("/audit/new")
	public Result<?> auditNew(@RequestBody StuTransfer rec) {
		StuTransfer transfer = stuTransferService.getById(rec.getId());
		if (transfer.getStatus() != 0) {
			return Result.error("转入驾校已审核，请勿重复操作！");
		}
		// 学员申请转校后，驾校如有变更，审核时作废当前转校记录
		Result<?> result = CancelAccountUtils.stuInfoCheck(transfer);
		if (!result.isSuccess()) {
			transfer.setFlow(4);
			transfer.setReason(result.getMessage());
			stuTransferService.updateById(transfer);
			return result;
		}
		transfer.setStatus(rec.getStatus());
		transfer.setAuditDate(new Date());
		transfer.setReason(rec.getReason());
		if (rec.getStatus() == 1) {
			// 开启转校流程
//			CancelAccountUtils.doTrans(transfer);
			CommonResponse resp = PushJgUtils.pushJg("/transfer", transfer);
			log.info("httpApi_transfer: {}", resp);
			if (resp.getErrorcode() != 0) {
				return Result.error("转校发送监管失败：" + resp.getMessage());
			}
			transfer.setFlow(40);
			//监管待审核
			transfer.setJgStatus(0);
			if (StringUtils.isBlank(rec.getReason())) {
				transfer.setReason("新驾校同意转入");
			}
		}
//		else {
//			transfer.setFlow(4);
//		}
		stuTransferService.updateById(transfer);
		return Result.ok("审核成功！");
	}


	/**
	 * 组装类
	 */
	private StuTransfer genVo(Studentinfo stu, Institution newIns) {
		StuTransfer res = new StuTransfer();
		res.setName(stu.getName());
		res.setIdcard(stu.getIdcard());
		res.setOldInscode(stu.getInscode());
		res.setOldInsname(stu.getInsname());
		res.setStunum(stu.getStunum());
		res.setInscode(newIns.getInscode());
		res.setInsname(newIns.getName());
		return res;
	}

	/**
	 * 手动通知监管
	 */
	@PostMapping("/notifyJg")
	public Result<?> notifyJg(@RequestBody StuTransfer transfer) {
		StuTransfer byId = stuTransferService.getById(transfer.getId());
		if (byId.getStatus() != 1 || byId.getInsAudit() != 1) {
			return Result.error("审核未通过，不能执行后续操作！");
		}
		if (byId.getFlow() != 42) {
			return Result.error("非法操作：42");
		}
		CommonResponse resp = PushJgUtils.pushJg("/transfer", byId);
		byId.setFlow(resp.getErrorcode() == 0 ? 4:42);
		stuTransferService.updateById(byId);
		if (resp.getErrorcode() != 0) {
			return Result.error(resp.getMessage());
		}
		return Result.ok(resp.getMessage());
	}


	// zlb 调用  ===================================================================================

	/**
	 * 提交转校申请（zlb）
	 */
	@PostMapping("/zlb/apply")
	@RedissonLock(key = "#transfer.stunum", keyPrefix = "zlb:trans:", waitTime = 1, lockTime = 10)
	public Result<?> zlbTransferApply(@RequestBody StuTransfer transfer) {
		return stuTransferService.transferApply(transfer);
	}


	@PostMapping("/processProgress")
	public Result<?> processProgress(@RequestBody String transfer){
		StuTransfer stuTransfer = AESCryptoUtil.decrypt(transfer, StuTransfer.class);
		if (Objects.isNull(Objects.requireNonNull(stuTransfer).getStunum())){
			return Result.error("学员编号不能为空");
		}
		Studentinfo studentinfo = stuInfoService.getOneByStuNum(stuTransfer.getStunum());
		if (Objects.isNull(studentinfo)){
			return Result.ok("未找到该学员");
		}
		stuTransfer = stuTransferService.getOne(new LambdaQueryWrapper<>(StuTransfer.class)
				.eq(StuTransfer::getStunum, stuTransfer.getStunum())
				.eq(StuTransfer::getOldInscode,studentinfo.getInscode())
				.orderByDesc(StuTransfer::getCrdate),false);
		if (Objects.isNull(stuTransfer) || Objects.equals(stuTransfer.getFlow(),4) ){
			return Result.ok();
		}
		StuTransferProcessProgressVo stuTransferProcessProgressVo = new StuTransferProcessProgressVo();
		stuTransferProcessProgressVo.setApplicationTime(stuTransfer.getCrdate());
		stuTransferProcessProgressVo.setFlow(1);
		stuTransferProcessProgressVo.setInsAudit(stuTransfer.getInsAudit());
		stuTransferProcessProgressVo.setStatus(stuTransfer.getStatus());
		stuTransferProcessProgressVo.setIsTransfer(0);
		if (Objects.nonNull(stuTransfer.getInsAuditDate())){
			stuTransferProcessProgressVo.setFlow(2);
			stuTransferProcessProgressVo.setInsAuditDate(stuTransfer.getInsAuditDate());
			if (stuTransfer.getInsAudit()!=1){
				stuTransferProcessProgressVo.setInsReason(stuTransfer.getInsReason());
			}else {
				stuTransferProcessProgressVo.setInsReason("转出驾校审核通过");
			}
		}
		if (Objects.nonNull(stuTransfer.getAuditDate())){
			stuTransferProcessProgressVo.setFlow(3);
			stuTransferProcessProgressVo.setAuditDate(stuTransfer.getAuditDate());

			if (stuTransfer.getStatus()!=1){
				stuTransferProcessProgressVo.setReason(stuTransfer.getReason());
			}else {
				stuTransferProcessProgressVo.setReason("转入驾校审核通过");
			}
		}
		if (Objects.equals(stuTransfer.getFlow(),4)){
			stuTransferProcessProgressVo.setFlow(4);
			stuTransferProcessProgressVo.setIsTransfer(1);
		}

		return Result.ok(stuTransferProcessProgressVo);
	}



	/**
	 * 接收监管审核结果
	 */
	@PostMapping("/receive")
	public Result<?> receive(@RequestBody StuTransfer transfer) {
		log.info("接收的transfer：{}", transfer.toString());
		StuTransfer one = stuTransferService.getOne(
				Wrappers.lambdaQuery(StuTransfer.class)
						.eq(StuTransfer::getStunum, transfer.getStunum())
						.eq(StuTransfer::getJgStatus, 0)
						.orderByDesc(StuTransfer::getCrdate)
						.last("limit 1")
		);
		if(Objects.isNull(one)){
			return Result.error("公众端未查询到学员的转校记录");
		}
		one.setJgStatus(transfer.getStatus());
		one.setJgAuditDate(new Date());
		if (Objects.isNull(transfer.getReason())){
			one.setJgReason("");
		}else {
			one.setJgReason(transfer.getReason());
		}
		if(Objects.equals(transfer.getStatus(),1)){
			one.setFlow(4);
		}else {
			one.setFlow(42);
		}
		stuTransferService.updateById(one);
		// 审核成功，开启转校流程
//		Result<?> result = doTrans(one);
		return Result.ok();
	}

}
