package com.jky.boot.zlb.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_studentinfo_logout
 * @Author: jeecg-boot
 * @Date:   2023-06-19
 * @Version: V1.0
 */
@Data
@TableName("t_m_studentinfo_logout")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_studentinfo_logout对象", description="t_m_studentinfo_logout")
public class TMStudentinfoLogout implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.Integer id;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private java.lang.String inscode;
	/**证件类型 	1:身份证
2:护照
3:军官证
4:其他*/
	@Excel(name = "证件类型 	1:身份证 2:护照 3:军官证 4:其他", width = 15)
    @ApiModelProperty(value = "证件类型 	1:身份证 2:护照 3:军官证 4:其他")
    private java.lang.String cardtype;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private java.lang.String idcard;
	/**国籍*/
	@Excel(name = "国籍", width = 15)
    @ApiModelProperty(value = "国籍")
    private java.lang.String nationality;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**性别	1:男性;2:女性*/
	@Excel(name = "性别	1:男性;2:女性", width = 15)
    @ApiModelProperty(value = "性别	1:男性;2:女性")
    private java.lang.String sex;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private java.lang.String phone;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
    private java.lang.String address;
	/**照片文件ID	  成功上传的学员头像照片文件ID*/
	@Excel(name = "照片文件ID	  成功上传的学员头像照片文件ID", width = 15)
    @ApiModelProperty(value = "照片文件ID	  成功上传的学员头像照片文件ID")
    private java.lang.Integer photo;
	/**业务类型  	0:初领
1:增领
9:其他*/
	@Excel(name = "业务类型  	0:初领 1:增领 9:其他", width = 15)
    @ApiModelProperty(value = "业务类型  	0:初领 1:增领 9:其他")
    private java.lang.String busitype;
	/**驾驶证号*/
	@Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
    private java.lang.String drilicnum;
	/**驾驶证初领日期  	YYYYMMDD*/
	@Excel(name = "驾驶证初领日期  	YYYYMMDD", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期  	YYYYMMDD")
    private java.lang.String fstdrilicdate;
	/**培训车型 	下列编码单选：
 A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String traintype;
	/**报名时间 	YYYYMMDD*/
	@Excel(name = "报名时间 	YYYYMMDD", width = 15)
    @ApiModelProperty(value = "报名时间 	YYYYMMDD")
    private java.lang.String applydate;
	/**图片路径*/
	@Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
    private java.lang.String photopath;
	/**驾校名称*/
	@Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private java.lang.String insname;
	/**学员统一编号*/
	@Excel(name = "学员统一编号", width = 15)
    @ApiModelProperty(value = "学员统一编号")
    private java.lang.String stunum;
	/**银行编号*/
	@Excel(name = "银行编号", width = 15)
    @ApiModelProperty(value = "银行编号")
    private java.lang.String bankcode;
	/**银行名称*/
	@Excel(name = "银行名称", width = 15)
    @ApiModelProperty(value = "银行名称")
    private java.lang.String bankname;
	/**是否签约合同 0未签约,1已签约,2签约失败*/
	@Excel(name = "是否签约合同 0未签约,1已签约,2签约失败", width = 15)
    @ApiModelProperty(value = "是否签约合同 0未签约,1已签约,2签约失败")
    private java.lang.String issigncontract;
	/**是否推送全国 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送全国 0未推送,1已推送,2推送失败", width = 15)
    @ApiModelProperty(value = "是否推送全国 0未推送,1已推送,2推送失败")
    private java.lang.Integer istoqg;
	/**是否推送计时 0未推送,1已推送,2推送失败*/
	@Excel(name = "是否推送计时 0未推送,1已推送,2推送失败", width = 15)
    @ApiModelProperty(value = "是否推送计时 0未推送,1已推送,2推送失败")
    private java.lang.Integer istojs;
	/**原准驾车型*/
	@Excel(name = "原准驾车型", width = 15)
    @ApiModelProperty(value = "原准驾车型")
    private java.lang.String perdritype;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**创建者*/
    @ApiModelProperty(value = "创建者")
    private java.lang.String createBy;
	/**更新者*/
    @ApiModelProperty(value = "更新者")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
	/**基础费用*/
	@Excel(name = "基础费用", width = 15)
    @ApiModelProperty(value = "基础费用")
    private java.lang.String basicCost;
	/**第二部分费用*/
	@Excel(name = "第二部分费用", width = 15)
    @ApiModelProperty(value = "第二部分费用")
    private java.lang.String secondCost;
	/**第三部分费用*/
	@Excel(name = "第三部分费用", width = 15)
    @ApiModelProperty(value = "第三部分费用")
    private java.lang.String thirdCoast;
	/**冻结金额*/
	@Excel(name = "冻结金额", width = 15)
    @ApiModelProperty(value = "冻结金额")
    private java.lang.String frozenAmount;
	/**教练编号*/
	@Excel(name = "教练编号", width = 15)
    @ApiModelProperty(value = "教练编号")
    private java.lang.String coachnum;
	/**厂商编号*/
	@Excel(name = "厂商编号", width = 15)
    @ApiModelProperty(value = "厂商编号")
    private java.lang.String platform;
	/**类型  1.退学2.注销*/
	@Excel(name = "类型  1.退学2.注销", width = 15)
    @ApiModelProperty(value = "类型  1.退学2.注销")
    private java.lang.Integer type;
	/**是否为跨地市转校*/
	@Excel(name = "是否为跨地市转校", width = 15)
    @ApiModelProperty(value = "是否为跨地市转校")
    private java.lang.String iscrosscity;
	/**原驾校所在地市名称*/
	@Excel(name = "原驾校所在地市名称", width = 15)
    @ApiModelProperty(value = "原驾校所在地市名称")
    private java.lang.String crosscityname;
	/**backupDate*/
	@Excel(name = "backupDate", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "backupDate")
    private java.util.Date backupDate;
}
