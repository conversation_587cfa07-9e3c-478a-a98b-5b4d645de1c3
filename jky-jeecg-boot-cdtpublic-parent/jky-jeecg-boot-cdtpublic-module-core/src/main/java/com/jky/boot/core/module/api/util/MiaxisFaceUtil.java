package com.jky.boot.core.module.api.util;

import cn.hutool.core.net.DefaultTrustManager;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.ssl.SSLSocketFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.SSLSocketFactory;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * 网证人脸检测
 *
 * <AUTHOR>
 */
@Slf4j
public class MiaxisFaceUtil {

    private static JSONObject tokenData;

    /**
     * 获取token
     */
    private static synchronized void getToken(String cid) {
        if (tokenData != null && isExpired()) {
            return;
        }
        String url = "https://oic.miaxis.com/prod-api/oms/oms/login?cid=" + cid;

        String responseContent = post(url, null, null);
        JSONObject jsonObject = JSON.parseObject(responseContent);
        if (jsonObject == null) {
            log.error("获取token失败");
            return;
        }
        if (jsonObject.getInteger("code") != 200) {
            log.error("获取token失败:{}", jsonObject.getString("message"));
            return;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        //获取失效时长 转换为失效时间戳
        data.put("expires_in", System.currentTimeMillis() + (long) data.getIntValue("expires_in") * 60 * 1000);
        tokenData = data;
    }


    /**
     * 查询认证结果
     */
    public static String recordByOrderId(String cid, String orderId) {
        if (tokenData == null || !isExpired()) {
            getToken(cid);
        }
        String url = "https://oic.miaxis.com/prod-api/oms/oms/sdk/recordByOrderId";
        log.info("根据网证流水号查询比对结果url=" + url);
        //请求头
        HashMap<String, String> handlers = new HashMap<>(16);

        handlers.put("Authorization", "Bearer " + tokenData.getString("access_token"));
        log.info("根据网证流水号查询比对结果请求头" + handlers);
        //请求体
        HashMap<String, Object> paramsMap = new HashMap<>(16);
        paramsMap.put("cid", cid);
        paramsMap.put("orderId", orderId);
        log.info("根据网证流水号查询比对结果请求体=" + paramsMap);

        String responseContent = post(url, handlers, paramsMap);
        log.info("根据流水号查询比对结果返回值=" + responseContent);
        JSONObject jsonObject = JSON.parseObject(responseContent);
        if (jsonObject == null) {
            log.error("查询比对结果失败");
            return null;
        }
        if (jsonObject.getInteger("code") != 200) {
            log.error("查询比对结果失败:{}", jsonObject.getString("msg"));
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null) {
            return null;
        }
        log.info(data.toJSONString());
        return data.toJSONString();
    }

    /**
     * 判断token是否快过期
     *
     * @return true:未过期 false:快要过期
     */
    private static boolean isExpired() {
        Long expiresIn = tokenData.getLong("expires_in");
        long currentTimeMillis = System.currentTimeMillis();
        return currentTimeMillis < expiresIn - 1000 * 60 * 5;
    }

    /**
     * 发送post请求
     *
     * @param url      请求地址
     * @param handlers 请求头
     * @param map      请求参数
     * @return 请求结果
     */
    private static String post(String url, Map<String, String> handlers, Map<String, Object> map) {
        SSLSocketFactory scsf = null;
        try {
            scsf = SSLSocketFactoryBuilder.create().setTrustManagers(new MyTrustManager()).build();
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            e.printStackTrace();
        }
        String response = HttpRequest.post(url)
                .setSSLSocketFactory(scsf)
                .header(Header.CONTENT_TYPE, "application/json")
                .headerMap(handlers, true)
                .body(JSON.toJSONString(map))
                .timeout(60000)
                .execute()
                .body();
        System.out.println(response);
        return response;
    }

    static class MyTrustManager extends DefaultTrustManager {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }
}