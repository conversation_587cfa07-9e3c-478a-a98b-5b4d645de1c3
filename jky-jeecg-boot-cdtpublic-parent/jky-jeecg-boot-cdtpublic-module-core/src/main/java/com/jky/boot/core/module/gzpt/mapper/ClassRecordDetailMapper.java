package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.boot.zlb.vo.BrProVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: t_m_class_record_detail
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface ClassRecordDetailMapper extends BaseMapper<ClassRecordDetail> {
    /**
     * 查询学时管理
     *
     * @param id 学时管理主键
     * @return 学时管理
     */
    ClassRecordDetail selectClassRecordDetailById(String id);

    /**
     * 查询学时管理列表
     *
     * @param classRecordDetail 学时管理
     * @return 学时管理集合
     */
    List<ClassRecordDetail> selectClassRecordDetailList(ClassRecordDetail classRecordDetail);

    /**
     * 新增学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    int insertClassRecordDetail(ClassRecordDetail classRecordDetail);

    /**
     * 修改学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    int updateClassRecordDetail(ClassRecordDetail classRecordDetail);

    /**
     * 删除学时管理
     *
     * @param id 学时管理主键
     * @return 结果
     */
    int deleteClassRecordDetailById(String id);

    /**
     * 批量删除学时管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteClassRecordDetailByIds(String[] ids);

    //根据id查询
    List<ClassRecordDetail> findDetailByIds(String[] ids);

    @Delete("delete from t_m_class_record_detail where stunum = #{stunum}")
    int deleteClassRecordDetailByStunum(String stunum);

    ClassRecordDetail selectByStunumAndRecnum(@Param("stunum") String stunum, @Param("recnum") String recnum);

    ClassRecordDetail selectCLassRecordDetail(ClassRecordDetail classRecordDetail);

    ClassRecordDetail selectkc( @Param("subjcode")String subjcode,@Param("type")String type,@Param("ty")Integer ty);

    ClassRecordDetail selectqb(@Param("subjcode")String subjcode,@Param("type")String type,@Param("ty")Integer ty);

    ClassRecordDetail selectyc( @Param("subjcode")String subjcode,@Param("type")String type,@Param("ty")Integer ty);

    ClassRecordDetail selectmn( @Param("subjcode")String subjcode,@Param("type")String type,@Param("ty")Integer ty);

    ClassRecordDetail selectsc( @Param("subjcode")String subjcode,@Param("type")String type,@Param("ty")Integer ty);


    /**
     * 查询学员指定类别的总学时
     */
    Double queryTypeTotal(@Param("stuNum") String stuNum, @Param("type") String type);

    /**
     * 给定大纲、科目、车型，查找学时分类
     */
    List<BrProVo> getBrType(@Param("outlineType") Integer outlineType,
                            @Param("subCode") String subCode,
                            @Param("carType") String carType);
    /**
     * 查询学员指定科目的学时进度
     */
    List<BrProVo> queryBrPro(@Param("stuNum") String stuNum, @Param("subCode") String subCode);
}
