package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 题库类型
 *
 * <AUTHOR>
 * @version 2024-12-27
 */
@Data
@TableName("chapter")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Chapter {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 标题
     */
    private String title;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 车型
     */
    private String carType;
}
