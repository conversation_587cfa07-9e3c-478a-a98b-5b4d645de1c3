package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jky.boot.common.sensitive.Sensitive;
import com.jky.boot.common.sensitive.SensitiveTypeEnum;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: t_m_mr890_face_record
 * @Author: jeecg-boot
 * @Date:   2023-06-02
 * @Version: V1.0
 */
@Data
@TableName("t_m_mr890_face_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_mr890_face_record对象", description="人脸记录查询对象")
public class TMMr890FaceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

	/**设备编号*/
	@Excel(name = "设备编号", width = 15)
    @ApiModelProperty(value = "设备编号")
    private String deviceId;
	/**身份证号码*/
	@Excel(name = "身份证号码", width = 15)
    @ApiModelProperty(value = "身份证号码")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    @CryptoFieldAnno
    private String idcard;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    @CryptoFieldAnno
    private String name;
	/**0 未通过 1 通过*/
	@Excel(name = "0 未通过 1 通过", width = 15, dicCode = "sys_mr890_face_status")
    @ApiModelProperty(value = "0 未通过 1 通过")
    @Dict(dicCode = "sys_mr890_face_status")
    private Integer status;
	/**签到类型*/
	@Excel(name = "签到类型", width = 15, dicCode = "sys_mr890_sign_type")
    @ApiModelProperty(value = "签到类型")
    @Dict(dicCode = "sys_mr890_sign_type")
    private Integer signType;
	/**????*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH-mm-ss")
    @ApiModelProperty(value = "????")
    private Date createTime;
	/**????*/
	@Excel(name = "照片地址", width = 15)
    @ApiModelProperty(value = "照片地址")
    private String ssoUrlPhoto;
	/**????KEY*/
	@Excel(name = "KEY", width = 15)
    @ApiModelProperty(value = "KEY")
    @TableField(value = "`key`")
    private String key;


    @TableField(exist = false)
    private String cid;

    @TableField(exist = false)
    private String orderId;

    /**
     * 培训或模拟中心名称
     */
    @TableField(exist = false)
    private String centerName;

}
