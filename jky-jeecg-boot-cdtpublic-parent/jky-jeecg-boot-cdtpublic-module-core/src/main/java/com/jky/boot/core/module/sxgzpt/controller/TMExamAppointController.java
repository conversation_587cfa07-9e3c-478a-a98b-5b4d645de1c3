package com.jky.boot.core.module.sxgzpt.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.TMExamAppoint;
import com.jky.boot.core.module.sxgzpt.service.ITMExamAppointService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * 	考试预约
 * <AUTHOR>
 * @since	2023-06-13
 * @version	V1.0
 */
@Api(tags="考试预约")
@RestController
@RequestMapping("/gzpt/tMExamAppoint")
@Slf4j
public class TMExamAppointController extends JeecgController<TMExamAppoint, ITMExamAppointService> {
	@Autowired
	private ITMExamAppointService tMExamAppointService;

	/**
	 * 分页列表查询
	 *
	 * @param tMExamAppoint
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "考试预约-分页列表查询")
	@ApiOperation(value="考试预约-分页列表查询", notes="考试预约-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMExamAppoint>> queryPageList(TMExamAppoint tMExamAppoint,
													  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													  HttpServletRequest req) {
		List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMExamAppoint.getInscode());
		tMExamAppoint.setInscode(null);
		QueryWrapper<TMExamAppoint> queryWrapper = QueryGenerator.initQueryWrapper(tMExamAppoint, req.getParameterMap());
		queryWrapper.in("inscode", insCodes);
		queryWrapper.orderByDesc("create_time");
		Page<TMExamAppoint> page = new Page<>(pageNo, pageSize);
		IPage<TMExamAppoint> pageList = tMExamAppointService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param tMExamAppoint
	 * @return
	 */
	@AutoLog(value = "考试预约-添加")
	@ApiOperation(value="考试预约-添加", notes="考试预约-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_exam_appoint:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMExamAppoint tMExamAppoint) {
		tMExamAppointService.save(tMExamAppoint);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param tMExamAppoint
	 * @return
	 */
	@AutoLog(value = "考试预约-编辑")
	@ApiOperation(value="考试预约-编辑", notes="考试预约-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_exam_appoint:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMExamAppoint tMExamAppoint) {
		tMExamAppointService.updateById(tMExamAppoint);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "考试预约-通过id删除")
	@ApiOperation(value="考试预约-通过id删除", notes="考试预约-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_exam_appoint:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		tMExamAppointService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "考试预约-批量删除")
	@ApiOperation(value="考试预约-批量删除", notes="考试预约-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_exam_appoint:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
		this.tMExamAppointService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "考试预约-通过id查询")
	@ApiOperation(value="考试预约-通过id查询", notes="考试预约-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMExamAppoint> queryById(@RequestParam(name="id") String id) {
		TMExamAppoint tMExamAppoint = tMExamAppointService.getById(id);
		if(tMExamAppoint==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMExamAppoint);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMExamAppoint
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_exam_appoint:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMExamAppoint tMExamAppoint) {
//        return super.exportXls(request, tMExamAppoint, TMExamAppoint.class, "考试预约");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_exam_appoint:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMExamAppoint.class);
//    }

}
