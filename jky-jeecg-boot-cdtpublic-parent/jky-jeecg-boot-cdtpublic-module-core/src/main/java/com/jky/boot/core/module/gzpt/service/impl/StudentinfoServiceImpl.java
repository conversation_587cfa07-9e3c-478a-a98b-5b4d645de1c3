package com.jky.boot.core.module.gzpt.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.jky.boot.base.service.ISchedulingApplyService;
import com.jky.boot.common.utils.CityUtil;
import com.jky.boot.common.utils.FileUtils;
import com.jky.boot.core.module.backup.service.ITableBackUpService;
import com.jky.boot.core.module.gzpt.dto.ApplyTimesDto;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.enums.BankEnum;
import com.jky.boot.core.module.gzpt.enums.TrainTypeEnum;
import com.jky.boot.core.module.gzpt.mapper.*;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.PushBankUtil;
import com.jky.boot.core.module.gzpt.utils.pdf.PdfUtils;
import com.jky.boot.core.module.gzpt.vo.StudentCountVO;
import com.jky.boot.core.module.gzpt.vo.StudentViewVO;
import com.jky.boot.core.module.gzpt.vo.TransNoticeVo;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.boot.zlb.entity.ZzTransferInscode;
import com.jky.boot.zlb.service.IZzTransferInscodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: t_m_studentinfo
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class StudentinfoServiceImpl extends ServiceImpl<StudentinfoMapper, Studentinfo> implements IStudentinfoService {
    @Autowired
    private ISchedulingApplyService schedulingApplyService;
    @Autowired
    private ClassRecordDetailMapper classRecordDetailMapper;
    @Autowired
    private StageTrainningTimeMapper stageTrainningTimeMapper;
    @Autowired
    private TotalTimeMapper totalTimeMapper;
    @Autowired
    private IStudentContractService studentContractService;
    @Autowired
    private IStudentinfoLogoutService iStudentinfoLogoutService;
    @Autowired
    private IInstitutionService iInstitutionService;
    @Autowired
    private ICostService iCostService;
    @Autowired
    private TrainSubjectCreditMapper trainSubjectCreditMapper;
    @Autowired
    private IBankRegService iBankRegService;
    @Autowired
    private IBankOrderService iBankOrderService;
    @Autowired
    private ITotalTimeService iTotalTimeService;
    @Autowired
    private IZzTransferInscodeService iZzTransferInscodeService;
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @Autowired
    private BankOrderMapper bankOrderMapper;
    @Autowired
    private IStuTransferService stuTransferService;
    @Autowired
    private IStudentinfoEnterService iStudentinfoEnterService;
    @Autowired
    private ITableBackUpService iTableBackUpService;
    /**
     * 本地路径
     */
    @Value(value = "${jeecg.path.upload}")
    private String uploadpathLocal;

    @Value("${jhgzurl}")
    private String jhgzurl;
    @Value(value = "${citySeal}")
    private String citySeal;
    private static final String success = "ok";


    /**
     * 查询学员信息
     *
     * @param id 学员信息主键
     * @return 学员信息
     */
    @Override
    public Studentinfo selectStudentinfoById(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询学员信息
     *
     * @param idcard 学员身份证号
     * @return 学员信息
     */
    @Override
    public Studentinfo selectStudentinfoByIdCard(String idcard) {
        Studentinfo studentinfo = baseMapper.selectOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, idcard)
                        .orderByDesc(Studentinfo::getCreateTime)
                        .last("limit 1")
        );
//        if (studentinfo == null) {
//            try{
//                String result = restTemplate.getForObject(KeepHttpUtilQG.toUrlSj(UrlAddressUtils.queryStudentInfo(idcard),"1"), String.class);
//                CommonResponse commonResponse = new CommonResponse(result);
//                log.info("[监管返回学员]{}",result);
////            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientGetSJ(UrlAddressUtils.queryStudentInfo(idcard));
//                studentinfo = JSONObject.parseObject(commonResponse.getData().toString(), Studentinfo.class);
//                if(studentinfo!=null){
//                    downloadAndUpload2Oss(studentinfo);
//                    baseMapper.insert(studentinfo);
//                }
//            }catch (Exception e){
//                log.error("[查询监管学员异常] idcard:{}",studentinfo.getIdcard(),e);
//            }
//        }
        return studentinfo;
    }

//    private void downloadAndUpload2Oss(Studentinfo studentinfo ){
//        if(StringUtils.isNotBlank(studentinfo.getPhotopath())){
//            try {
//                URL url = new URL(studentinfo.getPhotopath());
//                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//                connection.setRequestMethod("GET");
//
//                Calendar calendar = Calendar.getInstance();
//                calendar.setTime(new Date());
//                SimpleDateFormat fmt = new SimpleDateFormat("yyyy/MM/dd");
//                String bizPath = OssZWYUtils.UPLOAD_PATH + "/" + fmt.format(calendar.getTime());
//                String pictureUrl = ossZWYUtils.uploadFileAndGetUrl(bizPath + "/" + System.currentTimeMillis() + ".jpg", connection.getInputStream());
//
//                if (StringUtils.isNotBlank(pictureUrl) && pictureUrl.contains(OssZWYUtils.UPLOAD_PATH) && pictureUrl.contains("?")) {
//                    String subPath = StringUtils.substringBetween(pictureUrl, OssZWYUtils.UPLOAD_PATH, "?");
//                    studentinfo.setPhotopath("/" + OssZWYUtils.UPLOAD_PATH + subPath);
//                    log.info("[学员照片路径] photopath:{},{} ", pictureUrl, studentinfo.getPhotopath());
//                }
//            }catch (Exception e){
//                log.error("[图片下载上传至OSS异常] idcard:{}",studentinfo.getIdcard(),e);
//            }
//        }
//    }

    /**
     * 查询学员信息列表
     *
     * @param studentinfo 学员信息
     * @return 学员信息
     */
    @Override
    public List<Studentinfo> selectStudentinfoList(Studentinfo studentinfo) {
//        return baseMapper.selectStudentinfoList(studentinfo);
        QueryWrapper<Studentinfo> queryWrapper = QueryGenerator.initQueryWrapper(studentinfo, null);
        queryWrapper.setEntityClass(Studentinfo.class);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 修改学员信息
     *
     * @param studentinfo 学员信息
     * @return 结果
     */
    @Override
    public int updateStudentinfo(Studentinfo studentinfo) {
        return baseMapper.updateStudentinfo(studentinfo);
//        return baseMapper.updateById(studentinfo);
    }

    /**
     * 批量删除学员信息
     *
     * @param ids 需要删除的学员信息主键 did 0:普通删除,1:退学,2:注销
     * @return 结果
     */
    @Override
    public String deleteStudentinfoByIds(String[] ids, Long did, String inscode) {
        String result = "";
        for (String id : ids) {
            Studentinfo studentinfo = selectStudentinfoById(id);
            if (studentinfo == null) {
                return "学员ID有误";
            }
            if (did == 0) result = del(studentinfo); // 删除
            if (did == 1) result = dropout(studentinfo, inscode); // 退学
//            if (did == 1) result = checkBeforeDrop(studentinfo); // 退学
            if (did == 2) result = logout(studentinfo); // 注销

            if (!success.equals(result)) return result;

            // 无论删除,退学还是注销  都删除以下信息(不备份),备份的只有学员信息
            String stunum = studentinfo.getStunum();
            if (StringUtils.isNotEmpty(stunum)) {
                rmAndBak(studentinfo, did);
            }
        }
        return result;
    }

    @Override
    public void rmAndBak(Studentinfo stu, long did) {
        String stunum = stu.getStunum();
        // 删除开户信息
        BankReg bankReg = iBankRegService.getOneByStuNum(stunum);
        iBankRegService.deleteBankRegByStunum(stunum);
        // 删除合同信息
        studentContractService.deleteStudentContractByStunum(stunum);
        // 删除阶段报审
        stageTrainningTimeMapper.deleteStageTrainningTimeByStunum(stunum);
        // 删除电子教学日志
        classRecordDetailMapper.deleteClassRecordDetailByStunum(stunum);
        // 删除学时汇总
        iTotalTimeService.deleteTotalTimeByStunum(stunum);
        // 删除在线报名信息
        iStudentinfoEnterService.remove(new LambdaQueryWrapper<>(StudentinfoEnter.class).eq(StudentinfoEnter::getIdcard,stu.getIdcard()).eq(StudentinfoEnter::getTraintype,stu.getTraintype()));
        backup(did, stu);
        if(bankReg != null){
            iTableBackUpService.backup(stunum, "t_m_bank_reg", JSONObject.toJSONString(bankReg));
        }
        baseMapper.deleteById(stu.getId());
    }

    public String del(Studentinfo studentinfo) {

        String stunum = studentinfo.getStunum();
        if (StringUtils.isNotEmpty(stunum)) {
            // 开户冻结成功后不可以删除学员
            List<BankReg> bankRegs = iBankRegService.selectBankRegList(new BankReg().setStunum(stunum).setStatus(3));
            if (!CollectionUtils.isEmpty(bankRegs)) {
                return "学员已经与银行签约并冻结,无法删除!";
            }
        }
        // 普通删除,把照片也一并删除
        FileUtils.deleteFile(jhgzurl + studentinfo.getPhotopath());
        return success;
    }

    @Override
    public String dropout(Studentinfo studentinfo, String inscode) {
        String stunum = studentinfo.getStunum();
        if (StringUtils.isEmpty(stunum)) {
            return "学员编号为空,只能删除!";
        }
        List<BankReg> bankRegList = iBankRegService.selectBankRegList(new BankReg().setStunum(stunum));
        if (bankRegList.isEmpty()) {
            return success;
        }
        BankReg one = iBankRegService.getOne(new LambdaQueryWrapper<BankReg>().eq(BankReg::getStunum, stunum));
        if (Objects.isNull(one)) {
            return success;
        }
        if (one.getStatus() == 4) {
            return "学员资金冻结中,无法退学!";
        } else if (one.getStatus() == 5 && "9IC3C55B".equals(one.getBankcode())) {
            return "建行学员未资金冻结,无法退学!请先去手机端解约";
        } else if (one.getStatus() == 1 && "9IC3C55B".equals(one.getBankcode())) {
            return "建行学员未资金冻结,无法退学!请先去手机端解约";
        }
        if (StringUtils.isBlank(studentinfo.getBankcode())) {
//            BankReg one = iBankRegService.getOne(new LambdaQueryWrapper<>(BankReg.class).eq(BankReg::getStunum, studentinfo.getStunum()));
            if (one == null) {
                return success;
            }
            studentinfo.setBankcode(one.getBankcode());
            studentinfo.setBankname(one.getBankname());
        }
        // 退学前解约
        if (!CollectionUtils.isEmpty(bankRegList) && "9IC3C55B".equals(bankRegList.get(0).getBankcode())) {
            if (bankRegList.get(0).getStatus() == 0) {
                return success;
            }
//            iBankRegService.unFreeze(bankRegList.get(0));
//            iBankRegService.signCancel2(bankRegList.get(0));
            int i = iBankRegService.unFreeze(bankRegList.get(0));
            if (i == 1) {
                return "学员解冻失败";
            }
            i = iBankRegService.signCancel2(bankRegList.get(0));
            if (i == 1) {
                return "退学销户失败";
            }
            return success;
        }
//        if (!CollectionUtils.isEmpty(bankRegList) && "06C3CRCB".equals(bankRegList.get(0).getBankcode()) && "1".equals(studentinfo.getIsold())){
//            return "农商行老学员暂时不能退学";
//        }

        BankOrder bankOrder = new BankOrder();
        bankOrder.setStunum(studentinfo.getStunum());
        bankOrder.setOrdertype(4L);
        bankOrder.setInscode(studentinfo.getInscode());
        bankOrder.setInsname(studentinfo.getInsname());
        bankOrder.setStuname(studentinfo.getName());
        bankOrder.setBankcode(studentinfo.getBankcode());
        bankOrder.setBankname(studentinfo.getBankname());
        bankOrder.setStatus(0L);
        bankOrder.setTransferAmount(bankRegList.get(0).getRemainingAmount());
        bankOrder.setIdcard(studentinfo.getIdcard());
        bankOrder.setCreatedate(new Date());
        bankOrderMapper.insert(bankOrder);
        //发送给银行接口
        JSONObject json = new JSONObject();
        json.put("stunum", stunum);
        json.put("type", 1);
        json.put("inscode", studentinfo.getInscode());
        try {
            PushBankUtil.pushFundTransfer(4, json, studentinfo.getBankcode());
        } catch (Exception e) {
            log.error("学员退学通知银行失败{}", stunum,e);
            return "学员退学通知银行失败,未删除学员!";
        }
        bankOrderMapper.insert(bankOrder);
        return success;
    }

    public String logout(Studentinfo studentinfo) {
        String stunum = studentinfo.getStunum();
        if (StringUtils.isEmpty(stunum)) {
            return "学员编号为空,只能删除!";
        }
        // 三个科目已结算 不允许退学
        List<BankOrder> orderList = iBankOrderService.selectBankOrderList(new BankOrder().setStunum(stunum).setStatus(1L));
        if (orderList == null || orderList.size() < 3 && !"159258B1C2AA".equals(studentinfo.getPlatform())) {
            return "学员三个科目未全部结算,无法注销!";
        }
        return success;
    }

    // 备份
    public void backup(long did, Studentinfo studentinfo) {
        StudentinfoLogout studentinfoLogout = new StudentinfoLogout();
        BeanUtils.copyProperties(studentinfo, studentinfoLogout);
        studentinfoLogout.setType((int) did);
        iStudentinfoLogoutService.insertStudentinfoLogout(studentinfoLogout);
    }


    /**
     * 删除学员信息信息
     *
     * @param id 学员信息主键
     * @return 结果
     */
    @Override
    public int deleteStudentinfoById(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 学员签订合同
     */
    @Override
    public String signStudentContractById(String id) {
        Studentinfo studentinfo = baseMapper.selectById(id);
        if (StringUtils.isBlank(studentinfo.getSignpath())) {
            throw new RuntimeException("学员未签名，不能签订合同！");
        }
        Institution institution = iInstitutionService.getByInscode(studentinfo.getInscode());
        Cost cost = iCostService.getOne(
                Wrappers.<Cost>lambdaQuery()
                        .eq(Cost::getInscode, studentinfo.getInscode())
                        .eq(Cost::getTraintype, studentinfo.getTraintype())
        );
        if (Objects.isNull(cost)) {
            throw new RuntimeException("驾校未添加费用模板！");
        }
        try {
            String localPath = getContractLocalPath(institution, studentinfo, cost, false);
            String filename = OssZWYUtils.UPLOAD_PATH + "/stuContract/" + DateUtil.format(new Date(), "yyyy/MM/dd") + "/" + studentinfo.getStunum() + ".pdf";
            File afterCompress = new File(localPath);
            String ossUrl = ossZWYUtils.uploadFile(filename, Files.newInputStream(afterCompress.toPath()));
            String pdfUrl = OssZWYUtils.subStr(ossUrl);
            afterCompress.delete();
            // 操作合同表
            StudentContract contract = studentContractService.getOneByStunum(studentinfo.getStunum());
            if (Objects.isNull(contract)) {
                contract = new StudentContract();
                contract.setStunum(studentinfo.getStunum()).setPdfurl(pdfUrl).setTheoryCostPer(cost.getTheoryCostPer()).setTheoryCost(cost.getTheoryCost());
                contract.setImiCostPer(cost.getImiCostPer()).setImiCost(cost.getImiCost()).setRemoteCostPer(cost.getRemoteCostPer());
                contract.setSub2CostPer(cost.getSub2CostPer()).setSub2Cost(cost.getSub2Cost()).setSub3CostPer(cost.getSub3CostPer());
                contract.setFrozenAmount(cost.getFrozenAmount()).setIsSign(1).setSub3Cost(cost.getSub3Cost());
                studentContractService.save(contract);
            } else {
                contract.setStunum(studentinfo.getStunum()).setPdfurl(pdfUrl).setTheoryCostPer(cost.getTheoryCostPer()).setTheoryCost(cost.getTheoryCost());
                contract.setImiCostPer(cost.getImiCostPer()).setImiCost(cost.getImiCost()).setRemoteCostPer(cost.getRemoteCostPer());
                contract.setSub2CostPer(cost.getSub2CostPer()).setSub2Cost(cost.getSub2Cost()).setSub3CostPer(cost.getSub3CostPer());
                contract.setFrozenAmount(cost.getFrozenAmount()).setIsSign(1).setSub3Cost(cost.getSub3Cost());
                studentContractService.updateById(contract);
            }
            // 公众签订合同默认一步到位
            studentinfo.setIssigncontract(1);
            this.updateById(studentinfo);
            return pdfUrl;
        } catch (Exception e) {
            log.error("PDF导出失败:", e);
            return null;
        }
    }

    /**
     * 学员合同查看
     */
    @Override
    public String viewStudentContractByStunum(String stunum) {
        StudentContract studentContract = studentContractService.getOneByStunum(stunum);
        if (Objects.isNull(studentContract) || studentContract.getIsSign() == 0) {
            return null;
        }
        return ossZWYUtils.getPhotoUrl(studentContract.getPdfurl());
    }

    @Override
    public void setBlackStu(Studentinfo studentinfo) {
        baseMapper.setBlackStu(studentinfo);
    }

    @Override
    public List<StudentCountVO> selectStudentCounts(String startTime, String endTime, String inscode) {
        List<StudentCountVO> records = baseMapper.selectStudentCounts(startTime, endTime, JkSecurityUtils.getOrgCodeStartWithPriorIdByDeptId(inscode));
        List<Institution> institutionList = iInstitutionService.list();

        Map<String, StudentCountVO> studentCountMap = new HashMap<>();
        Map<String, String> insMap = institutionList.stream()
                .collect(Collectors.toMap(Institution::getInscode, Institution::getName, (a, b) -> a));
        for (StudentCountVO a : records) {
            StudentCountVO vo = studentCountMap.computeIfAbsent(a.getInscode(), key -> new StudentCountVO());
            vo.setInscode(a.getInscode());
            if (insMap.containsKey(a.getInscode())) {
                vo.setName(insMap.get(a.getInscode()));
            }
            if (Objects.equals(a.getBankcode(), BankEnum.RCB.getCode())) {
                vo.setCountRCB(a.getCount() == null ? 0 : a.getCount());
            } else if (Objects.equals(a.getBankcode(), BankEnum.PAB.getCode())) {
                vo.setCountPAB(a.getCount() == null ? 0 : a.getCount());
            } else if (Objects.equals(a.getBankcode(), BankEnum.CCB.getCode())) {
                vo.setCountCCB(a.getCount() == null ? 0 : a.getCount());
            } else if (Objects.equals(a.getBankcode(), BankEnum.NULL.getCode())) {
                vo.setCountNULL(a.getCount() == null ? 0 : a.getCount());
            }
        }
        List<StudentCountVO> result = new ArrayList<>(studentCountMap.values());
        result = result.stream().peek(a -> a.setCount(a.getCountRCB() + a.getCountPAB() + a.getCountCCB() + a.getCountNULL()))
                .sorted(Comparator.comparing(StudentCountVO::getCount).reversed())
                .collect(Collectors.toList());
        return  result;
//        long offset = (page.getCurrent() - 1) * page.getSize();
//        long size = page.getSize();
//        offset = offset > result.size() ? result.size() : offset;
//        size = size > result.size() ? result.size() : size;
//        List<StudentCountVO> subResult = result.subList((int) offset, (int) size);
//        page.setRecords(subResult);
//        page.setTotal(result.size());
//        return baseMapper.selectStudentCounts(startTime, endTime, JkSecurityUtils.getOrgCodeStartWithPriorIdByDeptId(inscode));
    }

    @Override
    public Studentinfo getStudentByStunum(String stunum) {
//        return baseMapper.getStudentByStunum(stunum);
        List<Studentinfo> stuList = baseMapper.selectList(Wrappers.<Studentinfo>lambdaQuery()
                .eq(Studentinfo::getStunum, stunum));
        if (stuList.isEmpty()) {
            return null;
        }
        return stuList.get(0);
    }

    @Override
    public Studentinfo selectByIdcardAndTraintype(String idcard, String traintype) {
//        return baseMapper.findStudentByIdcardAndTraintype(idcard,traintype);
        return baseMapper.selectOne(
                Wrappers.<Studentinfo>lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, idcard)
                        .eq(Studentinfo::getTraintype, traintype)
        );
    }

    @Override
    public IPage<StudentViewVO> selectStudentViewPage(Page<StudentViewVO> page, StudentViewVO studentViewVO) {
        return baseMapper.selectStudentArchivesPage(page, studentViewVO);
    }

    @Override
    public List<StudentViewVO> selectStudentViewList(StudentViewVO studentViewVO) {
        return baseMapper.selectStudentArchivesList(studentViewVO);
    }

    @Override
    public Page<Studentinfo> byid(Page<Studentinfo> page, String id) {
        return this.baseMapper.byId(page, id);
    }

    @Override
    public void saveTransfer(Studentinfo student, String insname) {
        // 1.未审核时候，覆盖信息  2.审核通过，信息不动 3.审核退回，重新新增记录
        List<ZzTransferInscode> lists = iZzTransferInscodeService.list(
                Wrappers.<ZzTransferInscode>lambdaQuery()
                        .eq(ZzTransferInscode::getStunum, student.getStunum())
                        .eq(ZzTransferInscode::getOldcity, student.getFromarea())
                        .orderByDesc(ZzTransferInscode::getCrdate)
        );
        if (lists != null && !lists.isEmpty()) {
            ZzTransferInscode transfer = lists.get(0);
            if (transfer.getAuditstate() == null || transfer.getAuditstate() == 0) { // 未审核
                transfer.setDistrict(student.getDistrict());
                transfer.setIdcard(student.getIdcard());
                transfer.setOldcity(student.getFromarea());
                transfer.setNewcity(student.getDistrict().substring(0, 4));
                if (student.getDistrict().equals("330782")) {
                    transfer.setNewcity("3312");
                }
                transfer.setNewinscode(student.getInscode());
                transfer.setNewinsname(insname);
                transfer.setCrdate(new Date());
                transfer.setTraintype(student.getTraintype());
                transfer.setName(student.getName());
                iZzTransferInscodeService.updateEntitie(transfer);
            }
        } else {
            ZzTransferInscode transfer = new ZzTransferInscode();
            transfer.setStunum(student.getStunum());
            transfer.setDistrict(student.getDistrict());
            transfer.setIdcard(student.getIdcard());
            transfer.setOldcity(student.getFromarea());
            transfer.setNewcity(student.getDistrict().substring(0, 4));
            if (student.getDistrict().equals("330782")) {
                transfer.setNewcity("3312");
            }
            transfer.setNewinscode(student.getInscode());
            transfer.setNewinsname(insname);
            transfer.setCrdate(new Date());
            transfer.setTraintype(student.getTraintype());
            transfer.setName(student.getName());
            iZzTransferInscodeService.save(transfer);
        }
    }

    @Override
    public String signStudentContractByIdcard(String stunum) {
        Studentinfo stu = baseMapper.selectOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getStunum, stunum)
        );
        Institution institution = iInstitutionService.getByInscode(stu.getInscode());
        Cost cost = iCostService.getOneByInsTrainType(stu.getInscode(), stu.getTraintype());
        if (Objects.isNull(cost)) {
            throw new RuntimeException("驾校未添加费用模板！");
        }
        try {
            // 生成合同文件
            String localPath = getContractLocalPath(institution, stu, cost, true);
            String filename = OssZWYUtils.UPLOAD_PATH + "/stuContract/" + DateUtil.format(new Date(), "yyyy/MM/dd") + "/" + stunum + ".pdf";
            File afterCompress = new File(localPath);
            // 上传至 oss
            String ossUrl = ossZWYUtils.uploadFile(filename, Files.newInputStream(afterCompress.toPath()));
            String pdfUrl = OssZWYUtils.subStr(ossUrl);
            // 删除本地文件
            afterCompress.delete();
            // 合同表和学员表操作
            StudentContract contract = studentContractService.getOneByStunum(stunum);
            if (Objects.isNull(contract)) {
                contract = new StudentContract();
                contract.setStunum(stunum).setPdfurl(pdfUrl).setTheoryCostPer(cost.getTheoryCostPer()).setTheoryCost(cost.getTheoryCost());
                contract.setImiCostPer(cost.getImiCostPer()).setImiCost(cost.getImiCost()).setRemoteCostPer(cost.getRemoteCostPer());
                contract.setSub2CostPer(cost.getSub2CostPer()).setSub2Cost(cost.getSub2Cost()).setSub3CostPer(cost.getSub3CostPer());
                contract.setFrozenAmount(cost.getFrozenAmount()).setIsSign(0).setSub3Cost(cost.getSub3Cost());
                studentContractService.save(contract);
                stu.setIssigncontract(0);
            } else {
                contract.setPdfurl(pdfUrl).setTheoryCostPer(cost.getTheoryCostPer()).setTheoryCost(cost.getTheoryCost());
                contract.setImiCostPer(cost.getImiCostPer()).setImiCost(cost.getImiCost()).setRemoteCostPer(cost.getRemoteCostPer());
                contract.setSub2CostPer(cost.getSub2CostPer()).setSub2Cost(cost.getSub2Cost()).setSub3CostPer(cost.getSub3CostPer());
                contract.setFrozenAmount(cost.getFrozenAmount()).setIsSign(1).setSub3Cost(cost.getSub3Cost());
                studentContractService.updateById(contract);
                stu.setIssigncontract(1);
            }
            baseMapper.updateById(stu);
            return pdfUrl;
        } catch (Exception e) {
            log.error("PDF导出失败:", e);
            return null;
        }
    }

    private String getContractLocalPath(Institution institution, Studentinfo stu, Cost cost, boolean isZLb) throws Exception{
        // 模板文件路径
        String inputFileName = "templates/pdf/学员签订合同模板.pdf";
        // 生成的文件路径
        String outputFileName = uploadpathLocal + "/pdf/" + DateUtil.format(new Date(), "yyyy/MM/dd");
        File file = new File(outputFileName);
        if (!file.exists()) {
            file.mkdirs();
        }
        String absolutePath = outputFileName + "/" + stu.getStunum() + ".pdf";
        File pdf = new File(absolutePath);
        OutputStream os = Files.newOutputStream(pdf.toPath());
        // 2 读入pdf表单
        PdfReader reader = new PdfReader(inputFileName);
        // 3 根据表单生成一个新的pdf
        PdfStamper ps = new PdfStamper(reader, os);
        // 4 获取pdf表单
        AcroFields form = ps.getAcroFields();
        // 5给表单添加中文字体
        BaseFont bf = BaseFont.createFont("templates/Font/SIMYOU.TTF", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        form.addSubstitutionFont(bf);
        // 6查询数据================================================
        Map<String, String> data = genContract(stu, institution, cost);
        if (StringUtils.isNotBlank(stu.getSignpath())) {
            // 添加 对应驾校的章
            try {
                // 添加 对应驾校的章
                String insPath = uploadpathLocal + citySeal + institution.getInscode() + ".png";
                Image instance = Image.getInstance(insPath);
                insertPdfImage(form, ps, "insSeal", instance);
            } catch (Exception e) {
                log.error("驾校章缺失：{}", e.getMessage());
            }
            try {
                // 添加 学员签名图片
                String signUrl = stu.getSignpath();
                Image stuSign = Image.getInstance(ossZWYUtils.getPhotoUrl(signUrl));
                if (isZLb) {
                    stuSign.setRotationDegrees(90f);
                }
                insertPdfImage(form, ps, "stuSign", stuSign);
            } catch (Exception e) {
                log.error("学员签名图片缺失：{}", e.getMessage());
            }
        }

        // 7遍历data 给pdf表单表格赋值
        for (String key : data.keySet()) {
            form.setField(key, data.get(key), true);
        }
        ps.setFormFlattening(true);

        //关闭流
        ps.close();
        reader.close();
        os.close();

        // pdf 生成后压缩大小
        PdfUtils.compress(absolutePath);
        return absolutePath;
    }

    private Map<String, String> genContract(Studentinfo studentinfo,
                                            Institution institution,
                                            Cost cost) {
        Map<String, String> data = new HashMap<>();
        // 一部分、学员信息填充
        data.put("stuname", studentinfo.getName());
        data.put("sex", "1".equals(studentinfo.getSex()) ? "男" : "女");
        data.put("phone", studentinfo.getPhone());
        data.put("idcard", studentinfo.getIdcard());
        data.put("stuaddress", studentinfo.getAddress());

        // 二部分、培训机构信息填充
        data.put("name", institution.getName());
        data.put("legal", institution.getLegal());
        data.put("insaddress", institution.getAddress());
        data.put("licnum", institution.getLicnum());
        data.put("contact", institution.getContact());
        data.put("insphone", institution.getPhone());

        // 三部分、车型填充
        data.put("traintype", studentinfo.getTraintype());
        data.put("traintypename", TrainTypeEnum.getByTrainType(studentinfo.getTraintype()).getTraintypename());

        // 四部分、学时填充
        Integer outline = CityUtil.getSubjectCreditType(studentinfo.getApplydate());
        List<TrainSubjectCredit> credits = trainSubjectCreditMapper.selectList(
                Wrappers.<TrainSubjectCredit>lambdaQuery()
                        .eq(TrainSubjectCredit::getType, outline)
                        .eq(TrainSubjectCredit::getTrainCarType, studentinfo.getTraintype())
        );
        Map<Long, Map<Long, Long>> collect = credits.stream().collect(Collectors.groupingBy(TrainSubjectCredit::getSubject, Collectors.toMap(TrainSubjectCredit::getClasstype, TrainSubjectCredit::getCreditration)));
        long time2 = collect.get(1L).get(2L) == null ? 0:collect.get(1L).get(2L) / 60;
        long time3 = collect.get(1L).get(4L) == null ? 0:collect.get(1L).get(4L) / 60;
        long time4_sub2 = collect.get(2L).get(3L) == null ? 0:collect.get(2L).get(3L) / 60;
        long time4_sub3 = collect.get(3L).get(3L) == null ? 0:collect.get(3L).get(3L) / 60;
        long time5 = collect.get(2L).get(1L) == null ? 0:collect.get(2L).get(1L) / 60;
        long time6 = collect.get(3L).get(1L) == null ? 0:collect.get(3L).get(1L) / 60;
        long time8 = collect.get(3L).get(2L) == null ? 0:collect.get(3L).get(2L) / 60;
        long time9 = collect.get(3L).get(4L) == null ? 0:collect.get(3L).get(4L) / 60;
        Integer imiFlag = cost.getImiFlag();
        data.put("time1", String.valueOf(time2 + time3));
        data.put("time2", String.valueOf(time2));
        data.put("time3", String.valueOf(time3));
        data.put("time4", String.valueOf(time4_sub2 + time4_sub3 + 2L * imiFlag));
        data.put("time5", String.valueOf(time5));
        data.put("time6", String.valueOf(time6 - 2L * imiFlag));
        data.put("time7", String.valueOf(time8 + time9));
        data.put("time8", String.valueOf(time8));
        data.put("time9", String.valueOf(time9));
        data.put("sub2Total", String.valueOf(time5 + time4_sub2));
        data.put("sub3Total", String.valueOf(time6));

        // 五部分、培训地址填充
        data.put("classPlace", cost.getClassPlace());
        data.put("remotePlace", cost.getRemotePlace());
        data.put("imiPlace", cost.getImiPlace());

        // 六部分、费用与支付方式
        data.put("signUp", cost.getBookCost().add(cost.getArchiveCost()).toPlainString());
        data.put("fee", "One");
        data.put("theoryCostPer", cost.getTheoryCostPer().toPlainString());
        data.put("imiCostPer", cost.getImiCostPer().toPlainString());
        data.put("remoteCostPer", cost.getRemoteCostPer().toPlainString());
        data.put("sub2CostPer", cost.getSub2CostPer().toPlainString());
        data.put("sub3CostPer", cost.getSub3CostPer().toPlainString());
        if (cost.getInsuranceCost().compareTo(BigDecimal.ZERO) > 0) {
            data.put("isInsurance", "One");
            data.put("insuranceCost", cost.getInsuranceCost().toPlainString());
        } else {
            data.put("isInsurance", "Two");
            data.put("insuranceCost", "×");
        }
        data.put("workday", String.valueOf(cost.getWorkday()));
        // 七部分、预约部分填充
        data.put("appointtime", cost.getAppointtime());
        data.put("appointnetwork", cost.getAppointnetwork());
        data.put("appointphone", cost.getAppointphone());
        // 八部分、违约部分填充
        data.put("insToStuCost", String.valueOf(cost.getInsToStuCost()));
        data.put("stuToInsCost", String.valueOf(cost.getStuToInsCost()));
        data.put("damages", cost.getDamages().toPlainString());
        data.put("damageDays", String.valueOf(cost.getDamageDays()));
        data.put("otherAppoint", StringUtils.isBlank(cost.getOtherAppoint()) ? "":cost.getOtherAppoint());
        data.put("signdate", DateFormatUtils.format(new Date(), "yyyy年MM月dd日"));

        // 理论费用总额
        BigDecimal theoryCost = cost.getTheoryCost();
        if (meetCheck(studentinfo, "2")) {
            theoryCost = BigDecimal.ZERO;
        }
        data.put("theoryCost", theoryCost.toPlainString());

        // 模拟费用总额
        BigDecimal imiCost = cost.getImiCost();
        if (meetCheck(studentinfo, "3")) {
            imiCost = BigDecimal.ZERO;
        }
        data.put("imiCost", imiCost.toPlainString());
        return data;
    }

    /**
     * 添加图片
     *
     * @param form    pdf表单
     * @param stamper 表单域
     * @param key     文本域主键标识
     * @param image   图片url
     * @throws Exception 异常
     */
    private static void insertPdfImage(AcroFields form, PdfStamper stamper, String key, Image image) throws Exception {
        //根据文本域主键标识，获取所在页码
        List<AcroFields.FieldPosition> list = form.getFieldPositions(key);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        AcroFields.FieldPosition target = list.get(0);
        int pageNo = target.page;
        //获取文本域位置信息
        Rectangle signRect = target.position;
        float x = signRect.getLeft();
        float y = signRect.getBottom();
        // 获取操作的页面
        PdfContentByte under = stamper.getOverContent(pageNo);
        // 根据域的大小缩放图片
        image.scaleToFit(signRect.getWidth(), signRect.getHeight());
        // 添加图片
        image.setAbsolutePosition(x, y);
        under.addImage(image);
    }
    private boolean meetCheck(Studentinfo stu, String classType) {
        String stunum = stu.getStunum();
        String applyDate = stu.getApplydate();
        String trainType = stu.getTraintype();
        Double already = classRecordDetailMapper.queryTypeTotal(stunum, classType);
        Integer outline = CityUtil.getSubjectCreditType(applyDate);
        Double total = trainSubjectCreditMapper.queryTotalTime(outline, trainType, classType);
        return already >= total;
    }

    @Override
    public Studentinfo getOneByStuNum(String stuNum) {
        return baseMapper.selectOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getStunum, stuNum)
        );
    }

    @Override
    public Studentinfo getOneByIdCard(String idCard) {
        List<Studentinfo> stuList = baseMapper.selectList(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, idCard)
                        .orderByDesc(Studentinfo::getCreateTime)
        );
        if (stuList.isEmpty()) {
            return null;
        }
        return stuList.get(0);
    }

    @Override
    public Result<?> unfreeze(String stuNum, Integer type, String bosid) {
        Studentinfo studentinfo = getOneByStuNum(stuNum);
        BankReg one = iBankRegService.getOne(
                Wrappers.lambdaQuery(BankReg.class)
                        .eq(BankReg::getStunum, studentinfo.getStunum())
        );
//        // 建行特殊处理
//        if ("9IC3C55B".equals(one.getBankcode())) {
//            int i = iBankRegService.transUnFreeze(one,type);
//            if (i == 1) {
//                return Result.error("学员解冻失败");
//            }
//            return Result.ok("解冻中...");
//        }
        BankOrder bankOrder = new BankOrder();
        bankOrder.setStunum(studentinfo.getStunum());
        bankOrder.setOrdertype(4L);
        bankOrder.setInscode(studentinfo.getInscode());
        bankOrder.setInsname(studentinfo.getInsname());
        bankOrder.setStuname(studentinfo.getName());
        bankOrder.setBankcode(studentinfo.getBankcode());
        bankOrder.setBankname(studentinfo.getBankname());
        bankOrder.setStatus(0L);
        bankOrder.setTransferAmount(one.getRemainingAmount());
        bankOrder.setIdcard(studentinfo.getIdcard());
        bankOrder.setCreatedate(new Date());
        bankOrder.setTransFlag(type);
        bankOrder.setBosid(bosid);
        bankOrder.setSerialno(one.getSerialno());
        //划转对象类型 0驾校 1学员 2教练员
        bankOrder.setUserType(1);
        iBankOrderService.save(bankOrder);
        //发送给银行接口
        JSONObject json = new JSONObject();
        json.put("stunum", stuNum);
        json.put("type", 1);
        json.put("inscode", studentinfo.getInscode());
        //建行需要额外以下参数
        json.put("name", studentinfo.getName());
        json.put("amount", one.getRemainingAmount());
        json.put("serialno", one.getSerialno());
        json.put("orderno", bankOrder.getId());
        String result=null;
        try {
             result = PushBankUtil.pushFundTransfer(4, json, studentinfo.getBankcode());
        } catch (Exception e) {
            log.error("银行退学指令发送失败：{},{}",result,stuNum,e);
            return Result.error("银行退学指令发送失败");
        }
        log.info("银行退学指令返回结果：{},{}",result,stuNum);
        return Result.ok("解冻中...");
    }

    @Override
    public Result<?> cancelAccount(String stuNum) {
        BankReg one = iBankRegService.getOne(
                Wrappers.lambdaQuery(BankReg.class)
                        .eq(BankReg::getStunum, stuNum)
        );
        // 建行特殊处理
        if ("9IC3C55B".equals(one.getBankcode())) {
            return Result.error("建行学员请至手机端销户");
        }
        // 其余银行暂时不做销户，默认可覆盖
//        JSONObject json = new JSONObject();
//        json.put("stunum", stuNum);
//        json.put("type", 1);
//        json.put("inscode", one.getInscode());
//        try {
//            PushBankUtil.pushFundTransfer(4, json, one.getBankcode());
//        } catch (Exception e) {
//            return Result.error("销户失败");
//        }
        return Result.ok("销户成功");
    }

    @Override
    public void doTrans(StuTransfer one) {
        String stuNum = one.getStunum();
        Studentinfo studentinfo = getOneByStuNum(stuNum);
        // 校验目标培训机构合同收费内容，有则修改学员冻结金额
        updateMoney(studentinfo, one.getInscode());

        // 修改驾校
        studentinfo.setInscode(one.getInscode());
        studentinfo.setInsname(one.getInsname());
        // 合同相关清理
        studentinfo.setIssigncontract(0);
        studentinfo.setSignpath("");
        studentinfo.setIssignature(0);
        studentinfo.setIstobank(0);
        studentinfo.setIstojs(0);
        studentinfo.setBankcode("");
        studentinfo.setBankname("");
        studentinfo.setCoachnum("");

        // 修改汇总、阶段报审
        LambdaUpdateWrapper<TotalTime> totalWrapper = Wrappers.lambdaUpdate(TotalTime.class);
        totalWrapper.set(TotalTime::getInsname, one.getInsname());
        totalWrapper.set(TotalTime::getInscode, one.getInscode());
        totalWrapper.eq(TotalTime::getStunum, one.getStunum());
        totalTimeMapper.update(null, totalWrapper);

        LambdaUpdateWrapper<StageTrainningTime> stageWrapper = Wrappers.lambdaUpdate(StageTrainningTime.class);
        stageWrapper.set(StageTrainningTime::getInscode, one.getInscode());
        stageWrapper.set(StageTrainningTime::getInsname, one.getInsname());
        stageWrapper.eq(StageTrainningTime::getStunum, one.getStunum());
        stageWrapper.ne(StageTrainningTime::getAuditstate, 1);
        stageTrainningTimeMapper.update(null, stageWrapper);
        // 修改计时平台
        Institution newIns = iInstitutionService.getByInscode(one.getInscode());
        studentinfo.setPlatform(newIns.getPlatform());
        studentinfo.setDistrict(newIns.getDistrict());

        // 修改签约状态
        baseMapper.updateById(studentinfo);

        // 删除开户信息
        iBankRegService.deleteBankRegByStunum(stuNum);
        // 删除合同信息
        studentContractService.deleteStudentContractByStunum(stuNum);
    }

    @Override
    public void doTransCarType(TransferCarType one) {
        String stuNum = one.getStunum();
        Studentinfo studentinfo = getOneByStuNum(stuNum);
        // 校验目标培训机构合同收费内容，有则修改学员三部分金额
        updateMoney(studentinfo, one.getInscode());


        this.update(Wrappers.<Studentinfo>lambdaUpdate()
                .set(Studentinfo::getTraintype, one.getTraintype())
                .eq(Studentinfo::getStunum, one.getStunum())
        );

        // 修改车型
        studentinfo.setTraintype(one.getTraintype());
        // 合同相关清理
        studentinfo.setIssigncontract(0);
        studentinfo.setSignpath("");
        studentinfo.setIssignature(0);
        studentinfo.setIstobank(0);
        //studentinfo.setIsFreeze(0);
        studentinfo.setIstojs(0);
        studentinfo.setBankcode("");
        studentinfo.setBankname("");
        studentinfo.setCoachnum("");

        // 修改汇总、阶段报审
        LambdaUpdateWrapper<TotalTime> totalWrapper = Wrappers.lambdaUpdate(TotalTime.class);
        totalWrapper.set(TotalTime::getTraintype, one.getTraintype());
        totalWrapper.eq(TotalTime::getStunum, one.getStunum());
        totalTimeMapper.update(null, totalWrapper);

        LambdaUpdateWrapper<StageTrainningTime> stageWrapper = Wrappers.lambdaUpdate(StageTrainningTime.class);
        stageWrapper.set(StageTrainningTime::getTraintype, one.getTraintype());
        stageWrapper.eq(StageTrainningTime::getStunum, one.getStunum());
        stageWrapper.ne(StageTrainningTime::getAuditstate, 1);
        stageTrainningTimeMapper.update(null, stageWrapper);


        // 修改签约状态
        baseMapper.updateById(studentinfo);

        // 删除开户信息
        iBankRegService.deleteBankRegByStunum(stuNum);
        // 删除合同信息
        studentContractService.deleteStudentContractByStunum(stuNum);
    }


    @Override
    public Result<?> passSubjectTwo(String id) {
        Studentinfo studentinfo = this.getById(id);
        if (studentinfo==null){
            return Result.error("学员信息不存在!,划转失败");
        }

        if (studentinfo.getIsassistance()!=1){
            return Result.error("当前学员非智能机器人教学,划转失败");
        }

        String stunum = studentinfo.getStunum();
        BankReg bankReg = this.iBankRegService.getOneByStuNum(stunum);
        if (bankReg == null ){
            return Result.error("学员冻结信息不存在!,划转失败");
        }
        if (bankReg.getStatus() != 3 || !StringUtils.equalsIgnoreCase(bankReg.getRegState(), "22")) {
            return Result.error("未查询到学员第二笔冻结成功,划转失败");
        }


        //查询学员机器人科目二划转订单是否已存在
        long count = bankOrderMapper.selectCount(
                Wrappers.lambdaQuery(BankOrder.class)
                        .eq(BankOrder::getStunum, stunum)
                        //智能机器人划转订单
                        .eq(BankOrder::getOrdertype, 9)
        );
        //判断订单是否已存在
        if (count>0){
            return Result.error("该学员的智能机器人科二划转订单已存在,不予划转");
        }
        //查询学员合同信息
        StudentContract contract = studentContractService.getOneByStunum(stunum);
        if (contract==null){
            return Result.error("学员合同不存在,划转失败");
        }

        //如果科目二总金额等于0则科目二已经划转过了
        BigDecimal sub2Cost = contract.getSub2Cost();
        if (sub2Cost.compareTo(BigDecimal.ZERO)==0) {
            return Result.error("科目二金额已划转");
        }
        //保存订单信息
        BankOrder bankOrder = new BankOrder();
        bankOrder.setStunum(stunum);
        bankOrder.setInscode(studentinfo.getInscode());
        bankOrder.setInsname(studentinfo.getInsname());
        bankOrder.setCreatedate(new Date());
        bankOrder.setStatus(0L);
        //智能机器人科目二划转订单类型
        bankOrder.setOrdertype(9L);
        bankOrder.setBankcode(studentinfo.getBankcode());
        bankOrder.setBankname(studentinfo.getBankname());
        bankOrder.setStuname(studentinfo.getName());
        bankOrder.setTransferAmount(sub2Cost);
        bankOrder.setIdcard(studentinfo.getIdcard());
        bankOrder.setApplyId(null);
        bankOrder.setCoachnum(studentinfo.getCoachnum());
        bankOrder.setUserType(0);
        bankOrderMapper.insert(bankOrder);

        //划转完订单置科目二总金额为0
        contract.setSub2Cost(new BigDecimal(0));
        //更新学员合同订单信息
        studentContractService.updateById(contract);

        return Result.ok("划转成功");
    }

    /**
     * 改学员冻结金额
     */
    private void updateMoney(Studentinfo stu, String inscode) {
        Cost cost = iCostService.getOneByInsTrainType(inscode, stu.getTraintype());
        ApplyTimesDto applyTimes = schedulingApplyService.getStuApplyTimes(stu.getStunum());
        BigDecimal total = cost.getFrozenAmount();
        BigDecimal sub2De = cost.getSub2CostPer().multiply(BigDecimal.valueOf(applyTimes.getSub2OprApplyTimes()));
        BigDecimal sub3De = cost.getSub3CostPer().multiply(BigDecimal.valueOf(applyTimes.getSub3OprApplyTimes()));
        BigDecimal classDe = cost.getTheoryCostPer().multiply(BigDecimal.valueOf(applyTimes.getClassApplyTimes()));
        BigDecimal imiDe = cost.getImiCostPer().multiply(BigDecimal.valueOf(applyTimes.getImiApplyTimes()));
        BigDecimal res = total.subtract(sub2De).subtract(sub3De).subtract(classDe).subtract(imiDe);
        stu.setFrozenAmount(res.toPlainString());
    }
}
