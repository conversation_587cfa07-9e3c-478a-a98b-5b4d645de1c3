package com.jky.boot.core.module.gzpt.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 额定学时对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Data
@TableName("t_d_train_subject_credit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_d_train_subject_credit对象", description="额定学时对象")
public class TrainSubjectCredit implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**科目*/
	@Excel(name = "科目", width = 15, dicCode = "sys_km")
    @ApiModelProperty(value = "科目")
    private java.lang.Long subject;
	/**培训类型*/
	@Excel(name = "培训类型", width = 15, dicCode = "sys_type")
    @ApiModelProperty(value = "培训类型")
    private java.lang.Long classtype;
	/**额定学时*/
	@Excel(name = "额定学时", width = 15)
    @ApiModelProperty(value = "额定学时")
    private java.lang.Long creditration;
	/**额定里程*/
	@Excel(name = "额定里程", width = 15)
    @ApiModelProperty(value = "额定里程")
    private java.lang.Long mileageration;
	/**0*/
	@Excel(name = "0", width = 15)
    @ApiModelProperty(value = "0")
    private java.lang.String memo;
	/**车型code*/
	@Excel(name = "车型code", width = 15)
    @ApiModelProperty(value = "车型code")
    private java.lang.String trainCarCode;
	/**车型*/
	@Excel(name = "车型", width = 15)
    @ApiModelProperty(value = "车型")
    private java.lang.String trainCarType;
	/**类型 1新大纲 2 老大刚*/
	@Excel(name = "类型 1新大纲 2 老大纲", width = 15, dicCode = "sys_TrainSubject")
    @ApiModelProperty(value = "类型 1新大纲 2 老大纲")
    private java.lang.Integer type;


	/**迁移追加字段*/
	/**创建人*/
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
	private java.util.Date updateTime;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
	private java.util.Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	/**迁移追加字段*/


}
