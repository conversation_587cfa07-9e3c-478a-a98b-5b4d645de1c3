package com.jky.boot.zlb.vo;

import com.jky.boot.common.sensitive.Sensitive;
import com.jky.boot.common.sensitive.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

@Data
public class BaseZlbPersonVo  implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long id;
    /**认证级别 1.匿名 2.实名 3.实人 （当认证级别为2或者3的时候实名信息才有效）*/
    @Excel(name = "认证级别 1.匿名 2.实名 3.实人 （当认证级别为2或者3的时候实名信息才有效）", width = 15)
    @ApiModelProperty(value = "认证级别 1.匿名 2.实名 3.实人 （当认证级别为2或者3的时候实名信息才有效）")
    private String authlevel;
    /**用户用户标识*/
    private String userId;
    /**用户真实姓名*/
    @Sensitive(type = SensitiveTypeEnum.CHINESE_NAME_ZLB)
    @Excel(name = "用户真实姓名", width = 15)
    @ApiModelProperty(value = "用户真实姓名")
    private String username;
    /** 证件号码（身份证）*/
    @Sensitive(type = SensitiveTypeEnum. ID_CARD_ZLB)
    @Excel(name = " 证件号码（身份证）", width = 15)
    @ApiModelProperty(value = " 证件号码（身份证）")
    private String idnum;
    /**性别 1男 2女*/
    @Excel(name = "性别 1男 2女", width = 15)
    @ApiModelProperty(value = "性别 1男 2女")
    private String sex;
    /** 民族(见国标GB3304-91)*/
    @Excel(name = " 民族(见国标GB3304-91)", width = 15)
    @ApiModelProperty(value = " 民族(见国标GB3304-91)")
    private String nation;
    /**登录名*/
    @Excel(name = "登录名", width = 15)
    @ApiModelProperty(value = "登录名")
    private String loginname;
    /**手机号码*/
    @Sensitive(type = SensitiveTypeEnum.MOBILE_PHONE)
    @Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    /**邮编*/
    @Excel(name = "邮编", width = 15)
    @ApiModelProperty(value = "邮编")
    private String postcode;

    @Excel(name = "生日", width = 15)
    @ApiModelProperty(value = "生日")
    private String birthday;

    /**1教练员，2学员*/
    @Excel(name = "1教练员，2学员", width = 15)
    @ApiModelProperty(value = "1教练员，2学员")
    private Integer type;
}
