package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 驾校详细信息编辑
 *
 * <AUTHOR>
 * @version 2024-03-21
 */
@Data
@TableName("t_m_ins_details_edit")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "t_m_ins_details_edit对象", description = "驾校详细信息编辑")
public class InsDetailsEdit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 培训机构编号
     */
    @Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    @Dict(dictTable = "t_m_institution",dicText = "NAME",dicCode = "INSCODE")
    private String inscode;
    /**
     * 地址
     */
    @Excel(name = "地址", width = 15)
    @ApiModelProperty(value = "地址")
    private String address;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;
    /**
     * 驾校首页图
     */
    @Excel(name = "驾校首页图", width = 15)
    @ApiModelProperty(value = "驾校首页图")
    private String logo;
    /**
     * 轮播图 图片组
     */
    @Excel(name = "轮播图 图片组", width = 15)
    @ApiModelProperty(value = "轮播图 图片组")
    private String carouselIds;
    /**
     * 简介说明
     */
    @Excel(name = "简介说明", width = 15)
    @ApiModelProperty(value = "简介说明")
    private String introduction;
    /**
     * 经度
     */
    @Excel(name = "经度", width = 15)
    @ApiModelProperty(value = "经度")
    private String lng;
    /**
     * 纬度
     */
    @Excel(name = "纬度", width = 15)
    @ApiModelProperty(value = "纬度")
    private String lat;
    /**
     * 投诉电话
     */
    @Excel(name = "投诉电话", width = 15)
    @ApiModelProperty(value = "投诉电话")
    private String complaintsHotline;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 审核状态
     */
    @Excel(name = "审核状态", width = 15)
    @ApiModelProperty(value = "审核状态")
    @Dict(dicCode = "sys_general_audit")
    private Integer auditStatus;
    /**
     * 审核理由
     */
    @Excel(name = "审核理由", width = 15)
    @ApiModelProperty(value = "审核理由")
    private String auditReason;
    /**
     * 审核人
     */
    @Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private String auditUser;
    /**
     * 审核时间
     */
    @Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;
}
