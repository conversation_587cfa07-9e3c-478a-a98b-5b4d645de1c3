package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.dto.InstitutionDto;
import com.jky.boot.core.module.gzpt.entity.Institution;
import com.jky.boot.core.module.gzpt.entity.Platform;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IPlatformService;
import com.jky.boot.core.module.gzpt.utils.NationwideUtil;
import com.jky.boot.core.module.gzpt.utils.PlatformUtils;
import com.jky.boot.core.module.gzpt.utils.PushJgUtils;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.system.module.manage.comm.SysCacheConstant;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import com.jky.crypto.utils.JkyDesensitizedUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 培训机构表
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="培训机构表")
@RestController
@RequestMapping("/gzpt/institution")
@Slf4j
public class InstitutionController extends JeecgController<Institution, IInstitutionService> {
	@Autowired
	private IBankRegService iBankRegService;

	@Autowired
	private IInstitutionService institutionService;

	@Autowired
	private IPlatformService iPlatformService;

	@Autowired
	private JkySysDepartService jkySysDepartService;

	@Autowired
	private RedisUtil redisUtil;


	/**
	 * 分页列表查询
	 *
	 * @param institution
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "培训机构表-分页列表查询")
	@ApiOperation(value="培训机构表-分页列表查询", notes="培训机构表-分页列表查询")
	@DesensitizedAnno
	@GetMapping(value = "/list")
	public Result<IPage<Institution>> queryPageList(Institution institution,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(institution.getInscode());
		institution.setInscode(null);
		QueryWrapper<Institution> queryWrapper = QueryGenerator.initQueryWrapper(institution, req.getParameterMap());
		queryWrapper.and(wq -> {
			wq.in("inscode", inscode)
					.or()
					.eq("inscode",JkSecurityUtils.getDefaultInscode());
		});
		queryWrapper.setEntityClass(Institution.class);
		queryWrapper.orderByDesc("create_time");
		Page<Institution> page = new Page<>(pageNo, pageSize);
		IPage<Institution> pageList = institutionService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param institution
	 * @return
	 */
	@AutoLog(value = "培训机构表-添加")
	@ApiOperation(value="培训机构表-添加", notes="培训机构表-添加")
	@RequiresPermissions("gzpt:institution:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody Institution institution) {
		institution.setInscode(JkSecurityUtils.getDefaultInscode());
		institution.setIslock(0);
		institutionService.save(institution);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param institution
	 * @return
	 */
	@AutoLog(value = "培训机构表-编辑")
	@ApiOperation(value="培训机构表-编辑", notes="培训机构表-编辑")
	@RequiresPermissions(value = {"gzpt:institution:edit", "gzpt:institution:lockOrUnlock"}, logical = Logical.OR)
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody Institution institution) {
		institutionService.updateInstitution(institution);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "培训机构表-通过id删除")
	@ApiOperation(value="培训机构表-通过id删除", notes="培训机构表-通过id删除")
	@RequiresPermissions("gzpt:institution:remove")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		Institution institution = institutionService.selectInstitutionById(Long.valueOf(id));

		// 查看是否推送成功，推送成功后部门表中会有数据
		if(Objects.nonNull(institution.getInscode())){
			JkySysDepart depart = jkySysDepartService.selectDeptByleader(institution.getInscode());
			if(depart != null){
				jkySysDepartService.deleteByOrgCode(institution.getInscode());
				// 清除部门树缓存
				redisUtil.del(SysCacheConstant.JKY_SYS_DEPARTS_CACHE + "::" + institution.getInscode());
			}
		}
		// 删除机构
		institutionService.deleteInstitutionById(Long.parseLong(id));
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "培训机构表-批量删除")
	@ApiOperation(value="培训机构表-批量删除", notes="培训机构表-批量删除")
	@RequiresPermissions("gzpt:institution:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.institutionService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "培训机构表-通过id查询")
	@ApiOperation(value="培训机构表-通过id查询", notes="培训机构表-通过id查询")
	@GetMapping(value = "/{id}")
	public Result<Institution> queryById(@PathVariable(name="id") String id) {
		Institution institution = institutionService.selectInstitutionById(Long.parseLong(id));
		if(institution==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(institution);
	}
//
    /**
    * 导出excel
    *
    * @param request
    * @param institution
    */
    @RequiresPermissions("gzpt:institution:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Institution institution) {
		String title = "培训机构表";
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 角色权限
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(institution.getInscode(),institution.getInscodes());
		institution.setInscode(null);
		QueryWrapper<Institution> queryWrapper = QueryGenerator.initQueryWrapper(institution, request.getParameterMap());
		queryWrapper.and(wq -> {
			wq.in("inscode", inscode)
					.or()
					.eq("inscode",JkSecurityUtils.getDefaultInscode());
		});
		queryWrapper.orderByDesc("create_time");

		// 处理勾选
		String selections = request.getParameter("selections");
		if (oConvertUtils.isNotEmpty(selections)) {
			List<String> arr = Arrays.asList(selections.split(","));
			queryWrapper.in("id", arr);
		}

		// 插入厂商名称
		List<Institution> records = this.service.list(queryWrapper);
		Set<String> platforms = records.stream().map(Institution::getPlatform).collect(Collectors.toSet());
		if(!platforms.isEmpty()){
			List<Platform> list = iPlatformService.list(Wrappers.lambdaQuery(Platform.class).in(Platform::getSerialNumber, platforms));
			Map<String, String> collect = list.stream().collect(Collectors.toMap(Platform::getSerialNumber, Platform::getPlatformName));
			records.forEach(e -> {
				e.setPlatformName(collect.get(e.getPlatform()));
			});
		}
		JkyDesensitizedUtils.desensitizeObj(records);
		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		mv.addObject("fileName", title);
		mv.addObject("entity", Institution.class);
		ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
		mv.addObject("params", exportParams);
		mv.addObject("data", records);
		return mv;
    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_institution:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, Institution.class);
//    }

	 /**
	  * 推送到全国平台
	  *
	  * @param id
	  * @return
	  * @throws Exception
	  */
	 @GetMapping("/pushQg/{id}")
	 @RequiresPermissions("gzpt:institution:pushQg")
	 @Transactional
	 public Result<?> pushQg(@PathVariable("id") Long id) throws Exception {
		 Institution institution = institutionService.selectInstitutionById(id);
		 CommonResponse response = NationwideUtil.pushInstitution(institution);
		 if (response.getErrorcode() != 0) {
			 return Result.error(response.getMessage());
		 }
		 JSONObject jsonObject = JSON.parseObject(response.getData().toString());
		 String inscode = jsonObject.get("inscode").toString();
		 institution.setInscode(inscode);
		 institutionService.updateInstitution(institution);

		 // 推送全国返回驾校编号后，将驾校添加至部门表
		 JkySysDepart old = jkySysDepartService.selectDeptByleader(inscode);
		 if(old == null){
			 JkySysDepart depart = new JkySysDepart();
			 LoginUser loginUser = JkSecurityUtils.getLoginUser();
			 depart.setOrgCode(inscode);
			 JkySysDepart one = jkySysDepartService.selectDeptByleader(institution.getDistrict());
			 depart.setParentId(one.getId());
			 depart.setDepartName(institution.getName());
			 depart.setCreateBy(loginUser.getRealname());
			 depart.setCreateTime(new Date());
			 depart.setDelFlag(CommonConstant.DEL_FLAG_0.toString());
			 depart.setStatus(CommonConstant.STATUS_1);
			 jkySysDepartService.save(depart);

			 // 清除部门树缓存
			 redisUtil.del(SysCacheConstant.JKY_SYS_DEPARTS_CACHE + "::" + inscode);
		 }
		 return Result.ok(response.getData());
	 }

	 /**
	  * 推送到计时平台
	  *
	  * @param id
	  * @return
	  */
	 @GetMapping("/pushJs/{id}")
	 @RequiresPermissions("gzpt:institution:pushJs")
	 public Result<?> pushJs(@PathVariable("id") Long id) throws Exception {
		 Institution institution = institutionService.selectInstitutionById(id);
		 if (institution.getInscode() == null) {
			 return Result.error("驾校未获取统一机构编号！");
		 }
		 Platform platform = iPlatformService.getByPlatformSerialNumber(institution.getPlatform());
		 if (platform == null) {
			 return Result.error("所属驾校未选择计时厂商!");
		 }
		 CommonResponse response = PushJsUtil.pushinstojs(institution, platform.getApi() + "gzInstitution");
		 if(response.getErrorcode()==0){
			 return Result.ok(JSONObject.parseObject(response.getResult()));
		 }else {
			 return Result.error(PlatformUtils.errMsg(institution.getPlatform(), response.getMessage()));
		 }
	 }

	 /**
	  * 批量推送计时
	  *
	  * @param id
	  * @return
	  * @throws Exception
	  */
	 @GetMapping("/batchPushJs/{id}")
	 @RequiresPermissions("gzpt:institution:pushJsBatch")
	 public Result<?> batchPushJs(@PathVariable("id") String id) {
		 if (StringUtils.isEmpty(id)) {
			 return Result.error("id不能为空");
		 }
		 String[] ids = id.split(",");
		 List<String> errorMsg = new ArrayList<>();
		 StringBuilder result = new StringBuilder();
		 for (int i = 0; i < ids.length; i++) {
			 Institution institution = institutionService.selectInstitutionById(Long.valueOf(ids[i]));
			 try {
				 if (institution.getInscode() == null) {
					 throw new Exception("驾校未获取统一机构编号");
				 }
				 System.out.println("---------------------------------------------------------------------");
				 System.out.println("----------OvO------------------------" + institution);
				 System.out.println("----------OvO------------------------" + institution.getInscode());
				 System.out.println("----------OvO------------------------" + institution.getPlatform());
				 System.out.println("---------------------------------------------------------------------");
				 Platform platform = iPlatformService.getByPlatformSerialNumber(institution.getPlatform());
				 if (platform == null) {
					 throw new Exception("所属驾校未选择计时厂商");
				 }
				 CommonResponse response = PushJsUtil.pushinstojs(institution, platform.getApi() + "gzInstitution");
				 String str = JSONObject.parseObject(response.getResult()).toString();
				 result.append(str);
			 } catch (Exception e) {
				 errorMsg.add("推送失败," + e.getMessage());
				 e.printStackTrace();
			 }

		 }
		 if (errorMsg.isEmpty()) {
			 return Result.ok(result.toString());
		 } else {
			 StringBuilder builder = new StringBuilder();
			 for (String error : errorMsg) {
				 builder.append(error);
			 }
			 return Result.error(builder.toString());
		 }
	 }

	 @GetMapping("/queryTransaction")
	 @RequiresPermissions("gzpt:institution:queryTransaction")
	 public Result<?> queryTransaction(InstitutionDto institutionDto) throws Exception {
		 Date startdate = DateUtils.dateTime("yyyyMMdd", institutionDto.getStartdate());
		 Date enddate = DateUtils.dateTime("yyyyMMdd", institutionDto.getEnddate());

		 String year = DateUtils.yearCompare(startdate, enddate);
		 if (year.compareTo("1.0") > 0) {
			 return Result.error("所选时间不能大于一年");
		 }
		 iBankRegService.queryTransaction(institutionDto);
		 return Result.ok();
	 }

	 /**
	  * 加密接口
	  */
	 @GetMapping("/encrypt")
	 public Result<?> encryptData() {
		 QueryWrapper<Institution> wrapper = new QueryWrapper<>();
		 wrapper.setEntityClass(Institution.class);
		 List<Institution> list = institutionService.list(wrapper);
		 institutionService.updateBatchById(list);
		 return Result.ok();
	 }

	 /**
	  * 同步培训机构状态
	  *
	  * @param id id
	  * @return 提示信息
	  */
	 @GetMapping(value = "/syncStatus")
	 public Result<?> syncStatus(@RequestParam("id") String id,
								 @RequestParam(value = "all", required = false) Boolean all) {
		 all = Objects.isNull(all) || !all;
		 List<Institution> insList = institutionService.list(
				 Wrappers.<Institution>lambdaQuery()
						 .eq(all, Institution::getId, id)
		 );
		 for (Institution ins : insList) {
			 if (StringUtils.isBlank(ins.getInscode())) {
				 continue;
			 }
			 JSONObject jsonObject = new JSONObject();
			 jsonObject.put("inscode", ins.getInscode());
			 Result<Institution> result = PushJgUtils.pushJgNew("/jgpt/sync/ins/status", jsonObject, Institution.class);
			 Institution resp = result.getResult();
			 ins.setLevels(resp.getLevels());
			 ins.setBusistatus(resp.getBusistatus());
			 ins.setIsBankruptcy(resp.getIsBankruptcy());
			 ins.setIslock(resp.getIslock());
			 institutionService.updateById(ins);
		 }
		 return Result.ok("同步成功");
	 }
}
