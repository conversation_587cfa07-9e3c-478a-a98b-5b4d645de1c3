package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * @Description: t_m_mr890ch_log
 * @Author: jeecg-boot
 * @Date:   2024-01-18
 * @Version: V1.0
 */
@Data
@TableName("t_m_mr890ch_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_mr890ch_log对象", description="t_m_mr890ch_log")
public class TMMr890chLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;
	/**
     * 日志类型
     * 1:mr890启用
     * 2:mr890停用
     * 3:人脸识别通过
     * 4:设置黑户
     * 5:监管注销学员
     * */
	@Excel(name = "日志类型", width = 15)
    @ApiModelProperty(value = "日志类型")
    @Dict(dicCode = "sensitive_log_type")
    private java.lang.Integer logType;
	/**日志内容*/
	@Excel(name = "日志内容", width = 15)
    @ApiModelProperty(value = "日志内容")
    private java.lang.String logContent;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15)
    @ApiModelProperty(value = "操作类型")
    private java.lang.Integer operateType;
	/**操作用户账号*/
	@Excel(name = "操作用户账号", width = 15)
    @ApiModelProperty(value = "操作用户账号")
    private java.lang.String userid;
	/**操作用户名称*/
	@Excel(name = "操作用户名称", width = 15)
    @ApiModelProperty(value = "操作用户名称")
    private java.lang.String username;
	/**IP*/
	@Excel(name = "IP", width = 15)
    @ApiModelProperty(value = "IP")
    private java.lang.String ip;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;

    @TableField(exist = false)
    private String createTimeBegin;
    @TableField(exist = false)
    private String createTimeEnd;

    @Getter
    public enum LogTypeEnum {
        //* 3:人脸识别通过
        //     * 4:设置黑户
        FACE_AUTH(3,"人脸识别通过"),
        BLACKLIST_FILL(4,"设置黑户"),
        STU_LOGOUT(5,"学员注销"),
        ENABEL(1,"设备启用"),
        DISABEL(2,"设备停用");
        private final Integer code;
        private final String memo;

        LogTypeEnum(Integer code, String memo){
            this.code = code;
            this.memo = memo;
        }
    }

}
