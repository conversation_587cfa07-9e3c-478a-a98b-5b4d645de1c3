package com.jky.boot.core.module.sxgzpt.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.TMInstitutionPackage;
import com.jky.boot.core.module.sxgzpt.service.ITMInstitutionPackageService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * 	驾校车型套餐设置
 * <AUTHOR>
 * @since   2023-06-13
 * @version V1.0
 */
@Api(tags="驾校车型套餐设置")
@RestController
@RequestMapping("/gzpt/tMInstitutionPackage")
@Slf4j
public class TMInstitutionPackageController extends JeecgController<TMInstitutionPackage, ITMInstitutionPackageService> {
	@Autowired
	private ITMInstitutionPackageService tMInstitutionPackageService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tMInstitutionPackage
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "驾校车型套餐设置-分页列表查询")
	@ApiOperation(value="驾校车型套餐设置-分页列表查询", notes="驾校车型套餐设置-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMInstitutionPackage>> queryPageList(TMInstitutionPackage tMInstitutionPackage,
															 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
															 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
															 HttpServletRequest req) {
		List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMInstitutionPackage.getInsCode());
		tMInstitutionPackage.setInsCode(null);
		QueryWrapper<TMInstitutionPackage> queryWrapper = QueryGenerator.initQueryWrapper(tMInstitutionPackage, req.getParameterMap());
		queryWrapper.in("ins_code", insCodes);
		queryWrapper.orderByDesc("create_time");
		Page<TMInstitutionPackage> page = new Page<>(pageNo, pageSize);
		IPage<TMInstitutionPackage> pageList = tMInstitutionPackageService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tMInstitutionPackage
	 * @return
	 */
	@AutoLog(value = "驾校车型套餐设置-添加")
	@ApiOperation(value="驾校车型套餐设置-添加", notes="驾校车型套餐设置-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_institution_package:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMInstitutionPackage tMInstitutionPackage) {
		tMInstitutionPackage.setInsCode(JkSecurityUtils.getDeptOrgCode());
		tMInstitutionPackageService.save(tMInstitutionPackage);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tMInstitutionPackage
	 * @return
	 */
	@AutoLog(value = "驾校车型套餐设置-编辑")
	@ApiOperation(value="驾校车型套餐设置-编辑", notes="驾校车型套餐设置-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_institution_package:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMInstitutionPackage tMInstitutionPackage) {
		tMInstitutionPackageService.updateById(tMInstitutionPackage);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "驾校车型套餐设置-通过id删除")
	@ApiOperation(value="驾校车型套餐设置-通过id删除", notes="驾校车型套餐设置-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_institution_package:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		tMInstitutionPackageService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "驾校车型套餐设置-批量删除")
	@ApiOperation(value="驾校车型套餐设置-批量删除", notes="驾校车型套餐设置-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_institution_package:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
		this.tMInstitutionPackageService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "驾校车型套餐设置-通过id查询")
	@ApiOperation(value="驾校车型套餐设置-通过id查询", notes="驾校车型套餐设置-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMInstitutionPackage> queryById(@RequestParam(name="id") String id) {
		TMInstitutionPackage tMInstitutionPackage = tMInstitutionPackageService.getById(id);
		if(tMInstitutionPackage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMInstitutionPackage);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMInstitutionPackage
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_institution_package:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMInstitutionPackage tMInstitutionPackage) {
//        return super.exportXls(request, tMInstitutionPackage, TMInstitutionPackage.class, "驾校车型套餐设置");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_institution_package:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMInstitutionPackage.class);
//    }

}
