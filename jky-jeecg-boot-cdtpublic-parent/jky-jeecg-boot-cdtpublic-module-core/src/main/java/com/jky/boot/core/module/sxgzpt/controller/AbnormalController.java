package com.jky.boot.core.module.sxgzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.sxgzpt.entity.Abnormal;
import com.jky.boot.core.module.sxgzpt.service.IAbnormalService;
import com.jky.boot.core.module.sxgzpt.service.ITrainRecordAbnormalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * @Description: t_d_abnormal
 * @Author: jeecg-boot
 * @Date: 2023-04-13
 * @Version: V1.0
 */
@Api(tags = "t_d_abnormal")
@RestController
@RequestMapping("/gzpt/abnormal")
@Slf4j
public class AbnormalController extends JeecgController<Abnormal, IAbnormalService> {
    @Autowired
    private IAbnormalService abnormalService;
    @Autowired
    private ITrainRecordAbnormalService trainRecordAbnormalService;

    /**
     * 分页列表查询
     *
     * @param abnormal
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "t_d_abnormal-分页列表查询")
    @ApiOperation(value = "t_d_abnormal-分页列表查询", notes = "t_d_abnormal-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Abnormal>> queryPageList(Abnormal abnormal,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 HttpServletRequest req) {
        QueryWrapper<Abnormal> queryWrapper = QueryGenerator.initQueryWrapper(abnormal, req.getParameterMap());

        Page<Abnormal> page = new Page<Abnormal>(pageNo, pageSize);
        IPage<Abnormal> pageList = page.setRecords(abnormalService.selectAbnormalList(abnormal));
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param abnormal
     * @return
     */
    @AutoLog(value = "t_d_abnormal-添加")
    @ApiOperation(value = "t_d_abnormal-添加", notes = "t_d_abnormal-添加")
    //@RequiresPermissions("org.jeecg.modules.demo:t_d_abnormal:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody Abnormal abnormal) {
        abnormalService.save(abnormal);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param abnormal
     * @return
     */
    @AutoLog(value = "t_d_abnormal-编辑")
    @ApiOperation(value = "t_d_abnormal-编辑", notes = "t_d_abnormal-编辑")
    //@RequiresPermissions("org.jeecg.modules.demo:t_d_abnormal:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Abnormal abnormal) {
        abnormalService.updateById(abnormal);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "t_d_abnormal-通过id删除")
    @ApiOperation(value = "t_d_abnormal-通过id删除", notes = "t_d_abnormal-通过id删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_d_abnormal:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        abnormalService.removeById(id);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("group_id", id);
//        trainRecordAbnormalService.remove(queryWrapper);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "t_d_abnormal-批量删除")
    @ApiOperation(value = "t_d_abnormal-批量删除", notes = "t_d_abnormal-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_d_abnormal:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.abnormalService.removeByIds(Arrays.asList(ids.split(",")));
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("group_id", Arrays.asList(ids));
//        trainRecordAbnormalService.remove(queryWrapper);
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "t_d_abnormal-通过id查询")
    @ApiOperation(value = "t_d_abnormal-通过id查询", notes = "t_d_abnormal-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Abnormal> queryById(@RequestParam(name = "id", required = true) String id) {
        Abnormal abnormal = abnormalService.getById(id);
        if (abnormal == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(abnormal);
    }
//
//    /**
//     * 导出excel
//     *
//     * @param request
//     * @param abnormal
//     */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_d_abnormal:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, Abnormal abnormal) {
//        return super.exportXls(request, abnormal, Abnormal.class, "t_d_abnormal");
//    }
//
//    /**
//     * 通过excel导入数据
//     *
//     * @param request
//     * @param response
//     * @return
//     */
//    //@RequiresPermissions("t_d_abnormal:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, Abnormal.class);
//    }

    @GetMapping(value = "/getAbnormalMapList")
    public Result<?> getAbnormalMapList() {
        return Result.OK(abnormalService.getAbnormal());
    }
}
