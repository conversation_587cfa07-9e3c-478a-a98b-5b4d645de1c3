package com.jky.boot.base.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.base.dao.InsRiskNotice;
import com.jky.boot.base.mapper.InsRiskNoticeMapper;
import com.jky.boot.base.service.IInsRiskNoticeService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 驾校风险提示
 *
 * <AUTHOR>
 * @version 2024-06-11
 */
@Service
public class InsRiskNoticeServiceImpl extends ServiceImpl<InsRiskNoticeMapper, InsRiskNotice> implements IInsRiskNoticeService {


    @Override
    public Map<String, String> getRiskMap() {
        List<InsRiskNotice> riskNotices = this.list(
                Wrappers.<InsRiskNotice>lambdaQuery()
                        .gt(InsRiskNotice::getExpirationDate, new Date())
        );
        return riskNotices.stream().collect(Collectors.toMap(InsRiskNotice::getInscode, InsRiskNotice::getContent));
    }
}
