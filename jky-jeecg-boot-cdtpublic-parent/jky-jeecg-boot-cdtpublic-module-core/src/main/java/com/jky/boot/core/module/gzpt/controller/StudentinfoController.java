package com.jky.boot.core.module.gzpt.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.base.dao.SchedulingImi;
import com.jky.boot.common.exception.JkyCdtpublicException;
import com.jky.boot.common.sensitive.DesensitizedUtils;
import com.jky.boot.common.utils.CityUtil;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.common.utils.http.RestTemplateUtils;
import com.jky.boot.core.module.gzpt.dto.ResultDto;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.event.ContractEvent;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.*;
import com.jky.boot.core.module.gzpt.vo.BlackOrLogoutVo;
import com.jky.boot.core.module.gzpt.vo.CreditVo;
import com.jky.boot.core.module.gzpt.vo.StudentCountVO;
import com.jky.boot.core.module.gzpt.vo.StudentViewVO;
import com.jky.boot.core.module.scheduling.entity.SchedulingImiCenter;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import com.jky.crypto.utils.JkyCryptorUtils;
import com.jky.crypto.utils.JkyDesensitizedUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import com.jky.boot.core.module.scheduling.entity.SchedulingImiCenter;
import com.jky.boot.core.module.scheduling.service.ISchedulingImiCenterService;

/**
 * @Description: 学员信息表
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Api(tags = "学员信息表")
@RestController
@RequestMapping("/gzpt/studentinfo")
@Slf4j
public class StudentinfoController extends JeecgController<Studentinfo, IStudentinfoService> {
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private JkySysDepartService sysDeptService;
    @Autowired
    private IInstitutionService iInstitutionService;
    @Autowired
    private IPlatformService iPlatformService;
    @Autowired
    private IBankRegService iBankRegService;
    @Autowired
    private IStudentContractService iStudentContractService;
    @Autowired
    private ICostService costService;
    @Autowired
    private IInstitutionBindingService institutionBindingService;
    @Autowired
    private IStudentinfoLogoutService iStudentinfoLogoutService;
    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private ICoachService iCoachService;
    @Autowired
    private IClassRecordDetailService classRecordDetailService;
    @Autowired
    private ITotalTimeService totalTimeService;
    @Autowired
    private ITrainSubjectCreditService trainSubjectCreditService;
    @Autowired
    private RedissonLockClient redissonLock;
    @Autowired
    private TotalTimeController totalTimeController;

    //资金上下限
    @Autowired
    private ICostLimitService costLimitService;
    //合同
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @Autowired
    private ITransferCarTypeService transferCarTypeService;
    @Autowired
    private IStudentinfoEnterService studentinfoEnterService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private ISchedulingImiCenterService schedulingImiCenterService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ITMMr890chLogService mr890chLogService;
    @Value("${jgpt.stuCheck:true}")
    private Boolean stuCheck;
    @Value("${zljp.km1pass:http://127.0.0.1:13306/zlbStuKm/km1pass}")
    private String km1pass;
    @Value("${jky.crypto-mybatis.default-key}")
    private String cryptoKey;


    /**
     * 分页列表查询
     *
     * @param studentinfo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @ApiOperation(value = "t_m_studentinfo-分页列表查询", notes = "t_m_studentinfo-分页列表查询")
    @DesensitizedAnno
    @GetMapping(value = "/list")
//	@RequiresPermissions("gzpt:studentinfo:list")
//	@PermissionData(pageComponent="base/BaseCompInfoList")
    public Result<IPage<Studentinfo>> queryPageList(Studentinfo studentinfo,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {

        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfo.getInscode(), studentinfo.getInscodes());
        studentinfo.setInscode(null);
        QueryWrapper<Studentinfo> queryWrapper = QueryGenerator.initQueryWrapper(studentinfo, req.getParameterMap());
        queryWrapper.setEntityClass(Studentinfo.class);
        //角色权限
        queryWrapper.in("inscode", inscode);

        if (StringUtils.isNotBlank(studentinfo.getCoachname())) {
            Coach one = iCoachService.getOne(
                    Wrappers.lambdaQuery(Coach.class)
                            .eq(Coach::getName, studentinfo.getCoachname()),false
            );
            if (Objects.nonNull(one)){
                queryWrapper.eq("coachnum", one.getCoachnum());
            }
        }
        queryWrapper.orderByDesc("applydate");
        queryWrapper.orderByDesc("create_time");
        if (Objects.nonNull(studentinfo.getApplydateBegin())) {
            queryWrapper.ge("applydate", studentinfo.getApplydateBegin().replace("-", ""));
        }
        if (Objects.nonNull(studentinfo.getApplydateEnd())) {
            queryWrapper.le("applydate", studentinfo.getApplydateEnd().replace("-", ""));
        }

        Page<Studentinfo> page = new Page<Studentinfo>(pageNo, pageSize);
        IPage<Studentinfo> pageList = studentinfoService.page(page, queryWrapper);

        List<Studentinfo> list = pageList.getRecords();
        for (Studentinfo student : list) {
            String photopath = student.getPhotopath();
            if (StringUtils.isNotBlank(photopath) && photopath.contains(OssZWYUtils.UPLOAD_PATH)) {
//                student.setPhotopath(OssZWYUtils.PROXY_URL + student.getPhotopath());
                student.setPhotopath(ossZWYUtils.getPhotoUrl(photopath));
            }
            Coach coach = iCoachService.selectCoachByCoachnum(student.getCoachnum() == null ? "" : student.getCoachnum());
            if (coach != null) {
                student.setCoachnum(coach.getCoachnum());
                student.setCoachname(coach.getName());
            }
        }
        return Result.OK(pageList);
    }
    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "t_m_studentinfo-分页列表查询", notes = "t_m_studentinfo-分页列表查询")
    @DesensitizedAnno
    @GetMapping(value = "/teachManageStudent/list")
    public Result<IPage<Studentinfo>> queryPageList2(Studentinfo studentinfo,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(name = "traintype") String traintype,
                                                    HttpServletRequest req) {
        // 根据驾校inscode和排班的traintype去筛选学员
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfo.getInscode(), studentinfo.getInscodes());
        studentinfo.setInscode(null);
        studentinfo.setTraintype(null);
        QueryWrapper<Studentinfo> queryWrapper = QueryGenerator.initQueryWrapper(studentinfo, req.getParameterMap());
        queryWrapper.setEntityClass(Studentinfo.class);
        // 检查是否存在于模拟中心表中
        if (inscode != null && !inscode.isEmpty()) {
            String firstInscode = inscode.get(0);
            LambdaQueryWrapper<SchedulingImiCenter> imiCenterWrapper = Wrappers.lambdaQuery();
            imiCenterWrapper.eq(SchedulingImiCenter::getInscode, firstInscode);
            SchedulingImiCenter imiCenter = schedulingImiCenterService.getOne(imiCenterWrapper);

            // 如果找到匹配的模拟中心记录，使用inscodeList作为驾校编码
            if (imiCenter != null && imiCenter.getInscodeList() != null && !imiCenter.getInscodeList().isEmpty()) {
                String[] inscodeArray = imiCenter.getInscodeList().split(",");
                inscode = Arrays.asList(inscodeArray);
            }
        }
        // 修改为 in 查询
        queryWrapper.in("inscode", inscode);
        // traintype代表这个课堂对应的科目c1 c2等
        if (StringUtils.isNotBlank(traintype)) {
            if ("C1".equals(traintype)) {
                queryWrapper.and(qw -> qw.eq("traintype", "C1").or().eq("traintype", "C2"));
            } else {
                queryWrapper.eq("traintype", traintype);
            }
        }
//        if (StringUtils.isNotBlank(studentinfo.getCoachname())) {
//            Coach one = iCoachService.getOne(
//                    Wrappers.lambdaQuery(Coach.class)
//                            .eq(Coach::getName, studentinfo.getCoachname()),false
//            );
//            if (Objects.nonNull(one)){
//                queryWrapper.eq("coachnum", one.getCoachnum());
//            }
//        }
//        queryWrapper.orderByDesc("applydate");
        queryWrapper.orderByDesc("create_time");
//        if (Objects.nonNull(studentinfo.getApplydateBegin())) {
//            queryWrapper.ge("applydate", studentinfo.getApplydateBegin().replace("-", ""));
//        }
//        if (Objects.nonNull(studentinfo.getApplydateEnd())) {
//            queryWrapper.le("applydate", studentinfo.getApplydateEnd().replace("-", ""));
//        }

        Page<Studentinfo> page = new Page<Studentinfo>(pageNo, pageSize);
        IPage<Studentinfo> pageList = studentinfoService.page(page, queryWrapper);

//        List<Studentinfo> list = pageList.getRecords();
//        for (Studentinfo student : list) {
//            String photopath = student.getPhotopath();
//            if (StringUtils.isNotBlank(photopath) && photopath.contains(OssZWYUtils.UPLOAD_PATH)) {
////                student.setPhotopath(OssZWYUtils.PROXY_URL + student.getPhotopath());
//                student.setPhotopath(ossZWYUtils.getPhotoUrl(photopath));
//            }
//            Coach coach = iCoachService.selectCoachByCoachnum(student.getCoachnum() == null ? "" : student.getCoachnum());
//            if (coach != null) {
//                student.setCoachnum(coach.getCoachnum());
//                student.setCoachname(coach.getName());
//            }
//        }
        return Result.OK(pageList);
    }
    /**
     * 新增学员信息
     */
    @AutoLog(value = "学员信息-添加", operateType = CommonConstant.OPERATE_TYPE_2)
    @ApiOperation(value = "学员信息-添加", notes = "学员信息-添加")
    @RequiresPermissions("gzpt:studentinfo:add")
    @PostMapping(value = "/add")
    public Result<String> add(@Valid @RequestBody Studentinfo studentinfo, BindingResult bindingResult) {
        String key = "StudentAdd-" + studentinfo.getIdcard();
        if (redissonLock.existKey(key)) {
            return Result.error("学员添加中，请稍后再试！！");
        }
        try {
            if (redissonLock.tryLock(key, -1, 10000)) {
                if (bindingResult.hasErrors()) {
                    StringBuilder errmsg = new StringBuilder();
                    List<FieldError> fieldErrors = bindingResult.getFieldErrors();
                    for (FieldError e : fieldErrors) {
                        errmsg.append(e.getDefaultMessage() + "!");
                    }
                    return Result.error(errmsg.toString());
                }
                // 根据身份证号和车型限制学员不能重复报名
                Studentinfo student = new Studentinfo();
                student.setIdcard(studentinfo.getIdcard());
                student.setTraintype(studentinfo.getTraintype());
                List<Studentinfo> studentinfoList = studentinfoService.selectStudentinfoList(student);
                if (studentinfoList != null && studentinfoList.size() > 0) {
                    return Result.error("该学员已经报名,无法重复报名!"); //的" + studentinfo.getTraintype()+ "车型
                }
                if (Objects.isNull(studentinfo.getPhotopath())) {
                    return Result.error("学员头像为空不能新增");
                }

                if (stuCheck) {
                    JSONObject json = new JSONObject();
                    json.put("idCard",studentinfo.getIdcard());
                    json.put("traintype",studentinfo.getTraintype());
                    CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.jgptUrl+"/sx/studentApplySearch", json.toString());
                    int errorcode = commonResponse.getErrorcode();
                    JSONObject json1 = (JSONObject) JSON.toJSON(commonResponse.getData());
                    log.info("[errorcode]-------------------------------------------"+errorcode);
                    log.info("[json1]-------------------------------------------"+json1);
                    if (errorcode==1){
                        String insname = json1.get("insname").toString();
                        return Result.error("该学员已在"+insname+"报名，不能重复报名");
                    }
                }
                // 根据身份证号限制中国学员未满十八周岁不能报名
                if ("中国".equals(studentinfo.getNationality()) && studentinfo.getIdcard().length()==18) {
                    int age = DateUtil.thisYear() - Integer.parseInt(studentinfo.getIdcard().substring(6, 10));
                    if (age < 18) {
                        return Result.error("该学员未满十八周岁,不能报名!");
                    } else if (age == 18) {
                        int month_day = Integer.parseInt(DateUtils.dateTimeNow("MMdd")) - Integer.parseInt(studentinfo.getIdcard().substring(10, 14));
                        if (month_day < 0) {
                            return Result.error("该学员未满十八周岁,不能报名!");
                        }
                    }
                }
                List<String> car = new ArrayList<>();
                car.add("C1");
                car.add("C2");
                if (!car.contains(student.getTraintype())){
                    studentinfo.setIstobank(1);
                }
                Cost cost = costService.getOneByInsTrainType(studentinfo.getInscode(), studentinfo.getTraintype());
                if (Objects.isNull(cost)) {
                    return Result.error("请添加该车型收费内容");
                }
                studentinfo.setFrozenAmount(cost.getFrozenAmount().toPlainString());
                if ("1".equals(studentinfo.getIscrosscity())) {
                    if (studentinfo.getCrossType() == 2) {
                        studentinfo.setFrozenAmount(cost.getFrozenAmount().subtract(cost.getSub3Cost()).subtract(cost.getRemoteCost()).toPlainString());
                    } else if (studentinfo.getCrossType() == 3) {
                        studentinfo.setFrozenAmount(cost.getFrozenAmount().subtract(cost.getSub2Cost()).subtract(cost.getImiCost()).toPlainString());
                    }
                }
                JkySysDepart sysDept = sysDeptService.selectDeptById(studentinfo.getInscode());
                Institution inscode = iInstitutionService.getByInscode(sysDept.getOrgCode());
                studentinfo.setPlatform(inscode.getPlatform());
                studentinfo.setDistrict(inscode.getDistrict());
                studentinfo.setInscode(sysDept.getOrgCode());
                studentinfo.setInsname(sysDept.getDepartName());
                if ("1".equals(studentinfo.getBusitype())) {
                    if (Objects.isNull(studentinfo.getDrilicnum())) {
                        return Result.error("增领时驾驶证号不能为空");
                    }
                    if (!studentinfo.getIdcard().equals(studentinfo.getDrilicnum())) {
                        return Result.error("驾驶证号与身份证号不符");
                    }
                } else {
                    // 驾驶证初领设置驾驶证、原准驾车型、驾驶证获取时间为空
                    studentinfo.setDrilicnum(null);
                    studentinfo.setPerdritype(null);
                    studentinfo.setFstdrilicdate(null);
                }


                // 新增学员初始化状态
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                String startDate = dateFormat.format(calendar.getTime());
                studentinfo.setApplydate(startDate);
                studentinfo.setIssigncontract(0);
                studentinfo.setIstoqg(0);
                studentinfo.setIstojs(0);
                studentinfo.setIstobank(0);
                studentinfo.setStutype(1);
                studentinfo.setName(studentinfo.getName().replaceAll("\\s+", ""));
                studentinfo.setIdcard(studentinfo.getIdcard().replaceAll("\\s+", ""));
                studentinfo.setIssignature(0);
                studentinfo.setIstoapp(0);
                substrPhotoPath(studentinfo);
                if ("1".equals(studentinfo.getCardtype())) {
                    studentinfo.setBirthday(studentinfo.getIdcard().substring(6, 14));
                    studentinfo.setBirthplace(studentinfo.getIdcard().substring(0, 6));
                    int gender = Integer.parseInt(String.valueOf(studentinfo.getIdcard().charAt(16)));
                    studentinfo.setSex((gender % 2) == 0 ? "2":"1");
                }
                log.info("[学员新增]studentinfo:{}", studentinfo);


                // 非跨地市学员校验资金上下限
                if ("2".equals(studentinfo.getIscrosscity())) {
                    Result<String> judgment = isInLimit(studentinfo);
                    if (!judgment.isSuccess()) {
                        return judgment;
                    }
                }
                studentinfoService.save(studentinfo);
                return Result.OK("添加成功！");
            }
        } catch (Exception e) {
            log.error("新增学员失败：", e);
            return Result.error("新增学员失败");
        } finally {
            redissonLock.unlock(key);
        }
        return Result.OK();
    }

    /**
     * 获取当前报名时间
     */
    @ApiOperation(value = "获取当前报名时间", notes = "获取当前报名时间")
    @RequiresPermissions("gzpt:studentinfo:add")
    @GetMapping(value = "/getApplyDate")
    public Result<String> getApplyDate(){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        String startDate = dateFormat.format(calendar.getTime());
        return Result.OK(startDate);
    }


    /**
     * 编辑
     */
    @AutoLog(value = "学员信息-编辑", operateType = CommonConstant.OPERATE_TYPE_3)
    @RequiresPermissions("gzpt:studentinfo:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody Studentinfo studentinfo) throws Exception {
        Studentinfo studentinfoById = studentinfoService.selectStudentinfoById(studentinfo.getId());
        studentinfo.setIdcard(studentinfoById.getIdcard());
        studentinfo.setName(studentinfoById.getName());
        if (!studentinfo.getCardtype().equals(studentinfoById.getCardtype())) {
            return Result.error("学员证件类型或报名时间或车型不允许修改");
        }
        if (!studentinfo.getApplydate().equals(studentinfoById.getApplydate())) {
            return Result.error("学员报名时间不允许修改");
        }
        if (!studentinfo.getTraintype().equals(studentinfoById.getTraintype())) {
            return Result.error("车型不允许修改");
        }
        substrPhotoPath(studentinfo);
        JkySysDepart sysDept = sysDeptService.selectDeptById(JkSecurityUtils.getDeptIdByUnknow(studentinfo.getInscode()));
        studentinfo.setInscode(sysDept.getOrgCode());
        studentinfo.setInsname(sysDept.getDepartName());
        if (studentinfo.getPlatform() == null) {
            Institution inscode = iInstitutionService.getByInscode(sysDept.getOrgCode());
            studentinfo.setPlatform(inscode.getPlatform());
        }

        if ("2".equals(studentinfo.getIscrosscity())) {
            Result<String> judgment = isInLimit(studentinfo);
            if (!judgment.isSuccess()) {
                return judgment;
            }
        }
        if (!StringUtils.isBlank(studentinfo.getIdcard()) && "1".equals(studentinfo.getCardtype())) {
            studentinfo.setBirthday(studentinfo.getIdcard().substring(6, 14));
            studentinfo.setBirthplace(studentinfo.getIdcard().substring(0, 6));
        }
        studentinfo.setStutype(null);
        studentinfo.setIsold(null);
        studentinfo.setFrozenAmount(null);
        boolean update = studentinfoService.updateById(studentinfo);

        if (update) {
            // 修改学员信息之后 如果已经推送过计时 就重推计时
            if (studentinfoById.getIstojs() != null && studentinfoById.getIstojs() == 1) {

                String driveApplyUrl="";
                // 根据身份证号和车型查询在线报名表
                StudentinfoEnter studentinfoEnter = studentinfoEnterService.getOne(
                        Wrappers.lambdaQuery(StudentinfoEnter.class)
                                .eq(StudentinfoEnter::getIdcard, studentinfo.getIdcard())
                                .eq(StudentinfoEnter::getTraintype, studentinfo.getTraintype())
                                .orderByDesc(StudentinfoEnter::getCreateTime)
                );

                if (studentinfoEnter != null && StringUtils.isNotBlank(studentinfoEnter.getFileUrl())) {
                    driveApplyUrl =ossZWYUtils.getPhotoUrl(studentinfoEnter.getFileUrl());
                }

                PushJsUtil.pushstutojs(studentinfo, driveApplyUrl,iPlatformService.getByPlatformSerialNumber(studentinfo.getPlatform()).getApi() + "gzstudent");
            }
            Studentinfo student = studentinfoService.getById(studentinfo.getId());
            // 修改学员信息之后 如果已经推送过银行,开户状态是开户,开户成功,开户失败(0,1,2)中一种 且 学员姓名电话身份证号三者其中一个有变化 就重推银行
            if ((studentinfoById.getIstobank() != null && studentinfoById.getIstobank() == 1) && (!studentinfoById.getName().equals(studentinfo.getName()) ||
                    !studentinfoById.getIdcard().equals(student.getIdcard()) || !studentinfoById.getPhone().equals(studentinfo.getPhone()))) {

                // 重推银行之后也要修改对应开户信息表的姓名身份证号
                BankReg bankReg = new BankReg();
                bankReg.setStunum(studentinfo.getStunum());
                List<BankReg> bankRegs = iBankRegService.selectBankRegList(bankReg);

                if (bankRegs != null && !bankRegs.isEmpty() && bankRegs.get(0).getStatus() < 2) {
                    bankReg.setId(bankRegs.get(0).getId());
                    bankReg.setStuname(student.getName());
                    bankReg.setIdcard(student.getIdcard());
                    iBankRegService.updateBankReg(bankReg);
                    PushBankUtil.PushStuToBank("1", studentinfo);
                }
            }
            Coach coach = iCoachService.selectCoachByCoachnum(studentinfo.getCoachnum());
            if (coach == null) {
                return Result.error("教练员无效");
            }
            studentinfo.setCoachname(coach.getName());
        }
        return Result.ok("编辑成功!");
    }


    /**
     * 驾校学员编辑
     */
    @AutoLog(value = "学员信息-编辑", operateType = CommonConstant.OPERATE_TYPE_3)
    @RequiresPermissions("gzpt:studentinfo:insEdit")
    @RequestMapping(value = "/insEdit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> insEdit(@RequestBody Studentinfo studentinfo) {
        if (StringUtils.isBlank(studentinfo.getPhotopath())) {
            return Result.error("头像为空不能修改");
        }
        Studentinfo byId = studentinfoService.getById(studentinfo.getId());
        substrPhotoPath(studentinfo);
        // 根据身份证号和车型限制学员不能重复报名
        List<Studentinfo> list = studentinfoService.list(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, studentinfo.getIdcard())
                        .eq(Studentinfo::getTraintype, studentinfo.getTraintype())
        );
        List<Studentinfo> collect = list.stream().filter(e -> !e.getId().equals(studentinfo.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            return Result.error("该学员已经报名,无法重复报名!");
        }
        if (studentinfo.getPhotopath().contains("dianziqianming")) {
            log.error("图片异常：{}", studentinfo.getPhotopath());
        }
        byId.setPhotopath(studentinfo.getPhotopath());
        byId.setPhone(studentinfo.getPhone());
        byId.setCoachnum(studentinfo.getCoachnum());
        studentinfoService.updateById(byId);
        return Result.ok("编辑成功");
    }

    /**
     * 修改车型
     *
     * @param studentinfo
     * @return
     */
    @AutoLog(value = "学员信息-修改车型", operateType = CommonConstant.OPERATE_TYPE_3)
    @ApiOperation(value = "学员信息-修改车型", notes = "学员信息-修改车型")
    @RequiresPermissions("gzpt:studentinfo:carEdit")
    @RequestMapping(value = "/carEdit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> carEdit(@RequestBody Studentinfo studentinfo) {
        if (StringUtils.isBlank(studentinfo.getId())) {
            return Result.error("必要参数缺失");
        }
        return transferCarTypeService.saveCarType(studentinfo);
    }

//    /**
//     * 修改报名时间
//     *
//     * @param studentinfo
//     * @return
//     */
//    @AutoLog(value = "学员信息-修改报名时间", operateType = CommonConstant.OPERATE_TYPE_3)
//    @ApiOperation(value = "学员信息-修改报名时间", notes = "学员信息-修改报名时间")
////    @RequiresPermissions("gzpt:studentinfo:carEdit")
//    @RequestMapping(value = "/applyDateEdit", method = {RequestMethod.PUT, RequestMethod.POST})
//    public Result<?> applyDateEdit(@RequestBody Studentinfo studentinfo) {
//        //LambdaUpdateWrapper<Studentinfo> updateWrapper = new LambdaUpdateWrapper<>(Studentinfo.class);
//        //updateWrapper.set(Studentinfo::getTraintype, studentinfo.getTraintype());
//        //updateWrapper.eq(Studentinfo::getId, studentinfo.getId());
//        //boolean b = studentinfoService.update(updateWrapper);
//        //if (b) {
//        //    return Result.ok("编辑成功");
//        //}
//        if (StringUtils.isBlank(studentinfo.getId())||StringUtils.isBlank(studentinfo.getNewApplyDate())||StringUtils.isBlank(studentinfo.getFilePath())) {
//            return Result.error("必要参数缺失");
//        }
//        return studentApplydateService.saveAndSendApplyDateEdit(studentinfo.getId(),studentinfo.getNewApplyDate(),studentinfo.getFilePath());
//        //return Result.ok("编辑成功");
//    }
//
//    /**
//     * 跨地市转校学员编辑冻结金额
//     */
//    @PostMapping("/moneyEdit")
//    public Result<?> moneyEdit(@RequestBody Studentinfo studentinfo) {
//        if (StringUtils.isBlank(studentinfo.getId()) ||
//            StringUtils.isBlank(studentinfo.getBasicCost()) ||
//            StringUtils.isBlank(studentinfo.getSecondCost()) ||
//            StringUtils.isBlank(studentinfo.getThirdCoast())) {
//            return Result.error("必要参数缺失");
//        }
//        Studentinfo byId = studentinfoService.getById(studentinfo.getId());
//        if (Objects.isNull(byId)) {
//            return Result.error("不存在该学员！");
//        }
//        byId.setBasicCost(studentinfo.getBasicCost());
//        byId.setSecondCost(studentinfo.getSecondCost());
//        byId.setThirdCoast(studentinfo.getThirdCoast());
//        studentinfoService.updateById(byId);
//        return Result.ok("编辑成功！");
//    }

    /**
     * 资金上下限判断
     */
    private Result<String> isInLimit(Studentinfo studentinfo) {
//        List<CostLimit> limitList = costLimitService.list(
//                Wrappers.<CostLimit>lambdaQuery()
//                        .eq(CostLimit::getInscode, studentinfo.getInscode())
//                        .eq(CostLimit::getTraintype, studentinfo.getTraintype())
//        );
//        if (limitList == null || limitList.size() < 1) {
//            return Result.error(String.format("请先添加该驾校该车型资金上下限！"));
//        }
//        CostLimit limit = limitList.get(0);
//        BigDecimal basicCost = new BigDecimal(studentinfo.getBasicCost());
//        BigDecimal secondCost = new BigDecimal(studentinfo.getSecondCost());
//        BigDecimal thirdCost = new BigDecimal(studentinfo.getThirdCoast());
//        int compareBaseDown = basicCost.compareTo(limit.getBasicDownlimit());
//        int compareBaseUp = basicCost.compareTo(limit.getBasicUplimit());
//        int compareSecDown = secondCost.compareTo(limit.getSecondDownlimit());
//        int compareSecUp = secondCost.compareTo(limit.getSecondUplimit());
//        int compareThiDown = thirdCost.compareTo(limit.getThirdDownlimit());
//        int compareThiUp = thirdCost.compareTo(limit.getThirdUplimit());
//        if (compareBaseUp == 1 || compareBaseDown == -1) {
//            return Result.error(String.format("基础费用不在上下限中，请重新填写!%s~%s", limit.getBasicDownlimit().toString(), limit.getBasicUplimit().toString()));
//        }
//        if (compareSecUp == 1 || compareSecDown == -1) {
//            return Result.error(String.format("第二部分费用不在上下限中，请重新填写!%s~%s", limit.getSecondDownlimit().toString(), limit.getSecondUplimit().toString()));
//        }
//        if (compareThiUp == 1 || compareThiDown == -1) {
//            return Result.error(String.format("第三部分费用不在上下限中，请重新填写!%s~%s", limit.getThirdDownlimit().toString(), limit.getThirdUplimit().toString()));
//        }
        return Result.ok();
    }


    /**
     * 批量删除
     *
     * @param ids 学员id数组
     * @param did 学员删除类型(0:普通删除,1:退学,2:注销)
     * @return
     */
    @AutoLog(value = "学员信息-批量0-删除/1-退学/2-注销", operateType = CommonConstant.OPERATE_TYPE_4)
    @ApiOperation(value = "学员信息-批量0-删除/1-退学/2-注销", notes = "学员信息-批量0-删除/1-退学/2-注销")
    @RequiresPermissions(value = {"gzpt:studentinfo:remove", "gzpt:studentinfo:drop", "gzpt:studentinfo:logOff"}, logical = Logical.OR)
    @PostMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids, @RequestParam(name = "did", required = true) Long did) {
        String[] strArrNum = ids.split(",");
        String result = this.studentinfoService.deleteStudentinfoByIds(strArrNum, did,null);
        if ("ok".equals(result)) {
            return Result.OK((did == 0 ? "删除" : did == 1 ? "退学" : did == 2 ? "注销" : "无操作") + "成功!");
        }
        return Result.error((did == 0 ? "删除" : did == 1 ? "退学" : did == 2 ? "注销" : "无操作") + "失败!" + result);
    }

    /**
     * 驾校删除学员功能
     *
     * @return
     */
    @AutoLog(value = "驾校删除学员功能", operateType = CommonConstant.OPERATE_TYPE_4)
    @ApiOperation(value = "驾校删除学员功能", notes = "驾校删除学员功能")
    @RequiresPermissions(value = {"gzpt:studentinfo:delete"}, logical = Logical.OR)
    @PostMapping(value = "/deleteOne")
    public Result<String> deleteOne(@RequestBody Map param) {
        String id = param.get("id").toString();
        Studentinfo studentinfo = studentinfoService.getById(id);
        if (Objects.isNull(studentinfo)) {
            return Result.error("非法操作");
        }
        String deptOrgCode = JkSecurityUtils.getDeptOrgCode();
        if (!deptOrgCode.startsWith("3308") && !studentinfo.getInscode().equals(deptOrgCode)) {
            return Result.error("非法操作");
        }
        if (studentinfo.getIssigncontract() == 1) {
            return Result.error("已签订合同，不允许删除");
        }
        iStudentContractService.deleteStudentContractByStunum(studentinfo.getStunum());
        studentinfoService.removeById(id);
        return Result.OK("删除成功");
    }

    /**
     * 通过id查询
     * 获取学员信息详细信息
     *
     * @param id
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:query")
    @ApiOperation(value = "获取学员信息详细信息-通过id查询", notes = "获取学员信息详细信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        Studentinfo studentinfo = studentinfoService.getById(id);
        if (studentinfo == null) {
            return Result.error("未找到对应数据");
        }

        JkySysDepart sysDept = sysDeptService.selectDeptByleader(studentinfo.getInscode());
        studentinfo.setInscode(sysDept.getId());
        String photopath = studentinfo.getPhotopath();
        if (StringUtils.isNotBlank(photopath) && photopath.contains(OssZWYUtils.UPLOAD_PATH)) {
            studentinfo.setPhotopath(ossZWYUtils.getPhotoUrl(photopath));
        }
        return Result.OK(studentinfo);
    }

    @PostMapping("/chackStudent")
    public Result chackStudent(@RequestBody Studentinfo studentinfo){
        if (Objects.isNull(studentinfo.getIdcard())){
            return  Result.error("学员身份证不能为空");
        }
        if (Objects.isNull(studentinfo.getTraintype())){
            return Result.error("学员车型不能未空");
        }
        Studentinfo one = studentinfoService.getOne(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getIdcard, studentinfo.getIdcard()).eq(Studentinfo::getTraintype, studentinfo.getTraintype()));
        if (Objects.isNull(one)){
            return Result.error("该学员不存在");
        }
        return Result.OK(one);

    }


    //    @PostMapping(value = "/applylogout")
    public Result<String> applyLogout(@RequestBody Map<String,String> param) {

        Studentinfo studentByStunum = studentinfoService.getById(param.get("id"));
        if (Objects.isNull(studentByStunum)){
            return Result.error("该学员不存在");
        }
//        String result = studentinfoService.applyLogout(studentByStunum.getStunum(), param.get("reason"),studentByStunum.getPlatform());
        return Result.ok();
    }

    /**
     * 导出excel
     */
    @AutoLog(value = "导出学员信息", operateType = CommonConstant.OPERATE_TYPE_6)
    @RequiresPermissions("gzpt:studentinfo:export")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, Studentinfo studentinfo) {
        String key = "lock:stuInfo_export";
        if (!redissonLock.tryLock(key, 60)) {
            throw new JkyCdtpublicException("系统当前已有导出任务，请稍后再试");
        }
        try {
            List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfo.getInscode(), studentinfo.getInscodes());
            studentinfo.setInscode(null);
            QueryWrapper<Studentinfo> queryWrapper = QueryGenerator.initQueryWrapper(studentinfo, request.getParameterMap());
            queryWrapper.setEntityClass(Studentinfo.class);
            //角色权限
            queryWrapper.in("inscode", inscode);

            queryWrapper.inSql(StringUtils.isNotBlank(studentinfo.getCoachname()),
                    "coachnum",
                    "select coachnum from t_m_coach where name = '" + studentinfo.getCoachname() + "' ");

            queryWrapper.orderByDesc("applydate");
            queryWrapper.orderByDesc("create_time");
            if (Objects.nonNull(studentinfo.getApplydateBegin())) {
                queryWrapper.ge("applydate", studentinfo.getApplydateBegin().replace("-", ""));
            }
            if (Objects.nonNull(studentinfo.getApplydateEnd())) {
                queryWrapper.le("applydate", studentinfo.getApplydateEnd().replace("-", ""));
            }

            String selections = request.getParameter("selections");
            if (com.jky.boot.common.utils.StringUtils.isNotBlank(selections)) {
                List<String> arr = Arrays.asList(selections.split(","));
                queryWrapper.in("id", arr);
            }
            List<Studentinfo> list = studentinfoService.list(queryWrapper);

            if (list.size() > 2000) {
                throw new JkyCdtpublicException("导出条数超过2000,请筛选时间段进行导出");
            }
            // 脱敏
            JkyDesensitizedUtils.desensitizeObj(list);
            ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
            mv.addObject(NormalExcelConstants.FILE_NAME, "学员信息");
            mv.addObject(NormalExcelConstants.CLASS, Studentinfo.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学员信息", "导出人:" + JkSecurityUtils.getRealname(), "学员信息"));
            mv.addObject(NormalExcelConstants.DATA_LIST, list);
            return mv;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            redissonLock.unlock(key);
        }
    }


    /**
     * 导出选中学员信息列表
     *
     * @param request
     * @param studentinfo
     */
    @AutoLog(value = "导出选中学员信息", operateType = CommonConstant.OPERATE_TYPE_6)
    @RequiresPermissions("gzpt:studentinfo:export")
//	 @PermissionData(pageComponent="base/BaseCompInfoList")
    @RequestMapping(value = "/exportByIds")
    public ModelAndView exportByIds(HttpServletRequest request, Studentinfo studentinfo) throws IllegalAccessException {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfo.getInscode(), studentinfo.getInscodes());
        studentinfo.setInscode(null);
        QueryWrapper<Studentinfo> queryWrapper = QueryGenerator.initQueryWrapper(studentinfo, request.getParameterMap());
        //角色权限
        queryWrapper.in("inscode", inscode);
        queryWrapper.setEntityClass(Studentinfo.class);
        // 过滤选中数据
        List<Studentinfo> list = null;
        String selections = request.getParameter("selections");
        if (StringUtils.isNotBlank(selections)) {
            queryWrapper.in("id", Arrays.asList(selections.split(",")));
            list = studentinfoService.list(queryWrapper);
        }

        // 脱敏
        DesensitizedUtils.desensitization(list);

        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "学员信息");
        mv.addObject(NormalExcelConstants.CLASS, Studentinfo.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学员信息", "导出人:" + JkSecurityUtils.getRealname(), "学员信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }


    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:import")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, Studentinfo.class);
    }


    /**
     * 学员签订合同
     */
    @RequiresPermissions("gzpt:studentinfo:sign")
    @AutoLog(value = "学员合同-签订")
    @GetMapping("/contract/{id}")
    @RedissonLock(key = "#id", keyPrefix = "stu:contract:", waitTime = 2, lockTime = 10)
    public Result<?> contract(@PathVariable("id") String id) {
        String url = studentinfoService.signStudentContractById(id);
        if (Objects.isNull(url)) {
            return Result.error("学员签订合同失败");
        }
        return Result.ok("学员签订合同成功");
    }

    /**
     * 查看学员合同
     */
    @RequiresPermissions("gzpt:studentinfo:viewContract")
    @ApiOperation(value = "学员合同-查看", notes = "学员合同-查看")
    @GetMapping("/viewContract/{id}")
    public Result<?> viewContract(@PathVariable("id") String id) {
        return Result.OK(studentinfoService.viewStudentContractByStunum(id));
    }


    /**
     * 科目二资金划转
     * @param id
     * @return
     */
    @PostMapping("/passSubjectTwo/{id}")
    @RedissonLock(key = "#studentinfo.id", keyPrefix = "passSubjectTwo:", waitTime = 1, lockTime = 5)
    public Result<?> passSubjectTwo(@PathVariable("id") String id) {

        return this.studentinfoService.passSubjectTwo(id);
    }


    /**
     * 推送学员编号到银行
     */
    @PostMapping("/PushStuToBank")
    @RedissonLock(key = "#studentinfo.id", keyPrefix = "pushStuToBank:", waitTime = 1, lockTime = 5)
    public Result<?> PushStuToBank(@RequestBody Studentinfo studentinfo) {
        // 校验
        Studentinfo student = studentinfoService.selectStudentinfoById(studentinfo.getId());
        if (!student.getTraintype().equals("C1") && !student.getTraintype().equals("C2")){
            return Result.OK("大车无需推送银行，可直接推送计时");
        }
        if (StringUtils.isEmpty(student.getStunum())) {
            return Result.error("该学员无全国统一编号");
        }
        if (student.getIssigncontract() != 1) {
            return Result.error("学员未签约！请联系学员到衢学车签订合同！");
        }
        if (student.getIstobank() == 1) {
            if (student.getBankcode().equals(studentinfo.getBankcode())) {
                return Result.error("请勿重复推送");
            } else {
                bankRegService.deleteBankRegByStunum(student.getStunum());
            }
        }
        try {
            student.setBankcode(studentinfo.getBankcode());
            student.setBankname(studentinfo.getBankname());
            // 支付宝支付的人，更改学员信息里的银行信息即可
            if (studentinfo.getBankcode ().equals("alipay")) {
                student.setIstobank(1);
                studentinfoService.updateById(student);
                return Result.ok("推送成功");
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("message", "执行成功");
            // 建设银行不需要发指令
            if (!"9IC3C55B".equals(student.getBankcode())) {
                String data = PushBankUtil.PushStuToBank("1", student);
                jsonObject = JSON.parseObject(data);
                if (jsonObject.get("errorcode") == null || Integer.parseInt(jsonObject.getString("errorcode")) != 0) {
                    return Result.error(jsonObject.getString("message"));
                }
            }
            // 保存 reg
            BankReg bankReg = new BankReg();
            bankReg.setBalanceAmount(StringUtils.isBlank(student.getFrozenAmount())?null:new BigDecimal(student.getFrozenAmount()));
            bankReg.setStunum(student.getStunum());
            bankReg.setStuname(student.getName());
            bankReg.setIdcard(student.getIdcard());
            bankReg.setInscode(student.getInscode());
            bankReg.setInsname(student.getInsname());
            bankReg.setStatus(0);
            bankReg.setBankcode(studentinfo.getBankcode());
            bankReg.setBankname(studentinfo.getBankname());
            iBankRegService.save(bankReg);
            student.setIstobank(1);
            studentinfoService.updateById(student);
            return Result.ok(jsonObject.getString("message"));
        } catch (Exception e) {
            log.error("学员推送银行失败",e);
            return Result.error("学员推送银行失败");
        }
    }

    /**
     * 推送到全国平台
     *
     * @param id
     * @return
     * @throws Exception
     */
    @RequiresPermissions("gzpt:studentinfo:pushQg")
    @AutoLog(value = "推送到全国平台")
    @ApiOperation(value = "推送到全国平台", notes = "推送到全国平台")
    @GetMapping("/pushQg/{id}")
    public Result<?> pushQg(@PathVariable("id") String id) throws Exception {
        Studentinfo studentinfo = studentinfoService.selectStudentinfoById(id);
        if (StringUtils.isNotEmpty(studentinfo.getStunum())) {
            return Result.error("学员已经获取过统一编号!");
        }
        CommonResponse response = NationwideUtil.pushStudent(studentinfo);
        if (response == null) {
            return Result.error("图片上传失败！");
        }
        if (response.getErrorcode() != 0) {
            return Result.error(response.getMessage());
        }
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(response.getData()));
        String stunum = jsonObject.get("stunum").toString();
        studentinfo.setStunum(stunum);
        studentinfo.setIstoqg(1);
        studentinfo.setPhoto(Long.valueOf(response.getResult()));

        // 推送学员数据到app
        com.jky.boot.core.module.gzpt.vo.Result resp = null;
        try {
            if (StringUtils.isNotEmpty(studentinfo.getCoachnum())) {
                Coach coach = iCoachService.selectCoachByCoachnum(studentinfo.getCoachnum());
                studentinfo.setCoachname(coach.getName());
            }
            resp = PushAppUtil.pushStuToApp(studentinfo);
        } catch (Exception e) {
            log.error("错误:", e);
        }

        log.info("resp:{}", JSON.toJSONString(resp));
        if (resp != null) {
            JSONObject o = JSONObject.parseObject(resp.getResult());
            if (resp.getCode() == "200") {
                studentinfo.setIstoapp(1);
            } else {
                if (o != null && "同步学员重复".equals(o.get("msg"))) {
                    studentinfo.setIstoapp(1);
                } else {
                    studentinfo.setIstoapp(2);
                }

            }
        }

        studentinfoService.updateById(studentinfo);

        // 预备 zlb 学员合同
        if (studentinfo.getBaseType() == 1) {
            StudentContract byId = iStudentContractService.getById(studentinfo.getStunum());
            if (Objects.isNull(byId)) {
                ContractEvent event = new ContractEvent(this, studentinfo);
                applicationEventPublisher.publishEvent(event);
            }
        }
        return Result.OK(response.getData());
    }

    /**
     * 推送到计时平台
     *
     * @param id
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:pushJs")
    @AutoLog(value = "推送到计时平台")
    @ApiOperation(value = "推送到计时平台", notes = "推送到计时平台")
    @GetMapping("/pushJs/{id}")
    public Result<?> pushJs(@PathVariable("id") String id) throws Exception {
//        try {
        Studentinfo studentinfo = studentinfoService.selectStudentinfoById(id);
        if (studentinfo.getStunum() == null) {
            return Result.error("学员未获取统一编号！");
        }
        StudentContract contract = iStudentContractService.getOneByStunum(studentinfo.getStunum());
        if (contract == null && !"1".equals(studentinfo.getIsold())) {
            return Result.error("学员未签订合同!");
        }
        if (studentinfo.getBaseType() == 1 && (Objects.isNull(contract) || contract.getIsSign() == 0)) {
            return Result.error("学员未签订合同!");
        }
        BankReg bankReg = new BankReg();
        bankReg.setStunum(studentinfo.getStunum());
        bankReg.setStatus(3);
        List<BankReg> bankRegList = iBankRegService.selectBankRegList(bankReg);
        String str = "A1,A2,A3,B1,B2,B3,C5,C6";
        // 没有冻结记录,不是多伦,不是跨地市,不是老学员,车型不属于str,不是军官证 满足以上不能推送
        if ((bankRegList == null || bankRegList.isEmpty())
                && !"159258B1C2AA".equals(studentinfo.getPlatform())
//                && !"1".equals(studentinfo.getIscrosscity())
                && !"1".equals(studentinfo.getIsold())
                && !str.contains(studentinfo.getTraintype())
                && !"3".equals(studentinfo.getCardtype())
                && !(2 == studentinfo.getStutype())) {
//                return Result.error("学员冻结金额异常,不能推送!");
            return Result.error("请确认学员账户冻结情况！未冻结成功前，不允许推送计时");
        }
        String platformStr;
        if (studentinfo.getPlatform() == null) {
            platformStr = "";
        } else {
            platformStr = studentinfo.getPlatform();
        }
        Platform platform = iPlatformService.getByPlatformSerialNumber(platformStr);
        if (platform == null) {
            return Result.error("所属驾校未选择计时厂商!");
        }
        if (contract != null && contract.getPdfurl() != null && StringUtils.isNotEmpty(contract.getPdfurl())) {
//                studentinfo.getParams().put("contracturl", contract.getPdfurl());
            studentinfo.setContracturl(ossZWYUtils.getPhotoUrl(contract.getPdfurl()));
        }
//            studentinfo.getParams().put("totalamount", studentinfo.getFrozenAmount());
//            studentinfo.getParams().put("unitprice", totalprice / 3);
        studentinfo.setTotalamount(studentinfo.getFrozenAmount());
//        studentinfo.setUnitPrice(String.valueOf(totalprice / 3));
        if (Objects.nonNull(studentinfo.getFstdrilicdate())) {
            String fstdrilicdate = studentinfo.getFstdrilicdate();
            studentinfo.setFstdrilicdate(fstdrilicdate.replace("-", ""));
        }
        String photo = studentinfo.getPhotopath();
        studentinfo.setPhotopath(StringUtils.isNotBlank(photo) ? ossZWYUtils.getPhotoUrl(photo) : "");


        String driveApplyUrl="";
        // 根据身份证号和车型查询在线报名表
        StudentinfoEnter studentinfoEnter = studentinfoEnterService.getOne(
                Wrappers.lambdaQuery(StudentinfoEnter.class)
                        .eq(StudentinfoEnter::getIdcard, studentinfo.getIdcard())
                        .eq(StudentinfoEnter::getTraintype, studentinfo.getTraintype())
                        .orderByDesc(StudentinfoEnter::getCreateTime)
        );

        if (studentinfoEnter != null && StringUtils.isNotBlank(studentinfoEnter.getFileUrl())) {
            driveApplyUrl =ossZWYUtils.getPhotoUrl(studentinfoEnter.getFileUrl());
        }

        CommonResponse response = PushJsUtil.pushstutojs(studentinfo, driveApplyUrl,platform.getApi() + "gzstudent");
        JSONObject jsonObject = JSON.parseObject(response.getResult());
        //东阳市南山交通技术培训中心有限公司 0644654002915522  浙江交通技师学院 7642554452662315
//        if ((studentinfo.getInscode().equals("0644654002915522") || studentinfo.getInscode().equals("7642554452662315")) && "0".equals(jsonObject.get("errorcode").toString())) {
//            //157C1C2BAF4 星唯  159258B1C2AA 多伦
//            if (platformStr.equals("157C1C2BAF4")) {
//                platformStr = "159258B1C2AA";
//            } else if (platformStr.equals("159258B1C2AA")) {
//                platformStr = "157C1C2BAF4";
//            }
//            platform = iPlatformService.getByPlatformSerialNumber(platformStr);
//            response = PushJsUtil.pushstutojs(studentinfo, platform.getApi() + "gzstudent");
//            jsonObject = JSON.parseObject(response.getResult());
//        }
        if (jsonObject != null && "0".equals(jsonObject.getString("errorcode"))) {
            studentinfo.setIstojs(1);
            studentinfoService.update(
                    Wrappers.<Studentinfo>lambdaUpdate()
                            .set(Studentinfo::getIstojs, 1)
                            .eq(Studentinfo::getId, studentinfo.getId())
            );
            return Result.OK(JSONObject.parseObject(response.getResult()));
        } else {
            return Result.error(PlatformUtils.errMsg(studentinfo.getPlatform(), response.getMessage()));
        }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return Result.error("出现异常了:" + e);
//        }
    }


    @RequiresPermissions("gzpt:studentinfo:pushJs")
    @AutoLog(value = "批量推送到计时平台")
    @ApiOperation(value = "批量推送到计时平台", notes = "批量推送到计时平台")
    @GetMapping("/batchPushJs/{id}")
    public Result<?> batchPushJs(@PathVariable("id") String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            return Result.error("id不能为空");
        }
        String[] ids = id.split(",");
        List<String> errorPushList = new ArrayList<>();
        List<String> successPushList = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            Studentinfo studentinfo = studentinfoService.selectStudentinfoById(ids[i]);
            if (studentinfo.getStunum() == null) {
                errorPushList.add(studentinfo.getName() + "学员未获取统一编号");
                continue;
            }
            StudentContract contract = iStudentContractService.getOneByStunum(studentinfo.getStunum());

            if (contract == null && !"1".equals(studentinfo.getIsold())) {
                errorPushList.add(studentinfo.getName() + "学员未签订合同!");
                continue;
            }

            BankReg bankReg = new BankReg();
            bankReg.setStunum(studentinfo.getStunum());
            bankReg.setStatus(3);
            List<BankReg> bankRegList = iBankRegService.selectBankRegList(bankReg);
            String str = "A1,A2,A3,B1,B2,B3,C5,C6";
            // 没有冻结记录,不是多伦,不是跨地市,不是老学员,车型不属于str,不是军官证 满足以上不能推送
            if ((bankRegList == null || bankRegList.size() == 0) && !"159258B1C2AA".equals(studentinfo.getPlatform()) && !"1".equals(studentinfo.getIscrosscity()) && !"1".equals(studentinfo.getIsold())
                    && !str.contains(studentinfo.getTraintype()) && !"3".equals(studentinfo.getCardtype())) {
                errorPushList.add(studentinfo.getName() + "学员冻结金额异常,不能推送!");
                continue;
            }

            String platformStr;
            if (studentinfo.getPlatform() == null) {
                platformStr = "";
            } else {
                platformStr = studentinfo.getPlatform();
            }

            Platform platform = iPlatformService.getByPlatformSerialNumber(platformStr);
            if (platform == null) {
                errorPushList.add(studentinfo.getName() + "所属驾校未选择计时厂商!");
                continue;
            }
            if (contract != null && contract.getPdfurl() != null && StringUtils.isNotEmpty(contract.getPdfurl())) {
//                studentinfo.getParams().put("contracturl", contract.getPdfurl());
                studentinfo.setContracturl(ossZWYUtils.getPhotoUrl(contract.getPdfurl()));
            }
//            studentinfo.getParams().put("totalamount", studentinfo.getFrozenAmount());
//            studentinfo.getParams().put("unitprice", totalprice / 3);
            studentinfo.setTotalamount(studentinfo.getFrozenAmount());
//            studentinfo.setUnitprice(String.valueOf(totalprice / 3));


            String driveApplyUrl="";
            // 根据身份证号和车型查询在线报名表
            StudentinfoEnter studentinfoEnter = studentinfoEnterService.getOne(
                    Wrappers.lambdaQuery(StudentinfoEnter.class)
                            .eq(StudentinfoEnter::getIdcard, studentinfo.getIdcard())
                            .eq(StudentinfoEnter::getTraintype, studentinfo.getTraintype())
                            .orderByDesc(StudentinfoEnter::getCreateTime)
            );

            if (studentinfoEnter != null && StringUtils.isNotBlank(studentinfoEnter.getFileUrl())) {
                driveApplyUrl =ossZWYUtils.getPhotoUrl(studentinfoEnter.getFileUrl());
            }

            CommonResponse response = PushJsUtil.pushstutojs(studentinfo, driveApplyUrl,platform.getApi() + "gzstudent");
            JSONObject jsonObject = JSON.parseObject(response.getResult());
            if ("0".equals(jsonObject.get("errorcode").toString())) {
                studentinfo.setIstojs(1);
                studentinfoService.updateStudentinfo(studentinfo);
                successPushList.add(JSONObject.parseObject(response.getResult()).toString());
            } else {
                errorPushList.add("推送失败了!" + JSONObject.parseObject(response.getResult()).toString());
            }
        }
        if (errorPushList.isEmpty()) {
            StringBuilder successBuilder = new StringBuilder();
            for (String success : successPushList) {
                successBuilder.append(success);
            }
            return Result.OK(successBuilder.toString());
        } else {
            StringBuilder errorBuilder = new StringBuilder();
            for (String error : errorPushList) {
                errorBuilder.append(error);
            }
            return Result.error(errorBuilder.toString());
        }

    }


    @ApiOperation(value = "findByStunum", notes = "findByStunum")
    @PostMapping("/findByStunum")
    public ResultDto findByStunum(@RequestBody Map<String, Object> param) throws Exception {
        ResultDto resultDto = new ResultDto();
        if (!param.containsKey("stunum") && StringUtils.isEmpty(param.get("stunum").toString())) {
            resultDto.setCode("500");
            resultDto.setMsg("学员编号不能为空！");
            return resultDto;
        }
        String stunum = param.get("stunum").toString();
        Studentinfo studentinfo = new Studentinfo();
        studentinfo.setStunum(stunum);
        List<Studentinfo> studentinfos = studentinfoService.selectStudentinfoList(studentinfo);
        if (studentinfos.size() == 0) {
            resultDto.setCode("500");
            resultDto.setMsg("学员不存在！");
            return resultDto;
        }
        resultDto.setCode("200");
        resultDto.setMsg("存在！");
        return resultDto;
    }

    /**
     * 判断是不是东阳南山或金华交通技师
     *
     * @return
     */
    @AutoLog(value = "判断是不是东阳南山或金华交通技师")
    @ApiOperation(value = "判断是不是东阳南山或金华交通技师", notes = "判断是不是东阳南山或金华交通技师")
    @GetMapping("/isspecial")
    public Result<?> isspecial() {
        String deptId = JkSecurityUtils.getDeptId();
        if ("263".equals(deptId) || "243".equals(deptId)) {
            return Result.OK(true);
        } else {
            return Result.OK(false);
        }
    }

    /**
     * 设置黑户
     *
     * @param studentinfo
     * @return
     */
    //@RequiresPermissions("gzpt:studentinfo:black")
    //@AutoLog(value = "设置黑户")
    //@ApiOperation(value = "设置黑户", notes = "设置黑户")
    //@GetMapping("/black")
//    public Result<?> setBlack(Studentinfo studentinfo ,HttpServletRequest request) {
//        Studentinfo studentinfo2 = studentinfoService.getById(studentinfo.getId());
//        studentinfo.setStutype(2);
//        String logContent = studentinfo2.getName()+studentinfo2.getIdcard()+"设置黑户";
//        mr890chLogService.keeplog(4,logContent,request);
//        studentinfoService.setBlackStu(studentinfo);
//        return Result.OK("设置成功");
//    }

    /**
     * 设置老学员
     *
     * @param
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:setoldstudent")
    @ApiOperation(value = "设置老学员", notes = "设置老学员")
    @GetMapping("/setoldstudent/{id}")
    public Result<?> setoldstudent(@PathVariable("id") String id) {
        Studentinfo studentinfo = new Studentinfo().setIsold("1").setId(id);
        studentinfoService.updateStudentinfo(studentinfo);
        return Result.OK("设置成功");
    }

    /**
     * 查询驾校报名数量
     *
     * @param
     * @return
     */
//    @RequiresPermissions("gzpt:studentinfo:listStudentCounts")
    @ApiOperation(value = "查询驾校报名数量", notes = "查询驾校报名数量")
    @GetMapping("/listStudentCounts")
    public Result<?> listStudentCounts(StudentCountVO studentCountVO,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       HttpServletRequest req) {


        String startTime = StringUtils.isBlank(studentCountVO.getBeginTime()) ? "10000101" : studentCountVO.getBeginTime().replace("-", "");
        String endTime = StringUtils.isBlank(studentCountVO.getEndTime()) ? "30000101" : studentCountVO.getEndTime().replace("-", "");
        log.info("startTime:{}", startTime);
        List<StudentCountVO> list = studentinfoService.selectStudentCounts(startTime, endTime, studentCountVO.getInscode());
        return Result.OK(list);
    }

    /**
     * 导出驾校报名数量
     *
     * @param studentCountVO
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:exportStudentCounts")
    @ApiOperation(value = "导出驾校报名数量", notes = "导出驾校报名数量")
    @GetMapping("/exportReport")
    public ModelAndView exportStudentCounts(StudentCountVO studentCountVO) {

        String startTime = studentCountVO.getBeginTime() == null ? "10000101" : studentCountVO.getBeginTime().replace("-", "");
        String endTime = studentCountVO.getEndTime() == null ? "30000101" : studentCountVO.getEndTime().replace("-", "");
        List<StudentCountVO> list = studentinfoService.selectStudentCounts(startTime, endTime, studentCountVO.getInscode());

        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "驾校报名数量");
        mv.addObject(NormalExcelConstants.CLASS, StudentCountVO.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("驾校报名数量", "导出人:" + JkSecurityUtils.getRealname(), "驾校报名数量"));
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }

    /**
     * 学员信息强制删除
     */
    @RequiresPermissions("gzpt:studentinfo:remove")
    @AutoLog(value = "学员信息强制删除", operateType = CommonConstant.OPERATE_TYPE_4)
    @ApiOperation(value = "学员信息强制删除", notes = "学员信息强制删除")
    @PostMapping("/{id}")
    public Result<?> remove(@PathVariable("id") String id) {
        Studentinfo studentinfo = studentinfoService.selectStudentinfoById(id);
        // 如果没有学员编号就直接根据id删除
        if (StringUtils.isEmpty(studentinfo.getStunum())) {
            studentinfoService.deleteStudentinfoById(id);
            return Result.OK("该学员已强制删除!");
        }
        // 强制删除也要在开户冻结之后才可以
        BankReg bankReg = new BankReg();
        bankReg.setStunum(studentinfo.getStunum());
        bankReg.setStatus(3);
        List<BankReg> bankRegs = iBankRegService.selectBankRegList(bankReg);
        // 开户冻结成功后才可以删除学员
        if (bankRegs.size() > 0) {
            return Result.error("学员已经与银行签约并冻结,无法删除!");
        }

        if (StringUtils.isNotEmpty(studentinfo.getStunum())) {
            // 删除开户信息
            int rows = iBankRegService.deleteBankRegByStunum(studentinfo.getStunum());
            // 删除合同信息
            iStudentContractService.deleteStudentContractByStunum(studentinfo.getStunum());
            // 如果推送过银行就把删除的学员进行备份
            if (rows > 0) {
                StudentinfoLogout studentinfoLogout = new StudentinfoLogout();
                BeanUtils.copyProperties(studentinfoLogout, studentinfo);
                iStudentinfoLogoutService.insertStudentinfoLogout(studentinfoLogout);
            }
        }
        studentinfoService.deleteStudentinfoById(id);

        return Result.OK("该学员已强制删除!");
    }

    /**
     * 推送学员编号到银行（强制）
     */
    @RequiresPermissions("gzpt:studentinfo:PushStuToBankForce")
    @AutoLog(value = "推送学员编号到银行")
    @ApiOperation(value = "推送学员编号到银行", notes = "推送学员编号到银行")
    @PostMapping("/PushStuToBankForce")
    public Result<?> PushStuToBankForce(@RequestBody Studentinfo studentinfo) {
        try {
            Studentinfo student = studentinfoService.selectStudentinfoById(studentinfo.getId());
            List<BankReg> bankRegList = iBankRegService.selectBankRegList(new BankReg(student.getStunum(), 3));

            if (CityUtil.cityCoode.equals("3304")) {
                InstitutionBinding institutionBinding = institutionBindingService.selectInstitutionBindingByInscode(student.getInscode());
                student.setBankcode(institutionBinding.getBankcode());
                student.setBankname(institutionBinding.getBankname());
            } else if (CityUtil.cityCoode.equals("3307")) {
                student.setBankcode(studentinfo.getBankcode());
                student.setBankname(studentinfo.getBankname());
            }


            if ("159258B1C2AA".equals(student.getPlatform())) {
                return Result.error("多伦的学员不能推送银行");
            }
            if (StringUtils.isEmpty(student.getStunum())) {
                return Result.error("该学员无全国统一编号");
            }
            if (!new Integer(1).equals(student.getIssigncontract())) {
                return Result.error("请先签订合同");
            }
            if (bankRegList.size() > 0) {
                return Result.error("该学员已推送银行且已经冻结成功,不能强制推送!");
            }
            if ("1".equals(student.getIscrosscity())) {
                return Result.error("该学员是跨地市转校学员,不能推送银行!");
            }
            // 强制推送银行时 同时更新开户表的银行编码和银行名称
            BankReg bankReg = new BankReg();
            bankReg.setStunum(student.getStunum());
            bankReg.setBankcode(studentinfo.getBankcode());
            bankReg.setBankname(studentinfo.getBankname());
            iBankRegService.updateBankRegByStunum(bankReg);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("message", "执行成功");
            // 建设银行不需要发指令
            if (!"9IC3C55B".equals(studentinfo.getBankcode())) {
                String data = PushBankUtil.PushStuToBank("1", student);
                jsonObject = JSON.parseObject(data);
                if (Integer.parseInt(jsonObject.get("errorcode").toString()) != 0) {
                    return Result.error(jsonObject.get("message").toString());
                }
            }

            student.setIstobank(1);
            studentinfoService.updateStudentinfo(student);

            return Result.OK(jsonObject.get("message").toString());
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @ApiOperation(value = "isSchool", notes = "isSchool")
    @GetMapping("/isSchool")
    public Result<?> isSchool() {
        boolean isSchool = JkSecurityUtils.isSchoolOperator();
        // 判断是不是驾校, 并把布尔值放入params
        return Result.OK(isSchool);
    }


    /**
     * 新增学员信息
     */
    @AutoLog(value = "新增学员信息", operateType = CommonConstant.OPERATE_TYPE_2)
    @PostMapping("/addold")
    public ResultDto addold(@RequestBody Studentinfo studentinfo) {
        log.info("addold,studentinfo:{}", studentinfo);
        // 根据身份证号和车型限制学员不能重复报名
        Studentinfo student = new Studentinfo();
        student.setIdcard(studentinfo.getIdcard());
        List<Studentinfo> studentinfoList = studentinfoService.selectStudentinfoList(student);
        ResultDto resultDto = new ResultDto();
        if (studentinfoList != null && !studentinfoList.isEmpty()) {
            resultDto.setCode("500");
            resultDto.setMsg("学员已存在");
            return resultDto;
        }
        // 厂商按公众上面来
        Institution ins = iInstitutionService.getByInscode(studentinfo.getInscode());
        if (Objects.isNull(ins)) {
            resultDto.setCode("500");
            resultDto.setMsg("培训机构未同步！");
            return resultDto;
        }
        String platform = ins.getPlatform();
        studentinfo.setPlatform(platform);

        //新增学员初始化状态
        studentinfo.setIssigncontract(1);
        studentinfo.setIstoqg(1);
        studentinfo.setIstojs(1);
        studentinfo.setIstobank(1);
        studentinfo.setStutype(1);
        studentinfo.setIssignature(1);
        studentinfo.setIstoapp(0);
        studentinfo.setIsold("1");
        studentinfo.setFrozenAmount("0");
        studentinfo.setInsname(ins.getName());
        if ("1".equals(studentinfo.getCardtype())) {
            studentinfo.setBirthday(studentinfo.getIdcard().substring(6, 14));
            studentinfo.setBirthplace(studentinfo.getIdcard().substring(0, 6));
        }

        log.info("studentinfo:{}", studentinfo);

        if (studentinfoService.save(studentinfo)) {
            resultDto.setCode("200");
            resultDto.setMsg("新增成功");
        } else {
            resultDto.setCode("500");
            resultDto.setMsg("新增学员信息失败");
        }
        return resultDto;
    }

    /**
     * 嘉兴学员签名
     */
    @AutoLog(value = "学员签名")
    @ApiOperation(value = "学员签名", notes = "学员签名")
    @PostMapping("/stusign")
    public Result<?> stusign(@RequestBody Map<String, String> data) {
        String stunum = data.get("stunum");
        Studentinfo student = studentinfoService.getOne(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getStunum, stunum));
        if (Objects.isNull(student)) {
            return Result.error("签名失败,请先推送全国,获取学员统一编号");
        }
        String url = data.get("url");
        student.setSignpath(OssZWYUtils.subStr(url));
        student.setIssignature(1);
        studentinfoService.updateById(student);
        return Result.ok("签名成功");
    }


    /**
     * 指定身份证号和培训车型同步学员
     *
     * @param studentinfo
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:manualsync")
    @AutoLog(value = "指定身份证号和培训车型同步学员")
    @ApiOperation(value = "指定身份证号和培训车型同步学员", notes = "指定身份证号和培训车型同步学员")
    @PostMapping("/manualSync")
    public Result<?> manualSync(@RequestBody Studentinfo studentinfo) {
        String key = "manualSync-" + studentinfo.getIdcard();
        if (redissonLock.existKey(key)) {
            return Result.error("学员同步中，请稍后再试！！");
        }
        try {
            if (redissonLock.tryLock(key, -1, 20000)) {
                if (StringUtils.isEmpty(studentinfo.getIdcard()) || StringUtils.isEmpty(studentinfo.getTraintype())) {
                    return Result.error("请求参数不能为空");
                }
                String idcard = studentinfo.getIdcard();
                String traintype = studentinfo.getTraintype();
                log.info("----------------准备向监管平台接口发送请求------------------");
                log.info("----------------准备向监管平台接口发送请求------------------");
                log.info("----------------准备向监管平台接口发送请求------------------");
                CommonResponse studentRes = KeepHttpUtilQG.sendHttpClientGetSJ(UrlAddressUtils.queryStudentInfoBy(idcard, traintype));
                log.info("-------------------接口响应的结果对象是:{}------------------------", studentRes);
                if (studentRes.getErrorcode() == 0) {
                    log.error("-------------------errorcode:{}------------------------", studentRes.getErrorcode());
                    log.warn("-------------------data:{}------------------------", studentRes.getData());
                    // 执行成功，获取data
                    Studentinfo studentBean = JSONObject.parseObject(studentRes.getData().toString(), Studentinfo.class);
                    // 只能同步本驾校的
                    if (!JkSecurityUtils.isAdmin()) {
                        boolean isSchool = JkSecurityUtils.isSchoolOperator();
                        if (isSchool) {
                            String inscode = JkSecurityUtils.getDeptOrgCode();
                            if (!inscode.equals(studentBean.getInscode())) {
                                return Result.error("只能同步本驾校的学员");
                            }
                        }
                    }
                    log.info("---------------------data转换为Bean:{}--------------------------------", studentBean.toString());
                    if (Objects.nonNull(studentBean.getApplydate()) && Long.parseLong(studentBean.getApplydate()) >= 20231201) {
                        return Result.error("同步失败,只能同步老学员");
                    }
                    Studentinfo gzStudent = studentinfoService.selectByIdcardAndTraintype(studentBean.getIdcard(), studentBean.getTraintype());
                    if (gzStudent != null) {
                        return Result.error("学员已同步，无需重复同步");
                    }
                    // 开始同步
                    Studentinfo student = new Studentinfo();
                    student.setIdcard(studentBean.getIdcard());
                    List<Studentinfo> studentinfoList = studentinfoService.selectStudentinfoList(student);
                    if (studentinfoList != null && !studentinfoList.isEmpty()) {
                        return Result.OK();
                    }
                    // 厂商按公众上面来
                    String platform = iInstitutionService.getByInscode(studentBean.getInscode()).getPlatform();
                    studentBean.setPlatform(platform);
                    //新增学员初始化状态
                    studentBean.setIssigncontract(0);
                    studentBean.setIstoqg(0);
                    studentBean.setIstojs(0);
                    studentBean.setIstobank(0);
                    studentBean.setStutype(1);
                    studentBean.setIssignature(0);
                    studentBean.setIstoapp(0);
                    studentBean.setIsold("1");
                    studentBean.setFrozenAmount("0");
//                    studentBean.setThirdCoast("0");
//                    studentBean.setSecondCost("0");
//                    studentBean.setBasicCost("0");
//                    if (StringUtils.isNotBlank(studentBean.getPhotopath())) {
//                        String download = FileUploadUtils.download(studentBean.getPhotopath());
//                        studentBean.setPhotopath(download);
//                    }
                    boolean save = studentinfoService.save(studentBean);
                    if (save) {
                        return Result.OK("同步成功");
                    } else {
                        return Result.error("同步失败");
                    }
                }
                return Result.error(studentRes.getMessage());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("学员同步失败");
        } finally {
            redissonLock.unlock(key);
        }
        return Result.OK();

    }

    @RequiresPermissions("gzpt:studentinfo:manualsync11111")
    @AutoLog(value = "指定身份证号和培训车型同步学员")
    @ApiOperation(value = "指定身份证号和培训车型同步学员", notes = "指定身份证号和培训车型同步学员")
    @PostMapping("/manualSync1111")
    public Result<?> manualSync1111(@RequestBody Studentinfo studentinfo) {
        String key = "manualSync-" + studentinfo.getIdcard();
        if (redissonLock.existKey(key)) {
            return Result.error("学员同步中，请稍后再试！！");
        }
        try {
            if (redissonLock.tryLock(key, -1, 20000)) {
                if (StringUtils.isEmpty(studentinfo.getIdcard()) || StringUtils.isEmpty(studentinfo.getTraintype())) {
                    return Result.error("请求参数不能为空");
                }
                String idcard = studentinfo.getIdcard();
                String traintype = studentinfo.getTraintype();
                log.info("----------------准备向监管平台接口发送请求------------------");
                log.info("----------------准备向监管平台接口发送请求------------------");
                log.info("----------------准备向监管平台接口发送请求------------------");
                CommonResponse studentRes = KeepHttpUtilQG.sendHttpClientGetSJ(UrlAddressUtils.queryStudentInfoBy(idcard, traintype));
                log.info("-------------------接口响应的结果对象是:{}------------------------", studentRes);
                if (studentRes.getErrorcode() == 0) {
                    log.error("-------------------errorcode:{}------------------------", studentRes.getErrorcode());
                    log.warn("-------------------data:{}------------------------", studentRes.getData());
                    // 执行成功，获取data
                    Studentinfo studentBean = JSONObject.parseObject(studentRes.getData().toString(), Studentinfo.class);
                    // 只能同步本驾校的
                    if (!JkSecurityUtils.isAdmin()) {
                        boolean isSchool = JkSecurityUtils.isSchoolOperator();
                        if (isSchool) {
                            String inscode = JkSecurityUtils.getDeptOrgCode();
                            if (!inscode.equals(studentBean.getInscode())) {
                                return Result.error("只能同步本驾校的学员");
                            }
                        }
                    }
                    log.info("---------------------data转换为Bean:{}--------------------------------", studentBean.toString());
                    Studentinfo gzStudent = studentinfoService.selectByIdcardAndTraintype(studentBean.getIdcard(), studentBean.getTraintype());
                    if (gzStudent != null) {
                        return Result.error("学员已同步，无需重复同步");
                    }
                    // 开始同步
                    Studentinfo student = new Studentinfo();
                    student.setIdcard(studentBean.getIdcard());
                    List<Studentinfo> studentinfoList = studentinfoService.selectStudentinfoList(student);
                    if (studentinfoList != null && !studentinfoList.isEmpty()) {
                        return Result.OK();
                    }
                    // 厂商按公众上面来
                    String platform = iInstitutionService.getByInscode(studentBean.getInscode()).getPlatform();
                    studentBean.setPlatform(platform);
                    //新增学员初始化状态
                    studentBean.setIssigncontract(0);
                    studentBean.setIstoqg(0);
                    studentBean.setIstojs(0);
                    studentBean.setIstobank(0);
                    studentBean.setStutype(1);
                    studentBean.setIssignature(0);
                    studentBean.setIstoapp(0);
                    studentBean.setIsold("1");
                    studentBean.setFrozenAmount("0");
//                    studentBean.setThirdCoast("0");
//                    studentBean.setSecondCost("0");
//                    studentBean.setBasicCost("0");
//                    if (StringUtils.isNotBlank(studentBean.getPhotopath())) {
//                        String download = FileUploadUtils.download(studentBean.getPhotopath());
//                        studentBean.setPhotopath(download);
//                    }
                    boolean save = studentinfoService.save(studentBean);
                    if (save) {
                        return Result.OK("同步成功");
                    } else {
                        return Result.error("同步失败");
                    }
                }
                return Result.error(studentRes.getMessage());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("学员同步失败");
        } finally {
            redissonLock.unlock(key);
        }
        return Result.OK();

    }


    /*
     * /gzpt/studentinfo/classrecordDetailSync/" + id,
     * */
    @RequiresPermissions("gzpt:studentinfo:classrecordsync")
    @AutoLog(value = "同步电子教学日志")
    @ApiOperation(value = "同步电子教学日志", notes = "同步电子教学日志")
    @GetMapping("/classrecordDetailSync/{id}")
    public Result<?> classRecordSync(@PathVariable String id) {
        Studentinfo studentinfo = studentinfoService.selectStudentinfoById(id);
        if (studentinfo == null) {
            return Result.error("学员暂未同步");
        }
        // 发送http
        CommonResponse classRecordRes = KeepHttpUtilQG.sendHttpClientGetSJ(
                UrlAddressUtils.queryClassRecord(studentinfo.getStunum(), studentinfo.getApplydate())
        );
//        log.info("****classRecordRes******>{}****", classRecordRes);
        if (classRecordRes.getErrorcode() == 0) {
            // 执行成功
            List<ClassRecordDetail> saveList = JSONArray.parseArray(classRecordRes.getData().toString(), ClassRecordDetail.class);
//            log.info("saveList:{}", saveList);
            Institution ins = iInstitutionService.getByInscode(studentinfo.getInscode());
            Platform platform = new Platform();
            if (ins != null) {
                platform = iPlatformService.selectBySerialNumber(ins.getPlatform());
            }
            List<ClassRecordDetail> classRecordDetailList = classRecordDetailService.list(
                    new LambdaQueryWrapper<>(ClassRecordDetail.class)
                            .eq(ClassRecordDetail::getStunum, studentinfo.getStunum())
            );
            List<String> collect = classRecordDetailList.stream().map(ClassRecordDetail::getRecnum).collect(Collectors.toList());
            for (ClassRecordDetail insert : saveList) {
                if (StringUtils.isBlank(insert.getTrainCarType())) {
                    insert.setTrainCarType(studentinfo.getTraintype());
                }

                if (Objects.nonNull(platform)) {
                    insert.setPlatnum(platform.getPlatnum());
                }
                // 未同步
                if (collect.contains(insert.getRecnum())) {
                    List<ClassRecordDetail> collect1 = classRecordDetailList.stream().filter(e -> e.getRecnum().equals(insert.getRecnum())).collect(Collectors.toList());
                    insert.setId(collect1.get(0).getId());
                    classRecordDetailService.updateById(insert);
                } else {
                    classRecordDetailService.insertClassRecordDetail(insert);
                }
            }
            return Result.OK("同步学时成功");
        }
        return Result.error(classRecordRes.getMessage());
    }

    /*
     * /gzpt/studentinfo/classrecordDetailSync/" + id,
     * */
    @RequiresPermissions("gzpt:studentinfo:classrecordsync")
    @AutoLog(value = "电子教学日志列表")
    @ApiOperation(value = "电子教学日志列表", notes = "电子教学日志列表")
    @GetMapping("/totalTimeList")
    public Result totalTimeList(@RequestParam String stunum) {

        List<TotalTime> list = totalTimeService.list(new LambdaQueryWrapper<>(TotalTime.class).eq(TotalTime::getStunum, stunum));
        Studentinfo studentinfo = studentinfoService.getOne(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getStunum, stunum));
        for (TotalTime totalTime : list) {
            String applydate = studentinfo.getApplydate().replaceAll("-", "");
            Integer subjectCreditType = CityUtil.getSubjectCreditType(applydate);
            String traintype = studentinfo.getTraintype();
            Long subject = totalTime.getSubject();
            List<TrainSubjectCredit> trainSubjectCreditList = trainSubjectCreditService.list(new LambdaQueryWrapper<TrainSubjectCredit>()
                    .eq(TrainSubjectCredit::getType, subjectCreditType)
                    .eq(TrainSubjectCredit::getTrainCarType, traintype)
                    .eq(TrainSubjectCredit::getSubject, subject));
            for (TrainSubjectCredit trainSubjectCredit : trainSubjectCreditList) {
                int classType = trainSubjectCredit.getClasstype().intValue();
                switch (classType) {
                    case 2://课堂
                        totalTime.setClassCreditration(trainSubjectCredit.getCreditration());
                        if (Objects.nonNull(totalTime.getRatedmileage()) || totalTime.getRatedmileage() != 0L)
                            totalTime.setRatedmileage(totalTime.getRatedmileage() + trainSubjectCredit.getMileageration());
                        else {
                            totalTime.setRatedmileage(trainSubjectCredit.getMileageration());
                        }
                        break;
                    case 4://远程
                        totalTime.setNetworkCreditration(trainSubjectCredit.getCreditration());
                        if (Objects.nonNull(totalTime.getRatedmileage()) || totalTime.getRatedmileage() != 0L)
                            totalTime.setRatedmileage(totalTime.getRatedmileage() + trainSubjectCredit.getMileageration());
                        else {
                            totalTime.setRatedmileage(trainSubjectCredit.getMileageration());
                        }
                        break;
                    case 1://实操
                        totalTime.setVehicleCreditration(trainSubjectCredit.getCreditration());
                        if (Objects.nonNull(totalTime.getRatedmileage()) || totalTime.getRatedmileage() != 0L)
                            totalTime.setRatedmileage(totalTime.getRatedmileage() + trainSubjectCredit.getMileageration());
                        else {
                            totalTime.setRatedmileage(trainSubjectCredit.getMileageration());
                        }
                        break;
                    case 3://模拟
                        totalTime.setSimulatorCreditration(trainSubjectCredit.getCreditration());
                        if (Objects.nonNull(totalTime.getRatedmileage()) || totalTime.getRatedmileage() != 0L)
                            totalTime.setRatedmileage(totalTime.getRatedmileage() + trainSubjectCredit.getMileageration());
                        else {
                            totalTime.setRatedmileage(trainSubjectCredit.getMileageration());
                        }
                        break;
                }
            }
            if (StringUtils.isBlank(totalTime.getTraintype())) {
                totalTime.setTraintype(studentinfo.getTraintype());
            }

        }

        return Result.OK(list);
    }

    /**
     * 学员档案
     *
     * @param
     * @return
     */
    @GetMapping("/studentViewVO")
    public Result studentView(StudentViewVO studentViewVO,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              HttpServletRequest req) {
        //部门id转化为inscode
        String deptId = JkSecurityUtils.getDeptId();
        List<String> inscodes = JkSecurityUtils.getOrgCodeStartWithPriorIdByDeptId(deptId);

        studentViewVO.setInscodes(inscodes);
        Page<StudentViewVO> page = new Page<StudentViewVO>(pageNo, pageSize);
        IPage<StudentViewVO> studentViewVOIPage = studentinfoService.selectStudentViewPage(page, studentViewVO);
        return Result.ok(studentViewVOIPage);
    }

    /**
     * 学员档案导出
     *
     * @param
     * @return
     */
    @RequiresPermissions("gzpt:studentinfo:exportStudentView")
    @AutoLog(value = "学员档案导出", operateType = CommonConstant.OPERATE_TYPE_1)
    @ApiOperation(value = "学员档案导出", notes = "学员档案导出")
    @GetMapping("/exportStudentView")
    public ModelAndView exportStudentView(StudentViewVO studentViewVO,
                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                          HttpServletRequest req) {

        //部门id转化为inscode

        String deptId = JkSecurityUtils.getDeptId();
        List<String> inscodes = JkSecurityUtils.getOrgCodeStartWithPriorIdByDeptId(deptId);

        studentViewVO.setInscodes(inscodes);
        List<StudentViewVO> list = studentinfoService.selectStudentViewList(studentViewVO);

        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "学员档案");
        mv.addObject(NormalExcelConstants.CLASS, StudentViewVO.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学员档案", "导出人:" + JkSecurityUtils.getRealname(), "学员档案"));
        mv.addObject(NormalExcelConstants.DATA_LIST, list);
        return mv;
    }


    @RequestMapping(value = "/stu/info", method = RequestMethod.GET)
    public Result<IPage<Studentinfo>> pageByConditions(@RequestParam(name = "id", required = false) String id,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest request, HttpServletResponse response) {
        Page<Studentinfo> page = new Page<Studentinfo>(pageNo, pageSize);
        IPage<Studentinfo> pageList = studentinfoService.byid(page, id);
        return Result.OK(pageList);
    }

    /**
     * 绍兴公众图片信息上传到oss ,截取相对路径存储
     * http://sx-jposs-166.oss-cn-shaoxing-sxdx-d01-a.ops.dxdtsxzwy.com/sxgzpt/2023-07-11/1.png
     * ?Expires=2004412337&OSSAccessKeyId=ydLAvUbqnW4b02FL&Signature=E9l%2Bt21bP56ua1LrLF6w7O%2BFBfc%3D
     * @param studentinfo
     */
    private void substrPhotoPath(Studentinfo studentinfo) {
        String photopath = studentinfo.getPhotopath();
        if (StringUtils.isNotBlank(photopath) && photopath.contains(OssZWYUtils.UPLOAD_PATH) && photopath.contains("?")) {
            String subPath = StringUtils.substringBetween(photopath, OssZWYUtils.UPLOAD_PATH, "?");
            studentinfo.setPhotopath(OssZWYUtils.UPLOAD_PATH + subPath);
            log.info("[照片路径] photopath:{},{} ", photopath, studentinfo.getPhotopath());
        }
    }

    /**
     * 加密接口
     */
    @GetMapping("/encrypt")
    public Result<?> encryptData() {
        int page = 1;
        try {
            while (true){
                QueryWrapper<Studentinfo> wrapper = new QueryWrapper<>();
                wrapper.setEntityClass(Studentinfo.class);
                wrapper.eq("LENGTH(idcard)", 18);
                wrapper.last("limit 200");
                List<Studentinfo> list = studentinfoService.list(wrapper);
                if(CollectionUtils.isEmpty(list)){
                    break;
                }
                studentinfoService.updateBatchById(list);
                log.info("[学员数据加密,第{}次循环]", page);
                page++;
            }
        }catch (Exception e){
            log.error("学员数据加密结束异常", e);
            return Result.error("fail");
        }
        log.info("[学员数据加密结束,一共{}页]",page);
        return Result.ok();
    }

    /**
     * 出生年龄、出生地市
     */
    @GetMapping("/addBirth")
    public Result<?> addBirth() {
        String defaltBirthday = "19010101";
        String defaltBirthplace = "3399";
        int page = 1;
        try {
            while (true){
                List<Studentinfo> list = studentinfoService.list(
                        Wrappers.lambdaQuery(Studentinfo.class)
                                .eq(Studentinfo::getBirthday, defaltBirthday)
                                .eq(Studentinfo::getBirthplace, defaltBirthplace)
                                .last("limit 50")
                );
                if(CollectionUtils.isEmpty(list)){
                    break;
                }
                list.forEach(e -> {
                    e.setBirthday(e.getIdcard().substring(6, 14));
                    e.setBirthplace(e.getIdcard().substring(0, 4));
                    studentinfoService.update(
                            Wrappers.lambdaUpdate(Studentinfo.class)
                                    .set(Studentinfo::getBirthday, e.getBirthday())
                                    .set(Studentinfo::getBirthplace, e.getBirthplace())
                                    .eq(Studentinfo::getId, e.getId())
                    );
                });

                log.info("[学员数据加密,第{}次循环]", page);
                page++;
            }
        }catch (Exception e){
            log.error("学员数据加密结束异常", e);
            return Result.error("fail");
        }
        log.info("[学员数据加密结束,一共{}页]",page);
        return Result.ok();
    }


    /**
     * 修正学时汇总
     */
    @GetMapping("/editTotalTime")
    public Result<?> editTotalTime(@RequestParam("count") Integer count) {
        QueryWrapper<ClassRecordDetail> wrapper = new QueryWrapper<>();
        wrapper.select("STUNUM");
        wrapper.apply("DURATION != DURATION_Y  or MILEAGE_Y != MILEAGE");
        wrapper.groupBy("STUNUM");
        wrapper.last("limit " + count);
        List<ClassRecordDetail> list = classRecordDetailService.list(wrapper);
        for (ClassRecordDetail one : list) {
            // 修改有效学时和里程
            UpdateWrapper<ClassRecordDetail> inner = new UpdateWrapper<>();
            inner.setSql("DURATION_Y = DURATION, MILEAGE_Y = MILEAGE");
            inner.eq("stunum", one.getStunum());
            classRecordDetailService.update(inner);

            HashMap<String, Object> map = new HashMap<>();
            map.put("stunum", one.getStunum());
            for (int i = 0; i < 3; i++) {
                System.out.println("科目：" + i);
                map.put("subject", i+1);
                Result<?> result = totalTimeController.summaryTotalTime(map);
                System.out.println(result.getMessage());
            }
        }
        return Result.ok();
    }
    @PostMapping("/gzfinancial")
    public ResultDto gzfinancial(@RequestBody String params ,HttpServletRequest request) {
        ResultDto resultDto = new ResultDto();
        String sign = request.getHeader("sign");



        SHA256withRSA rsaUtil = new SHA256withRSA();

        try {
            boolean issign = rsaUtil.verify(params.getBytes(), sign, publicKey);
            if (!issign) {
                resultDto.setErrorcode("500");
                resultDto.setMessage("验签错误");
                return resultDto;
            }
        } catch (Exception e) {
            log.error("查询学员资金情况验签异常",e);
            resultDto.setErrorcode("500");
            resultDto.setMessage("验签失败");
            return resultDto;
        }
        SM4Utils sm4 = new SM4Utils();
        sm4.setSecretKey("CF64BF82E875E212");
        sm4.setHexString(false);
        sm4.setEnCoding("utf-8");
        String cipherText = sm4.decryptData_ECB(params);
        JSONObject jsonObject = JSONObject.parseObject(cipherText);
        log.info("解密：+++++"+jsonObject);
        String idcard = jsonObject.get("idcard").toString();
        String traintype = jsonObject.get("traintype").toString();

        if (StringUtils.isBlank(idcard)||StringUtils.isBlank(traintype)){
            resultDto.setErrorcode("500");
            resultDto.setMessage("请求参数不能为空");
            return resultDto;
        }
        Studentinfo studentinfo = studentinfoService.getOne(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getIdcard, idcard).eq(Studentinfo::getTraintype, traintype));
        if (Objects.isNull(studentinfo)){
            resultDto.setErrorcode("500");
            resultDto.setMessage("未查到该学员");
            return resultDto;
        }
        JSONObject obj = new JSONObject();
        obj.put("isblack",studentinfo.getStutype()-1);
        BankReg bankReg = bankRegService.getOne(new LambdaQueryWrapper<>(BankReg.class).eq(BankReg::getStunum, studentinfo.getStunum()));
        if (Objects.isNull(bankReg)){
            obj.put("isfreeze",0);
        }else {
            if (bankReg.getStatus()==3){
                obj.put("isfreeze",1);
                obj.put("totalamount",bankReg.getBalanceAmount());
                obj.put("freezetime",bankReg.getBankdate());
                obj.put("bankcode",bankReg.getBankcode());
                obj.put("bankname",bankReg.getBankname());
                obj.put("remainamount",bankReg.getRemainingAmount());
            }else {
                obj.put("isfreeze",0);
                obj.put("totalamount",null);
                obj.put("freezetime",null);
                obj.put("bankcode",bankReg.getBankcode());
                obj.put("bankname",bankReg.getBankname());
            }
        }
        BankOrder secondBankOrder = bankOrderService.getOne(new LambdaQueryWrapper<>(BankOrder.class).eq(BankOrder::getOrdertype, 2).eq(BankOrder::getStuname, studentinfo.getStunum()));
        if (Objects.nonNull(secondBankOrder)){
            if (secondBankOrder.getStatus()==1){
                obj.put("secondamount",secondBankOrder.getTransferAmount());
                obj.put("secondtime",secondBankOrder.getBankdate());
            }else {
                obj.put("secondamount",0);
                obj.put("secondtime",null);
            }
        }else {
            obj.put("secondamount",null);
            obj.put("secondtime",null);
        }
        BankOrder thirdBankOrder = bankOrderService.getOne(new LambdaQueryWrapper<>(BankOrder.class).eq(BankOrder::getOrdertype, 3).eq(BankOrder::getStuname, studentinfo.getStunum()));
        if (Objects.nonNull(thirdBankOrder)){
            if (thirdBankOrder.getStatus()==1){
                obj.put("thirdamount",thirdBankOrder.getTransferAmount());
                obj.put("thirdtime",thirdBankOrder.getBankdate());
            }else {
                obj.put("thirdamount",0);
                obj.put("thirdtime",null);
            }
        }else {
            obj.put("thirdamount",null);
            obj.put("thirdtime",null);
        }
        String s = sm4.encryptData_ECB(obj.toString());
        resultDto.setErrorcode("200");
        resultDto.setMessage("查询成功");
        resultDto.setData(s);


        return resultDto;
    }

    @Value("${RSA.publicKey}")
    private String publicKey;

//    public static void main(String[] args) {
//        SM4Utils sm4 = new SM4Utils();
//        sm4.setSecretKey("CF64BF82E875E212");
//        sm4.setHexString(false);
//        sm4.setEnCoding("utf-8");
//        String params="Td+leeUTkwyXx59FFamM6BoGn4RxiSGGi+lLz/2X7hQsn59dI+fu6cX4PzhNgFwFL2NwhI9tvNKRSlOGNL8LKw==";
//        String cipherText = sm4.decryptData_ECB(params);
//        System.out.println(cipherText);
//    }

    /**
     * 不学科目一
     */
    @PostMapping("/passSubjectOne")
    public Result<?> subjectOnePass(@RequestBody Studentinfo rec) {
        Studentinfo byId = studentinfoService.getById(rec.getId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("stunum", byId.getStunum());
        String json = jsonObject.toJSONString();
        ResponseEntity<Result> response;
        try {
            response = restTemplateUtils.postForEntity(km1pass, json, Result.class);
        } catch (Exception e) {
            log.error("推送浙里驾培失败：", e);
            return Result.error("操作失败！");
        }
        HttpStatus statusCode = response.getStatusCode();
        Result body = response.getBody();
        if ((!statusCode.is2xxSuccessful() ||
                Objects.isNull(body) ||
                body.getCode() != 200)) {
            return Result.error("操作失败！");
        }
        return Result.ok("操作成功！");
    }

    /**
     * 衢学车签订合同免除签名验证
     */
    @PostMapping("/releaseOcrCheck")
    @RequiresPermissions("gzpt:studentinfo:releaseOcrCheck")
    public Result<?> releaseOcrCheck(@RequestBody Studentinfo rec) {
        Studentinfo studentinfo = studentinfoService.getById(rec.getId());
        if (studentinfo == null) {
            return Result.error("请求参数不能为空！");
        }
        //衢学车签订合同免除 签名验证  1天有效期
        redisUtil.set("releaseOcrCheck"+studentinfo.getIdcard(), 1,3600 * 24);

        return Result.ok("操作成功！");
    }

    /**
     * 监管端设置黑户
     */
    @PostMapping("/recJgptBlack")
    public Result<?> recJgptBlack(@RequestBody String recEncrypt) {
        SM4Utils sm4 = new SM4Utils();
        BlackOrLogoutVo rec = JSONObject.parseObject(sm4.decryptData_ECB(recEncrypt), BlackOrLogoutVo.class);
        Studentinfo stu = studentinfoService.getOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getIdcard, rec.getIdcard())
                        .eq(Studentinfo::getTraintype, rec.getTrainType())
                        .eq(Studentinfo::getInscode, rec.getInscode())
        );
        if (Objects.isNull(stu)) {
            return Result.error("学员不存在！");
        }
        if (2 == stu.getStutype()) {
            return Result.error("学员已经是黑户，请勿重复操作！");
        }
        String nameBak = stu.getName();
        String idCardBak = stu.getIdcard();
        stu.setStutype(2);
        studentinfoService.setBlackStu(stu);

        // 记录日志
        TMMr890chLog res = new TMMr890chLog();
        String realName = JkyCryptorUtils.decrypt(rec.getRealName(), cryptoKey);
        res.setLogType(4);
        res.setLogContent(nameBak + idCardBak + "设置黑户通过");
        res.setUserid(rec.getUserName());
        res.setUsername(realName);
        res.setIp(rec.getIp());
        mr890chLogService.save(res);
        return Result.ok("操作成功！");
    }

    /**
     * 设置机器人教学
     */
    @PostMapping("/assistance")
    @RequiresPermissions("gzpt:studentinfo:assistance")
    public Result<?> assistance(@RequestBody StudentContract rec) {
        Studentinfo studentinfo = studentinfoService.getOneByStuNum(rec.getStunum());
        if (Objects.isNull(studentinfo) || studentinfo.getIssigncontract() !=0) {
            return Result.error("必须在签订合同前设置智能机器人");
        }
        StudentContract stuContract = iStudentContractService.getOneByStunum(rec.getStunum());

        if(Objects.nonNull(stuContract)){
            stuContract.setTheoryCostPer(rec.getTheoryCostPer());
            stuContract.setTheoryCost(rec.getTheoryCost());
            stuContract.setRemoteCostPer(rec.getRemoteCostPer());
            stuContract.setSub2CostPer(rec.getSub2CostPer());
            stuContract.setSub2Cost(rec.getSub2Cost());
            stuContract.setSub3CostPer(rec.getSub3CostPer());
            stuContract.setSub2Cost(rec.getSub3Cost());
            iStudentContractService.updateById(stuContract);
        }
        studentinfo.setIsassistance(1);
        //2 二笔冻结（智能机器人-实操冻一起）
        studentinfo.setFrozencount(2);
        studentinfoService.updateById(studentinfo);
        return Result.ok("操作成功！");
    }
    /**
     * 查找学员合同信息
     */
    @PostMapping("/getStuContract")
    @AutoLog(value = "查找学员合同信息")
    public Result<?> getStuContract(@RequestBody Studentinfo stu) {
        if(stu.getStunum()==null){
            return Result.error("学员编号不能为空！");
        }
        if(stu.getIssigncontract() != 0){
            return Result.error("学员已签订合同");
        }
        StudentContract stuContract = iStudentContractService.getOneByStunum(stu.getStunum());
        if(Objects.nonNull(stuContract)){
            return Result.ok(stuContract);
        }else{
            Cost cost = costService.getOneByInsTrainType(stu.getInscode(), stu.getTraintype());
            if(Objects.nonNull(cost)){
                stuContract = new StudentContract();
                stuContract.setStunum(stu.getStunum());
                stuContract.setTheoryCostPer(cost.getTheoryCostPer());
                stuContract.setTheoryCost(cost.getTheoryCost());
                stuContract.setRemoteCostPer(cost.getRemoteCostPer());
                stuContract.setSub2CostPer(cost.getSub2CostPer());
                stuContract.setSub2Cost(cost.getSub2Cost());
                stuContract.setSub3CostPer(cost.getSub3CostPer());
                stuContract.setSub2Cost(cost.getSub3Cost());
                return Result.ok(stuContract);
            }else{
                return Result.error("未找该驾校合同费用信息！");
            }
        }
    }

    /**
     * 设置学员免单
     */
    @PostMapping("/freeOrder")
    public Result<?> freeOrder(@RequestBody Studentinfo rec) {
        if (rec == null) {
            return Result.error("未找到对应的学员信息！");
        }
        String redisKey = "student:freeOrder:" + rec.getStunum();
        boolean hasKey = redisUtil.hasKey(redisKey);
        if (hasKey) {
            return Result.error("此学员已设置免单");
        }
        try {
            // 将学员ID存入Redis，设置1天过期时间（秒）
            redisUtil.set(redisKey, rec.getStunum(), 24 * 60 * 60);
            log.info("学员免单操作成功，学员ID: {}, Redis Key: {}", rec.getStunum(), redisKey);
            return Result.OK("学员免单操作成功！");
        } catch (Exception e) {
            log.error("学员免单操作失败，学员ID: {}, 错误: {}", rec.getStunum(), e.getMessage(), e);
            return Result.error("学员免单操作失败，请联系管理员！");
        }
    }

    /**
     * 获取定额的学时
     */
    @PostMapping(value = "/subCredit")
    public Result<?> subCredit(@RequestBody Studentinfo rec) {
        List<TrainSubjectCredit> list = trainSubjectCreditService.list(
                Wrappers.<TrainSubjectCredit>lambdaQuery()
                        .eq(TrainSubjectCredit::getType, 6)
                        .eq(TrainSubjectCredit::getTrainCarType, rec.getTraintype())
        );
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Result.error("无该车型大纲");
        }
        Map<Long, Map<Long, Long>> collect = list.stream().collect(Collectors.groupingBy(TrainSubjectCredit::getSubject, Collectors.toMap(TrainSubjectCredit::getClasstype, TrainSubjectCredit::getCreditration)));
        CreditVo res = new CreditVo();
        res.setClassHours(collect.get(1L).get(2L) / 60);
        res.setImiHours(collect.get(2L).get(3L) / 60);
        res.setRemoteHours(collect.get(3L).get(4L) / 60);
        res.setSub2OprHours(collect.get(2L).get(1L) / 60);
        res.setSub3OprHours(collect.get(3L).get(1L) / 60);
        return Result.ok(res);
    }
}
