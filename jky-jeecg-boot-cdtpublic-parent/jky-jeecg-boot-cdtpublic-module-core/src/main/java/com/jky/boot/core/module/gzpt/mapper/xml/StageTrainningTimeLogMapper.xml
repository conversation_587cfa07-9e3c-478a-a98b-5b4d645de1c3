<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.StageTrainningTimeLogMapper">
    <resultMap type="com.jky.boot.core.module.gzpt.entity.StageTrainningTimeLog" id="StageTrainningTimeLogResult">
        <result property="stageid" column="stageid"/>
        <result property="inscode" column="inscode"/>
        <result property="stunum" column="stunum"/>
        <result property="subject" column="subject"/>
        <result property="message" column="message"/>
        <result property="crdate" column="crdate"/>
        <result property="recarray" column="recarray"/>
    </resultMap>

    <sql id="selectStageTrainningTimeLogVo">
        select stageid, inscode, stunum, subject, message, crdate, recarray
        from t_m_stage_trainning_time_log
    </sql>

    <select id="selectStageTrainningTimeLogList" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTimeLog"
            resultMap="StageTrainningTimeLogResult">
        <include refid="selectStageTrainningTimeLogVo"/>
        <where>
            <if test="inscode != null  and inscode != ''">and inscode = #{inscode}</if>
            <if test="stunum != null  and stunum != ''">and stunum = #{stunum}</if>
            <if test="subject != null  and subject != ''">and subject = #{subject}</if>
            <if test="message != null  and message != ''">and message = #{message}</if>
            <if test="crdate != null ">and crdate = #{crdate}</if>
            <if test="recarray != null  and recarray != ''">and recarray = #{recarray}</if>
        </where>
    </select>

    <select id="selectStageTrainningTimeLogByStageid" parameterType="Long" resultMap="StageTrainningTimeLogResult">
        <include refid="selectStageTrainningTimeLogVo"/>
        where stageid = #{stageid}
    </select>

    <insert id="insertStageTrainningTimeLog" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTimeLog">
        insert into t_m_stage_trainning_time_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stageid != null">stageid,</if>
            <if test="inscode != null">inscode,</if>
            <if test="stunum != null">stunum,</if>
            <if test="subject != null">subject,</if>
            <if test="message != null">message,</if>
            <if test="crdate != null">crdate,</if>
            <if test="recarray != null">recarray,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stageid != null">#{stageid},</if>
            <if test="inscode != null">#{inscode},</if>
            <if test="stunum != null">#{stunum},</if>
            <if test="subject != null">#{subject},</if>
            <if test="message != null">#{message},</if>
            <if test="crdate != null">#{crdate},</if>
            <if test="recarray != null">#{recarray},</if>
        </trim>
    </insert>

    <update id="updateStageTrainningTimeLog" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTimeLog">
        update t_m_stage_trainning_time_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="inscode != null">inscode = #{inscode},</if>
            <if test="stunum != null">stunum = #{stunum},</if>
            <if test="subject != null">subject = #{subject},</if>
            <if test="message != null">message = #{message},</if>
            <if test="crdate != null">crdate = #{crdate},</if>
            <if test="recarray != null">recarray = #{recarray},</if>
        </trim>
        where stageid = #{stageid}
    </update>

    <delete id="deleteStageTrainningTimeLogByStageid" parameterType="Long">
        delete
        from t_m_stage_trainning_time_log
        where stageid = #{stageid}
    </delete>

    <delete id="deleteStageTrainningTimeLogByStageids" parameterType="String">
        delete from t_m_stage_trainning_time_log where stageid in
        <foreach item="stageid" collection="array" open="(" separator="," close=")">
            #{stageid}
        </foreach>
    </delete>
</mapper>