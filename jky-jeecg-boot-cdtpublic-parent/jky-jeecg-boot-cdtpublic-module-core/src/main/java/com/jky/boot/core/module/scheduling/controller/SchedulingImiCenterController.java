package com.jky.boot.core.module.scheduling.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.StudentinfoEnter;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.jky.boot.core.module.scheduling.entity.SchedulingImiCenter;
import com.jky.boot.core.module.scheduling.service.ISchedulingImiCenterService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 模拟中心
 * @Author: Roo
 * @Date:   2025-05-22
 * @Version: V1.0
 */
@Api(tags="模拟中心")
@RestController
@RequestMapping("/scheduling/schedulingImiCenter")
@Slf4j
public class SchedulingImiCenterController extends JeecgController<SchedulingImiCenter, ISchedulingImiCenterService> {
	@Autowired
	private ISchedulingImiCenterService schedulingImiCenterService;

	/**
	 * 分页列表查询
	 *
	 * @param schedulingImiCenter
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "模拟中心-分页列表查询")
	@ApiOperation(value = "模拟中心-分页列表查询", notes = "模拟中心-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<SchedulingImiCenter>> queryPageList(SchedulingImiCenter schedulingImiCenter,
															@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
															@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
															HttpServletRequest req) {
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
		schedulingImiCenter.setInscode(inscode.get(0));
		QueryWrapper<SchedulingImiCenter> queryWrapper = QueryGenerator.initQueryWrapper(schedulingImiCenter, req.getParameterMap());
		Page<SchedulingImiCenter> page = new Page<SchedulingImiCenter>(pageNo, pageSize);
		IPage<SchedulingImiCenter> pageList = schedulingImiCenterService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param schedulingImiCenter
	 * @return
	 */
	@AutoLog(value = "模拟中心-添加")
	@ApiOperation(value = "模拟中心-添加", notes = "模拟中心-添加")
	//@RequiresPermissions("scheduling:scheduling_imi_center:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SchedulingImiCenter schedulingImiCenter, HttpServletRequest request) {
		List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
		String userName = JwtUtil.getUserNameByToken(request);
		schedulingImiCenter.setUserName(userName);
		schedulingImiCenter.setInscode(insCodes.get(0));
		// 检查相同Inscode的记录是否已存在
		QueryWrapper<SchedulingImiCenter> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("inscode", insCodes.get(0));
		long count = schedulingImiCenterService.count(queryWrapper);
		if (count > 0) {
			return Result.error("添加失败！每个机构只能有一条模拟中心记录！");
		}
		schedulingImiCenterService.save(schedulingImiCenter);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param schedulingImiCenter
	 * @return
	 */
	@AutoLog(value = "模拟中心-编辑")
	@ApiOperation(value="模拟中心-编辑", notes="模拟中心-编辑")
	//@RequiresPermissions("scheduling:scheduling_imi_center:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody SchedulingImiCenter schedulingImiCenter) {
		schedulingImiCenterService.updateById(schedulingImiCenter);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "模拟中心-通过id删除")
	@ApiOperation(value="模拟中心-通过id删除", notes="模拟中心-通过id删除")
	//@RequiresPermissions("scheduling:scheduling_imi_center:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		schedulingImiCenterService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "模拟中心-批量删除")
	@ApiOperation(value="模拟中心-批量删除", notes="模拟中心-批量删除")
	//@RequiresPermissions("scheduling:scheduling_imi_center:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.schedulingImiCenterService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "模拟中心-通过id查询")
	@ApiOperation(value="模拟中心-通过id查询", notes="模拟中心-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SchedulingImiCenter> queryById(@RequestParam(name="id",required=true) String id) {
		SchedulingImiCenter schedulingImiCenter = schedulingImiCenterService.getById(id);
		if(schedulingImiCenter==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(schedulingImiCenter);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param schedulingImiCenter
    */
    //@RequiresPermissions("scheduling:scheduling_imi_center:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SchedulingImiCenter schedulingImiCenter) {
        return super.exportXls(request, schedulingImiCenter, SchedulingImiCenter.class, "模拟中心");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    //@RequiresPermissions("scheduling:scheduling_imi_center:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SchedulingImiCenter.class);
    }

}