package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.StuTransfer;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.TransferCarType;
import com.jky.boot.core.module.gzpt.vo.StudentCountVO;
import com.jky.boot.core.module.gzpt.vo.StudentViewVO;
import org.jeecg.common.api.vo.Result;

import java.util.List;

public interface IStudentinfoService extends IService<Studentinfo> {
    /**
     * 查询学员信息
     *
     * @param id 学员信息主键
     * @return 学员信息
     */
    Studentinfo selectStudentinfoById(String id);

    /**
     * 查询学员信息
     *
     * @param idcard 学员身份证号
     * @return 学员信息
     */
    Studentinfo selectStudentinfoByIdCard(String idcard);

    /**
     * 查询学员信息列表
     *
     * @param studentinfo 学员信息
     * @return 学员信息集合
     */
    List<Studentinfo> selectStudentinfoList(Studentinfo studentinfo);

    /**
     * 修改学员信息
     *
     * @param studentinfo 学员信息
     * @return 结果
     */
    int updateStudentinfo(Studentinfo studentinfo);

    /**
     * 批量删除学员信息
     *
     * @param ids 需要删除的学员信息主键集合
     * @return 结果
     */
    String deleteStudentinfoByIds(String[] ids, Long did,String inscode);

    /**
     * 删除学员信息信息
     *
     * @param id 学员信息主键
     * @return 结果
     */
    int deleteStudentinfoById(String id);

    /**
     * 学员签订合同
     */
    String signStudentContractById(String id);

    /**
     * 查看学员合同
     */
    String viewStudentContractByStunum(String id);

    // 设置黑户
    void setBlackStu(Studentinfo studentinfo);

    List<StudentCountVO> selectStudentCounts(String startTime, String endTime, String inscode);

    // 通过手机号查学员信息
    Studentinfo getStudentByStunum(String stunum);


    /**
     * 根据身份证和培训车型查询学员
     * @param idcard 身份证
     * @param traintype 培训车型
     * @return stu
     */
    Studentinfo selectByIdcardAndTraintype(String idcard, String traintype);

    IPage<StudentViewVO> selectStudentViewPage(Page<StudentViewVO> page, StudentViewVO studentViewVO);
    List<StudentViewVO>selectStudentViewList(StudentViewVO studentViewVO);

    Page<Studentinfo> byid(Page<Studentinfo> page, String id);

    /**
     * 保存学员异地转校记录
     */
    void saveTransfer(Studentinfo student, String insname);


    String signStudentContractByIdcard(String stunum);

    Studentinfo getOneByStuNum(String stuNum);

    /**
     *  按身份证查询学员，并返回报名日期最近的一条数据
     */
    Studentinfo getOneByIdCard(String idCard);

    /**
     * 退学
     */
    String dropout(Studentinfo studentinfo, String inscode);

    /**
     * 退学删除和备份
     */
    void rmAndBak(Studentinfo stu, long did);

    /**
     * 学员资金解冻(仅供转校、注销使用)
     * @param stuNum 学员编号
     * @param type 1-转校、2-注销
     */
    Result<?> unfreeze(String stuNum, Integer type, String bosid);

    /**
     * 销户
     *
     * @param stuNum 学员编号
     * @return result
     */
    Result<?> cancelAccount(String stuNum);

    /**
     * 转校
     */
    void doTrans(StuTransfer one);

    /**
     * 转车型
     */
    void doTransCarType(TransferCarType one);

    /**
     * 科目二资金划转
     * @param id
     * @return
     */
    Result<?> passSubjectTwo(String id);


}
