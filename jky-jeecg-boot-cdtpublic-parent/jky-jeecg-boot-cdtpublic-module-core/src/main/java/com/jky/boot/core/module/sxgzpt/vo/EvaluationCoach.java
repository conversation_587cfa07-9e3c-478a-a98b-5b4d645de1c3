package com.jky.boot.core.module.sxgzpt.vo;

import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

@Data
public class EvaluationCoach {
    private String coachnum;
    private String coachname;
    private java.lang.String inscode;
    private java.lang.String insname;
    /** 教学质量评分*/
//    @Excel(name = "教学质量评分", width = 15)
    private Double teachingQuality;
    /** 教学态度评分*/
//    @Excel(name = "教学态度评分", width = 15)
    private Double teachingAttitude;
    /** 车辆状况评分*/
//    @Excel(name = "车辆状况评分", width = 15)
    private Double vehicleCondition;
    /** 教学环境评分*/
//    @Excel(name = "教学环境评分", width = 15)
    private Double trainingEnvironment;

    private Integer times;
}
