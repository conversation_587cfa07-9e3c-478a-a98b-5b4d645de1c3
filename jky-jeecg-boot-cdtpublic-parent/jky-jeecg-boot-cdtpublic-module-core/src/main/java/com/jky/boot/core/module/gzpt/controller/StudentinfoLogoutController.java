package com.jky.boot.core.module.gzpt.controller;

import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.crypto.annotation.DesensitizedAnno;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jky.boot.core.module.gzpt.vo.StudentViewVO;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jky.boot.core.module.gzpt.entity.StudentinfoLogout;
import com.jky.boot.core.module.gzpt.service.IStudentinfoLogoutService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 学员信息归档对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="学员信息归档对象")
@RestController
@RequestMapping("/gzpt/studentinfoLogout")
@Slf4j
public class StudentinfoLogoutController extends JeecgController<StudentinfoLogout, IStudentinfoLogoutService> {
	@Autowired
	private IStudentinfoLogoutService studentinfoLogoutService;

	/**
	 * 分页列表查询
	 *
	 * @param studentinfoLogout
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "学员信息归档对象-分页列表查询")
	@ApiOperation(value="学员信息归档对象-分页列表查询", notes="学员信息归档对象-分页列表查询")
	@GetMapping(value = "/list")
	@DesensitizedAnno
	public Result<IPage<StudentinfoLogout>> queryPageList(StudentinfoLogout studentinfoLogout,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfoLogout.getInscode());
		studentinfoLogout.setInscode(null);
		QueryWrapper<StudentinfoLogout> queryWrapper = QueryGenerator.initQueryWrapper(studentinfoLogout, req.getParameterMap());
		queryWrapper.setEntityClass(StudentinfoLogout.class);
		queryWrapper.in("inscode",inscode);
		Page<StudentinfoLogout> page = new Page<StudentinfoLogout>(pageNo, pageSize);
		IPage<StudentinfoLogout> pageList = studentinfoLogoutService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param studentinfoLogout
	 * @return
	 */
	@AutoLog(value = "学员信息归档对象-添加")
	@ApiOperation(value="学员信息归档对象-添加", notes="学员信息归档对象-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_studentinfo_logout:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody StudentinfoLogout studentinfoLogout) {
		studentinfoLogoutService.save(studentinfoLogout);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param studentinfoLogout
	 * @return
	 */
	@AutoLog(value = "学员信息归档对象-编辑")
	@ApiOperation(value="学员信息归档对象-编辑", notes="学员信息归档对象-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_studentinfo_logout:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody StudentinfoLogout studentinfoLogout) {
		studentinfoLogoutService.updateById(studentinfoLogout);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "学员信息归档对象-通过id删除")
	@ApiOperation(value="学员信息归档对象-通过id删除", notes="学员信息归档对象-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_studentinfo_logout:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		studentinfoLogoutService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "学员信息归档对象-批量删除")
	@ApiOperation(value="学员信息归档对象-批量删除", notes="学员信息归档对象-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_studentinfo_logout:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.studentinfoLogoutService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "学员信息归档对象-通过id查询")
	@ApiOperation(value="学员信息归档对象-通过id查询", notes="学员信息归档对象-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<StudentinfoLogout> queryById(@RequestParam(name="id",required=true) String id) {
		StudentinfoLogout studentinfoLogout = studentinfoLogoutService.getById(id);
		if(studentinfoLogout==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(studentinfoLogout);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param studentinfoLogout
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_studentinfo_logout:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, StudentinfoLogout studentinfoLogout) {
//
//		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentinfoLogout.getInscode());
//		studentinfoLogout.setInscode(null);
//		QueryWrapper<StudentinfoLogout> queryWrapper = QueryGenerator.initQueryWrapper(studentinfoLogout, request.getParameterMap());
//		queryWrapper.in("inscode",inscode);
//		List<StudentinfoLogout> list = studentinfoLogoutService.list(queryWrapper);
//
//		ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//		mv.addObject(NormalExcelConstants.FILE_NAME, "学员档案");
//		mv.addObject(NormalExcelConstants.CLASS, StudentinfoLogout.class);
//		mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("学员档案", "导出人:" + JkSecurityUtils.getRealname(), "学员档案"));
//		mv.addObject(NormalExcelConstants.DATA_LIST, list);
//		return mv;
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_studentinfo_logout:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, StudentinfoLogout.class);
//    }

	 /**
	  * 加密接口
	  */
	 @GetMapping("/encrypt")
	 public Result<?> encryptData() {
		 QueryWrapper<StudentinfoLogout> wrapper = new QueryWrapper<>();
		 wrapper.setEntityClass(StudentinfoLogout.class);
		 wrapper.eq("LENGTH(idcard)", 18);
		 wrapper.last("limit 200");
		 List<StudentinfoLogout> list = studentinfoLogoutService.list(wrapper);
		 studentinfoLogoutService.updateBatchById(list);
		 return Result.ok();
	 }
}
