package com.jky.boot.core.module.gzpt.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 监管平台 推送变更车型 审核
 */

@Data
public class TransferCarTypeAuditDto {
    @ApiModelProperty(value = "学员编号")
    private String stunum;
    @ApiModelProperty(value = "新车型")
    private String traintype;
    @ApiModelProperty(value = "旧车型")
    private String oldtraintype;
    @ApiModelProperty(value = "审核状态 0 未审核 1.审核通过 3,未通过")
    private Integer status;
    @ApiModelProperty(value = "审核原因")
    private String reason;
    @ApiModelProperty(value = "审核人")
    private String audituser;
    @ApiModelProperty(value = "审核时间")
    private String auditdate;
}
