package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: t_m_examiner
 * @Author: jeecg-boot
 * @Date:   2023-04-14
 * @Version: V1.0
 */
@Data
@TableName("t_m_examiner")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_examiner对象", description="t_m_examiner")
public class TMExaminer implements Serializable {
    private static final long serialVersionUID = 1L;

	/**考核员编号*/
	@Excel(name = "考核员编号", width = 15)
    @ApiModelProperty(value = "考核员编号")
//    @JsonProperty("id")
//    @JSONField(name = "id")
    private String examnum;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;
	/**1:男性;2:女性*/
	@Excel(name = "1:男性;2:女性", width = 15, dicCode = "sys_user_sex")
    @ApiModelProperty(value = "1:男性;2:女性")
    @Dict(dicCode = "sys_user_sex")
    private Integer sex;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private String idcard;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.MOBILE_PHONE)
    private String mobile;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;
    /**培训机构名称*/
    @Excel(name = "培训机构名称", width = 15)
    @ApiModelProperty(value = "培训机构名称")
    @TableField(exist = false)
    private String insname;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.ADDRESS)
    private String address;
	/**照片文件url*/
	@Excel(name = "照片文件url", width = 15)
    @ApiModelProperty(value = "照片文件url")
    private String photo;
	/**指纹图片ID*/
	@Excel(name = "指纹图片ID", width = 15)
    @ApiModelProperty(value = "指纹图片ID")
    private Integer fingerprint;
	/**职业资格证号*/
	@Excel(name = "职业资格证号", width = 15)
    @ApiModelProperty(value = "职业资格证号")
    private String occupationno;
	/**职业资格等级 1:一级 2:二级 3:三级 4:四级*/
	@Excel(name = "职业资格等级 1:一级 2:二级 3:三级 4:四级", width = 15, dicCode = "sys_jobLevel")
    @ApiModelProperty(value = "职业资格等级 1:一级 2:二级 3:三级 4:四级")
    private String occupationlevel;
	/**准驾车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "准驾车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "准驾车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private String dripermitted;
	/**准教车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P */
	@Excel(name = "准教车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P ", width = 15)
    @ApiModelProperty(value = "准教车型： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P ")
    private String teachpermitted;
	/**供职状态: 0:在职 1:离职 */
	@Excel(name = "供职状态: 0:在职 1:离职 ", width = 15, dicCode = "sys_Serve_status")
    @ApiModelProperty(value = "供职状态: 0:在职 1:离职 ")
    @Dict(dicCode = "sys_Serve_status")
    private String employstatus;
	/**入职日期*/
	@Excel(name = "入职日期", width = 15)
    @ApiModelProperty(value = "入职日期")
    private String hiredate;
	/**离职日期*/
	@Excel(name = "离职日期", width = 15)
    @ApiModelProperty(value = "离职日期")
    private String leavedate;
	/**
状态：1.已备案，0.解除备案*/
	@Excel(name = " 状态：1.已备案，0.解除备案", width = 15, dicCode = "sys_general_status")
    @ApiModelProperty(value = " 状态：1.已备案，0.解除备案")
    @Dict(dicCode = "sys_general_status")
    private java.lang.Integer status;
	/**备案时间*/
	@Excel(name = "备案时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "备案时间")
    private Date crdate;
	/**区县编号*/
	@Excel(name = "区县编号", width = 15)
    @ApiModelProperty(value = "区县编号")
    private String district;
	/**备案平台*/
	@Excel(name = "备案平台", width = 15)
    @ApiModelProperty(value = "备案平台")
    private String platform;
	/**
0-未审核 1-审核通过 2-审核不通过*/
	@Excel(name = " 0-未审核 1-审核通过 2-审核不通过", width = 15, dicCode = "sys_general_audit")
    @ApiModelProperty(value = " 0-未审核 1-审核通过 2-审核不通过")
    @Dict(dicCode = "sys_general_audit")
    private java.lang.Integer auditstate;
	/**审核时间*/
	@Excel(name = "审核时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "审核时间")
    private java.util.Date auditdate;
	/**审核原因*/
	@Excel(name = "审核原因", width = 15)
    @ApiModelProperty(value = "审核原因")
    private java.lang.String auditreason;
	/**下载*/
	@Excel(name = "下载", width = 15)
    @ApiModelProperty(value = "下载")
    private java.lang.Integer isUploadgj;
	/**驾驶证号*/
	@Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private java.lang.String drilicence;
	/**ID*/
	@Excel(name = "ID", width = 15)
    @ApiModelProperty(value = "ID")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonProperty(value = "id")
    private java.lang.String examid;
	/**驾驶证初领日期*/
	@Excel(name = "驾驶证初领日期", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期")
    private java.lang.String fstdrilicdate;
    /**是否是培训机构用户*/
    @ApiModelProperty(value = "是否是培训机构用户")
    @TableField(exist = false)
    private java.lang.Boolean isSchool;
}
