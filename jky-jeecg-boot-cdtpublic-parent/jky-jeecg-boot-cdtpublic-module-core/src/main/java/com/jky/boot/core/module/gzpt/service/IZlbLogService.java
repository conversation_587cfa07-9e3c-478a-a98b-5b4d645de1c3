package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.ZlbLog;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: t_m_zlb_log
 * @Author: jeecg-boot
 * @Date: 2024-01-18
 * @Version: V1.0
 */
public interface IZlbLogService extends IService<ZlbLog> {

    public void saveZlbLog(Integer logType, String logContent,String operateType, String certId, String zlbId, String requestParam, HttpServletRequest req);
}
