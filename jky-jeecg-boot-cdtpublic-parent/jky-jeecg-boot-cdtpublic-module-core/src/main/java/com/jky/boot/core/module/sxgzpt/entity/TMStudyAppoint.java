package com.jky.boot.core.module.sxgzpt.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *  学习培训预约
 * <AUTHOR>
 * @since   2023-06-13
 * @version V1.0
 */
@Data
@TableName("t_m_study_appoint")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_study_appoint对象", description="学习培训预约")
public class TMStudyAppoint implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Integer id;
	/**学员姓名*/
	@Excel(name = "学员姓名", width = 15)
    @ApiModelProperty(value = "学员姓名")
    private String stuName;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private String stuNum;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    private String tel;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private String idCard;
	/**学驾车型*/
	@Excel(name = "学驾车型", width = 15)
    @ApiModelProperty(value = "学驾车型")
    private Integer carType;
	/**设备类型*/
	@Excel(name = "设备类型", width = 15)
    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;
	/**预约时间*/
	@Excel(name = "预约时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "预约时间")
    private Date appointTime;
	/**预约状态*/
	@Excel(name = "预约状态", width = 15)
    @ApiModelProperty(value = "预约状态")
    private Integer appointStatus;
	/**设备编号*/
	@Excel(name = "设备编号", width = 15)
    @ApiModelProperty(value = "设备编号")
    private String deviceNum;
	/**驾校*/
	@Excel(name = "驾校", width = 15)
    @ApiModelProperty(value = "驾校")
    private String insCode;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
	/**修改人*/
    @ApiModelProperty(value = "修改人")
    private String updateBy;
}
