package com.jky.boot.core.module.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ZlbLogInOutQueryParams implements Serializable {

    @NotBlank(message = "身份证不能为空")
    private String idcard;

    @NotNull(message = "类型不能为空")
    private Integer eventtype;

    @NotBlank(message = "机构编号不能为空")
    private String inscode;

    //A1A2A3B1B2C5
    private String traintype;
}

