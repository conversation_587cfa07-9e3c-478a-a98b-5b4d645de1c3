package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 模拟课程预约
 *
 * <AUTHOR>
 * @version 2024-09-29
 */
@Data
@TableName("scheduling_imi")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SchedulingImi {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 驾校编号
     */
	@Dict(dictTable = "t_m_institution", dicText = "NAME", dicCode = "INSCODE")
    private String inscode;
    /**
     * 教练编号
     */
    @Dict(dictTable = "t_m_coach", dicText = "NAME", dicCode = "COACHNUM")
    private String coachnum;
    /**
     * 培训车型
     */
    private String traintype;
    /**
     * 科目
     */
    private Integer subject;
    /**
     * 开课日期
     */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date classDate;
    /**
     * 课程时间
     */
    private String classTime;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 学时
     */
    private Integer classHours;
    /**
     * 上课教室
     */
    private String classRoomName;
    /**
     * 学员容量
     */
    private Integer stuAmount;
    /**
     * 当前预约人数
     */
    private Integer curAmount;
    /**
     * 发布状态（0-未发布，1-已发布）
     */
    @Dict(dicCode = "send_status")
    private Integer publish;
    /**
     * 发布时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    /**
     * 创建时间
     */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 开始日期（前端传入，不存入数据库）
     */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date classDateStart;

    /**
     * 结束日期（前端传入，不存入数据库）
     */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date classDateEnd;

    /**
     * 时间方案ID
     */
    @TableField(exist = false)
    private String templateId;
}
