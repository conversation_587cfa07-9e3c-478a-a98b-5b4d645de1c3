package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 模拟器接入平台对象
 * <AUTHOR>
 * @since   2023-06-02
 * @version V1.0
 */
@Data
@TableName("t_m_ins_device")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_ins_device对象", description="模拟器接入平台汇总对象")
public class TMInsDevice implements Serializable {
    private static final long serialVersionUID = 1L;

	/**驾校设备接入ID*/
    @TableId(value = "ins_device_id", type = IdType.ASSIGN_ID)
	@Excel(name = "驾校设备接入ID", width = 15)
    @ApiModelProperty(value = "驾校设备接入ID")
    @JsonSerialize(using= ToStringSerializer.class)
    @JsonProperty(value = "id")
    private String insDeviceId;
	/**????*/
	@Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private String companyName;
	/**??????*/
	@Excel(name = "设备安装地址", width = 15)
    @ApiModelProperty(value = "设备安装地址")
    private String address;
	/**???*/
	@Excel(name = "联系人", width = 15)
    @ApiModelProperty(value = "联系人")
    @CryptoFieldAnno
    private String contactPerson;
	/**????*/
	@Excel(name = "联系方式", width = 15)
    @ApiModelProperty(value = "联系方式")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.MOBILE_PHONE)
    @CryptoFieldAnno
    private String mobile;
	/**????IP?? ????*/
	@Excel(name = "申请接入IP地址", width = 15)
    @ApiModelProperty(value = "申请接入IP地址")
    private String ipList;
	/**??????*/
	@Excel(name = "模拟器供应商", width = 15, dicCode = "sys_ins_device_supplier")
    @ApiModelProperty(value = "模拟器供应商")
    @JsonSerialize(using= ToStringSerializer.class)
    @Dict(dicCode = "sys_ins_device_supplier")
    private Integer simulatorVendor;
	/**??????*/
	@Excel(name = "应配设备数量", width = 15)
    @ApiModelProperty(value = "应配设备数量")
    private Integer deviceTotal;
	/**??????*/
	@Excel(name = "实际设备数量", width = 15)
    @ApiModelProperty(value = "实际设备数量")
    private Integer deviceQuantity;
	/**?????? ????*/
	@Excel(name = "驾校编号列表", width = 15)
    @ApiModelProperty(value = "驾校编号列表")
    private String inscodeList;
	/**??/??*/
    @ApiModelProperty(value = "启用/禁用")
    private Integer enable;

    /** 地区 */
    private String district;

//    课堂容纳人数
    private String classStudentNum;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
