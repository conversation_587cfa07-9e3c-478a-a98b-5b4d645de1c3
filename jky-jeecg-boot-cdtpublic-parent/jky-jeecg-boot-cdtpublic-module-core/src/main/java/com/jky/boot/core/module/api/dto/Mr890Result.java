package com.jky.boot.core.module.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@Data
public class Mr890Result implements Serializable {
    public static final Integer SUCCESS_CODE = 0;

    public static final Integer FAILED_CODE = 1;


    private int errorcode;
    private String message;
    private Object data;

    public static Mr890Result success(Object data) {
        return new Mr890Result(SUCCESS_CODE, "success", data);
    }

    public static Mr890Result success() {
        return new Mr890Result(SUCCESS_CODE, "success", null);
    }

    public static Mr890Result filed(String message) {
        return new Mr890Result(FAILED_CODE, message, null);
    }

    public static Mr890Result filed(String message, Object data) {
        return new Mr890Result(FAILED_CODE, message, data);
    }

    public static Mr890Result filed(Object data) {
        return new Mr890Result(FAILED_CODE, "failed", data);
    }

    public boolean successed() {
        return Objects.equals(this.errorcode, SUCCESS_CODE);
    }
}
