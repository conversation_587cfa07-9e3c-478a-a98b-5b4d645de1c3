package com.jky.boot.core.module.gzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jky.boot.core.module.gzpt.entity.AttachmentInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AttachmentInfoMapper extends BaseMapper<AttachmentInfo> {
    int deleteByPrimaryKey(Long id);

    int insert(AttachmentInfo record);

    int insertSelective(AttachmentInfo record);

    AttachmentInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AttachmentInfo record);

    int updateByPrimaryKey(AttachmentInfo record);
}