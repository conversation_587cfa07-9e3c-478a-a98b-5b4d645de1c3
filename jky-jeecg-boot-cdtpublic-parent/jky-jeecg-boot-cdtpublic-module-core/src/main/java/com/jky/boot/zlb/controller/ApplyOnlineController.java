package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.vo.InstitutionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 在线报名
 */
@RestController
@RequestMapping("/zlb/applyOnline")
@Slf4j
public class ApplyOnlineController {
    private final IInstitutionService institutionService;

    public ApplyOnlineController(IInstitutionService institutionService) {
        this.institutionService = institutionService;
    }

    /**
     * 在线报名首页
     *
     * @param areaCode     区域码
     * @param name         驾校名称
     * @param lon          经度
     * @param lat          纬度
     * @param distanceSort asc”升序，“desc”降序
     * @param priceSort    “asc”升序，“desc”降序
     * @param pageNo    页数
     * @param pageSize  每页大小
     * @return 驾校列表
     */
    @GetMapping(value = "/searchInstitution")
    public Result<?> searchInstitution(@RequestParam(name = "areaCode", required = false) String areaCode,
                                       @RequestParam(name = "name", required = false) String name,
                                       @RequestParam(name = "lon", required = false) Double lon,
                                       @RequestParam(name = "lat", required = false) Double lat,
                                       @RequestParam(name = "distanceSort", required = false) String distanceSort,
                                       @RequestParam(name = "priceSort", required = false) String priceSort,
                                       @RequestParam(name = "score", required = false) Integer score,
                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                       @RequestParam(name = "trainType", required = false) String trainType) {
        IPage<InstitutionVo> res;
        if (StringUtils.isBlank(trainType)) {
            res = institutionService.searchInstitution(areaCode, name, lon, lat, distanceSort, priceSort, score, pageNo, pageSize);
        } else {
            res = institutionService.getInsByTrainType(areaCode, name, lon, lat, distanceSort, priceSort, trainType, pageNo, pageSize);
        }
        return Result.ok(res);
    }
}
