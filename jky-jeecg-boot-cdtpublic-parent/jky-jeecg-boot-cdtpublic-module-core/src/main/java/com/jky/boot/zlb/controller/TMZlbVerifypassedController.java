package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.zlb.entity.TMZlbVerifypassed;
import com.jky.boot.zlb.service.ITMZlbVerifypassedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: t_m_zlb_verifypassed
 * @Author: jeecg-boot
 * @Date:   2023-06-20
 * @Version: V1.0
 */
@Api(tags="t_m_zlb_verifypassed")
@RestController
@RequestMapping("/zlb/tMZlbVerifypassed")
@Slf4j
public class TMZlbVerifypassedController extends JeecgController<TMZlbVerifypassed, ITMZlbVerifypassedService> {
	@Autowired
	private ITMZlbVerifypassedService tMZlbVerifypassedService;
	
	/**
	 * 分页列表查询
	 *
	 * @param tMZlbVerifypassed
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_zlb_verifypassed-分页列表查询")
	@ApiOperation(value="t_m_zlb_verifypassed-分页列表查询", notes="t_m_zlb_verifypassed-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMZlbVerifypassed>> queryPageList(TMZlbVerifypassed tMZlbVerifypassed,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TMZlbVerifypassed> queryWrapper = QueryGenerator.initQueryWrapper(tMZlbVerifypassed, req.getParameterMap());
		Page<TMZlbVerifypassed> page = new Page<TMZlbVerifypassed>(pageNo, pageSize);
		IPage<TMZlbVerifypassed> pageList = tMZlbVerifypassedService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param tMZlbVerifypassed
	 * @return
	 */
	@AutoLog(value = "t_m_zlb_verifypassed-添加")
	@ApiOperation(value="t_m_zlb_verifypassed-添加", notes="t_m_zlb_verifypassed-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_zlb_verifypassed:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMZlbVerifypassed tMZlbVerifypassed) {
		tMZlbVerifypassedService.save(tMZlbVerifypassed);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param tMZlbVerifypassed
	 * @return
	 */
	@AutoLog(value = "t_m_zlb_verifypassed-编辑")
	@ApiOperation(value="t_m_zlb_verifypassed-编辑", notes="t_m_zlb_verifypassed-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_zlb_verifypassed:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMZlbVerifypassed tMZlbVerifypassed) {
		tMZlbVerifypassedService.updateById(tMZlbVerifypassed);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_zlb_verifypassed-通过id删除")
	@ApiOperation(value="t_m_zlb_verifypassed-通过id删除", notes="t_m_zlb_verifypassed-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_zlb_verifypassed:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tMZlbVerifypassedService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_zlb_verifypassed-批量删除")
	@ApiOperation(value="t_m_zlb_verifypassed-批量删除", notes="t_m_zlb_verifypassed-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_zlb_verifypassed:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tMZlbVerifypassedService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "t_m_zlb_verifypassed-通过id查询")
	@ApiOperation(value="t_m_zlb_verifypassed-通过id查询", notes="t_m_zlb_verifypassed-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMZlbVerifypassed> queryById(@RequestParam(name="id",required=true) String id) {
		TMZlbVerifypassed tMZlbVerifypassed = tMZlbVerifypassedService.getById(id);
		if(tMZlbVerifypassed==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMZlbVerifypassed);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMZlbVerifypassed
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_zlb_verifypassed:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMZlbVerifypassed tMZlbVerifypassed) {
//        return super.exportXls(request, tMZlbVerifypassed, TMZlbVerifypassed.class, "t_m_zlb_verifypassed");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_zlb_verifypassed:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMZlbVerifypassed.class);
//    }

}
