package com.jky.boot.core.module.gzpt.enums;

public enum TrainTypeEnum {
    A1("大型客车", "10", "4", "6", "6", "26", "20", "16", "2", "14"),
    A2("牵引车", "12", "4", "8", "6", "30", "22", "18", "2", "16"),
    A3("城市公交车", "14", "4", "10", "6", "41", "33", "20", "2", "18"),
    B1("中型客车", "10", "4", "6", "6", "26", "20", "16", "2", "14"),
    B2("大型货车", "14", "4", "10", "6", "44", "32", "22", "2", "20"),
    C1("手动挡小型汽车", "12", "4", "8", "4", "12", "22", "10", "2", "8"),
    C2("小型自动挡汽车", "12", "4", "8", "4", "8", "22", "10", "2", "8"),
    C3("低速载货汽车", "12", "4", "8", "6", "8", "16", "8", "2", "6"),
    C4("三轮汽车", "10", "4", "6", "6", "4", "10", "8", "2", "6"),
    C5("小型自动挡残疾汽车", "12", "4", "8", "6", "8", "24", "10", "2", "8"),
    C6("轻型牵引挂车", "4", "4", "0", "6", "10", "4", "6", "2", "4"),
    D("普通三轮摩托车", "10", "4", "6", "6", "4", "10", "8", "2", "6"),
    E("普通二轮摩托车", "10", "4", "6", "6", "4", "10", "8", "2", "6"),
    F("轻便摩托车", "10", "4", "6", "6", "4", "10", "8", "2", "6"),
    M("轮式自行机械车", "0", "4", "0", "6", "0", "0", "0", "0", "0"),
    N("无轨电车", "0", "4", "0", "6", "0", "0", "0", "0", "0"),
    P("有轨电车", "0", "4", "0", "6", "0", "0", "0", "0", "0");


    private final String traintypename;
    private final String time1;
    private final String time2;
    private final String time3;
    private final String time4;
    private final String time5;
    private final String time6;
    private final String time7;
    private final String time8;
    private final String time9;

    TrainTypeEnum(String traintypename, String time1, String time2, String time3, String time4, String time5, String time6, String time7, String time8, String time9) {
        this.traintypename = traintypename;
        this.time1 = time1;
        this.time2 = time2;
        this.time3 = time3;
        this.time4 = time4;
        this.time5 = time5;
        this.time6 = time6;
        this.time7 = time7;
        this.time8 = time8;
        this.time9 = time9;
    }

    public String getTraintypename() {
        return traintypename;
    }

    public String getTime1() {
        return time1;
    }

    public String getTime2() {
        return time2;
    }

    public String getTime3() {
        return time3;
    }

    public String getTime4() {
        return time4;
    }

    public String getTime5() {
        return time5;
    }

    public String getTime6() {
        return time6;
    }

    public String getTime7() {
        return time7;
    }

    public String getTime8() {
        return time8;
    }

    public String getTime9() {
        return time9;
    }

    public static TrainTypeEnum getByTrainType(String traintype) {
        switch (traintype) {
            case "A1":
                return A1;

            case "A2":
                return A2;

            case "A3":
                return A3;

            case "B1":
                return B1;

            case "B2":
                return B2;

            case "C1":
                return C1;

            case "C2":
                return C2;

            case "C3":
                return C3;

            case "C4":
                return C4;

            case "C5":
                return C5;
            case "C6":
                return C6;
            case "D":
                return D;

            case "E":
                return E;

            case "F":
                return F;

            case "M":
                return M;

            case "N":
                return N;

            case "P":
                return P;

            default:
                return null;
        }
    }
}
