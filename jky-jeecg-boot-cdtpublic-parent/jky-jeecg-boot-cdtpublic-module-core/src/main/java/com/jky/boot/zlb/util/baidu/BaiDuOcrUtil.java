package com.jky.boot.zlb.util.baidu;

import com.alibaba.fastjson.JSONObject;
import com.jky.boot.common.utils.HttpClientUtil;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.net.URLEncoder;

public class BaiDuOcrUtil {
    public static final String API_KEY = "TZOb6sto504AZz8uWvo6jTdK";
    public static final String SECRET_KEY = "NBgXGPd2fu37CRkAG3ZV01Ygw94DbfTI";

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    public static void mains(String []args) throws IOException{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // image 可以通过 getFileContentAsBase64("C:\fakepath\11111.png") 方法获取,如果Content-Type是application/x-www-form-urlencoded时,第二个参数传true
        RequestBody body = RequestBody.create(mediaType, "image=iVBORw0KGgoAAAANSUhEUgAAAlgAAAEsCAYAAAAfPc2WAAAAAXNSR0IArs4c6QAAIABJREFUeF7tnQf0PUlV56%2BSBiQzwJCVRUkqC4usIDlKElhkAAeBBSWHhUPOQQTBJWcXGBgkLEHJYcgsAosERQm7wC5IEAaEJQ7Jtb9DN7x5vO661X2rq1%2B%2FT53zPyP%2Bqu%2B99anq6vuqbt36BaNAAAIQgAAEIAABCIQS%2BIVQaQiDAAQgAAEIQAACEDAcLAYBBCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQwMFiDEAAAhCAAAQgAIFgAjhYwUARBwEIQAACEIAABHCwGAMQgAAEIAABCEAgmAAOVjBQxEEAAhCAAAQgAAEcLMYABCAAAQhAAAIQCCaAgxUMFHEQgAAEIAABCEAAB4sxAAEIQAACEIAABIIJ4GAFA0UcBCAAAQhAAAIQOBQH6%2Bpm9kgzu4SZndrMvmJmXzazf27%2F%2BwUz%2B9rG%2F9bf9O8EhggEIAABCEAAAhDIJXAIDtbVzOx4MxvT1h%2FtcMY656tzzrr%2F%2FdVc%2BNSHAAQgAAEIQGCdBMY4HftG4q1mdtUFGf3%2F25WyJ5nZU8zsuwuyDVMgAAEIQAACEAggcAgOlrb%2BzhrAqoSIE83stWb2sva%2FOFslKCMTAhCAAAQgMDOBQ3CwvmVmp5%2BZ6xh13zez15vZi83sdaxsjUHIMxCAAAQgAIFlEDgEB%2BsTjdNy4WXgdluhlSw5W6xsuZFREQIQgAAEILAcAofgYP2hmb1gOciLW0KMV3HEKIAABCAAAQgMEzgEB0sE%2FtjM7tk4Whds0zQcyrggxutQepp2QgACEIDAoggcioO1Cf1IMzvnxr%2Bjev7vc5jZKRfVW9ON%2BbyZ3cPMXj5dFBIgAAEIQAACEOgjcIgOVs5oOHvrfOm%2F5zIzOWNnM7PztP9%2FrYid38yOyBG6gLq3bex%2B7gLswAQIQAACEIDAKgngYM3braczs%2BuZ2dFmdm0z0%2F%2BuUXSy8ow1FKMTAhCAAAQgcAgEcLDq9bKcq98zs5u0ztZpZzZFqSu%2BM7NO1EEAAhCAAAQOggAO1jK6eXNl67ozbTm%2Bysyu2Tb%2Fze1djR9cBg6sgAAEIAABCOw3ARys5fWfnK27N5dP37WN%2B5rLwu%2BZ2RXMDCdrLuLogQAEIACB1RLAwVpX18o5O2FCbJdWtW64LiS0BgIQgAAEIDA%2FARys%2BZmX1HiMmb1wggKtYtUKvJ9gNo9CAAIQgAAElkUAB2tZ%2FZFrzU3N7HFmdr7cBwfqMyYCYSIKAhCAAAQOkwAf0%2F3t96s0MVNvNbOcPtQ1Oqn6qb%2FvLzEshwAEIAABCMxEgI%2FpTKALqPnciJWr2zeB889K2MKYKNBZiIQABCAAgcMiwMd0P%2Fv7zM1Jw69nmv4eM7u8mWkVa6gwJjLBUh0CEIAABCCwTYCP6X6Oif884qqbS5nZh3Gw9rPDsRoCEIAABPaLAA7WfvXXpVtzH2ZmSkjqLV8ys3O3lVnB8lKjHgQgAAEIQGAkARyskeBmfkwrVk9o%2Fp1phF45VNcys%2BNxsEbQ4xEIQAACEIDACAI4WCOgzfzIHczsGSN1fqNJPHpnM3vRxvOsYI2EyWMQgAAEIAABLwEcLC%2BpOvWuama6J%2FAUGepfZ2ZHm9kFzOzjO57DwcqASVUIQAACEIDAGAI4WGOozfOMnKvXmtlpM9VpO%2FHYgWdwsDKBUh0CEIAABCCQSwAHK5fYPPVv1eS4ek7mylVn2VnMTFuDfQUHa54%2BRAsEIAABEVBS6D81s0ua2b%2B28bBPbhNFQ2jFBHCwltW5Nzaze5nZb480S8lHtTU4VHCwRsLlMQhAAAKZBK5oZu%2FouUHjDyfeHZtpCtXnJoCDNTfx3fquY2bHmdlZJ5gjx%2BnqZvY2HKwJFHkUAhCAQByBN5nZNXvEfcDMLhOnCklLI4CDVb9H7mpmT3LcEThk6afM7G5m9gZHc1jBckCiCgQgAIGJBPR9%2FbaZna5HjkI5FNJBWSkBHKy6HasVJ50SzOmHE9sko6mVqr6W4WDV7XO0QwACh0Hg7Gb2lYGmyvk6w2GgOMxW5nzYD5NQuVbrlKBSKhyRoULOkZab35LxzHZVHKwJ8HgUAhCAgJOArif74EDdTzcxWBdyyqLaHhLAwZqv07RMrO3Au5vZUZmrVrJSjpG2AZ860WQcrIkAeRwCENhJQKflNL9pZV65%2Bz5iZg8ws7cfKK8bmNlfD7T9nU0A%2FJUPlM1BNBsHq2w3X75ZbdI%2F3SGouwNzVqs2LfsXM7tlu%2BI1xWLp%2F15CwC86LoSeYgPPQgAC6yPQdwG9ftDJiXjX%2BpqcbJFu0Rj6QfxCM9NJQspKCeBglenYY5oTgcpzMuVUoCx7n5n9uZm9IshMbUu%2BdUCWJkM5WBQIQAACOQT%2B98B2l%2BJMdR%2FqoZXHND9o7zvQaP39%2FocG5ZDai4MV29vaBnyYmd17otgfm9ltzez5E%2BVsP%2F4IM3vwgMyvNb%2B4jgzWiTgIQOAnBDTf6v06YYVAvtX8GDx9T7sONZhbK1T6sd1XtML19BWOBZrUEsDBihkK%2F8nMnthkXz9fgDht4V3Pkc9qjCot019h4MG%2FMLPbjRHMMxCAwM8RUBySEk3ezMxuZGY6VTal%2FHP7400f7qUVzVtDIRBna2JIFepwSEUxVur%2FvqIYrVcfEpBDaysOVl6PK5ZK5W%2Fb%2F17YzJ5gZtfOE9NbWytXOiU4NgXDkBma%2FPQr85QDlbSFeKgBqUFdiJhAAtvvW6DoIqI6h0ofVf2Q0X9PVUDTEjOAf9bMzj%2FQ1t80s48WYLFkkToleMEBA3XK8MNLbgC2TSOAg%2BXjpwBOOVJn8lUfXeuOzQnDZ45%2BevjBVPzVj9qcLMqzRSlD4D%2B0W7RyonMv8S5j0bDUWism0e9bqXbM5VBt95Lac64aA2JA53sTV3wpBkuxWIdS9G39QeIH7TlWul18KH2cbCcOVhKR3cHMnpGuNqnG%2FzOzezT3CD5vkpThh%2F9bG9fVV0vbh1cqqP%2FQRcu5eveeOFbbfTXXiolyAh3drPo8qtBgmdqOWg7VLhxLm7tf2W6D9nWdnOZjC%2FXrEsXKefrygGH6QVtidXOJLA7WpqW9pEvoiKn5qobaoNWh15rZy9r%2FXqyt3G05lmz%2FV81McRB9RQHwDy1pQAHZ2pLQEXDl35Fz%2BCtm9sP29KU%2B0roHbClF%2BXAUc7GPpfSKyfWbrZQXNHGMZy4MZ2w71G%2F6oXW1BX0UlzZ3Kx2Bgrb7ivJhPbpw%2Fy5JPElGl9QblWxZ2ktaCcNJd0UpsFy%2Fnqfkq%2BqzX%2BkP%2FqQJAtWx3O9WaKQn%2F5U%2BHiVivyKbu8uh6pOveDZdpPqhSAMmyFK%2F78O2YF8TS80VOlTxrAlccx%2FNbYcSZ%2BoAy9JKbjtK2%2F%2FAdo7r0yMHTImWD6WQZPRQenqgnUt7SefqkluY2ePajOpz6CwZW%2BWxX%2FEPb0xU1Md%2FafFXnUOlVSr90wpVTnm5md0k54GCdXGwfh6ukudqW3zO3Gu5c57ukpt6%2Bq%2FEsMptRwkbNmX2JRrt6mgL8caljViQfJKMLqgzapmytJd0Dg5yro6bQ1GbNV0vWnd6Zo6twF1NS%2BW%2F0vbhEj4iUx2q7bbrWPjQtuhMw%2BAkNfu8RSj7o%2BcKrehq1WPuktOO1GW9c9u%2BqS%2BnHXPYmfoRpyD4y81hyEJ0kGR0IR1R04ylvaRzsPjSjCtX2%2B2ZI5h9F8NU%2Fitt0SjGpEbR9qyyHd%2BpUJJTpaXQdmHtss9B7pEO1qmbjN%2FK41RrZTFnzlNdBSNPWWFTTKDePx1w0H%2F1zzMe9%2B3OUKVh%2BLuBl0xpHH659ks4o36SjM4Ie6mqciabpbYh167UxJUrb0z9ObcMl5j%2FajPmTQHO%2BuiWKB9rDhRcvITgkTL3LU1D18yxweHbmLSa%2BPo2Nm4kwsmP5c55crB0etBbxjpU2%2FJT81RuO7z2j62XylAvLqXe87E2l3zug00SUQW69xWSjJakvxDZS3tJ58CSmrjmsCGlIzJvTyr%2FlWwRkzWOBWXQfmkK9oH%2F3bOyNjW9gRD%2Fmpkdn0hG2dcVOT9IUu937jhPyYtyqPbdwZL9yvs0lHpATpiu4zqEouuBfmmgoSQZPYBRkDvZrAFJzS3CXH65HzatDF3EzD5iZv%2FaKlMw%2F71yFe95fW3DPHwPTkUuBXO3sva7zVH602wYFeXoX75NS5KbqHfMlnrKIcqd81LySm1Bp%2FTmtmOOsUY2959RTvUfSUbnGJGVdSzxJS2NZM4g96lt8W7NRN6FONXmGs9%2Fpjll%2BI72n%2B7%2F%2BlwNI9C5k4BOCirJrTep4vublRDlTPrmxpVUOWhTH7bcOS9anrcttfR67dtVj2zufgcrdxxO6ReerUTgUDt57jQNU7p3qI%2Bi70KcYuecz%2BJQzUl7vK7ck4I6yn%2FzdqtprNZoxyRanrddtfR67dtVj2zuOFhTxs%2Fqnj1UB6tER85xpU4Ju%2FdB5v%2FZWKHSShUrVMvuNQUzv9jMtLLqLcq8%2FyBv5YF60Y5JtDxvE2vp9dq3qx7Z3HGwpoyf1T2LgxXbpdGX1MZatz%2FSFCCqgGjljsKh2p9%2Bk6UKZNZ1UP%2FRabaCxLWN%2BBJn%2FVS1aMckWl7K%2Fu7vtfR67dtVj2zuOFhTxs%2FqnsXBKtOll27FKuBcv%2Bpyg3vLWLVcqdt3NNa4Tmi5dPbHstyTggpi1xVV%2FyOwidGOSbQ8b1Nr6fXat6se2dxxsKaMn9U9i4NVpkvlYF27SVCoDOqUfgI60fkUM3tSpTsa6Zs4AnKu%2FiYjc77i6PSO%2FK84E06SFO2YRMvzNreWXq99u%2Bqlsrn%2FvZldYoqCPXp2H%2Ftvj%2FDuh6k4WLH9tG9bhKwcxfb%2FIUv7RBN3pUMXnqIrjOSQlciJFP1hi5bn4VPCUfTqnVIvlc1dK9NDuaGm6F7as7XGzdI4HLQ9OFhx3b9vQe6KbzqGlaO4AXDAkuQsfTKj%2Fd8zM%2BVsK1GiP2zR8rxtrqXXa9%2Bueqls7nrmUL45%2B9h%2FU%2FqeZ3cQOJTBXrrzb2Vmz1vA5KFVhEeb2TmbD9hjE40%2Bi5l9ozQY5B8EgQuY2f%2FNbGmpuSf6wxYtz4upll6vfX31ath9w%2BaH4n2awxUKzVC%2BtQ%2Bb2dPb%2FGuyc1%2BvqJraF97noxIKe%2FUdTL1Sk9zaAXZB7N8ysyc3sSfX3LMGf97MzrdnNmPusgmkkkxuW19q7on%2BwEfL8%2FZiLb1e%2B5biYF2xPWm8azzd1Mw%2B3V60fdqpDTuA53NvDjkAJNOaWGqSm2bVcp%2FetxirXJKKkXiLmb2C9Ai56KrW3%2FyFLkPe3FxQ%2FEgz04WzcxU57HKyzuNUWGruiXZMouU58YQH63v1Tq03N6%2BXm9mNe4zWQYqPNitauliZkibgvTkkLYkaJxEoNcmtEe%2B%2BxVhF9AEJPiMolpXRd1mz4pyuMLOTpa1p5bO6sqPJpeae6A98tDwHmpOq1NLrtW9XPfVpdwdqn5xTOOp4bfjFNszhDD0P%2FLi9FYDVKy9RfAI%2FKUfNUpOcQ%2FVeVblrm0rg0Hnp1OHb248oCUCXMYR1WKHvF%2Fqrmou%2FFZ8yd%2FllM5NzPlRKvUvRjkm0PG9f1NLrta%2Bvnhysob69uJl9bKqS9vnUqcUvm9kZzQwHyw%2B81Hvpt2BFNYG5uzO7GKu%2FbY6Sy7lSnBXl5wmwwlV%2FVGhbt%2B8DUvK0XqrltRyEaL3R8lLcur%2FX0uu1r6%2BeUm%2BcdUDIrc3s%2BVOVtM%2BndhXeama6FYItQh9wtgh9nNy1cLBOjioyxkoT5GPMTBfeejOT6%2Bi6MlsfbWbXbZ4%2Fwt2Ty6jIJczz98NSP8S17IrWGy3PO0Jq6fXa11fvpe381fd3JRa%2B21Ql7fPHNiulOsHdVzT3aoX33axiuYgT5O7C5K%2BEg%2FUzVncys6f50Q3WVCJF3a%2F2ugny5GzdvV1BO9cEOTUfzXW4NlcOa9q9T7qX%2BiGuZVe03mh53rFVS6%2FXvr56WqFSypq%2Bomz%2FvzNVSfu8cq8pB1tf0Y%2FU15OmIUmbNA1JROMq4GD9hNvNmxf1ReMQnuwpTR6Pb0%2FhBYhLilB8gZwS%2FVOws%2F79u%2BRT9SoohkuM3tis1Gn7Vafcvtmcertce2XOpVrTPtQ6lqpLGSaw1A9xLbui9UbL847nWnq99vXV%2Bw0z05U4fUWr%2BQpKTwXDp%2BzQ3Ke7LIeK7oDV%2FFKj7Gv%2F1WC1Wp2H6GDpFMtF2x79uJkpV8oLJ56o%2FFzzst%2FDzF65gJGSerEXYOLJTPiCmR3VpBVQv2wWxQ9pu%2FRtSzN4Yfak%2BrvWO17Lrmi90fK8w6eWXq99ffV0sk8%2FpJTws6%2F8upn940RFWp167YCMz5qZDlvUKvvaf7V4rVJvrcm3Fkx9sHWMPOo%2BLGVNz4mxmqPdqRdbNmjrUiy6cv72aL2O11%2Bl8sSEk5U3SlL9Xesdr2VXtN5oed7eraXXa99QPa08X3agQkSgu%2BbdByaMrbkSvs%2F9FzEGkDFx1WbfAN6kWblSAGbEB0cvj%2BKjFLC5tJJ6sTt79etOv%2FJ2lc7h%2BoMmLcOVKgfbayvhUe2BAe9hgaX1SUl7Uv0dMd7H2F%2FLrmi90fK8LGvp9do3VE%2Fz4l0GKmgevlmGorO313%2F9w8YzOiF4VYcMrYRfvQ1NcFQPq7LP%2FRcG4dAF1Zp85%2BauLUHdT3WaIMV3bLa1nhkkK1pM6sXu9D3UzB7hVL65wqVVrl9xPle72iEEb6b6u9Y7XsuuaL3R8rzvRC29XvuG6qUC3XUI6GwORbdrfhA%2FvA0hcFTvraKVLMWnzln2uf%2Fm5LRqXbUm35JQt2OsfrX5pfOu5oSgfgVNLQqqVKzV0CmZqTqmPH%2FmZmXt604BymF1QWfd7Wr75nCt%2BfjxUifyWnZF642W533laun12jdUTzFWuqKmr6htitXqyvYKlX7EybHSPYNR5bfagzVR8lJy9rn%2FUm3j704Ca3OwomOsnBhHV4teYVEer%2BdmWKPtPzmfU8tSY7i6dq05gd5SJ%2FJadkXrjZbnede02qJTtkNlyXO3fuT%2ByGF%2F1AqVhykOlocSdUIJLPklzW1oZIxVru6p9aNWWHSqRqdrvEUrcbfxVs6o1zlcuoT1aoGHCjJM%2BLmqaxrrm42r4QB4%2BqGWXdF6o%2BWl2PXdLbn9nA6j6LqqpZYUN%2F2wi1yhGuLAFuFSR8nK7drXj84tmo%2F24wL25pfSvRErLMot842tpfdU%2B75jZke2x6pTdaf8fQn5uvZ1rKe4pz5ktdpdy65ovdHyUv05dLdk6tnu79Er4169OY7%2FGJljniHIfQw1ngkhUGvynWK8nKvjpghY6LNT%2B0IxCw8Z0TZdNfGCEc9NfWTT6dJpIGV3Pv1UoT3PRziwhUybLHZuB8BrcC27ovVGy9vFr1vx1aqU3sepc0GnI2pl3NvnXT3djbmEE78%2Fbk4PXrNSLr05xk1uv1B%2FZgJRL%2FKcZn9pRStXm9ym9oWciHOO6Ii3O487jxCd%2FYiuB3pqkz5CsWSRpdaHJrINfbKWOpHXsitHb%2BfY%2FH67la3xt6Yy5w8LJRa9X5u%2BxnNCcA7ONU9754zDOVigowKBqR%2F1CiZbauDWsClC59S%2BUL6oMTLE87xm9sWIRgTJuKuZPWlkezZNEBOtCChT%2F1pL6n0YMyYiWNWyK6VX40ErRTrgsS%2FpRqb0R%2Bn%2BP4uZ3b65kuaeQSe1p7S1e3YJp71T47B0v0RwRMZEAvvYyamBOxFJtcen9IV%2BMX51guU5ObEmqMl69A7Nitwzsp74%2BcprXrnqWpt6H6aMqyn457ar23JWAkrKzwiU6v8LtSlrtNqsLcGSRQHxmqMU1H9CGzfap0%2B3azygpDFO2XOPf6dZVJuTQKmXr2Qb2CLcTTf1Qg%2F1yZScWCX7WpP3E5p%2FurQ1pywhyDfH3il1U%2F1e6x0vaZfyt2kVSj8s9uGS8yn9O%2BXZEluEylGlXIDXD1hhTrVN9suxevZGxdRKvS6b3sz4ntJR6u8lx38pm5EbTKDW5DulGSWC3N%2FQThqfnGJY4tnSL1xKfqppUTmxUnrG%2FP3S7UOp3EBjZM%2F5jFJW6DDCpdprij7YXlh7vJl9baQhqX6v9Y6XsOuxZvbHZqaEupQ0gagVXMVX6WobbQP%2B%2B7Ta7BqbK1R6WIlKv9yuVm0KU3JSBa4PlVM66mQbOOKBEuN%2FhBk8UpNArcl3apuj0jR8rnWsXjnVIMfzJV84TwZ3pWQYuuS6VE4sB5qDqKJf1h8YuK5JuXq0vfWmJuD63c02xw%2BcVEqOK6cJO6tF2qVVEx1%2BuPgUgw7o2agV3LM2sZlKBno3MztXIX45geiXNDO9J0NFq5q6iqd2iRz%2FtduC%2FpEE9tXB6muuTtFpYH8lwUMfu0%2FPfJS45AuXyuAu3c83M90R1le%2B364MnDhyLPHYMIGXmNlNnZCUu%2BedTeoKrWy9ObHlUXJcOc0Nd7C6033iJedqbaf7pnDtni25FabrxbQNqPmidHzVOXasVPXxkXMlJ2uoKB%2FgtyMAT5Sx1PdyYrN4PIfA2hysnLbPXbfkC5fK4K7ldn2sUpmfNak%2BcW4wB6JPW4BaERhTdMKzc7a0wrW5nVhyXI2xtXsmZZe2gf6xrbyZB%2BpQTvftYqvcUW8xs1e0P4iG%2BJeYu%2BXMahtQ8VVzFe%2BWnq5Be03CqC%2B0J6Lnsn1IT2r8l%2Bi%2FJbQbGzYI0MnzDYdSL5wng7vifh5mZp9JHE3Xyt%2BYXFrzUdxPTVqB0RZtVNncTtQHee4PsacdqfHukTFXHR3y0I%2BP7p9CB7yOYqk5NMUvUq%2FiqhRIrvv6IopCLpRmRVt1Q5c%2BS5e3HZ83s%2FMkjDvGzF4U0YAAGXP2X4C5iChBwDu4S%2Bg%2BNJmlXri7NKepnjIAU6duFKP1rfZ6oXslwCugXMHXlFgCWkXUdsjcpdY7nhrvc3PY1Le5UiSnatOh2rYr1Y5SfOfSe%2B3m0MWrzUwrSVOKtrWf066AK%2FxCRTmyUvFQqqMrvobKZczs%2FYk672220y83pQHBz87Vf8FmIy6SQKnJIdLGtcgq9cL9U2JZ%2FHVmpuV1lYs0W4UfTwBVILYmNEosgTs1iRifFitykjTF3L3RzB5ZyKFOjfdJxk94OHfOS7UjV57X9Ln0avt5SgC70uY8uc1ZpwSf2yXVDsWPHpuAIucqNSdd3sze0yNHqTwe3F6bUzqmzNu%2FpcaNVz%2F1ZiBAJ88AuVWRmmjG9IXn9KASdj5ro5mePGLXaoOr56NzGJqu0WzT3tLMxPfsC2myVh6uEOhkXczM7m9mOulbumjb9X3tyUul8NDK666P%2FKYdue9ZiffWw2UOvVpR1crqmPLhdkX8ZWb2owEBqSu8Nn8A7hKjVBOpu1K1fXi%2BAedKp3KX4lh1Zl4gsXI6pk94ZmEEciebhZm%2FV%2BaUmDB1pYx%2BPQ6V7SV4BbBqS2CoaMtEEwClHAEFeesiWv27YuUPwKvM7IYTm6p2aPtZTmTpIkdKunYd2oh%2Bz6LlednMoVfzv7bwvHnFFG6gAzWPb0%2B5etqSuoR%2BM4Rhl7zUCr2euXqb4mTX839tZjfwGEqdXgJRaT8ODjEO1nxdXmLCTJ0e7Ptlp4Sqv5ZourJlK%2FiXUp7AadpVJH0oftfMLlFe5ck0aBVrbCoE3e13XzO76Aw26%2FTko9rs%2Fn3qot%2BzaHleTHPpVXC7krcOlS6%2BSrcq6KBMTsk5hLMt99TNaqi2soeKVqf0A6WvKN5uaatXOfyWVDcqce2S2lTUFhysonhPJjx6wvRMXFrhUoLG7aKYhFRW9P%2FarhLMRwhNHQElS5SjpdUgrQxNiZHxUM11sHTvnxJQ6kh%2FSdu6032fMjPdtqCE2JWeAAAdPElEQVRtqVSJfs%2Bi5aXs7%2F4%2Bl16lDlFc5q4DGEp7oPlDd4Kmtl6H2qVtwOsMVOi70keO%2B2MSwHQrwtC4wMHyjrh0vRJXL6W17nENHKz5Oi96wkwtvetKCW0P6vTgrpKKxdLfz2tmWsKn1CVQejvRu0WoY%2FLamtOKx9CtAGNp5Zzu69MR%2FZ5Fy%2FOymVOvVoB0AEPjTOUj7TbgixPxVd62eBIh6xqczZJ6pqur1BJDPxbZIvT2kq8ePoOP00m1gJUBa2LV6AlzavCotnZSp3cUl6OPL2U5BLSdqBNTWtlSsPzU7URPkHsXuK5ktbqXLqKUOkgR%2FZ5Fy%2FOyq6FXK9unaFab%2FqfXSGc9z2EcrYpu%2FhhU6gbPJe8pB0ttWmKQuxPd4qrhM2R0CbAyYE2sGj1hpm6VTx1%2F1i9GnSA6cqBdr2%2Fuz7vuxHbzeFkCqXHVp92TpqFk4HqpuSfFI1dvtDzvaKil12tfbr2c9igXn9LFpIq2LT0B%2BktM05Bq2xL%2FzhZhZq%2FkTjaZ4qm%2BQSBngkmBU4zOVxOVPAn8lAPpQQNyZLO2hbRdSFkmgchx1bVQH7i%2FMDNl%2BC5VSs090Tyi5Xl51tLrtS%2B3Xk57lLPqEQ4Ft2lOO%2BuS%2BqUVXf302aUZFWAPQe6ZEEtNcplmHET1nAnGAyRCnlIxKJB4aBzICXuIxyDqVCEQMQ42DdeRdsWtlC6l5p5oHtHyvFxr6fXal1vP2x7lTzvOIfyOZvZMR71aVVLtrWXXGL2kaRhDjRiskdTGPZZ64XI%2FOFHydDpLJ9b6ilbKdD8hwe7j%2Br30U1HjoLMzFduXao9OpP2ZI74vd7yn9HZ%2Fj%2BYRLa9WO7x6S9XzckwdvunsKzV%2BotrvbW%2BUPuQskMDSB%2BkCkY02KfqFi5KnQPa%2FSrRK17zoqDZleQSixoFappQLujplTNGl0481s%2BPbhyPtyrEnWm%2B0PG9baun12pdbz9ueVD0crFzy1K9GAAdrPvSpiSO3L6LkKdhd%2BW6OGkChYFIFww9diTEfSTRtEogaB5KpsaDTW8qx5ik%2FbBKMvtTMHm1mH9t6INIujy1dnWi90fK8baml12tfbj1ve1L1cLByyVO%2FGoHcj3o1Q%2Fdc8ZXN7O2JNvyGmf1DRjtTE1FO3yoHjlaphorisBSPRVkWgchxoJbpiL6OvqeKciTdu3XOd9WNtitlDw6Wl1Cdet7xkKon6%2FfhNFuqHTnzc50eQ%2BtkAnTyZIQuAR9q8kldMlFTmZRPcEn7SaXIF1i2ycahotUKZU3OcQIzmkPVkQQix4FM%2BKZzBSuVfyjaLi%2BeaL3R8mq1w6u3VD0PR11M7wlFWPpptqPbld0hlnx7S420Bcmlk8t3xp17rqvZ1DzmF5lnwsppnSe4WRme9WFlqzCHbNm6kePgGDN7ocNcOePKLTRUIu1ymPTTKtF6o%2BV521JLr9e%2B3Hqp9ijlwnMdQpe%2Bkn4ZM3u%2Fox18ex2Q9r0KnVy%2BBz2Oy%2B3NTJeu5pTUhJXbt55tTNmn7cS75BhK3aIEIseB7nRL5b5S5nddSv03OFgnEch9z7yDIbJfvTpL1ku158Tm3sEjHAakVk4dIopVUYoJOYme2w5KjZtijUNwPgE6OZ9ZzhOeE3ofNDMldswpHmcoN6ZL%2BpV%2FRpNEqii7u7K8U%2BoTSH24vO%2B4tl1ekGiO7rdUdve3OZodZZdD1cmqROuNludtTy29Xvty6nkzs6dkejO3p%2BSU%2BPvlmsvZ35Mh2PteZoik6tII0Mlle0SXkKa2Um40IrHjO5uPnC5oHSq5MV2Sdcr2iorUKobidJRNnq3CsuPHIz3iQyyn%2BvntKcIhnTkrrRF2edq%2FXSdab7Q8b5tq6fXal1MvysFaauZ2sXi1mV0%2FAwrf3gxY%2B1qVTi7Xc7dr8go9KyFedwEOpUfY9bhH7piYrk6XVr60VaRLX4fKY8zs%2FuXwIdlJYOqH2Js5W7%2FOdcm0t0y1y6sHB2ssqXmf817e3GfVkuebszensL%2BSifMqZvaOzGeovmcEcLDKdZgnI7FimRTTlFM8cnNWGnbpVibu%2BySM0uqVTh9yqjCn9%2BLrTnFkvCtXslonSOV4e8sUuzod3SW9umngNBuKh67uiNC72cZoeVH8ln5VzHY7dfm8J4i9j8%2BSY6%2FkLHm2zbfb9hozezyOlveV2L96OFhl%2Bszzi0YfK320copH7ruanFtXyhG6o662Cr%2FWvPxnTMjhVOFE0AGPj3UAvCtXnYm5c8VYuzadq3c31%2B6cdoDRruP6U%2FVuq4uW5%2Bly75bakrfMdrVTaRiePuJgwJJjr9ROpZZQ28YWHK2x5Bb%2BXO6kufDmLMa8XzezjyasGbNEXEruLlMVT6C4glRZ%2BrHplP37%2FvcxDkDOylUtB0sXTuvi6aGi%2BzFvtZVaYgyPIR3R8jzjzetgLd3x2G6rxt1TzexMHggbdZbsSP62mb03sz191XG0gkAuRQwOVrmeKDUxl5K7i4RiyBTzNVS0VXiJHVellCOL5E0CueMhd%2BVKusbE9OXatd2r302sXm3W31zJmqp3245oed7R641ZWvLW2WZbx4w7sdcNE8%2F0QqtQ7w1mpi3syKKV2790xPBG6kRWAQI4WAWgtiJLTcyl5O4i4T1VyFZhuXGUkpwzHsasXEn%2FmMzZOXbtamOOg7W5kjVV71IcLG%2FM0j44WGPH3d2a08pPSb0AFf9%2BMzPTlVGlilYo74ujVQpvebk4WOUYR0%2F0naWl5PaRuJiZ%2FV2bwmGI1kObY%2F6PKIcTyT0EvONhzArCri04b0d47eqT59ki3H5WjqByuQ2V3Dlvaju8vHbV81wdk9ueKfaMeXbMuJOeu5vZk8conPEZXXB%2B0Rn06TL1B8ygBxXBBJb%2BcgY3d1ZxpSbmUnJTztPDEvTYKpx1eP1UmXc8eE6f7nJYPFfn7Gq5164%2BajpBmApy335WDuEvrsjBUlOmcowYld1pTiWZVXlze%2FG7kiQPlbErV5I5ZtU0oq1eGVpZUuqIOYrGgG5PGHNScQ770NFDAAer3NAoNTGWkjtEgq3CcuNkqmTPeLixmb08Q9GUlatOjceulEn6sCv%2F1maKhtQzqb%2FnznkR7ehualDi4dwSoT9X52b9PkdXVyZdoUmu2edkjV252tS9VCdLMVdaYY0cl6k%2B%2BkFzx%2BG1cbJSmJb199zJZlnWL9uaUhNjhNwxv0i9W4UvNTPFJlDmIZAaD%2F%2FSbLWcNdOUiA9byi7v3BPxod5svldvhKOo61MUQ9SlY9El2Xd13OO4aW8Ux8wh8NPqQ1u1rzIzXQe2XXQwRqkLUquJKZtyD1dc3Mzu16ywifuRrRP0544T3Sk7ur8rLu4JI05B7pL%2F92b2m17FbT1dVXXv1obMR6leg0DuZFPDxn3VWWpinCp37C9S9cODnXFWV21ycb19Xztuz%2BxOjYec5kSsXEU4Jts2T9lq2paVO%2BcNba0OOQD6yL9lx0lIrfzIyVIKlBMcnZPq39z2OFSerMrQYQO15XQbd6mevslx9XDHNV45Nnjb9ztmphyA206dbsu4sJkpYHxK8cTDdfK%2F71zdemwbw5VzxY50KC3E88zsOWamd5ayUALewbtQ8xdtVqmJcarcMb9IO9DercLcX56L7siFG5caDznmR6xclXCwJDNqJSt3zhvSO8RLW2epRMJ6T3Q45NkDnZTq39z25IwH1U3pl%2BOSm9cqxwZv%2Bz7ROlK7ZMsR%2BaMcpVt15RA%2FKSNBqnJ96ZaOVOnymF25OUT0J2YmJzGnKO5V4%2BdPcx6i7nwEvIN3PovWoyk1MY1lP1XuiQO%2FrvTL64hEF3jvKhyTSHU9vT9fS1LjwWOJZChhZ%2BoEnkdWKQerc7I8l1IP2TnmvZOT9biNe0OHruqRbm%2Bi0M5OrbzoQ7nrbrpU%2F45pT2Q%2F5sgaU1fbjamVmrOYmbbC%2B4pWeXQ5vXKL5RY5VzmnGT9uZgqnGJPH7B7tOEvdA7vdhn04cZnLfRX1S7%2Bcq4A0shGlJsapcqc%2BLxz6tfXABJfjm1iT7tTRSIQ85iCQ6s%2BUCD1%2F23bLIVU35%2B8pu8bOPVNXssbqzWl7roOVI3u7bun2pPpxiu3eZz0rNd9unJNfGhCo%2BKljvQrbemOu9rm5mb3EzMbmMVN4xeudW4xdcxQAP2fAfSbGw61e%2BuU8VLJa8k3FIGklaMxFyakJL9WnU59Xn2qrUPEjZ050sOK9FNhLKUcg1Z9Dmv%2Bpda7kDEeXsbFLHjumxGSl3g%2BPfk8dzxahR06qTun2TBlffbZ3sX5naO8mTLWx%2B%2FvQSs1rzey6A4JeZ2bX8yrKcJA2RX6lCe4%2F58b%2FwxO3tav%2F5GQpfi%2Bnb482s5dltI%2BqMxDI6cAZzFmNCjkVl0y05hzOINdtMakJL9WnU5%2Fv7LlRcwrmlYk2atLLDeBczSCYqSGp%2FtxlhvIYKUv2JwvaODZ2yWvS2JWs1Pvh1Z%2Bq1xfknnou9%2B%2Bl2zNmfKXa0MWuee5W3ZQ1tFKTWjGSU6cfhN9KGdf%2B3bvFtylO2%2Bwv2JKf4tfXf4qrur%2FTVlXT9rLCMigLIlD65VxQU2cz5c7thaZDCqcEgY99YTt7pj6%2F2S4lgrx8gqwCfT88G%2F3DU5STQFR9rw%2BR4pjmKLmxS7k2bctPPT%2FlvUvJ3vV3rUTImc2NqcnRVXoOT80XObbuil1T3OepM4T0rdRoNezrCdY64ZhKmCxTcrd41QbJ3ZV4NMVvqP9u35xC%2FbOMQwQK3dBJb8pCCJR%2BORfSzFnN0CSyuUy8S7lenKGTQ0MGT3lhJXfq85u2aZUutQWoRJEpJ2zWDlqZspyVnDsu%2FOLcKV3j2Y6JPCXptdVjl1fWrnql5%2FDUfDFk%2B1DwfvecN%2FVLV%2F99ZnbZHqXaBrzOgEFeB1sHGu7l6BTZLp1DPyBT%2FDz9p%2B%2BF%2FqV2RWSythZv02z9a%2FufUpmAp3MrmzhJvXK0XKRJhqfLiOfIF6Kke3%2BVsFixGV1m5zGNm%2FrCTn1%2B2%2BZU7IPqKwPxG8c0lmdcBFIrOToOrhNKyp2z5tKXCDJ16q80k8gElZu2eh2GKe1LzRe7ZHvST2w%2BJz7aEjvKaaic1mftqJvaJlRbUslPvacGlcBUST9TJcUv5xusmwAU15oqyl2mhKgPSlXk72UJ5HRuWUtipHcZyq%2B1lW5Ap1B0WaZ%2BmZQsnhdAsUvKRTW2TH1hpz6%2FbbeYp64AmeNDMJbnmp%2BbckXLPnNZars7u6IScs6xIpeaLzbHySPbzPWeBKq7xpcOB%2BUkKt12nBVjpW3CoXLGHXFYqR8ou%2BT9lmPe03Mpfjnf4N9rVrGUPd9bFG%2BmtimpLaUCgZzOrWBelkrP5bBaPn5DllR%2FZeVr2fWralOCsgp7f6X1aZ7ywnomIE3%2B3%2FE3%2B6Sab22yKCveZKjctMkP898z5VIdAmsmoDlDDkXunDDnipwnxk%2B7A0rkGbVCqmtkdMraWzYdzdz5MWeLvbOnSxDqsS%2FXnpTM3OB76f%2BDNnVESjZ%2FDyawJgdrKEN5h02TxbmDGW7KTk2Uyu77tIn6p7ywEUvou8zXiSnFWg2VL5rZr5qZlq8pEIDAzwicvY3bHJO2pTRHjwMSvZJ2jJm9MKNhmyvkOfOj5kMlMc39DirGyetM5tjjabJWQRWHlnNwQg7wNbgo2oM3tk7uwIrVHitt6M6sTU0lTrVpglQOlKGiQMjU1RkeIlNe2FS81JQVts81k9X5Eg3QKZuco8ceHtSBAATKEujbQiu5kpa7UtPdHOGdH8ccPpDsO2UeFPHak9ODCoHRboC2O71FN3joB74WIr7mfYh60wgcooMlYtETgyefS9TVMWNfWB1j1qQ1FOTpPca8a9QpJuH9iV%2BDP2wPHXxm2rDlaQhAYOUElDT0NSNWl0piUe64p2QqGDtfe9Rovr6nmSmsI6doN0FbuqXCZXJsWXXdNTlYni3C7c6MXNou%2BSJt2j1Wj17GhwyM5txEfLtEadn81ok3RvFaV1%2F1W0XjIACBCAJKTfCMBThZmnOVRT7XuRKDsfO1l592DZTcVAcEcsujOGmYiyyv%2FpocLE%2BQ%2BzadqNNtnuXmKNapF7bPaUzl58q9SmLXSFNajE81wf7nSgzDm5jZy%2FOGKrUhAIEDJOCZW0tjmZI%2FLjVfR30XlJD0PiNAkDdrBDTvI1Gd69VXul6XpkF3TnmDAJUna8qVIanA8a7N3mO9KUapF1bP73KyUs%2BNuQx1l61ynlKnBXXY4EIEvKe6mr9DAAIj7wWMABdxOjI170Z%2Bg3VS%2By8zvn0dI%2FJmRYyWHTIiO7eQiaPEejKMbwr%2BfJv9NvfS25xTKHM6WNsrc%2Fdrblt%2FdILkWdoYrVHAtx7Sr6KrJQQdZ2a3jFCGDAhAYPUEcjO%2BRwCJCCGZ08FSm5UuR7sRR4wAQN6sEdCGHlmrg6U2e%2FK3bLPJWcXJWbrOyZuS6uLUC9s93%2FWtNzNxlAMo%2FRc0s0%2BY2akSjdHpJP3iokAAAhBIEcjN%2BJ6S1%2Ff3yENQqfm6xDd4yh2YslcZ4HWHZiqB9Fi%2BB%2FNcic5dCjzl%2FXhTZoCkBtcN2tMrQ%2B2Q0%2FKkDNk5eVNS%2FFIv7KaDJSfw6U47Ix0s2aCUDPdNNObHZnZN8rOkupy%2FQwACGwRyM7574U2JterTkZqvS%2BiULTkLAH22H8oVW97xkV1vzQ6WYNzKzI7NpKIXQrmadNptlwfvXRHq1Ea%2FQN6VOTl1z3W2PXKFrVOpgPfPmtmRCRt0E70y7L%2FNaSvVIAABCIiAMuHrlGHqfsEUrVKOhJKCfiClvA1P8SYudYj7aZWoOzCjv2E5bdjrumt3sNQ5yvfxbOcqznZnbr94OStCnaxoxp7MytIt28%2FkHJ2RK2ybKj2xX6qvYNL%2FMvIYtLOJVIMABFZIwDsf7mq60tYoF1SprTCvg1XiB%2B5me7s7MJXoWpdUKydiTpmSpiJHz%2BrqRn%2F8lwpI24VazTnvSAPlwWulxbsi1KmJSgOxbfaUSWVT1pjMxLkIv5nxQo9J5JdrD%2FUhAIF1EdB8%2BPyMlazIGKsUSW9G%2BugQjT67puTNYn5O9fbW3w%2FFweqafeE2%2BDoT0%2BjqEadQ%2BpSn9vY9Rs%2Fxwly%2FvQHeM9a4M8vTa9SBAATG%2FOissdU1dxof78gYmzfLK7%2Brp4WJNzapIx5pZh%2FMfXjf63s%2Bevvexm37vTFMU9pd%2BheSd%2Bl5qA1zOFed%2Fpu3l7d6YiWIyZoy8ngWAodLoO%2FOxFIxVl7SnoDzGt9i5c168cjwGW%2Fbu3rfa%2BKar3BoTlaNTs3tmOj6Udtru%2Byay2mJcLDm7nsdHVa8w6kdHaqXUcliCXx3wKIKBCCwk0AXe1QqxioHe2rHYe75uLPd4%2FzltHOo7qvM7IZRwvZBTq1Orc2m75fOFLvmcq46G717%2B31tqtH3crKUzJWVrCkjjWchAIF9I7BUB0sco04bpvpEP5x1uvxgSo2P7NLg%2Fn57tctUFlOfz%2BXi3dtfkoMlW3LSXHC6MHdUUB8CEFgigSU7WB0vrfhp2%2FBehQDiYBUCu3SxutZFN5Kfe6ShpU4LpswZkzaikzm3Q7jZFjlZT3SuZOm5uVcHU9z5OwQgAIEcAvvgYHXtyU2k7eXAFqGX1ErrnbO9H%2B%2Bxme0reVowZUrOitCmrJoOluzQduHrmzsST5NqoJnNkU7CYQZVIAABCIwisE8OlhoYHZtFkPuoYbPOh7x70qVPC3rp5v7iqLXitt0eOVmvNbPTOhtaKiGqUz3VIAABCIwisG8Olhrp%2FQ4OASFNw6jhchgPLekUSop4zi%2BOmituu5ws70pW6YzHKcb8HQIQgMAYAvvoYHXt3Kfv4Ji%2BKfZM7W2iYg07UMGpXxxLWXHb5WR5TxfqUMIrDrR%2FaTYEILCfBPbZwdpP4guwGgdrAZ1QwIR9%2FMWRE0v2%2BfaCVDllFAhAAAJLJ4CDtfQeKmAfDlYBqIgcTUAXPj%2FB%2BbQmrFu3pz%2Bdj1ANAhCAQBUCOFhVsNdVioNVlz%2FaT07gXGb2xQwoPzKzyzZZ35eQqTnDbKpCAAIHRgAH68A6XM3FwTrATl9wk3WNzrfN7FQZNp5oZpc%2FtDuuMvhQFQIQqE8AB6t%2BH8xuAQ7W7MhRmCDwR03C12dnOv8Hl8COUQQBCOwVARysvequGGNxsGI4IiWWwDXM7Llmdl6n2IO7gsHJhWoQgMAyCOBgLaMfZrUCB2tW3CjLJHBhM%2FuE4xkcLAckqkAAAtUI4GBVQ19PMQ5WPfZo9hH4kpkdlajKFqGPJbUgAIE6BIbmsaXcrFGHzIq14mCtuHNX0rRbmNlxA205yDuuVtK3NAMCh0JgaB5b0s0ah9Ifs7QTB2sWzCiZSECT0%2BO2VrIO%2Bo6riTx5HAIQmJ%2FA9jy21Js15iezUo04WCvtWJoFAQhAAAIQgEA9AjhY9dijGQIQgAAEIACBlRLAwVppx9IsCEAAAhCAAATqEcDBqscezRCAAAQgAAEIrJQADtZKO5ZmQQACEIAABCBQjwAOVj32aIYABCAAAQhAYKUEcLBW2rE0CwIQgAAEIACBegRwsOqxRzMEIAABCEAAAislgIO10o6lWRCAAAQgAAEI1COAg1WPPZohAAEIQAACEFgpARyslXYszYIABCAAAQhAoB4BHKx67NEMAQhAAAIQgMBKCeBgrbRjaRYEIAABCEAAAvUI4GDVY49mCEAAAhCAAARWSgAHa6UdS7MgAAEIQAACEKhHAAerHns0QwACEIAABCCwUgI4WCvtWJoFAQhAAAIQgEA9AjhY9dijGQIQgAAEIACBlRLAwVppx9IsCEAAAhCAAATqEcDBqscezRCAAAQgAAEIrJQADtZKO5ZmQQACEIAABCBQjwAOVj32aIYABCAAAQhAYKUEcLBW2rE0CwIQgAAEIACBegRwsOqxRzMEIAABCEAAAislgIO10o6lWRCAAAQgAAEI1COAg1WPPZohAAEIQAACEFgpARyslXYszYIABCAAAQhAoB4BHKx67NEMAQhAAAIQgMBKCeBgrbRjaRYEIAABCEAAAvUI4GDVY49mCEAAAhCAAARWSgAHa6UdS7MgAAEIQAACEKhHAAerHns0QwACEIAABCCwUgI4WCvtWJoFAQhAAAIQgEA9AjhY9dijGQIQgAAEIACBlRLAwVppx9IsCEAAAhCAAATqEcDBqscezRCAAAQgAAEIrJQADtZKO5ZmQQACEIAABCBQjwAOVj32aIYABCAAAQhAYKUEcLBW2rE0CwIQgAAEIACBegRwsOqxRzMEIAABCEAAAislgIO10o6lWRCAAAQgAAEI1COAg1WPPZohAAEIQAACEFgpARyslXYszYIABCAAAQhAoB4BHKx67NEMAQhAAAIQgMBKCeBgrbRjaRYEIAABCEAAAvUI4GDVY49mCEAAAhCAAARWSgAHa6UdS7MgAAEIQAACEKhHAAerHns0QwACEIAABCCwUgI4WCvtWJoFAQhAAAIQgEA9AjhY9dijGQIQgAAEIACBlRLAwVppx9IsCEAAAhCAAATqEcDBqscezRCAAAQgAAEIrJQADtZKO5ZmQQACEIAABCBQjwAOVj32aIYABCAAAQhAYKUEcLBW2rE0CwIQgAAEIACBegRwsOqxRzMEIAABCEAAAislgIO10o6lWRCAAAQgAAEI1COAg1WPPZohAAEIQAACEFgpARyslXYszYIABCAAAQhAoB4BHKx67NEMAQhAAAIQgMBKCeBgrbRjaRYEIAABCEAAAvUI4GDVY49mCEAAAhCAAARWSgAHa6UdS7MgAAEIQAACEKhHAAerHns0QwACEIAABCCwUgI4WCvtWJoFAQhAAAIQgEA9AjhY9dijGQIQgAAEIACBlRLAwVppx9IsCEAAAhCAAATqEcDBqscezRCAAAQgAAEIrJQADtZKO5ZmQQACEIAABCBQjwAOVj32aIYABCAAAQhAYKUEcLBW2rE0CwIQgAAEIACBegRwsOqxRzMEIAABCEAAAislgIO10o6lWRCAAAQgAAEI1COAg1WPPZohAAEIQAACEFgpgX8DGIqwpZMYQHcAAAAASUVORK5CYII%3D&detect_direction=false&probability=false&detect_alteration=false");
        Request request = new Request.Builder()
            .url("https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=" + getAccessToken())
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Accept", "application/json")
            .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response.body().string());

    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getOcr(String image) throws IOException {
        //String image = getFileContentAsBase64(file);
        //image=URLEncoder.encode(image);
//
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // image 可以通过 getFileContentAsBase64("C:\fakepath\11111.png") 方法获取,如果Content-Type是application/x-www-form-urlencoded时,第二个参数传true
        RequestBody body = RequestBody.create(mediaType, "image=" + image + "&detect_direction=true&probability=false&detect_alteration=false");
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String result = response.body().string();

        return result;
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public static String getOcr(MultipartFile file) throws IOException {
        String image = getFileContentAsBase64(file);
        image=URLEncoder.encode(image);
//
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // image 可以通过 getFileContentAsBase64("C:\fakepath\11111.png") 方法获取,如果Content-Type是application/x-www-form-urlencoded时,第二个参数传true
        RequestBody body = RequestBody.create(mediaType, "image=" + image + "&detect_direction=false&probability=false&detect_alteration=false");
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String result = response.body().string();

        return result;
    }

    /**
     * 获取文件base64编码
     *
     * @param file      文件
     * @return base64编码信息，不带文件头
     * @throws IOException IO异常
     */
    static String getFileContentAsBase64(MultipartFile file)  {
        try {
            // 将文件内容转换成 Base64 编码
            byte[] b = file.getBytes();
            String base64 = Base64.getEncoder().encodeToString(b);
            return base64;
        } catch (IOException e) {
            // 处理异常
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取文件base64编码
     *
     * @param path      文件路径
     * @param urlEncode 如果Content-Type是application/x-www-form-urlencoded时,传true
     * @return base64编码信息，不带文件头
     * @throws IOException IO异常
     */
    static String getFileContentAsBase64(String path, boolean urlEncode) throws IOException {
        byte[] b = Files.readAllBytes(Paths.get(path));
        String base64 = Base64.getEncoder().encodeToString(b);
        if (urlEncode) {
            base64 = URLEncoder.encode(base64, "utf-8");
        }
        return base64;
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken()  throws IOException{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        //String getTokenURL = "https://aip.baidubce.com/oauth/2.0/token?client_id=" + API_KEY + "&client_secret=" + SECRET_KEY + "&grant_type=client_credentials";
        String result = response.body().string();
//        try {
//            result = HttpClientUtil.httpsGET(getTokenURL);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        //System.out.println("Token result json:" + result);

        BaiduAccessToken object=JSONObject.parseObject(result, BaiduAccessToken.class);
        //System.out.println(object.getAccessToken());
        return object.getAccessToken();
    }

}
