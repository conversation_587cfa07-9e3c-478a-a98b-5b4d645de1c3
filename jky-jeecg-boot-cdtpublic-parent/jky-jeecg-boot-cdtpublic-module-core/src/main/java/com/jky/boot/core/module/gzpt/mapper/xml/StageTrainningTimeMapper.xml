<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.StageTrainningTimeMapper">
    <resultMap type="com.jky.boot.core.module.gzpt.entity.StageTrainningTime" id="StageTrainningTimeResult">
        <result property="id" column="id"/>
        <result property="inscode" column="inscode"/>
        <result property="insname" column="insname"/>
        <result property="stunum" column="stunum"/>
        <result property="idcard" column="idcard"/>
        <result property="stuname" column="stuname"/>
        <result property="subject" column="subject"/>
        <result property="totaltime" column="totaltime"/>
        <result property="vehicletime" column="vehicletime"/>
        <result property="classtime" column="classtime"/>
        <result property="simulatortime" column="simulatortime"/>
        <result property="networktime" column="networktime"/>
        <result property="mileage" column="mileage"/>
        <result property="applydate" column="applydate"/>
        <result property="auditstate" column="auditstate"/>
        <result property="operatorname" column="operatorname"/>
        <result property="auditdate" column="auditdate"/>
        <result property="district" column="district"/>
        <result property="traintype" column="traintype"/>
        <result property="ispushsj" column="ispushsj"/>
        <result property="ispushjs" column="ispushjs"/>
        <result property="isface" column="isface"/>
        <result property="crdate" column="crdate"/>
        <result property="pushjsdate" column="pushjsdate"/>
        <result property="pushsjdate" column="pushsjdate"/>
        <result property="duration" column="duration"/>
        <result property="isbank" column="isbank"/>
        <result property="resultmeg" column="resultmeg"/>
    </resultMap>
    <resultMap type="com.jky.boot.core.module.gzpt.entity.StageTrainningTime" id="StageTrainningTimeResult2">
        <result property="id" column="id"/>
        <result property="inscode" column="inscode"/>
        <result property="insname" column="insname"/>
        <result property="stunum" column="stunum"/>
        <result property="idcard" column="idcard"/>
        <result property="stuname" column="stuname"/>
        <result property="subject" column="subject"/>
        <result property="totaltime" column="totaltime"/>
        <result property="vehicletime" column="vehicletime"/>
        <result property="classtime" column="classtime"/>
        <result property="simulatortime" column="simulatortime"/>
        <result property="networktime" column="networktime"/>
        <result property="mileage" column="mileage"/>
        <result property="applydate" column="applydate"/>
        <result property="auditstate" column="auditstate"/>
        <result property="operatorname" column="operatorname"/>
        <result property="auditdate" column="auditdate"/>
        <result property="district" column="district"/>
        <result property="traintype" column="traintype"/>
        <result property="ispushsj" column="ispushsj"/>
        <result property="ispushjs" column="ispushjs"/>
        <result property="isface" column="isface"/>
        <result property="crdate" column="crdate"/>
        <result property="pushjsdate" column="pushjsdate"/>
        <result property="pushsjdate" column="pushsjdate"/>
        <result property="message" column="message"/>
        <result property="duration" column="duration"/>
        <result property="isbank" column="isbank"/>
        <result property="resultmeg" column="resultmeg"/>
        <result property="coachnum" column="coachnum"/>
        <result property="coachname" column="coachname"/>
        <result property="sex" column="sex"/>
    </resultMap>

    <sql id="selectStageTrainningTimeVo">
        select id,
               inscode,
               insname,
               stunum,
               idcard,
               stuname,
               subject,
               totaltime,
               vehicletime,
               classtime,
               simulatortime,
               networktime,
               mileage,
               applydate,
               auditstate,
               operatorname,
               auditdate,
               district,
               traintype,
               ispushsj,
               ispushjs,
               isface,
               crdate,
               pushjsdate,
               pushsjdate,
               duration,
               isbank,
               resultmeg
        from t_m_stage_trainning_time
    </sql>

    <select id="selectStageTrainningTimeList" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTime" resultMap="StageTrainningTimeResult">
        <include refid="selectStageTrainningTimeVo"/>
        <where>
            <if test="ids != null  and ids != ''">
                id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="inscode != null  and inscode != ''">and inscode = #{inscode}</if>
            <if test="idcard != null  and idcard != ''">and idcard = #{idcard}</if>
            <if test="stunum != null  and stunum != ''">and stunum = #{stunum}</if>
            <if test="stuname != null  and stuname != ''">and stuname like concat(concat('%', #{stuname}), '%')</if>
            <if test="subject != null  and subject != ''">and subject = #{subject}</if>
            <if test="isbank != null  and isbank != ''">and isbank = #{isbank}</if>
            <if test="auditstate != null  and auditstate != ''">and auditstate = #{auditstate}</if>
            <if test="ispushsj != null   ">and ispushsj = #{ispushsj}</if>
            <if test="ispushjs != null  ">and ispushjs = #{ispushjs}</if>
            <if test="isbank != null  ">and isbank = #{isbank}</if>
        </where>
    </select>

    <select id="findStageTrainningTimeList" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTime" resultMap="StageTrainningTimeResult2">
        SELECT a.*,b.message,stu.coachnum,c.name as coachname,stu.sex FROM T_M_STAGE_TRAINNING_TIME a LEFT JOIN
        T_M_STAGE_TRAINNING_TIME_LOG b on a.id=b.STAGEID
        LEFT JOIN T_M_STUDENTINFO stu on a.stunum=stu.stunum
        LEFT JOIN T_M_COACH c on stu.COACHNUM=c.COACHNUM
        <where>
            <if test="stageTrainningTime.inscodes != null">
                and a.inscode in
                <foreach collection="stageTrainningTime.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="stageTrainningTime.idcard != null  and stageTrainningTime.idcard != ''">and a.idcard = #{stageTrainningTime.idcard}</if>
            <if test="stageTrainningTime.stunum != null  and stageTrainningTime.stunum != ''">and a.stunum = #{stageTrainningTime.stunum}</if>
            <if test="stageTrainningTime.stuname != null  and stageTrainningTime.stuname != ''">and a.stuname like concat(concat('%', #{stageTrainningTime.stuname}), '%')</if>
            <if test="stageTrainningTime.subject != null and stageTrainningTime.subject != '' ">and a.subject = #{stageTrainningTime.subject}</if>
            <if test="stageTrainningTime.auditstate != null  and stageTrainningTime.auditstate != ''">and a.auditstate = #{stageTrainningTime.auditstate}</if>
            <if test="stageTrainningTime.ispushsj != null   ">and a.ispushsj = #{stageTrainningTime.ispushsj}</if>
            <if test="stageTrainningTime.ispushjs != null   ">and a.ispushjs = #{stageTrainningTime.ispushjs}</if>
            <if test="stageTrainningTime.isbank != null   ">and a.isbank = #{stageTrainningTime.isbank}</if>
            <if test="stageTrainningTime.pushjsdate_begin != null ">and a.pushjsdate &gt;= #{stageTrainningTime.pushjsdate_begin}</if>
            <if test="stageTrainningTime.pushjsdate_end != null ">and a.pushjsdate &lt;= #{stageTrainningTime.pushjsdate_end}</if>

            <if test="stageTrainningTime.pushsjdate_begin != null ">and a.pushsjdate &gt;= #{stageTrainningTime.pushsjdate_begin}</if>
            <if test="stageTrainningTime.pushsjdate_end != null ">and a.pushsjdate &lt;= #{stageTrainningTime.pushsjdate_end}</if>

            <if test="stageTrainningTime.auditdate_begin != null ">and a.auditdate &gt;= concat(#{stageTrainningTime.auditdate_begin},' 00:00:00')</if>
            <if test="stageTrainningTime.auditdate_end != null ">and a.auditdate &lt;= concat(#{stageTrainningTime.auditdate_end},' 23:59:59')</if>

            <if test="stageTrainningTime.coachname != null  and stageTrainningTime.coachname != ''">and c.name like concat(concat('%', #{stageTrainningTime.coachname}), '%')
            </if>
        </where>
        order by a.crdate desc
    </select>

    <select id="selectStageTrainningTimeById" parameterType="string" resultMap="StageTrainningTimeResult">
        <include refid="selectStageTrainningTimeVo"/>
        where id = #{id}
    </select>

    <update id="updateStageTrainningTime" parameterType="com.jky.boot.core.module.gzpt.entity.StageTrainningTime">
        update t_m_stage_trainning_time
        <trim prefix="SET" suffixOverrides=",">
            <if test="inscode != null">inscode = #{inscode},</if>
            <if test="insname != null">insname = #{insname},</if>
            <if test="stunum != null">stunum = #{stunum},</if>
            <if test="idcard != null">idcard = #{idcard},</if>
            <if test="stuname != null">stuname = #{stuname},</if>
            <if test="subject != null">subject = #{subject},</if>
            <if test="totaltime != null">totaltime = #{totaltime},</if>
            <if test="vehicletime != null">vehicletime = #{vehicletime},</if>
            <if test="classtime != null">classtime = #{classtime},</if>
            <if test="simulatortime != null">simulatortime = #{simulatortime},</if>
            <if test="networktime != null">networktime = #{networktime},</if>
            <if test="mileage != null">mileage = #{mileage},</if>
            <if test="applydate != null">applydate = #{applydate},</if>
            <if test="auditstate != null">auditstate = #{auditstate},</if>
            <if test="operatorname != null">operatorname = #{operatorname},</if>
            <if test="auditdate != null">auditdate = #{auditdate},</if>
            <if test="district != null">district = #{district},</if>
            <if test="traintype != null">traintype = #{traintype},</if>
            <if test="ispushsj != null">ispushsj = #{ispushsj},</if>
            <if test="ispushjs != null">ispushjs = #{ispushjs},</if>
            <if test="isface != null">isface = #{isface},</if>
            <if test="crdate != null">crdate = #{crdate},</if>
            <if test="pushjsdate != null">pushjsdate = #{pushjsdate},</if>
            <if test="pushsjdate != null">pushsjdate = #{pushsjdate},</if>
            <if test="isbank != null">isbank = #{isbank},</if>
            <if test="resultmeg != null">resultmeg = #{resultmeg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStageTrainningTimeById" parameterType="Long">
        delete
        from t_m_stage_trainning_time
        where id = #{id}
    </delete>

    <delete id="deleteStageTrainningTimeByIds">
        delete from t_m_stage_trainning_time where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findByIsBank" parameterType="Long" resultMap="StageTrainningTimeResult">
        select a.*
        from t_m_stage_trainning_time a
                 LEFT JOIN T_M_STUDENTINFO b on
            a.stunum = b.stunum
        where b.platform!='159258B1C2AA' and a.subject<![CDATA[ > ]]> 1
         and a.AUDITSTATE=1 and (a.isbank=#{isbank} or a.isbank = '2')
--           and b.iscrosscity=2
          and b.traintype not in ('A1'
            , 'A2'
            , 'B1'
            , 'B2'
            , 'A3'
            , 'C6')
          and b.istobank=1 and (b.isold=0 or b.isold is null)

    </select>

    <select id="findStageByIds" resultMap="StageTrainningTimeResult2">
        -- select a.* ,b.message from t_m_stage_trainning_time a LEFT JOIN T_M_STAGE_TRAINNING_TIME_LOG b
        -- on a.id=b.STAGEID where a.id in
        SELECT a.*,b.message,stu.coachnum,c.name as coachname,stu.sex FROM T_M_STAGE_TRAINNING_TIME a LEFT JOIN
        T_M_STAGE_TRAINNING_TIME_LOG b on a.id=b.STAGEID
        LEFT JOIN T_M_STUDENTINFO stu on a.stunum=stu.stunum
        LEFT JOIN T_M_COACH c on stu.COACHNUM=c.COACHNUM
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="automaticPushSj" resultMap="StageTrainningTimeResult">
        select *
        from t_m_stage_trainning_time
        where ISPUSHSJ = #{ispushsj}
          and auditstate = 0
    </select>

    <select id="automaticPushJs" resultMap="StageTrainningTimeResult">
        select *
        from t_m_stage_trainning_time
        where ISPUSHSJ = 1
          AND ISPUSHJS = #{ispushjs}
          and auditstate = 0
    </select>

    <select id="pinjia" resultType="com.jky.boot.core.module.gzpt.entity.StageTrainningTime">
        SELECT `SUBJECT`,AUDITSTATE,CRDATE from t_m_stage_trainning_time where STUNUM in
                                                                               (SELECT STUNUM from t_m_studentinfo where IDCARD in
                                                                                                                         (SELECT idnum FROM `base_zlb_person_info`where id = #{id}))
    </select>
    <select id="findOldByIsBank" resultType="com.jky.boot.core.module.gzpt.entity.StageTrainningTime">
        SELECT
            a.*
        FROM
            t_m_stage_trainning_time a
                LEFT JOIN T_M_STUDENTINFO b ON a.stunum = b.stunum
                LEFT JOIN T_M_bank_reg r ON a.stunum = r.stunum
        WHERE
         a.`SUBJECT` = 3
        AND a.AUDITSTATE = 1
        and b.applydate <![CDATA[ < ]]> ********
--         AND b.iscrosscity = 2
--         AND r.BLOCKID is NOT NULL
        AND r.status = 3
        AND r.remaining_amount <![CDATA[ > ]]> 0
        AND b.traintype NOT IN ( 'A1', 'A2', 'B1', 'B2', 'A3', 'C6' )
        <if test="stunum != null and stunum !=''">
            AND b.stunum = #{stunum}
        </if>
--           and r.bankcode = '9IC3C55B'
        and b.isold = '1'
        AND b.istobank =1
        and a.isbank  in (0,2)
    </select>
</mapper>
