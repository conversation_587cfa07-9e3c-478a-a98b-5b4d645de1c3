package com.jky.boot.core.module.gzpt.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.common.utils.ServletUtils;
import com.jky.boot.core.module.gzpt.entity.TMMr890chLog;
import com.jky.boot.core.module.gzpt.mapper.TMMr890chLogMapper;
import com.jky.boot.core.module.gzpt.service.ITMMr890chLogService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.utils.JkyCryptorUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: t_m_mr890ch_log
 * @Author: jeecg-boot
 * @Date: 2024-01-18
 * @Version: V1.0
 */
@Service
public class TMMr890chLogServiceImpl extends ServiceImpl<TMMr890chLogMapper, TMMr890chLog> implements ITMMr890chLogService {

    @Value("${jky.crypto-mybatis.default-key}")
    private String cryptoKey;

    @Override
    public void keeplog(Integer type ,String logContent, HttpServletRequest req) {
        TMMr890chLog mr890chLog = new TMMr890chLog();
        mr890chLog.setLogContent(logContent);
        mr890chLog.setLogType(type);
        LoginUser loginUser = JkSecurityUtils.getLoginUser();
        mr890chLog.setUserid(loginUser.getUsername());
        String decrypt = JkyCryptorUtils.decrypt(loginUser.getRealname(), cryptoKey);
        mr890chLog.setUsername(decrypt);
        HttpServletRequest request = ServletUtils.getRequest();
        String clientIp = ServletUtil.getClientIP(request);
        mr890chLog.setIp(clientIp);
        this.save(mr890chLog);

    }

    @Override
    public void jgKeepLog(Integer type, String logContent, String ip, String userid, String username) {
        TMMr890chLog mr890chLog = new TMMr890chLog();
        mr890chLog.setLogContent(logContent);
        mr890chLog.setLogType(type);
        mr890chLog.setUserid(userid);
        mr890chLog.setUsername(username);
        mr890chLog.setIp(ip);
        this.save(mr890chLog);
    }
}
