package com.jky.boot.zlb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.zlb.entity.TMStudentInfoEnter;
import com.jky.boot.zlb.mapper.TMStudentInfoEnterMapper;
import com.jky.boot.zlb.service.ITMStudentInfoEnterService;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class TMStudentInfoEnterServiceImpl extends ServiceImpl<TMStudentInfoEnterMapper, TMStudentInfoEnter> implements ITMStudentInfoEnterService {

    @Override
    public TMStudentInfoEnter getOneByIdCard(String idCard) {
        List<TMStudentInfoEnter> enters = baseMapper.selectList(
            Wrappers.<TMStudentInfoEnter>lambdaQuery()
                .eq(TMStudentInfoEnter::getIdcard, idCard)
                .orderByDesc(TMStudentInfoEnter::getCreateTime)
        );
        if (enters.isEmpty()) {
            return null;
        }
        return enters.get(0);
    }
}
