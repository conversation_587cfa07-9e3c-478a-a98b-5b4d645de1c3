package com.jky.boot.core.module.gzpt.mapper;

import com.jky.boot.core.module.gzpt.entity.TMExaminer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description: t_m_examiner
 * @Author: jeecg-boot
 * @Date:   2023-04-14
 * @Version: V1.0
 */
@Mapper
public interface TMExaminerMapper extends BaseMapper<TMExaminer> {
    /**
     * 查询【请填写功能名称】
     *
     * @param examnum 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    TMExaminer selectExaminerByExamnum(String examnum);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param examiner 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<TMExaminer> selectExaminerList2(TMExaminer examiner);
    TMExaminer selectExaminerByExamid(Long examid);

    /**
     * 新增【请填写功能名称】
     *
     * @param examiner 【请填写功能名称】
     * @return 结果
     */
    int insertExaminer(TMExaminer examiner);

    /**
     * 修改【请填写功能名称】
     *
     * @param examiner 【请填写功能名称】
     * @return 结果
     */
    int updateExaminer(TMExaminer examiner);

    /**
     * 删除【请填写功能名称】
     *
     * @param examnum 【请填写功能名称】主键
     * @return 结果
     */
    int deleteExaminerByExamnum(String examnum);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param examnums 需要删除的数据主键集合
     * @return 结果
     */
    int deleteExaminerByExamnums(String[] examnums);
}
