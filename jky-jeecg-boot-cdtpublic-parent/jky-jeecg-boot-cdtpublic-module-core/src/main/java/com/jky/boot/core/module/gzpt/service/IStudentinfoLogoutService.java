package com.jky.boot.core.module.gzpt.service;

import com.jky.boot.core.module.gzpt.entity.StudentinfoLogout;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 学员信息归档对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IStudentinfoLogoutService extends IService<StudentinfoLogout> {
    /**
     * 查询学员信息归档
     *
     * @param id 学员信息归档主键
     * @return 学员信息归档
     */
    StudentinfoLogout selectStudentinfoLogoutById(Long id);

    /**
     * 新增学员信息归档
     *
     * @param studentinfoLogout 学员信息归档
     * @return 结果
     */
    int insertStudentinfoLogout(StudentinfoLogout studentinfoLogout);

    /**
     * 修改学员信息归档
     *
     * @param studentinfoLogout 学员信息归档
     * @return 结果
     */
    int updateStudentinfoLogout(StudentinfoLogout studentinfoLogout);

    /**
     * 批量删除学员信息归档
     *
     * @param ids 需要删除的学员信息归档主键集合
     * @return 结果
     */
    int deleteStudentinfoLogoutByIds(Long[] ids);

    /**
     * 删除学员信息归档信息
     *
     * @param id 学员信息归档主键
     * @return 结果
     */
    int deleteStudentinfoLogoutById(Long id);
}
