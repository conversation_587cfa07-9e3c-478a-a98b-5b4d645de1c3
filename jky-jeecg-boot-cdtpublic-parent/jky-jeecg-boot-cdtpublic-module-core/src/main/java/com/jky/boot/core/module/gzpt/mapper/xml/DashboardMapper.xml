<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.DashboardMapper">

    <select id="getStudentTotal" resultType="java.lang.Long">
        SELECT
        COUNT( * )
        FROM
        T_M_STUDENTINFO STU
        <choose>
            <when test="leader.length()==4">
                WHERE
                STU.DISTRICT LIKE CONCAT(#{leader},'%')
            </when>
            <when test="leader.length()==6">
                WHERE
                STU.DISTRICT = #{leader}
            </when>
            <otherwise>
                WHERE
                STU.INSCODE = #{leader}
            </otherwise>
        </choose>
        and STU.inscode not in (select inscode from t_m_institution where is_bankruptcy = 1)
        and STU.applydate <![CDATA[ >= ]]> '********'
    </select>
    <select id="getCoachTotal" resultType="java.lang.Long">
        SELECT
        COUNT( * )
        FROM
        T_M_COACH COACH
        <choose>
            <when test="leader.length()==4">
                LEFT JOIN T_M_INSTITUTION INS ON COACH.INSCODE = INS.INSCODE
                WHERE
                INS.DISTRICT LIKE CONCAT(#{leader},'%')
            </when>
            <when test="leader.length()==6">
                LEFT JOIN T_M_INSTITUTION INS ON COACH.INSCODE = INS.INSCODE
                WHERE
                INS.DISTRICT = #{leader}
            </when>
            <otherwise>
                WHERE
                COACH.INSCODE = #{leader}
            </otherwise>
        </choose>
        and COACH.inscode not in (select inscode from t_m_institution where is_bankruptcy = 1)
    </select>
    <select id="getCarInfoTotal" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM T_M_CARINFO CARINFO
        <choose>
            <when test="leader.length()==4">
                LEFT JOIN T_M_INSTITUTION INS ON CARINFO.INSCODE = INS.INSCODE
                WHERE
                INS.DISTRICT LIKE CONCAT(#{leader},'%')
            </when>
            <when test="leader.length()==6">
                LEFT JOIN T_M_INSTITUTION INS ON CARINFO.INSCODE = INS.INSCODE
                WHERE
                INS.DISTRICT = #{leader}
            </when>
            <otherwise>
                WHERE
                CARINFO.INSCODE = #{leader}
            </otherwise>
        </choose>
        and CARINFO.inscode not in (select inscode from t_m_institution where is_bankruptcy = 1)
    </select>
    <select id="getInstitutionTotal" resultType="java.lang.Long">
        SELECT
        COUNT(*)
        FROM
        T_M_INSTITUTION
        WHERE
        is_bankruptcy = 0 and
        <choose>
            <when test="leader.length()==4">
                DISTRICT LIKE CONCAT( #{leader}, '%' )
            </when>
            <when test="leader.length()==6">
                DISTRICT = #{leader}
            </when>
            <otherwise>
                INSCODE = #{leader}
            </otherwise>
        </choose>
    </select>
    <select id="getWeekAddStudentTotal" resultType="java.util.Map">
        SELECT DATE_FORMAT(STU.create_time, '%Y-%m-%d') AS create_time, COUNT(*) AS `count`
        FROM `t_m_studentinfo` as STU
        <choose>
            <when test="leader.length()==4">
                WHERE
                STU.DISTRICT LIKE CONCAT(#{leader},'%')
            </when>
            <when test="leader.length()==6">
                WHERE
                STU.DISTRICT = #{leader}
            </when>
            <otherwise>
                WHERE
                STU.INSCODE = #{leader}
            </otherwise>
        </choose>
        AND DATE_FORMAT(STU.create_time, '%Y-%m-%d') IN
        <foreach collection="dates" item="date" index="index" open="(" close=")" separator=",">
            #{date}
        </foreach>
        and STU.inscode not in (select inscode from t_m_institution where is_bankruptcy = 1)
        GROUP BY DATE_FORMAT(STU.create_time, '%Y-%m-%d')
        ORDER BY create_time DESC
    </select>
</mapper>
