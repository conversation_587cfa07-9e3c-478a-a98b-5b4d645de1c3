package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 *  t_m_studentinfo_enter
 * <AUTHOR>
 * @version 2023-04-06
 */
@Data
@TableName("t_m_studentinfo_enter")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_studentinfo_enter对象", description="在线报名学员对象")
public class StudentinfoEnter implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
	/**培训机构编号*/
	@Excel(name = "培训机构编号", width = 15)
    @ApiModelProperty(value = "培训机构编号")
    @Length(max = 16, min = 16, message = "培训机构编号有误！")
    @NotBlank(message = "培训机构编号为空！")
    private String inscode;
	/**证件类型   1:身份证 2:护照 3:军官证 4:其他*/
	@Excel(name = "证件类型   1:身份证 2:护照 3:军官证 4:其他", width = 15)
    @ApiModelProperty(value = "证件类型   1:身份证 2:护照 3:军官证 4:其他")
    private String cardtype;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    @Length(max = 18, min = 18, message = "身份证号有误！")
    @NotBlank(message = "身份证号为空！")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private String idcard;
	/**国籍*/
	@Excel(name = "国籍", width = 15)
    @ApiModelProperty(value = "国籍")
    private String nationality;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;
	/**性别 1:男性;2:女性*/
	@Excel(name = "性别 1:男性;2:女性", width = 15)
    @ApiModelProperty(value = "性别 1:男性;2:女性")
    private String sex;
	/**手机号码*/
	@Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.MOBILE_PHONE)
    private String phone;
	/**联系地址*/
	@Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
    private String address;
	/**成功上传的学员头像照片文件ID*/
	@Excel(name = "成功上传的学员头像照片文件ID", width = 15)
    @ApiModelProperty(value = "成功上传的学员头像照片文件ID")
    private Integer photo;
	/**业务类型   0:初领 1:增领 9:其他*/
	@Excel(name = "业务类型   0:初领 1:增领 9:其他", width = 15)
    @ApiModelProperty(value = "业务类型   0:初领 1:增领 9:其他")
    private String busitype;
	/**驾驶证号*/
	@Excel(name = "驾驶证号", width = 15)
    @ApiModelProperty(value = "驾驶证号")
    private String drilicnum;
	/**驾驶证初领日期*/
	@Excel(name = "驾驶证初领日期", width = 15)
    @ApiModelProperty(value = "驾驶证初领日期")
    private String fstdrilicdate;
	/**培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
	@Excel(name = "培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P", width = 15)
    @ApiModelProperty(value = "培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private String traintype;
	/**报名时间*/
	@Excel(name = "报名时间", width = 15)
    @ApiModelProperty(value = "报名时间")
    private String applydate;
	/**图片路径*/
	@Excel(name = "图片路径", width = 15)
    @ApiModelProperty(value = "图片路径")
    private String photopath;
	/**驾校名称*/
	@Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
	/**学员统一编号*/
	@Excel(name = "学员统一编号", width = 15)
    @ApiModelProperty(value = "学员统一编号")
    @Length(max = 16, min = 16, message = "学员统一编号有误！")
    @NotBlank(message = "学员统一编号为空！")
    private String stunum;
	/**银行code*/
	@Excel(name = "银行code", width = 15)
    @ApiModelProperty(value = "银行code")
    private String bankcode;
	/**银行名称*/
	@Excel(name = "银行名称", width = 15)
    @ApiModelProperty(value = "银行名称")
    private String bankname;
	/**原准驾车型*/
	@Excel(name = "原准驾车型", width = 15)
    @ApiModelProperty(value = "原准驾车型")
    private String perdritype;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**冻结金额*/
	@Excel(name = "冻结金额", width = 15)
    @ApiModelProperty(value = "冻结金额")
    private String frozenAmount;
	/**教练编号*/
	@Excel(name = "教练编号", width = 15)
    @ApiModelProperty(value = "教练编号")
    @Dict(dictTable = "t_m_coach", dicCode = "coachnum", dicText = "name")
    private String coachnum;

    /**
     * 是否为跨地市转校 1:是  2:否
     */
    private String iscrosscity;

    /**
     * 外地转入前所在地市
     */
    private String fromarea;

    /**
     * 跨地市转校学员学习类型（0-非跨地市学员默认值，1-全学，2-只学科目二，3-只学科目三）
     */
    private Integer crossType;

    /**是否入学*/
	@Excel(name = "是否入学", width = 15)
    @ApiModelProperty(value = "是否入学")
    private Integer isEnter;
    /**是否审核*/
    @Excel(name = "是否审核", width = 15)
    @ApiModelProperty(value = "是否审核")
    private Integer examineType;
    /**审核时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date examineTime;
    /**审核人*/
    @Excel(name = "审核人", width = 15)
    @ApiModelProperty(value = "审核人")
    private String expeople;

    private String fileUrl;
}
