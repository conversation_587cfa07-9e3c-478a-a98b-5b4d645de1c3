package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 驾校账户信息
 *
 * <AUTHOR>
 * @version 2024-12-13
 */
@Data
@TableName("payee_account")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PayeeAccount {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 驾校编号
     */
    private String inscode;
    /**
     * 驾校名称
     */
    private String insname;
    /**
     * 收款账户类型
     */
    private String accountType;
    /**
     * 账户类型
     */
    private String customerType;
    /**
     * 他行银行联行号
     */
    private String contactLine;
    /**
     * 收款账户户号
     */
    private String accountNo;
    /**
     * 收款账户户名
     */
    private String accountName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateBy;
}
