<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.BalanceAmountMapper">
    <insert id="insertBalanceAmount">
        insert into t_m_balance_amount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stuname != null">stuname,</if>
            <if test="stunum != null">stunum,</if>
            <if test="idcard != null">idcard,</if>
            <if test="balanceAmount != null">balance_amount,</if>
            <if test="status != null">status,</if>
            <if test="message != null">message,</if>
            <if test="crdate != null">crdate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stuname != null">#{stuname},</if>
            <if test="stunum != null">#{stunum},</if>
            <if test="idcard != null">#{idcard},</if>
            <if test="balanceAmount != null">#{balanceAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="message != null">#{message},</if>
            <if test="crdate != null">#{crdate},</if>
        </trim>
    </insert>
    <update id="updateBalanceAmount">
        update t_m_balance_amount
        <trim prefix="SET" suffixOverrides=",">
            <if test="stuname != null">stuname = #{stuname},</if>
            <if test="stunum != null">stunum = #{stunum},</if>
            <if test="idcard != null">idcard = #{idcard},</if>
            <if test="balanceAmount != null">balance_amount = #{balanceAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="message != null">message = #{message},</if>
            <if test="crdate != null">crdate = #{crdate},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectBalanceAmountList" resultType="com.jky.boot.core.module.gzpt.entity.BalanceAmount">
        select t1.id,
        t1.stuname,
        t1.stunum,
        t1.idcard,
        t1.balance_amount,
        t1.status,
        t1.message,
        t1.crdate
        from t_m_balance_amount t1 left join t_m_studentinfo t2 on t1.stunum = t2.stunum
        <where>
            <if test="inscodes != null ">
                and t2.inscode in
                <foreach collection="inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="stuname != null  and stuname != ''">and t1.stuname like concat(concat('%', #{stuname}), '%')</if>
            <if test="stunum != null  and stunum != ''">and t1.stunum = #{stunum}</if>
            <if test="idcard != null  and idcard != ''">and t1.idcard = #{idcard}</if>
            <if test="balanceAmount != null ">and t1.balance_amount = #{balanceAmount}</if>
            <if test="status != null ">and t1.status = #{status}</if>
            <if test="message != null  and message != ''">and t1.message = #{message}</if>
            <if test="crdate != null ">and t1.crdate between #{crdate} and replace(#{crdate},"00:00:00","23:59:59")</if>
        </where>
        order by t1.create_time

    </select>
    <select id="selectBalanceAmountPage" resultType="com.jky.boot.core.module.gzpt.entity.BalanceAmount">
        select t1.id,
        t1.stuname,
        t1.stunum,
        t1.idcard,
        t1.balance_amount,
        t1.status,
        t1.message,
        t1.crdate
        from t_m_balance_amount t1 left join t_m_studentinfo t2 on t1.stunum = t2.stunum
        <where>
            <if test="balanceAmout.inscodes != null ">
                and t2.inscode in
                <foreach collection="balanceAmout.inscodes" item="ins" open="(" separator="," close=")">
                    #{ins}
                </foreach>
            </if>
            <if test="balanceAmout.stuname != null  and balanceAmout.stuname != ''">and t1.stuname like concat(concat('%', #{balanceAmout.stuname}), '%')</if>
            <if test="balanceAmout.stunum != null  and balanceAmout.stunum != ''">and t1.stunum = #{balanceAmout.stunum}</if>
            <if test="balanceAmout.idcard != null  and balanceAmout.idcard != ''">and t1.idcard = #{balanceAmout.idcard}</if>
            <if test="balanceAmout.balanceAmount != null ">and t1.balance_amount = #{balanceAmout.balanceAmount}</if>
            <if test="balanceAmout.status != null ">and t1.status = #{balanceAmout.status}</if>
            <if test="balanceAmout.message != null  and message != ''">and t1.message = #{balanceAmout.message}</if>
            <if test="balanceAmout.crdate != null ">and t1.crdate between #{balanceAmout.crdate} and replace(#{balanceAmout.crdate},'00:00:00','23:59:59')</if>
        </where>
        order by t1.create_time

    </select>

    <sql id="selectBalanceAmountVo">
        select id,
               stuname,
               stunum,
               idcard,
               balance_amount,
               status,
               message,
               crdate
        from t_m_balance_amount
    </sql>
</mapper>
