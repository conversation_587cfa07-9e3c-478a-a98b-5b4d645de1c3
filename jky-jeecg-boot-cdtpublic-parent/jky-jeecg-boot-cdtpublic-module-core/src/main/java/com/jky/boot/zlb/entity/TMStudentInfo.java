package com.jky.boot.zlb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jky.boot.common.sensitive.Sensitive;
import com.jky.boot.common.sensitive.SensitiveTypeEnum;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.IgnoreScanAnno;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@TableName("t_m_studentinfo")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_studentinfo对象", description="学员信息表")
public class TMStudentInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**培训机构编号 org_code*/
    @ApiModelProperty(value = "培训机构编号")
    private java.lang.String inscode;
    /**证件类型 	1:身份证
     2:护照
     3:军官证
     4:其他*/
    @ApiModelProperty(value = "证件类型 	1:身份证 2:护照 3:军官证 4:其他")
    private java.lang.String cardtype;
    /**身份证号*/
    @Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    @NotBlank(message="身份证号不能为空")
    @Sensitive(type = SensitiveTypeEnum.ID_CARD_ZLB)
    @CryptoFieldAnno
    private java.lang.String idcard;

    public void setIdcard(String idcard) {
        this.idcard = com.jky.boot.common.utils.StringUtils.cleanBlank(idcard);
    }
    public String getIdcard() {
        return idcard;
    }


    /**国籍*/
    @ApiModelProperty(value = "国籍")
    private java.lang.String nationality;
    /**姓名*/
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    @CryptoFieldAnno
//    @Sensitive(type = SensitiveTypeEnum.CHINESE_NAME_ZLB)
    private java.lang.String name;
    /**性别	1:男性;2:女性*/
    @Excel(name = "性别", width = 15,replace = {"男_1","女_2"})
    @ApiModelProperty(value = "性别	1:男性;2:女性")
    @Dict(dicCode = "sys_user_sex")
    private java.lang.String sex;
    /**手机号码*/
    @Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    @Sensitive(type = SensitiveTypeEnum.MOBILE_PHONE)
    @CryptoFieldAnno
    private java.lang.String phone;
    /**联系地址*/
    @Excel(name = "联系地址", width = 15)
    @ApiModelProperty(value = "联系地址")
    @CryptoFieldAnno
    private java.lang.String address;
    /**照片文件ID	  成功上传的学员头像照片文件ID*/
    @ApiModelProperty(value = "照片文件ID	  成功上传的学员头像照片文件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private java.lang.Long photo;
    /**业务类型  	0:初领
     1:增领
     9:其他*/
    @ApiModelProperty(value = "业务类型  	0:初领 1:增领 9:其他")
    private java.lang.String busitype;
    /**驾驶证号*/
    @ApiModelProperty(value = "驾驶证号")
    private java.lang.String drilicnum;
    /**驾驶证初领日期  	YYYYMMDD*/
    @ApiModelProperty(value = "驾驶证初领日期  	YYYYMMDD")
    private java.lang.String fstdrilicdate;
    /**培训车型 	下列编码单选：
     A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
    @Excel(name = "培训车型", width = 15)
    @ApiModelProperty(value = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String traintype;
    /**报名时间 	YYYYMMDD*/
    @NotNull(message="报名时间不能为空")
    @Excel(name = "报名时间", width = 15)
    @ApiModelProperty(value = "报名时间 	YYYYMMDD")
    private java.lang.String applydate;
    /**图片路径*/
    @ApiModelProperty(value = "图片路径")
    private java.lang.String photopath;
    /**驾校名称*/
    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private java.lang.String insname;
    /**学员统一编号 推送全国平台/计时平台获取*/
    @Excel(name = "学员统一编号", width = 15)
    @ApiModelProperty(value = "学员统一编号")
    private java.lang.String stunum;
    /**银行编号*/
    @Excel(name = "银行编号", width = 15)
    @ApiModelProperty(value = "银行编号")
    private java.lang.String bankcode;
    /**银行名称*/
    @Excel(name = "银行名称", width = 15)
    @ApiModelProperty(value = "银行名称")
    private java.lang.String bankname;
    /**是否签约合同 0未签约,1已签约,2签约失败*/
    @Excel(name = "是否签约", width = 15,replace = {"未签约_0","已签约_1","签约失败_2"})
    @ApiModelProperty(value = "是否签约合同 0未签约,1已签约,2签约失败")
    private java.lang.Integer issigncontract;
    /**是否推送全国 0未推送,1已推送,2推送失败*/
    @Excel(name = "是否推送全国", width = 15,replace = {"未推送_0","已推送_1","推送失败_2"})
    @ApiModelProperty(value = "是否推送全国 0未推送,1已推送,2推送失败")
    @Dict(dicCode = "sys_push")
    private java.lang.Integer istoqg;
    /**是否推送计时 0未推送,1已推送,2推送失败*/
    @Excel(name = "是否推送计时", width = 15,replace = {"未推送_0","已推送_1","推送失败_2"})
    @ApiModelProperty(value = "是否推送计时 0未推送,1已推送,2推送失败")
    @Dict(dicCode = "sys_push")
    private java.lang.Integer istojs;
    /**原准驾车型*/
    @ApiModelProperty(value = "原准驾车型")
    private java.lang.String perdritype;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**创建者*/
    @ApiModelProperty(value = "创建者")
    private java.lang.String createBy;
    /**更新者*/
    @ApiModelProperty(value = "更新者")
    private java.lang.String updateBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private java.util.Date updateTime;
    /**基础费用*/
    @ApiModelProperty(value = "基础费用")
    private java.lang.String basicCost;
    /**第二部分费用*/
    @ApiModelProperty(value = "第二部分费用")
    private java.lang.String secondCost;
    /**第三部分费用*/
    @ApiModelProperty(value = "第三部分费用")
    private java.lang.String thirdCoast;
    /**冻结金额*/
    @ApiModelProperty(value = "冻结金额")
    private java.lang.String frozenAmount;
    /**厂商编号*/
    @ApiModelProperty(value = "厂商编号")
    @Dict(dicCode = "sys_platform")
    private java.lang.String platform;
    /**教练编号*/
    @ApiModelProperty(value = "教练编号")
    private java.lang.String coachnum;
    /**是否为跨地市转校 1:是  2:否*/
    @ApiModelProperty(value = "是否为跨地市转校 1:是  2:否")
    private java.lang.String iscrosscity;
    /**源驾校地市名称*/
    @ApiModelProperty(value = "源驾校地市名称")
    private java.lang.String crosscityname;
    /**是否推送银行 0未推送,1已推送,2推送失败*/
    @Excel(name = "是否推送银行", width = 15,replace = {"未推送_0","已推送_1","推送失败_2"})
    @ApiModelProperty(value = "是否推送银行 0未推送,1已推送,2推送失败")
    @Dict(dicCode = "sys_push")
    private java.lang.Integer istobank;
    /**1:普通学员 2:黑户*/
    @ApiModelProperty(value = "1:普通学员 2:黑户")
    private java.lang.Integer stutype;
    /**代管人身份证*/
    @ApiModelProperty(value = "代管人身份证")
    private java.lang.String otherIdcard;
    /**代管人手机号*/
    @ApiModelProperty(value = "代管人手机号")
    private java.lang.String otherPhone;
    /**身份证物理id*/
    @ApiModelProperty(value = "身份证物理id")
    private java.lang.String cardid;
    /**是否老学员 0或 null ： 否 1 ：是*/
    @ApiModelProperty(value = "是否老学员 0或 null ： 否 1 ：是")
    private java.lang.String isold;
    /**0:未签名,1:已签名,2:签名失败*/
    @Excel(name = "是否签名", width = 15,replace = {"未签名_0","已签名_1","签名失败_2"})
    @ApiModelProperty(value = "0:未签名,1:已签名,2:签名失败")
    private java.lang.Integer issignature;
    /**是否推送app 0未推送,1已推送,2推送失败*/
    @Excel(name = "是否推送app", width = 15,replace = {"未推送_0","已推送_1","推送失败_2"})
    @ApiModelProperty(value = "是否推送app 0未推送,1已推送,2推送失败")
    private java.lang.Integer istoapp;
    /**签名照片路径*/
    @ApiModelProperty(value = "签名照片路径")
    private java.lang.String signpath;

    /**是否有银行开户冻结*/
    private String regfalg;

    /**是否同步阶段报审*/
    private String stimefalg;

    /**外地转入前所在地市*/
    private String fromarea;
    /**所属区县*/
    private String district;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 出生地市
     */
    private String birthplace;

    @TableField(exist = false)
    private String applydateBegin;
    @TableField(exist = false)
    private String applydateEnd;

    /**
     * 参数
     */
//    @TableField(exist = false)
//    private Map<String, Object> params;

    /**教练员姓名*/
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    @Sensitive(type = SensitiveTypeEnum.CHINESE_NAME_ZLB)
    private java.lang.String coachname;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    private java.lang.String contracturl;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    private java.lang.String totalamount;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    private java.lang.String unitprice;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    private Boolean isSchool;
    @TableField(exist = false,whereStrategy = FieldStrategy.NEVER)
    @IgnoreScanAnno
    private List<String> inscodes;

    @TableField(exist = false)
    private String idnum;

    @TableField(exist = false)
    private Long subject;

    @TableField(exist = false)
    private Long vehicletime;

    @TableField(exist = false)
    private Long classtime;

    @Excel(name = "课程编号", width = 15)
    @ApiModelProperty(value = "课程编号")
    private String classno;

    @TableField (exist = false)
    private String contractstatus;
}
