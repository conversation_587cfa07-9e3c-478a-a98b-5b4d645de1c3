package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jky.crypto.annotation.CryptoFieldAnno;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: t_m_zlb_log
 * @Author: jeecg-boot
 * @Date:   2024-01-18
 * @Version: V1.0
 */
@Data
@TableName("t_m_zlb_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_zlb_log对象", description="t_m_zlb_log")
public class ZlbLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**
     * 日志类型
     * */
	@Excel(name = "日志类型", width = 15)
    @ApiModelProperty(value = "日志类型")
    //@Dict(dicCode = "sensitive_log_type")
    private Integer logType;
	/**日志内容*/
	@Excel(name = "日志内容", width = 15)
    @ApiModelProperty(value = "日志内容")
    private String logContent;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15)
    @ApiModelProperty(value = "操作类型")
    private String operateType;
	/**临时凭证id*/
	@Excel(name = "临时凭证id", width = 15)
    @ApiModelProperty(value = "临时凭证id")
    private String certId;
	/**浙里办用户id*/
    @ApiModelProperty(value = "浙里办用户id")
    private String zlbId;
    @ApiModelProperty(value = "入参")
    private String requestParam;
	/**IP*/
	@Excel(name = "IP", width = 15)
    @ApiModelProperty(value = "IP")
    private String ip;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


    /**类型 0:学员;1:教练员*/
    @Excel(name = "类型 0:学员;1:教练员", width = 15)
    @ApiModelProperty(value = "类型 0:学员;1:教练员")
    private String personType;
    /**学员/教练员编号*/
    @Excel(name = "学员/教练员编号", width = 15)
    @ApiModelProperty(value = "学员/教练员编号")
    private String personNum;
    /**姓名*/
    @Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String name;

    /**身份证*/
    @Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    private String idcard;
    /**驾校编号*/
    @Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private String inscode;
    /**驾校名称*/
    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
    /**ip行政区划*/
    @Excel(name = "ip行政区划", width = 15)
    @ApiModelProperty(value = "ip行政区划")
    private String ipCode;
    /**是否更新人员信息 0 否 1 是*/
    @Excel(name = "是否更新人员信息 0 否 1 是", width = 15)
    @ApiModelProperty(value = "是否更新人员信息 0 否 1 是")
    private String isPerson;
    /**是否更新ip信息 0 否 1 是*/
    @Excel(name = "是否更新ip信息 0 否 1 是", width = 15)
    @ApiModelProperty(value = "是否更新ip信息 0 否 1 是")
    private String isIp;

}
