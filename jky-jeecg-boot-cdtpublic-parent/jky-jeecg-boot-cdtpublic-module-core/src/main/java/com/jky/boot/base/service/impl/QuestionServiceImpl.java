package com.jky.boot.base.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.base.dao.Question;
import com.jky.boot.base.mapper.QuestionMapper;
import com.jky.boot.base.service.IQuestionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 题库
 *
 * <AUTHOR>
 * @version 2024-12-27
 */
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, Question> implements IQuestionService {

    @Override
    public List<String> getRandom100(Question question) {
        return baseMapper.getRandom100(question);
    }
}
