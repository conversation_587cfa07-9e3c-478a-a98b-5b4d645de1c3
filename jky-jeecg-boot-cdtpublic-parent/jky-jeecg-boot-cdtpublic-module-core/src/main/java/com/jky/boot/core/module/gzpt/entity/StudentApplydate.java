package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 申请修改学员报名日期
 */
@Data
@TableName("t_m_student_applydate")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_student_applydate对象", description="申请修改学员报名日期")
public class StudentApplydate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 学员编号
     */
    private String stunum;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 老的报名日期
     */
    private String oldApplydate;

    /**
     * 新报名日期
     */
    private String newApplydate;

    /**
     * pdf面签单存储在oss地址
     */
    private String pdf;

    /**
     * 审核人id
     */
    @TableField(value = "`shr`")
    private String checker;
    /**
     * 审核人姓名
     */
    private String shrXm;

    /**
     * 审核日期
     */
    private Date shrq;

    /**
     * 创建日期
     */
    private Date crdate;

    /**
     * 厂商
     */
    private String platform;

    /**
     * 厂商名称
     */
    private String platformMc;

    /**
     * 审核状态
     */
    private String shzt;

    /**
     * 审核状态
     */
    private String shyy;

    private Long pdfid;

    private String district;
}
