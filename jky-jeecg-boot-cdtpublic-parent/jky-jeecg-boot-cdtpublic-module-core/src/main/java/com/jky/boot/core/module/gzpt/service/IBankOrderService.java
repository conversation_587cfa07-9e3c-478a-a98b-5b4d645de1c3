package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.dto.MoneyTotalDto;
import com.jky.boot.core.module.gzpt.dto.OrderDto;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.vo.MoneyTotalVo;

import java.util.List;

/**
 * @Description: 资金划转对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IBankOrderService extends IService<BankOrder> {

    int deleteBankOrderByStunum(String stunum);

    List<BankOrder> selectBankOrderList(BankOrder setStatus);

    int updateBankOrderByStunum(BankOrder bankOrder);

    IPage<BankOrder> selectBankOrderPage(Page<BankOrder> page, BankOrder bankOrder);

    BankOrder selectTotalTransferAmount(BankOrder bankOrder);

//    String updateBankOrder(Map<String, Object> param);

    List<MoneyTotalVo> moneyTotal(BankOrder bankOrder);

    String resubmit(String id);
    List<String> selectstunum ();

    MoneyTotalDto moneytotalAmount(BankOrder bankOrder);

    List<BankOrder> selectfalse();

    BankOrder genOrder(OrderDto orderDto);
}
