package com.jky.boot.core.module.gzpt.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.common.exception.JkyCdtpublicException;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.DateIdUtil;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.mapper.*;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.ICoachService;
import com.jky.boot.core.module.gzpt.service.IStageTrainningTimeService;
import com.jky.boot.core.module.gzpt.utils.PushBankUtil;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import com.jky.boot.core.module.sxgzpt.entity.TMEvaluation;
import com.jky.boot.core.module.sxgzpt.service.ITMEvaluationService;
import com.jky.boot.core.util.ZlbStuUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * t_m_stage_trainning_time
 */
@Service
@Slf4j
public class StageTrainningTimeServiceImpl extends ServiceImpl<StageTrainningTimeMapper, StageTrainningTime> implements IStageTrainningTimeService {
    @Autowired
    private StageTrainningTimeLogMapper stageTrainningTimeLogMapper;
    @Autowired
    private InstitutionMapper institutionMapper;
    @Autowired
    private PlatformMapper platformMapper;
    @Autowired
    private BankOrderMapper bankOrderMapper;
    @Autowired
    private StudentinfoMapper studentinfoMapper;
    @Autowired
    private BankMapper bankMapper;
    @Autowired
    private ICoachService coachService;
    @Autowired
    private ITMEvaluationService tMEvaluationService;
    @Autowired
    private IBankRegService bankRegService;

    /**
     * 查询报审申请管理
     *
     * @param id 报审申请管理主键
     * @return 报审申请管理
     */
    @Override
    public StageTrainningTime selectStageTrainningTimeById(String id) {
        return baseMapper.selectStageTrainningTimeById(id);
    }

    /**
     * 查询报审申请管理列表
     *
     * @param stageTrainningTime 报审申请管理
     * @return 报审申请管理
     */
    @Override
    public List<StageTrainningTime> selectStageTrainningTimeList(StageTrainningTime stageTrainningTime) {
        return baseMapper.selectStageTrainningTimeList(stageTrainningTime);
    }

    @Override
    public List<StageTrainningTime> findStageTrainningTimeList(StageTrainningTime stageTrainningTime) {
        return baseMapper.findStageTrainningTimeList(stageTrainningTime);
    }

    @Override
    public IPage<StageTrainningTime> findStageTrainningTimeListPage(Page<StageTrainningTime> page, StageTrainningTime stageTrainningTime) {
        return baseMapper.findStageTrainningTimeList(page,stageTrainningTime);
    }


    /**
     * 新增报审申请管理
     *
     * @param stageTrainningTime 报审申请管理
     * @return 结果
     */
    @Override
    public int insertStageTrainningTime(StageTrainningTime stageTrainningTime) {
        return baseMapper.insert(stageTrainningTime);
    }

    /**
     * 修改报审申请管理
     *
     * @param stageTrainningTime 报审申请管理
     * @return 结果
     */
    @Override
    public int updateStageTrainningTime(StageTrainningTime stageTrainningTime) {
        //如果审核不通过
//        if (stageTrainningTime.getAuditstate().equals("2")) {
//            TotalTime totalTime = new TotalTime();
//            totalTime.setIsBaoshen("3");
//            totalTime.setSubject(stageTrainningTime.getSubject());
//            totalTime.setStunum(stageTrainningTime.getStunum());
//            totalTimeMapper.updateIsBaoshen(totalTime);
//        }
        return baseMapper.updateById(stageTrainningTime);
    }

    //推送银行 保存订单
    @Override
    public String saveOrder(StageTrainningTime stageTrainningTime) {
        return null;
    }

    @Override
    public String oldstuSaveOrder(StageTrainningTime stageTrainningTime) {
        String result = "";
        //审核通过 生成订单

        if (stageTrainningTime.getAuditstate().equals("1")) {
            BankOrder bankOrder = new BankOrder();
            bankOrder.setStunum(stageTrainningTime.getStunum());
            bankOrder.setOrdertype(stageTrainningTime.getSubject());
            bankOrder.setStatus(1L);

            //查询是否已推送银行
            List<BankOrder> bankOrders = bankOrderMapper.selectBankOrderList(bankOrder);

            bankOrder.setInscode(stageTrainningTime.getInscode());
            bankOrder.setInsname(stageTrainningTime.getInsname());
            bankOrder.setBosid(stageTrainningTime.getId() + "");
            bankOrder.setStuname(stageTrainningTime.getStuname());

            Studentinfo studentinfo = new Studentinfo();
            studentinfo.setStunum(stageTrainningTime.getStunum());

            List<Studentinfo> studentinfos = studentinfoMapper.selectList(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getStunum,stageTrainningTime.getStunum()));
            if (studentinfos.size() == 0) {
                return "学员不存在";//学员不存在
            }
            bankOrder.setBankcode(studentinfos.get(0).getBankcode());
            if (StringUtils.isEmpty(studentinfos.get(0).getBankname())) {
                Bank bank = new Bank();
                bank.setBankCode(studentinfos.get(0).getBankcode());
                Bank bank1 = bankMapper.selectOne(new LambdaQueryWrapper<Bank>().eq(Bank::getBankCode, studentinfos.get(0).getBankcode()));
                if (Objects.nonNull(bank1)) {
                    bankOrder.setBankname(bank1.getBankName());
                }
            } else {
                bankOrder.setBankname(studentinfos.get(0).getBankname());
            }


            JSONObject json = new JSONObject();

            String transferamount = "0";
            String remark = "";
            //第一笔在冻结后自动划转
            //划转失败后可以重新推送银行
            Studentinfo studentinfo1 = studentinfos.get(0);
            List<BankReg> bankRegList = bankRegService.list(new LambdaQueryWrapper<>(BankReg.class).eq(BankReg::getStunum, studentinfo1.getStunum()));
            List<BankReg> collect = bankRegList.stream().filter(e -> e.getStatus() == 3).collect(Collectors.toList());
            if (collect.isEmpty()){
                return "该学员无冻结成功记录";
            }
            remark = "3";
            if (bankRegList.isEmpty()){
                return "该学员无冻结信息无法划转";
            }
            BigDecimal reduce = bankRegList.stream().map(BankReg::getRemainingAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            transferamount = reduce.toString();
            if ("06C3CRCB".equals(bankRegList.get(0).getBankcode())){
                json.put("stunum", bankRegList.get(0).getStunum());
                json.put("inscode",bankRegList.get(0).getInscode());
            }else if (Objects.nonNull(studentinfo1.getIsold())&&Integer.parseInt(studentinfo1.getIsold())==1&&studentinfo1.getPlatform().equals("127B6EC4A11")){
                json.put("stunum", bankRegList.get(0).getOldStunum());
                json.put("inscode",bankRegList.get(0).getOldInscode());
            }else {

                if (Objects.nonNull(bankRegList.get(0).getOldStunum())){
                    json.put("stunum", bankRegList.get(0).getOldStunum());
                    json.put("inscode",bankRegList.get(0).getOldInscode());
                }else {
                    json.put("stunum", bankRegList.get(0).getStunum());
                    json.put("inscode",bankRegList.get(0).getInscode());
                }
            }

            BankOrder bankOrder1 = bankOrderMapper.selectOne(
                    new LambdaQueryWrapper<>(BankOrder.class)
                            .eq(BankOrder::getOrdertype, remark)
                            .eq(BankOrder::getStunum, studentinfo1.getStunum())
                            .eq(BankOrder::getStatus, 2));

            json.put("transferamount", transferamount);
            json.put("remark", remark);
//            String id = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + (int)(Math.random()*900 + 100);

//            if (bankRegList.get(0).getBankcode().equals("9IC3C55B")){
                String id = DateIdUtil.getSeq();
//            }
            if (Objects.nonNull(bankOrder1)){
                id = bankOrder1.getId();
            }
            //教练员分账--农商
            if (PushBankUtil.isCoachAccount(bankOrder)) {
                json.put("coachnum", bankOrder.getCoachnum());
            }
            json.put("orderno", id);
            json.put("serialno", bankRegList.get(0).getSerialno());
            bankOrder.setTransferAmount(new BigDecimal(transferamount));
            if (!bankRegList.get(0).getInscode().equals(studentinfos.get(0).getInscode())){
                return "该学员驾校与资金冻结所在驾校不一致";
            }
            int i = 0;
            try {
                if (bankOrders != null && bankOrders.size() > 0) {
                    bankOrder.setId(bankOrders.get(0).getId());
                    bankOrder.setUpdatedate(new Date());
                    i = bankOrderMapper.updateById(bankOrder);
                } else {
                    //资金划转
                    result = PushBankUtil.pushFundTransfer(3, json, bankRegList.get(0).getBankcode());
                    JSONObject jsonObject = JSON.parseObject(result);
                    //推送失败
                    if (Integer.parseInt(jsonObject.get("errorcode").toString()) != 0) {
                        return jsonObject.get("message").toString();
                    }
                    //保存订单
                    bankOrder.setStatus(0L);
                    bankOrder.setId(id);
                    bankOrder.setIdcard(studentinfos.get(0).getIdcard());
                    bankOrder.setBankcode(bankRegList.get(0).getBankcode());
                    bankOrder.setBankname(bankRegList.get(0).getBankname());
                    bankOrder.setCreatedate(new Date());
                    i = bankOrderMapper.insert(bankOrder);
                }
            } catch (IOException e) {
                e.printStackTrace();
                return "推送银行失败";
            }
            if (i == 0) {
                return "订单保存失败";//订单保存失败
            }
        } else {
            return "报审需要审核通过,才能推送银行";
        }
        return null;
    }

    /**
     * 批量删除报审申请管理
     *
     * @param ids 需要删除的报审申请管理主键
     * @return 结果
     */
    @Override
    public int deleteStageTrainningTimeByIds(Long[] ids) {
        return baseMapper.deleteStageTrainningTimeByIds(ids);
    }

    /**
     * 删除报审申请管理信息
     *
     * @param id 报审申请管理主键
     * @return 结果
     */
    @Override
    public int deleteStageTrainningTimeById(Long id) {
        return baseMapper.deleteStageTrainningTimeById(id);
    }


    @Override
    public int deleteStageTrainningTimeByStunum(String stunum) {
        return baseMapper.deleteStageTrainningTimeByStunum(stunum);
    }


    @Override
    public String checkStage(StageTrainningTime stageTrainningTime) {
        JSONObject json = new JSONObject();
        json.put("stunum", stageTrainningTime.getStunum());
        json.put("subject", stageTrainningTime.getSubject());
        json.put("vehicletime", stageTrainningTime.getVehicletime());
        json.put("classtime", stageTrainningTime.getClasstime());
        json.put("simulatortime", stageTrainningTime.getSimulatortime());
        json.put("networktime", stageTrainningTime.getNetworktime());
        CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.querysubject, json.toString());
        StageTrainningTimeLog log = new StageTrainningTimeLog();
        log.setStageid(stageTrainningTime.getId());
        log.setMessage(commonResponse.getMessage());
        JSONObject json1 = (JSONObject) JSON.toJSON(commonResponse.getData());
        if (commonResponse.getErrorcode() == 0) {
            log.setRecarray(json1.get("recarray").toString());
            log.setInscode(stageTrainningTime.getInscode());
            log.setStunum(stageTrainningTime.getStunum());
            log.setSubject(json1.get("subject").toString());
        }
        long logcount = stageTrainningTimeLogMapper.selectCount(
                Wrappers.<StageTrainningTimeLog>lambdaQuery().eq(StageTrainningTimeLog::getStageid,stageTrainningTime.getId())
        );
        if (logcount == 0) {
            log.setCrdate(new Date());
            stageTrainningTimeLogMapper.insertStageTrainningTimeLog(log);
        } else {
            log.setUpdateTime(new Date());
            stageTrainningTimeLogMapper.updateStageTrainningTimeLog(log);
        }
        if ("本部分已审核通过，无需查询".equals(commonResponse.getMessage())){
            stageTrainningTime.setIspushjs(1);
            stageTrainningTime.setAuditstate("1");
        }
        stageTrainningTime.setIspushsj(1);
        stageTrainningTime.setPushsjdate(new Date());
        baseMapper.updateStageTrainningTime(stageTrainningTime);
        return commonResponse.getMessage();
    }

    //推送计时
    @Override
    public int stagePushJs(StageTrainningTime stageTrainningTime) throws Exception {

        Studentinfo stu = new Studentinfo();
        stu.setIdcard(stageTrainningTime.getIdcard());
        stu.setStunum(stageTrainningTime.getStunum());
        List<Studentinfo> studentinfos = studentinfoMapper.selectList(new LambdaQueryWrapper<>(Studentinfo.class).eq(Studentinfo::getStunum,stageTrainningTime.getStunum()).eq(Studentinfo::getIdcard,stageTrainningTime.getIdcard()));
        Studentinfo studentinfo = studentinfos.get(0);
//        JSONObject insjson = new JSONObject();
//        insjson.put("inscode",stageTrainningTime.getInscode());
//        insjson.put("coachnum","");
//        CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.jgptUrl+"/getFocusList", insjson.toString());
//
//        JSONObject json1 = (JSONObject) JSON.toJSON(commonResponse.getData());
//        int focusList = Integer.parseInt(json1.get("focusList").toString());
//        if (focusList>0 ){
//            List<ClassRecordDetail> list = classRecordDetailMapper.selectList
//                    (new LambdaQueryWrapper<>(ClassRecordDetail.class)
//                            .eq(ClassRecordDetail::getStunum, stageTrainningTime.getStunum())
//                            .likeRight(ClassRecordDetail::getSubjcode, "___" + stageTrainningTime.getSubject()));
//            List<ClassRecordDetail> collect = list.stream().filter(e -> "0".equals(e.getAuditstate())).collect(Collectors.toList());
//            if (collect.size()>0 && Integer.parseInt(studentinfo.getApplydate())>********){
//                return 3;
//            }
//        }

        List<String> car = new ArrayList<>();
        car.add("C1");
        car.add("C2");
        if (stageTrainningTime.getSubject() !=1){
            List<BankOrder> bankOrderList = bankOrderMapper.selectList(new LambdaQueryWrapper<>(BankOrder.class).eq(BankOrder::getStunum, stageTrainningTime.getStunum()).eq(BankOrder::getOrdertype, stageTrainningTime.getSubject()));

            if (CollectionUtils.isEmpty(bankOrderList)
                    && car.contains(studentinfo.getTraintype())
                    && Integer.parseInt(studentinfo.getApplydate())>=********){
                return 4;
            }
        }
        if (Integer.parseInt(stageTrainningTime.getAuditstate())==1){
            stageTrainningTime.setIspushjs(1);
            baseMapper.updateById(stageTrainningTime);
            return 0;
        }
        JSONObject json = new JSONObject();
        json.put("bosid", stageTrainningTime.getId().toString());
        json.put("stunum", stageTrainningTime.getStunum());
        json.put("inscode", stageTrainningTime.getInscode());
        json.put("subject", stageTrainningTime.getSubject());
        json.put("totaltime", stageTrainningTime.getTotaltime());
        json.put("duration", stageTrainningTime.getTotaltime());
        json.put("vehicletime", stageTrainningTime.getVehicletime());
        json.put("classtime", stageTrainningTime.getClasstime());
        json.put("simulatortime", stageTrainningTime.getSimulatortime());
        json.put("networktime", stageTrainningTime.getNetworktime());

        json.put("mileage", stageTrainningTime.getMileage());
        json.put("esignature", 0);
        json.put("pdfid", 0);
        json.put("rectype", 5);
        StageTrainningTimeLog stageTrainningTimeLog = stageTrainningTimeLogMapper.selectStageTrainningTimeLogByStageid(stageTrainningTime.getId());
        if (Objects.isNull(stageTrainningTimeLog)||StringUtils.isEmpty(stageTrainningTimeLog.getRecarray()) || (StringUtils.isNotEmpty(stageTrainningTimeLog.getMessage()) && !stageTrainningTimeLog.getMessage().equals("查询成功"))) {
            return 9;//校验不合格
        }
        json.put("recarray", stringToArr(stageTrainningTimeLog.getRecarray()));
        json.put("examresult", 0);//不合格

        if (studentinfos.size() == 0) {
            return 2;//学员不存在
        }
        //取学员厂商
        String platform = "";

        platform = studentinfo.getPlatform();
        //学员没有厂商就取驾校厂商
        if (StringUtils.isEmpty(platform)) {
            Institution byInscode = institutionMapper.getByInscode(stageTrainningTime.getInscode());
            if (byInscode != null) {
                if (StringUtils.isNotEmpty(byInscode.getPlatform())) {
                    platform = byInscode.getPlatform();
                }
            }
        }
        Platform byPlatformSerialNumber = platformMapper.getByPlatformSerialNumber(platform);
        String url = byPlatformSerialNumber.getApi() + "applyreviewmsg";
        JSONObject data = new JSONObject();
        data.put("Messagetype", 1);
        data.put("data", json);

        CommonResponse push = PushJsUtil.push(data, url);

       /* JSONObject result=JSONObject.parseObject(push.getData().toString());
        System.out.println("data:"+push.getData());
        Integer code = Integer.valueOf(result.get("code").toString());*/
        if (push.getErrorcode() == 0) {
            stageTrainningTime.setIspushjs(1);
        } else {
            stageTrainningTime.setIspushjs(2);
            stageTrainningTime.setPushjsdate(new Date());
            baseMapper.updateStageTrainningTime(stageTrainningTime);
            throw new JkyCdtpublicException(push.getMessage());

        }
        stageTrainningTime.setPushjsdate(new Date());
        baseMapper.updateStageTrainningTime(stageTrainningTime);
        return push.getErrorcode();
    }

    @Override
    public List<StageTrainningTime> findByIsBank(Long isbank) {
        return baseMapper.findByIsBank(isbank);
    }

    @Override
    public List<StageTrainningTime> findOldByIsBank(String stunum) {
        return baseMapper.findOldByIsBank(stunum);
    }


    @Override
    public List<StageTrainningTime> findStageByIds(Long[] ids) {
        return baseMapper.findStageByIds(ids);
    }

    @Override
    public void automaticPushSj(Long ispushsj) {
        List<StageTrainningTime> stageTrainningTimes = baseMapper.automaticPushSj(ispushsj);
        for (StageTrainningTime sta : stageTrainningTimes) {
            this.checkStage(sta);
        }

    }

    @Override
    public void automaticPushJs(Long ispushjs) throws Exception {
        List<StageTrainningTime> stageTrainningTimes = baseMapper.automaticPushJs(ispushjs);
        for (StageTrainningTime sta : stageTrainningTimes) {
            this.stagePushJs(sta);
        }
    }

    public List<Map<String, String>> stringToArr(String s){
        if(StringUtils.isEmpty(s)){
            return null;
        }
        // 结果 map
        List<Map<String, String>> resultMap = new ArrayList<>();
        // 去除数组字符串中的中括号
        String replace = s.replace("[", "");
        String realS = replace.replace("]", "");
        // 拆分
        String[] split = realS.split(",");
        // map 组装
        for (String one : split) {
            // 去除大括号
            String replace1 = one.replace("{", "");
            String realOne = replace1.replace("}", "");
            // 拆分
            String[] map = realOne.split(":");
            // 去除引号
            for(int i = 0; i < 2; i++){
                map[i] = map[i].replace("\"", "");
            }
            // 组装
            HashMap<String, String> temp = new HashMap<>();
            temp.put(map[0], map[1]);
            resultMap.add(temp);
        }
        return resultMap;
    }


    @Override
    public IPage<StageTrainningTime> pingjia(Page<StageTrainningTime> page, String id) {
        IPage<StageTrainningTime> iPage = new Page<>();
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (Objects.isNull(stu)) {
            return iPage;
        }

        String stunum = stu.getStunum();
        String coachNum = stu.getCoachnum();
        Coach coach = coachService.getOne(
                Wrappers.lambdaQuery(Coach.class)
                        .eq(Coach::getCoachnum, coachNum)
        );
        if (Objects.isNull(coach)) {
            return iPage;
        }
        String coachName = coach.getName();

        // 查找已评价科目
        List<TMEvaluation> evaluations = tMEvaluationService.list(
                Wrappers.<TMEvaluation>lambdaQuery()
                        .eq(TMEvaluation::getStunum, stunum)
        );
        List<Integer> subjects = evaluations.stream().map(TMEvaluation::getPart).collect(Collectors.toList());
        List<StageTrainningTime> stageList = this.list(
                Wrappers.lambdaQuery(StageTrainningTime.class)
                        .eq(StageTrainningTime::getStunum, stunum)
                        .eq(StageTrainningTime::getAuditstate, "1")
                        .notIn(CollectionUtils.isNotEmpty(subjects), StageTrainningTime::getSubject, subjects)
        );
        if (CollectionUtils.isEmpty(stageList)) {
            return iPage;
        }

        for (StageTrainningTime stageTrainningTime : stageList) {
            stageTrainningTime.setCoachname(coachName);
        }
        iPage.setRecords(stageList);
        return iPage;
    }
}
