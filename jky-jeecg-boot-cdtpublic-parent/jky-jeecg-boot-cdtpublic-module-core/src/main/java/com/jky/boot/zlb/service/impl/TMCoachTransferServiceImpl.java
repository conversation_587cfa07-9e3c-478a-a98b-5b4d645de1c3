package com.jky.boot.zlb.service.impl;

import com.jky.boot.zlb.entity.TMCoachTransfer;
import com.jky.boot.zlb.mapper.TMCoachTransferMapper;
import com.jky.boot.zlb.service.ITMCoachTransferService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: t_m_coach_transfer
 * @Author: jeecg-boot
 * @Date:   2023-06-19
 * @Version: V1.0
 */
@Service
public class TMCoachTransferServiceImpl extends ServiceImpl<TMCoachTransferMapper, TMCoachTransfer> implements ITMCoachTransferService {

    @Override
    public int updateEntitie(TMCoachTransfer transfer) {
        return baseMapper.updateById(transfer);
    }
}
