package com.jky.boot.core.module.gzpt.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReleaseStuCoachSetDto {
    @ApiModelProperty(value = "人员类型0 学员 1 教练员")
    private java.lang.String roleType;
    @ApiModelProperty(value = "操作类型0 人脸比对 1 定位判断 2 特殊学员设置")
    private java.lang.String operateType;
    @ApiModelProperty(value = "学员编号/教练员编号")
    private java.lang.String operateId;
    @ApiModelProperty(value = "ip")
    private java.lang.String ip;
    @ApiModelProperty(value = "操作人id")
    private java.lang.String userId;
    @ApiModelProperty(value = "操作人")
    private java.lang.String username;
}
