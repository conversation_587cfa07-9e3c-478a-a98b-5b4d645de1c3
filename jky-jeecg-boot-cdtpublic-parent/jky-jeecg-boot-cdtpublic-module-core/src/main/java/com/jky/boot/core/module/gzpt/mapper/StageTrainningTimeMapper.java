package com.jky.boot.core.module.gzpt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jky.boot.core.module.gzpt.entity.StageTrainningTime;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: t_m_stage_trainning_time
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface StageTrainningTimeMapper extends BaseMapper<StageTrainningTime> {

    /**
     * 查询报审申请管理
     *
     * @param id 报审申请管理主键
     * @return 报审申请管理
     */
    StageTrainningTime selectStageTrainningTimeById(String id);

    List<StageTrainningTime> findStageTrainningTimeList(@Param("stageTrainningTime")StageTrainningTime stageTrainningTime);
    IPage<StageTrainningTime> findStageTrainningTimeList(IPage<?> page,@Param("stageTrainningTime")StageTrainningTime stageTrainningTime);

    /**
     * 查询报审申请管理列表
     *
     * @param stageTrainningTime 报审申请管理
     * @return 报审申请管理集合
     */
    List<StageTrainningTime> selectStageTrainningTimeList(StageTrainningTime stageTrainningTime);

    /**
     * 修改报审申请管理
     *
     * @param stageTrainningTime 报审申请管理
     * @return 结果
     */
    int updateStageTrainningTime(StageTrainningTime stageTrainningTime);

    /**
     * 删除报审申请管理
     *
     * @param id 报审申请管理主键
     * @return 结果
     */
    int deleteStageTrainningTimeById(Long id);

    /**
     * 批量删除报审申请管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteStageTrainningTimeByIds(Long[] ids);


    //根据ID查询
    List<StageTrainningTime> findStageByIds(Long[] ids);

    //查询未推送银行的数据
    List<StageTrainningTime> findByIsBank(Long isbank);

    //自动推送省局校验
    List<StageTrainningTime> automaticPushSj(Long ispushsj);

    //自动推送学时
    List<StageTrainningTime> automaticPushJs(Long automaticPushJs);

    @Delete("delete from t_m_stage_trainning_time where stunum = #{stunum}")
    int deleteStageTrainningTimeByStunum(String stunum);


    IPage<StageTrainningTime> pinjia(IPage<?> page,  @Param("id")Long id);

    List<StageTrainningTime> findOldByIsBank(@Param("stunum") String stunum);
}
