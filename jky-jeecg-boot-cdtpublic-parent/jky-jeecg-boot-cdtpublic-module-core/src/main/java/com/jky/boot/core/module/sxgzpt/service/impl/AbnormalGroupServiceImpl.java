package com.jky.boot.core.module.sxgzpt.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.dto.ClassRecordDetailTDO;
import com.jky.boot.core.module.gzpt.dto.GroupTDO;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IClassRecordDetailService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.sxgzpt.entity.AbnormalGroup;
import com.jky.boot.core.module.sxgzpt.mapper.AbnormalGroupMapper;
import com.jky.boot.core.module.sxgzpt.service.IAbnormalGroupService;
import com.jky.boot.core.module.sxgzpt.service.ITrainRecordAbnormalService;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常组管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
@DS("slave")
public class AbnormalGroupServiceImpl extends ServiceImpl<AbnormalGroupMapper, AbnormalGroup> implements IAbnormalGroupService {
    @Autowired
    private AbnormalGroupMapper abnormalGroupMapper;
    //    @Autowired
//    private IAbnormalGroupNoService abnormalGroupNoService;
    @Autowired
    private IClassRecordDetailService classRecordDetailService;
    //    @Autowired
//    private ITrainRecordService trainRecordService;
    @Autowired
    private ITrainRecordAbnormalService trainRecordAbnormalService;
//    @Autowired
//    private ITrainRecordAbnormalNoService trainRecordAbnormalNOService;
    @Autowired
    private IStudentinfoService studentService;
//    @Autowired
//    ISendmsgDetailService sendmsgDetailService;

    /**
     * 查询异常组管理列表
     *
     * @param abnormalGroup 异常组管理
     * @return 异常组管理
     */
    @Override
    public IPage<AbnormalGroup> selectAbnormalGroupList(AbnormalGroup abnormalGroup, Page<AbnormalGroup> page) {
        if (abnormalGroup.getType() != null && !abnormalGroup.getType().isEmpty()) {
            abnormalGroup.setAbnormalSeq(abnormalGroup.getType());
            abnormalGroup.setType(null);
        }
        return abnormalGroupMapper.selectAbnormalGroupList(abnormalGroup, page);
    }

    /**
     * 审核 有效/无效
     *
     * @param groupTDO
     */
    @Override
    public String audit(GroupTDO groupTDO) {

        AbnormalGroup abnormalGroup = this.getById(groupTDO.getParentId());
        List<ClassRecordDetailTDO> list = new ArrayList<>();
        List<ClassRecordDetail> classRecordDetailList = this.getClassRecordDetail(abnormalGroup, groupTDO.getType());
        try {
            list = classRecordDetailList.stream().map(item -> {
                ClassRecordDetailTDO bean = new ClassRecordDetailTDO();
                bean.setId(item.getId());
                bean.setStarttime(item.getStarttimeDate());
                bean.setAbnormalType(item.getAbnormalType());
                bean.setAbnormalRnum(item.getAbnormalRnum());
                return bean;
            }).collect(Collectors.toList());

            if (groupTDO.getStatus() == 1) {//有效
                //更新电子教学日志或分钟明细
                ClassRecordDetailTDO classRecordDetail = list.get(0);
                Integer type = groupTDO.getCalsstype() == 0 ? classRecordDetail.getAbnormalType() : groupTDO.getCalsstype();
                updateClassRecordDetail(list, type, groupTDO, abnormalGroup);
            }
            UpdateWrapper updateWrapper = updateTrainAbnormal(groupTDO, abnormalGroup, list);
            trainRecordAbnormalService.update(updateWrapper);
        }catch (Exception e){
            e.printStackTrace();
            abnormalGroup.setStatus(0);
            this.updateById(abnormalGroup);
            return "审核失败";
        }
//        if (groupTDO.getType() == 1) {//疑似违规
////            trainRecordAbnormalNOService.update(updateWrapper);
//        } else {//异常组
//        }
        abnormalGroup.setStatus(1);
        this.updateById(abnormalGroup);
        return "审核成功";

    }

    /**
     * 查询电子教学日志
     * type 1.疑似违规 2.异常组
     *
     * @param abnormalGroup
     * @param type
     * @return
     */
    List<ClassRecordDetail> getClassRecordDetail(AbnormalGroup abnormalGroup, Integer type) {
        String tablename = "";
//        if (type == 1) {
//            tablename = "t_m_train_record_abnormal_no";
//        } else {
            tablename = "t_m_train_record_abnormal";
//        }
        String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", abnormalGroup.getCrdate());

        Date date = DateUtils.parseDate(s);
        List<ClassRecordDetail> list = null;
        if (null != abnormalGroup.getIsNewjgpt() && abnormalGroup.getIsNewjgpt() == 1) {
            list = trainRecordAbnormalService.getClassRecordDetailList(abnormalGroup.getId(), date, tablename);
        } else {
            String time = DateUtils.getTime(date, 20);
            time = time + " 000000";
            Date starttime = DateUtils.parseDate(time);
            list = trainRecordAbnormalService.getClassRecordDetailList(abnormalGroup.getId(), starttime, date, tablename);
        }
        return list;
    }

    /**
     * @param groupTDO
     * @param abnormalGroup
     * @param list
     */
    public UpdateWrapper updateTrainAbnormal(GroupTDO groupTDO, AbnormalGroup abnormalGroup, List<ClassRecordDetailTDO> list) {

        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("group_id", abnormalGroup.getId());

        List<String> abnormalIds = list.stream().map(item -> item.getAbnormalId()).collect(Collectors.toList());
        if (!abnormalIds.isEmpty()){
            updateWrapper.in("id", abnormalIds);
        }

        Date endtime = DateUtils.addSecond(abnormalGroup.getCrdate(), 10);
        updateWrapper.between("crdate", abnormalGroup.getCrdate(), endtime);
        updateWrapper.set("status", groupTDO.getStatus());
        updateWrapper.set("dispose_date", new Date());
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        updateWrapper.set("operator", loginUser.getId());
        updateWrapper.set("operator_name", loginUser.getUsername());
        updateWrapper.set("reason", groupTDO.getReason());
        return updateWrapper;
    }

    /**
     * 更新
     * type 1.分钟明细 2.电子教学日志
     *
     * @param list
     * @param type
     */
    public void updateClassRecordDetail(List<ClassRecordDetailTDO> list, Integer type, GroupTDO groupTDO, AbnormalGroup abnormalGroup) {

        for (ClassRecordDetailTDO bean : list) {

            QueryWrapper queryWrapper = new QueryWrapper();
            String abnormalSeq = abnormalGroup.getAbnormalSeq();
            if (type == 2) {//电子教学日志
                queryWrapper.eq("id", bean.getId());
                queryWrapper.eq("starttime_date", bean.getStarttime());
                ClassRecordDetail recordDetail = classRecordDetailService.getOne(queryWrapper);
                if (1 == recordDetail.getIsAbnormal()) {
                    abnormalSeq = abnormalGroup.getAbnormalSeq() + "," + recordDetail.getAbnormalSeq();
                }
                UpdateWrapper updateWrapper = new UpdateWrapper();
                updateWrapper.eq("id", bean.getId());
                updateWrapper.eq("starttime_date", bean.getStarttime());
                updateWrapper.set("abnormal_seq", abnormalSeq);
//                updateWrapper.set("isabnormalno", 1);
                classRecordDetailService.update(updateWrapper);
            }
//            else if (type == 1) {//分钟明细
//                UpdateWrapper updateWrapper = new UpdateWrapper<>();
//                updateWrapper.eq("rnum", bean.getId());
//                updateWrapper.eq("traintime", bean.getStarttime());
//                updateWrapper.set("abnormal_seq", abnormalSeq);
//                updateWrapper.set("is_abnormal", 1);
//                trainRecordService.update(updateWrapper);
//            }
            //发送通知
//            JSONObject jb = new JSONObject();
//            jb.put("rnum", bean.getAbnormalRnum());
//            jb.put("stunum", abnormalGroupNo.getObjnum());
//            jb.put("abnormalSeq", abnormalGroupNo.getAbnormalSeq());
//            jb.put("abnormal", abnormalGroupNo.getAbnormalName());
//            jb.put("status", groupTDO.getStatus());
//            jb.put("reason", groupTDO.getReason());
//            SendmsgDetail msg = new SendmsgDetail();
//            msg.setUrl("/reviewabnormal");
//            msg.setContent(jb.toString());
//            msg.setIspush(0);
//            Studentinfo studentInfo = studentService.getById(abnormalGroupNo.getObjnum());
//            msg.setPlatform(studentInfo.getPlatform());
//            sendmsgDetailService.save(msg);
        }
    }

    /**
     * 拆分id 和 培训时间/异常id
     *
     * @param idList
     * @return
     */
    public List<ClassRecordDetailTDO> splitIds(String idList) {
        String[] ids = idList.split(",");
        List<ClassRecordDetailTDO> result = new ArrayList<>();
        ClassRecordDetailTDO classRecordDetailTDO = null;
        for (String id : ids) {
            classRecordDetailTDO = new ClassRecordDetailTDO();
            String[] split = id.split("#");
            classRecordDetailTDO.setId(split[0]);
            classRecordDetailTDO.setStarttime(DateUtils.parseDate(split[1]));
            classRecordDetailTDO.setAbnormalId(split[2]);
            classRecordDetailTDO.setAbnormalType(Integer.valueOf(split[3]));
            classRecordDetailTDO.setAbnormalRnum(split[4]);
            result.add(classRecordDetailTDO);
        }
        return result;
    }
}
