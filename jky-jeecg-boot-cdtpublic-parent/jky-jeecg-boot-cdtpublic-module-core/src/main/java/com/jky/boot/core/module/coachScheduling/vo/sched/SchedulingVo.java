package com.jky.boot.core.module.coachScheduling.vo.sched;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @version 2023-10-18  15:11
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SchedulingVo {
    private String todayDate;
    private Integer course;
    private Integer orderNum;
    private Integer orderCap;
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;
    private String id;
    private String companyName;
    private String address;
}
