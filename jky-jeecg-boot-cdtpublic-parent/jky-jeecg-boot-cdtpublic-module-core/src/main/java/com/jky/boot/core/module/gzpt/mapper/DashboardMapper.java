package com.jky.boot.core.module.gzpt.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface DashboardMapper {
    /**
     * 获取学员总数量
     *
     * @return 学员总数量
     */
    long getStudentTotal(@Param("leader") String leader);

    /**
     * 获取教练员总数量
     *
     * @return 教练员总数量
     */
    long getCoachTotal(@Param("leader") String leader);

    /**
     * 获取教练车总数量
     *
     * @return 教练车总数量
     */
    long getCarInfoTotal(@Param("leader") String leader);

    /**
     * 获取驾校总数量
     *
     * @return 驾校总数量
     */
    long getInstitutionTotal(@Param("leader") String leader);

    /**
     * 获取前一周新增学员
     *
     * @return 前一周新增学员
     */
    @MapKey("createTime")
    List<Map<String, Object>> getWeekAddStudentTotal(@Param("leader") String leader, @Param("dates")List<String> dates);
}
