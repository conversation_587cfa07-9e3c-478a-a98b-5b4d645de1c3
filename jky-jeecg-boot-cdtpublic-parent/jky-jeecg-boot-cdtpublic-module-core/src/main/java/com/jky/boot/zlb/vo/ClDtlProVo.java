package com.jky.boot.zlb.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *  学时进度 View Object
 * <AUTHOR>
 * @version 2023-10-11  10:56
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClDtlProVo {
    /**
     * 总学时
     */
    private Double total;

    /**
     * 有效学时
     */
    private Double progress;

    /**
     * 分支学时明细
     */
    private List<BrProVo> branches;

    /**
     * 报审状态（审核状态 0=未通过, 1=审核通过,2=推送失败）
     */
    private Integer auditSta;

    /**
     * 报审时间
     */
    private Date auditDate;
}
