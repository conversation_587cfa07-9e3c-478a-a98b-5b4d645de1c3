package com.jky.boot.core.module.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class DeviceStateUploadParams implements Serializable {
    /**
     * 设备版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String versionName;
    /**
     * 设备编号
     */
    @NotBlank(message = "设备编号不能为空")
    String deviceId;
    /**
     * 设备状态
     * 0离线 1在线 2升级中
     */
    @NotNull(message = "设备状态不能为空")
    private Integer state;
}
