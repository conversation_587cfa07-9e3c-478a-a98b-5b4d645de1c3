package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.cas.entity.BaseZlbPersonInfo;
import com.jky.boot.core.module.gzpt.entity.StudentContract;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IStudentContractService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.Constant;
import com.jky.boot.core.module.sxgzpt.dto.CoachLabelDto;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.boot.zlb.entity.TMCoach;
import com.jky.boot.zlb.entity.TMStudentInfoEnter;
import com.jky.boot.zlb.service.ITMAuthrecordService;
import com.jky.boot.zlb.service.ITMCoachService;
import com.jky.boot.zlb.service.ITMStudentInfoEnterService;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import com.jky.crypto.utils.JkyDesensitizedUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;


@Api(tags = "t_m_coach")
@RestController
@RequestMapping("/zlb/tmc/tMCoach")
@Slf4j
public class TMCoachController extends JeecgController<TMCoach, ITMCoachService> {
    @Autowired
    private ITMCoachService tMCoachService;
    @Autowired
    private IStudentinfoService stuInfoService;
    @Autowired
    private ITMStudentInfoEnterService studentInfoEnterService;
    @Autowired
    private IStudentContractService stuContractService;
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ITMAuthrecordService authRecordService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "t_m_coach-分页列表查询", notes = "t_m_coach-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<TMCoach>> queryPageList(TMCoach tMCoach,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                HttpServletRequest req) {
        QueryWrapper<TMCoach> queryWrapper = QueryGenerator.initQueryWrapper(tMCoach, req.getParameterMap());
        Page<TMCoach> page = new Page<>(pageNo, pageSize);
        IPage<TMCoach> pageList = tMCoachService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "t_m_coach-添加")
    @ApiOperation(value = "t_m_coach-添加", notes = "t_m_coach-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody TMCoach tMCoach) {
        tMCoachService.save(tMCoach);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "t_m_coach-编辑")
    @ApiOperation(value = "t_m_coach-编辑", notes = "t_m_coach-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody TMCoach tMCoach) {
        tMCoachService.updateById(tMCoach);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "t_m_coach-通过id删除")
    @ApiOperation(value = "t_m_coach-通过id删除", notes = "t_m_coach-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        tMCoachService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "t_m_coach-批量删除")
    @ApiOperation(value = "t_m_coach-批量删除", notes = "t_m_coach-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.tMCoachService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "t_m_coach-通过id查询", notes = "t_m_coach-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<TMCoach> queryById(@RequestParam(name = "id") String id) {
        TMCoach tMCoach = tMCoachService.getById(id);
        if (tMCoach == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(tMCoach);
    }

    @RequestMapping(value = "/list2", method = RequestMethod.GET)
    public Result<IPage<TMCoach>> pageByConditions(@RequestParam(name = "inscode", required = false) String inscode,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        // 重点关注名单
        List<String> authCoach = authRecordService.getAuthCoach();
        Page<TMCoach> page = new Page<>(pageNo, pageSize);
        IPage<TMCoach> pageList = tMCoachService.list(page, inscode);
        for (TMCoach record : pageList.getRecords()) {
            record.setMark("3.0").setAuth(0);
            if (StringUtils.isNotBlank(record.getPhotourl())) {
                record.setPhotourl(ossZWYUtils.getPhotoUrl(record.getPhotourl()));
            }
            if (StringUtils.isNotBlank(record.getCoachnum())) {
                CoachLabelDto dto = (CoachLabelDto) redisUtil.get("coach:label:" + record.getCoachnum());
                if (Objects.nonNull(dto)) {
                    record.setMark(String.valueOf(dto.getAverage()));
                    record.setLabel(dto.getTopThreeLabel());
                }
            }
            if (authCoach.contains(record.getCoachnum())) {
                record.setAuth(1);
            }
        }
        return Result.ok(pageList);
    }


    @RequestMapping(value = "/list3", method = RequestMethod.GET)
    public Result<IPage<TMCoach>> pageByConditions(@RequestParam(name = "idcard", required = false) String idcard,
                                                   @RequestParam(name = "name", required = false) String name,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<TMCoach> page = new Page<>(pageNo, pageSize);
        IPage<TMCoach> pageList = tMCoachService.coach(page, idcard, name);
        return Result.OK(pageList);
    }

    @RequestMapping(value = "/person", method = RequestMethod.GET)
    public Result<?> person(@RequestParam(name = "id") String id) {
		BaseZlbPersonInfo person = ZlbStuUtils.getZlbPerson(id);
        if (Objects.isNull(person)) {
            return Result.error("请先登录浙里办！");
        }

        // 有学员信息，直接返回
        Map<String, Object> map = new HashMap<>();
        Studentinfo stu = stuInfoService.getOneByIdCard(person.getIdnum());
        if (Objects.nonNull(stu)) {
            stu.setName(stu.getName().replaceFirst("^.", "*"));
            stu.setPhone(JkyDesensitizedUtils.desensitizeString(stu.getPhone(), DesensitizedTypeEnum.MOBILE_PHONE));
            TMCoach coach = tMCoachService.getOne(
                    Wrappers.<TMCoach>lambdaQuery()
                            .eq(TMCoach::getCoachnum, stu.getCoachnum())
            );
            if (Objects.nonNull(coach)) {
                stu.setBankname(coach.getName());
            }
            IPage<Studentinfo> iPage = new Page<>();
            iPage.setRecords(Collections.singletonList(stu));
            map.put("isSign", 0);
            if (StringUtils.isNotBlank(stu.getStunum())) {
                StudentContract contract = stuContractService.getOne(
                        Wrappers.lambdaQuery(StudentContract.class)
                                .eq(StudentContract::getStunum, stu.getStunum())
                );
                if (Objects.nonNull(contract) &&
                        (Objects.nonNull(contract.getIsSign()) && contract.getIsSign() == 1)) {
                    map.put("isSign", 1);
                }
            }
            redisUtil.set(Constant.YXC_REDIS_STU_NUM_BY_ZLB_ID + id, stu.getStunum(), 3600 * 12);
            map.put("page", iPage);
            map.put("status", 3);
            map.put("msg", "学员信息");
            return Result.ok(map);
        }

        // 无学员信息，返回预报名信息
        TMStudentInfoEnter stuInfoEnter = studentInfoEnterService.getOneByIdCard(person.getIdnum());
        if (Objects.isNull(stuInfoEnter)) {
            map.put("status", 1);
            map.put("msg", "您还未报名,请先报名！");
            return Result.ok(map);
        }
        map.put("status", 2);
        map.put("msg", "您已预报名,请等待驾校审核！");
        return Result.ok(map);
    }

    @RequestMapping(value = "/selectcoach", method = RequestMethod.GET)
    public Result<IPage<TMCoach>> selectname(@RequestParam(name = "inscode", required = false) String inscode,
                                             @RequestParam(name = "traintype", required = false) String traintype,
                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<TMCoach> page = new Page<>(pageNo, pageSize);
        IPage<TMCoach> pageList = tMCoachService.selectcoach(page, inscode, traintype);
        return Result.OK(pageList);
    }

}
