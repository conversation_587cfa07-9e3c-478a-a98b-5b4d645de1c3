package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.CostLimit;
import com.jky.boot.core.module.gzpt.service.ICostLimitService;
import com.jky.boot.system.module.manage.entity.JkySysDepart;
import com.jky.boot.system.module.manage.service.JkySysDepartService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

 /**
 * @Description: 机构资金监管上下限管理对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="机构资金监管上下限管理对象")
@RestController
@RequestMapping("/gzpt/costLimit")
@Slf4j
public class CostLimitController extends JeecgController<CostLimit, ICostLimitService> {
	@Autowired
	private ICostLimitService costLimitService;

	@Autowired
	private JkySysDepartService jkySysDepartService;
	
	/**
	 * 分页列表查询
	 */
	@ApiOperation(value="机构资金监管上下限管理对象-分页列表查询", notes="机构资金监管上下限管理对象-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<CostLimit>> queryPageList(CostLimit costLimit,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		//判断前端传的是id还是orgcode
		List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(costLimit.getInscode());
		costLimit.setInscode(null);
		QueryWrapper<CostLimit> queryWrapper = QueryGenerator.initQueryWrapper(costLimit, req.getParameterMap());
		queryWrapper.in("inscode",inscode);
		queryWrapper.orderByDesc("create_time");
		Page<CostLimit> page = new Page<CostLimit>(pageNo, pageSize);
		IPage<CostLimit> pageList = costLimitService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 */
	@AutoLog(value = "机构资金监管上下限管理对象-添加")
	@ApiOperation(value="机构资金监管上下限管理对象-添加", notes="机构资金监管上下限管理对象-添加")
	@RequiresPermissions("gzpt:limit:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody CostLimit costLimit) {
		JkySysDepart depart = jkySysDepartService.getById(costLimit.getInscode());
		costLimit.setInscode(depart.getOrgCode());
		costLimit.setInsname(depart.getDepartName());
		costLimit.setCreateBy(JkSecurityUtils.getRealname());

		List<CostLimit> costLimits = costLimitService.selectCostLimitListByInscode(costLimit.getInscode());
		if(!costLimits.isEmpty()){
			for (CostLimit limit : costLimits) {
				if (costLimit.getTraintype().equals(limit.getTraintype())) {
					return Result.error("该驾校对" + costLimit.getTraintype() + "车型的限制已经存在,无需重复添加!");
				}
			}
		}
		costLimitService.save(costLimit);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 */
	@AutoLog(value = "机构资金监管上下限管理对象-编辑")
	@ApiOperation(value="机构资金监管上下限管理对象-编辑", notes="机构资金监管上下限管理对象-编辑")
	@RequiresPermissions("gzpt:limit:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody CostLimit costLimit) {
		JkySysDepart depart = jkySysDepartService.selectDeptByleader(costLimit.getInscode());
		costLimit.setInscode(depart.getOrgCode());
		costLimit.setUpdateBy(JkSecurityUtils.getRealname());

		costLimitService.updateCostLimit(costLimit);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 */
	@AutoLog(value = "机构资金监管上下限管理对象-通过id删除")
	@ApiOperation(value="机构资金监管上下限管理对象-通过id删除", notes="机构资金监管上下限管理对象-通过id删除")
	@RequiresPermissions("gzpt:limit:remove")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		costLimitService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 */
	@AutoLog(value = "机构资金监管上下限管理对象-批量删除")
	@ApiOperation(value="机构资金监管上下限管理对象-批量删除", notes="机构资金监管上下限管理对象-批量删除")
	@RequiresPermissions("gzpt:limit:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.costLimitService.deleteCostLimitByIds(ids.split(","));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 */
	@ApiOperation(value="机构资金监管上下限管理对象-通过id查询", notes="机构资金监管上下限管理对象-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<CostLimit> queryById(@RequestParam(name="id",required=true) String id) {
		CostLimit costLimit = costLimitService.selectCostLimitById(id);
		if(costLimit==null) {
			return Result.error("未找到对应数据");
		}
		JkySysDepart depart = jkySysDepartService.selectDeptByleader(costLimit.getInscode());
		costLimit.setInscode(depart.getOrgCode());
		return Result.ok(costLimit);
	}

	 /**
	  * 获取机构资金监管上下限管理详细信息 根据机构编号
	  */
	 @GetMapping(value = "/getByInscode")
	 public Result<?> getInfoByInscode(@RequestParam(name="inscode")String inscode) {
		 JkySysDepart sysDept = jkySysDepartService.getById(inscode);
		 CostLimit costLimit = costLimitService.selectCostLimitListByInscode(sysDept.getOrgCode()).get(0);
		 return Result.ok(costLimit);
	 }
}
