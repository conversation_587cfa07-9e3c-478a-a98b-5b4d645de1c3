package com.jky.boot.core.module.coachScheduling.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.coachScheduling.entity.SchedulingConfig;
import com.jky.boot.core.module.coachScheduling.service.ICoachSchedulingService;
import com.jky.boot.core.module.coachScheduling.service.ISchedulingConfigService;
import com.jky.boot.core.module.coachScheduling.vo.SchedulingConfigVo;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.jky.service.JCommService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.ParameterResolutionDelegate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: coach_config
 * @Author: jeecg-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
@Api(tags="coach_config")
@RestController
@RequestMapping("/coachConfig")
@Slf4j
public class SchedulingConfigController extends JeecgController<SchedulingConfig, ISchedulingConfigService> {

	@Autowired
	private ISchedulingConfigService schedulingConfigService;

	@Autowired
	private ICoachSchedulingService coachSchedulingService;

	@Autowired
	JCommService commService;
	
	/**
	 * 分页列表查询
	 *
	 * @param coachConfig
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "coach_config-分页列表查询")
	@ApiOperation(value="coach_config-分页列表查询", notes="coach_config-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SchedulingConfig coachConfig,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		List<String> inscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(coachConfig.getInscode());
		coachConfig.setInscode(null);
		QueryWrapper<SchedulingConfig> queryWrapper = QueryGenerator.initQueryWrapper(coachConfig, req.getParameterMap());
		queryWrapper.in("inscode",inscodes);
		queryWrapper.orderByAsc("create_time");
		Page<SchedulingConfig> page = new Page<SchedulingConfig>(pageNo, pageSize);
		IPage<SchedulingConfig> pageList = schedulingConfigService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param coachConfig
	 * @return
	 */
	@AutoLog(value = "coach_config-添加")
	@ApiOperation(value="coach_config-添加", notes="coach_config-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SchedulingConfigVo coachConfig) {
		SchedulingConfig schedulingConfig = coachConfig.getSchedulingConfig();
		List<String> inscodes = commService.queryForListString("select org_code from sys_depart where id = ? or org_code = ?",schedulingConfig.getInscode(),schedulingConfig.getInscode() );
		if(inscodes.size() == 0){
			return Result.OK("培训机构不存在！");
		}
		String inscode = inscodes.get(0);
		schedulingConfig.setInscode(inscode);
		schedulingConfigService.saveOrUpdate(schedulingConfig);
		if(coachConfig.getIsAssert()){
			coachSchedulingService.deleteOldScheduling(inscode);
			coachSchedulingService.createScheduling("and f1.inscode = " +inscode);
		}
		return Result.OK("成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param coachConfig
	 * @return
	 */
	@AutoLog(value = "coach_config-编辑")
	@ApiOperation(value="coach_config-编辑", notes="coach_config-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SchedulingConfigVo coachConfig) {
		SchedulingConfig schedulingConfig = coachConfig.getSchedulingConfig();
		List<String> inscodes = commService.queryForListString("select org_code from sys_depart where id = ? or org_code = ?",schedulingConfig.getInscode(),schedulingConfig.getInscode() );
		if(inscodes.size() == 0){
			return Result.OK("培训机构不存在！");
		}
		String inscode = inscodes.get(0);
		schedulingConfig.setInscode(inscode);
		schedulingConfigService.updateById(schedulingConfig);
		if(coachConfig.getIsAssert()){
			coachSchedulingService.createScheduling("and inscode = " + coachConfig.getSchedulingConfig().getInscode());
		}
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "coach_config-通过id删除")
	@ApiOperation(value="coach_config-通过id删除", notes="coach_config-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		schedulingConfigService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "coach_config-批量删除")
	@ApiOperation(value="coach_config-批量删除", notes="coach_config-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.schedulingConfigService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "coach_config-通过id查询")
	@ApiOperation(value="coach_config-通过id查询", notes="coach_config-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SchedulingConfig coachConfig = schedulingConfigService.getById(id);
		if(coachConfig==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(coachConfig);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param coachConfig
//    */
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, SchedulingConfig coachConfig) {
//        return super.exportXls(request, coachConfig, SchedulingConfig.class, "coach_config");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, SchedulingConfig.class);
//    }

}
