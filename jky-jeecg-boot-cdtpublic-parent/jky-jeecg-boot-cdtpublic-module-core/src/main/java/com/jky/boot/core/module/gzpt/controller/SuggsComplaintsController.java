package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.SuggsComplaints;
import com.jky.boot.base.service.ISuggsComplaintsService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 投诉与建议
 *
 * <AUTHOR>
 * @version 2024-02-26
 */
@Api(tags = "投诉与建议")
@RestController
@RequestMapping("/gzpt/suggsComplaints")
@Slf4j
public class SuggsComplaintsController extends JeecgController<SuggsComplaints, ISuggsComplaintsService> {
	private final ISuggsComplaintsService suggsComplaintsService;

	public SuggsComplaintsController(ISuggsComplaintsService suggsComplaintsService) {
		this.suggsComplaintsService = suggsComplaintsService;
	}

	/**
	 * 分页列表查询
	 */
	@ApiOperation(value = "投诉与建议-分页列表查询", notes = "投诉与建议-分页列表查询")
	@GetMapping(value = "/list")
	@DesensitizedAnno
	public Result<IPage<SuggsComplaints>> queryPageList(SuggsComplaints suggsComplaints,
														@RequestParam(name = "pageNo", defaultValue = "1")
														Integer pageNo,
														@RequestParam(name = "pageSize", defaultValue = "10")
														Integer pageSize,
														HttpServletRequest req) {
		List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(suggsComplaints.getInscode());
		suggsComplaints.setInscode(null);
		QueryWrapper<SuggsComplaints> queryWrapper = QueryGenerator.initQueryWrapper(suggsComplaints, req.getParameterMap());
		queryWrapper.in("inscode", insCodes);
		queryWrapper.orderByDesc("create_time");
		Page<SuggsComplaints> page = new Page<>(pageNo, pageSize);
		IPage<SuggsComplaints> pageList = suggsComplaintsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 */
	@AutoLog(value = "投诉与建议-添加")
	@ApiOperation(value = "投诉与建议-添加", notes = "投诉与建议-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody SuggsComplaints suggsComplaints) {
		suggsComplaintsService.save(suggsComplaints);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 */
	@AutoLog(value = "投诉与建议-编辑")
	@ApiOperation(value = "投诉与建议-编辑", notes = "投诉与建议-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
	public Result<String> edit(@RequestBody SuggsComplaints suggsComplaints) {
		suggsComplaintsService.updateById(suggsComplaints);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 */
	@AutoLog(value = "投诉与建议-通过id删除")
	@ApiOperation(value = "投诉与建议-通过id删除", notes = "投诉与建议-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name = "id") String id) {
		suggsComplaintsService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 */
	@AutoLog(value = "投诉与建议-批量删除")
	@ApiOperation(value = "投诉与建议-批量删除", notes = "投诉与建议-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
		this.suggsComplaintsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 */
	@ApiOperation(value = "投诉与建议-通过id查询", notes = "投诉与建议-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<SuggsComplaints> queryById(@RequestParam(name = "id") String id) {
		SuggsComplaints suggsComplaints = suggsComplaintsService.getById(id);
		if (suggsComplaints == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(suggsComplaints);
	}
}
