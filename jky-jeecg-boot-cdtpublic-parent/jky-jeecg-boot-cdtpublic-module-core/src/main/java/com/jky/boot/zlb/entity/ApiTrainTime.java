package com.jky.boot.zlb.entity;
// default package

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * ApiTrainTime entity. <AUTHOR> Persistence Tools
 */
@Data
@TableName( "api_traintime")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="api_traintime对象", description="api_traintime")
public class ApiTrainTime implements Serializable {
    private static final long serialVersionUID = 1L;

    // Fields
    @TableId(type = IdType.ASSIGN_UUID)
     private String id;
     //身份证号
     private String sfzh;
     //地区编号
     private String dqbm;
     //开始训练时间
     private String kssj;
     //结束训练时间
     private String jssj;
     //本次学时
     private String bcxs;
     //总学时
     private String zxs;
     //教练姓名
     private String coachname;
     //再教育状态0，学习中1，结业
     private String status;
     //本次报名学习时间
     private String bmrq;
     //远程教育厂商, 1或空值(通安), 2(维尔)
     private String factory;

}
