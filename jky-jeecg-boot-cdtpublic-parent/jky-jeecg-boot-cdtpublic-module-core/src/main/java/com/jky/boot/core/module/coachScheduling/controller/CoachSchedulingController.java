package com.jky.boot.core.module.coachScheduling.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.coachScheduling.dto.CoachRestDto;
import com.jky.boot.core.module.coachScheduling.dto.CoachSchedulingDto;
import com.jky.boot.core.module.coachScheduling.dto.CreateCoachSchedulingDto;
import com.jky.boot.core.module.coachScheduling.entity.CoachScheduling;
import com.jky.boot.core.module.coachScheduling.entity.CoachSchedulingTitle;
import com.jky.boot.core.module.coachScheduling.entity.SchedulingConfig;
import com.jky.boot.core.module.coachScheduling.service.ICoachSchedulingService;
import com.jky.boot.core.module.coachScheduling.service.ISchedulingConfigService;
import com.jky.boot.core.module.coachScheduling.vo.DisOperationCoachSchedulingVo;
import com.jky.boot.core.module.coachScheduling.vo.sched.CoachSchedulingVo;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.jky.service.JCommService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

 /**
 * @Description: coach_scheduling
 * @Author: jeecg-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
@Api(tags="coach_scheduling")
@RestController
@RequestMapping("/coachScheduling")
@Slf4j
public class CoachSchedulingController extends JeecgController<CoachScheduling, ICoachSchedulingService> {

	@Autowired
	private ICoachSchedulingService coachSchedulingService;

	@Autowired
	private ISchedulingConfigService schedulingConfigService;

	 @Autowired
	 JCommService commService;

	/**
	 * 分页列表查询
	 *
	 * @param coachScheduling
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "coach_scheduling-分页列表查询")
	@ApiOperation(value="coach_scheduling-分页列表查询", notes="coach_scheduling-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CoachSchedulingDto coachScheduling,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		Integer classNum = 16;
		List<String> inscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(coachScheduling.getInscode());
		SchedulingConfig schedulingConfig = null;
		if(inscodes.size() == 1){
			LambdaQueryWrapper<SchedulingConfig> wrapper = Wrappers.lambdaQuery(SchedulingConfig.class).eq(SchedulingConfig::getInscode, inscodes.get(0));
			schedulingConfig = schedulingConfigService.getOne(wrapper);
		}else if(StringUtils.isNotEmpty(coachScheduling.getCoachName()) || StringUtils.isNotEmpty(coachScheduling.getCoachNum()) || StringUtils.isNotEmpty(coachScheduling.getIdCard())){
			schedulingConfig = schedulingConfigService.queryConfigByCoach(coachScheduling);
		}else{
			return Result.error("请选择培训机构或教练员信息");
		}
		if(schedulingConfig != null){
			classNum = (schedulingConfig.getWorkerEndTime() - schedulingConfig.getWorkerStartTime())*7;
		}
		pageSize = pageSize * classNum;
		Page<CoachSchedulingVo> page = new Page<CoachSchedulingVo>(pageNo, pageSize);
		initCoachScheduling(coachScheduling);
		IPage<CoachSchedulingVo> pageList = coachSchedulingService.queryList(page, coachScheduling,inscodes);
		long pages = pageList.getTotal() / pageSize;
		long total = pageList.getTotal() / (classNum);
		pageList.setTotal(total);
		pageList.setPages(pages);
		return Result.OK(pageList);
	}

	 /**
	  * 分页列表查询
	  *
	  * @param coachScheduling
	  * @param pageNo
	  * @param pageSize
	  * @param req
	  * @return
	  */
	 @AutoLog(value = "coach_scheduling-分页列表查询")
	 @ApiOperation(value="coach_scheduling-分页列表查询", notes="coach_scheduling-分页列表查询")
	 @GetMapping(value = "/zlblist")
	 public Result<?> queryPagezlbList(CoachSchedulingDto coachScheduling,
									@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 Integer classNum = 16;
		 List<String> inscodes  = null;
//		 List<String> inscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(coachScheduling.getInscode());
		 SchedulingConfig schedulingConfig = null;
		 if(Objects.nonNull(coachScheduling.getInscode())){
			 LambdaQueryWrapper<SchedulingConfig> wrapper = Wrappers.lambdaQuery(SchedulingConfig.class).eq(SchedulingConfig::getInscode, coachScheduling.getInscode());
			 schedulingConfig = schedulingConfigService.getOne(wrapper);
		 } else if(StringUtils.isNotEmpty(coachScheduling.getCoachName()) || StringUtils.isNotEmpty(coachScheduling.getCoachNum()) || StringUtils.isNotEmpty(coachScheduling.getIdCard())){
			 schedulingConfig = schedulingConfigService.queryConfigByCoach(coachScheduling);
		 }else{
			 return Result.error("请选择培训机构或教练员信息");
		 }
		 if(schedulingConfig != null){
			 classNum = (schedulingConfig.getWorkerEndTime() - schedulingConfig.getWorkerStartTime())*7;
		 }
		 pageSize = pageSize * classNum;
		 Page<CoachSchedulingVo> page = new Page<CoachSchedulingVo>(pageNo, pageSize);
		 initZlbCoachSchedulingDate(coachScheduling);
		 IPage<CoachSchedulingVo> pageList = coachSchedulingService.queryList(page, coachScheduling,inscodes);
		 long pages = pageList.getTotal() / pageSize;
		 long total = pageList.getTotal() / (classNum);
		 pageList.setTotal(total);
		 pageList.setPages(pages);
		 return Result.OK(pageList);
	 }

	 @AutoLog(value = "coach_scheduling-分页列表查询")
	 @ApiOperation(value="coach_scheduling-分页列表查询", notes="coach_scheduling-分页列表查询")
	 @GetMapping(value = "/alllist")
	 public Result<?> alllist(CoachSchedulingDto coachScheduling,
									@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
									@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		 List<String> inscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(coachScheduling.getInscode());
		 Page<DisOperationCoachSchedulingVo> page = new Page<DisOperationCoachSchedulingVo>(pageNo, pageSize);
		 IPage<DisOperationCoachSchedulingVo> pageList = coachSchedulingService.alllist(page, coachScheduling,inscodes);
		 return Result.OK(pageList);
	 }


	 @AutoLog(value = "排班表头")
	 @ApiOperation(value="排班表头", notes="排班表头")
	 @GetMapping(value = "/title")
	 public Result<?> coachSchedulingtitle(CoachSchedulingDto coachScheduling) {
		 List<String> inscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(coachScheduling.getInscode());
		 initCoachScheduling(coachScheduling);
		 List<CoachSchedulingTitle> list = coachSchedulingService.queryTitle(coachScheduling,inscodes);
		 if(list ==null || list.size() == 0){
			 list = new ArrayList<>();
			 int i = 1;
		 	for(Date date = coachScheduling.getStartDay(); i <= 7; date = DateUtils.addDays(date, 1)){
				list.add(new CoachSchedulingTitle("星期" + DateUtils.getWeek(date) , date,0 )) ;
				i++;
			}
		 }
		 return Result.OK(list);
	 }

	 public void initCoachScheduling(CoachSchedulingDto coachScheduling){
		 Date date = coachScheduling.getDate() == null ? new Date() : coachScheduling.getDate();
		 if(coachScheduling.getType() != null && coachScheduling.getType() == 2){
			 coachScheduling.setStartDay(DateUtils.addDays(date,  8 -DateUtils.getWeek(date)));
			 coachScheduling.setEndDay(DateUtils.addDays(date,14 - DateUtils.getWeek(date)));
		 }else{
			 coachScheduling.setStartDay(DateUtils.addDays(date, -DateUtils.getWeek(date) + 1 ));
			 coachScheduling.setEndDay(DateUtils.addDays(date,7 - DateUtils.getWeek(date)));
		 }
	 }
	 public void initZlbCoachSchedulingDate(CoachSchedulingDto coachScheduling){
		 Date date = coachScheduling.getDate() == null ? new Date() : coachScheduling.getDate();

		 coachScheduling.setStartDay(date);
		 coachScheduling.setEndDay(date);
	 }

	 /**
	  * 创建下周排班表
	  */
	 @AutoLog(value = "coach_scheduling-分页列表查询")
	 @ApiOperation(value="coach_scheduling-分页列表查询", notes="coach_scheduling-分页列表查询")
	 @GetMapping(value = "/create")
	 public Result<?> createCoachScheduling() {
		 coachSchedulingService.createScheduling(null);
		 return Result.OK("生成排班成功");
	 }

	 @AutoLog(value = "coach_scheduling-分页列表查询")
	 @ApiOperation(value="coach_scheduling-分页列表查询", notes="coach_scheduling-分页列表查询")
	 @PostMapping(value = "/createClassroom")
	 public Result<?> createClassroom(@RequestBody CreateCoachSchedulingDto createCoachSchedulingDto) {
		 List<CoachScheduling> coachSchedulings = coachSchedulingService.createClassroom(createCoachSchedulingDto);
		 return Result.OK(coachSchedulings.stream().map(item -> {
			 DisOperationCoachSchedulingVo coachSchedulingVo = new DisOperationCoachSchedulingVo();
			 BeanUtils.copyProperties(item, coachSchedulingVo);
			 coachSchedulingVo.setCompanyName(commService.queryForString("select COMPANY_NAME from t_m_ins_device where INS_DEVICE_ID = ? ", item.getClassRoomId()));
					 return coachSchedulingVo;
				 }).collect(Collectors.toList()));
	 }


	/**
	 *  编辑
	 *
	 * @param
	 * @return
	 */
	@AutoLog(value = "coach_scheduling-编辑")
	@ApiOperation(value="coach_scheduling-编辑", notes="coach_scheduling-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody CoachRestDto coachRest) {
		coachSchedulingService.edit(coachRest);
		return Result.OK("编辑成功!");
	}


	 @PostMapping(value = "/save")
	 public Result<?> save(@RequestBody List<CoachScheduling> coachSchedulings) {
		 for(CoachScheduling coachScheduling : coachSchedulings){
			 coachSchedulingService.check(coachScheduling);
		 }
		 coachSchedulingService.saveBatch(coachSchedulings);
		 return Result.OK("编辑成功!");
	 }

    /**
    * 导出excel
    *
    * @param request
    * @param coachScheduling
    */
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, CoachSchedulingDto coachScheduling) {
//
//    }

	@AutoLog(value = "coach_scheduling-编辑")
	@ApiOperation(value="coach_scheduling-编辑", notes="coach_scheduling-编辑")
	@PutMapping(value = "/editAll")
	public Result<?> editAll(@RequestBody CoachScheduling coachScheduling, @RequestParam(name = "isAlter", defaultValue = "false") Boolean isAlter) {
		coachSchedulingService.editAll(coachScheduling, isAlter);
		return Result.OK("编辑成功!");
	}


	 @GetMapping(value = "/delete")
	 public Result<?> delete(String id) {
		 CoachScheduling coachScheduling = coachSchedulingService.getById(id);
		 if(coachScheduling.getDate().before(new Date())){
			 return Result.error("旧排课不能删除!");
		 }
		 if(coachScheduling.getOrderNum() != 0){
			 return Result.error("该课程已有学员预约，不能删除!");
		 }
		 coachSchedulingService.removeById(id);
		 return Result.OK("编辑成功!");
	 }


}
