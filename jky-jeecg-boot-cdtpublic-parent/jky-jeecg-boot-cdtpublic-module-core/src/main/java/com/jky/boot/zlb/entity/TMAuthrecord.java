package com.jky.boot.zlb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 重点关注名单
 *
 * <AUTHOR>
 * @version 2023-06-20
 */
@Data
@TableName("newjgpt.t_m_authrecord")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_authrecord对象", description="t_m_authrecord")
public class TMAuthrecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**inscode*/
    @Excel(name = "inscode", width = 15)
    @ApiModelProperty(value = "inscode")
    private String inscode;
    /**insname*/
    @Excel(name = "insname", width = 15)
    @ApiModelProperty(value = "insname")
    private String insname;
    /**type*/
    @Excel(name = "type", width = 15)
    @ApiModelProperty(value = "type")
    private String type;
    /**carnum*/
    @Excel(name = "carnum", width = 15)
    @ApiModelProperty(value = "carnum")
    private String objNum;
    /**licnum*/
    @Excel(name = "licnum", width = 15)
    @ApiModelProperty(value = "licnum")
    private String licnum;
    /**name*/
    @Excel(name = "name", width = 15)
    @ApiModelProperty(value = "name")
    private String name;
    /**imei*/
    @Excel(name = "imei", width = 15)
    @ApiModelProperty(value = "imei")
    private String imei;
    /**coachname*/
    @Excel(name = "coachname", width = 15)
    @ApiModelProperty(value = "coachname")
    private String coachname;
    /**createtime*/
    @Excel(name = "createtime", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createtime")
    private Date createtime;
    /**expiretime*/
    @Excel(name = "expiretime", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "expiretime")
    private Date expiretime;
    /**renewal*/
    @Excel(name = "renewal", width = 15)
    @ApiModelProperty(value = "renewal")
    private String renewal;
    /**state*/
    @Excel(name = "state", width = 15)
    @ApiModelProperty(value = "是否有效 1为有效 0为无效")
    private Integer state;
    /**addname*/
    @Excel(name = "addname", width = 15)
    @ApiModelProperty(value = "addname")
    private String addname;
    /**ids*/
    @Excel(name = "ids", width = 15)
    @ApiModelProperty(value = "ids")
    private String ids;
    /**phone*/
    @Excel(name = "phone", width = 15)
    @ApiModelProperty(value = "phone")
    private String phone;
    /**idcard*/
    @Excel(name = "idcard", width = 15)
    @ApiModelProperty(value = "idcard")
    private String idcard;
    /**district*/
    @Excel(name = "district", width = 15)
    @ApiModelProperty(value = "district")
    private String district;

    private String reason;

    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**createBy*/
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
    /**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(exist = false)
    private String oprType;
}
