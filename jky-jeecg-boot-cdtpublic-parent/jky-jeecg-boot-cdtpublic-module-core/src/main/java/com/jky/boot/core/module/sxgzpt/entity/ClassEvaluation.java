package com.jky.boot.core.module.sxgzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 课后评价
 *
 * <AUTHOR>
 * @version 2024-05-13 11:03
 */
@Data
@TableName("t_m_class_evaluation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class ClassEvaluation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 教学日志id = stunum + recnum
     */
    private String recordId;

    /**
     * 学员编号
     */
    private String stunum;

    /**
     * 姓名
     */
    private String stuname;

    /**
     * 培训机构编号 org_code
     */
    private String inscode;

    /**
     * 驾校名称
     */
    private String insname;

    /**
     * 教练编号
     */
    private String coachnum;

    /**
     * 教练名称
     */
    private String coachname;

    /**
     * 总体满意度 1:一星2:二星3:三星4:四星5:五星（最满意）
     */
    private Double overall;

    /**
     * 教学质量评分
     */
    private Double teachingQuality;

    /**
     * 教学态度评分
     */
    private Double teachingAttitude;

    /**
     * 教学环境评分
     */
    private Double teachingEnv;

    /**
     * 修改标识
     */
    private Integer editFlag;

    /**
     * 课堂评价的标签
     */
    private String label;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
