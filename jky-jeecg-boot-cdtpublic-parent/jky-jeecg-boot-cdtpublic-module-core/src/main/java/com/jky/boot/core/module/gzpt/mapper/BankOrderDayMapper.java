package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.BankOrderDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 每日资金划转对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface BankOrderDayMapper extends BaseMapper<BankOrderDay> {

    List<BankOrderDay> selectBankOrderDayList(BankOrderDay bankOrderDay);

    int insertBankOrderDay(BankOrderDay bankOrderDay);

    int updateBankOrderDay(BankOrderDay bankOrderDay);
}
