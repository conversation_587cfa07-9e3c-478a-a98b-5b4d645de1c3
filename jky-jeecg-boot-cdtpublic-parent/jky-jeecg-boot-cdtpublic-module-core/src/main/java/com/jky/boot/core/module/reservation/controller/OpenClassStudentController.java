package com.jky.boot.core.module.reservation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.base.dao.OpenClass;
import com.jky.boot.base.dao.OpenClassStudent;
import com.jky.boot.base.service.IOpenClassService;
import com.jky.boot.base.service.IOpenClassStudentService;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 课堂排班学员
 *
 * <AUTHOR>
 * @version 2024-09-25
 */
@RestController
@RequestMapping("/openClass/student")
@Slf4j
public class OpenClassStudentController {

    @Autowired
    private RedissonLockClient redissonLock;
    @Autowired
    private IOpenClassService openClassService;
    @Autowired
    private IStudentinfoService studentInfoService;
    @Autowired
    private IOpenClassStudentService openClassStudentService;

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(OpenClassStudent openClassStudent,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
        QueryWrapper<OpenClassStudent> queryWrapper = QueryGenerator.initQueryWrapper(openClassStudent, req.getParameterMap());
        queryWrapper.in("inscode", insCodes).orderByDesc("create_time");
        Page<OpenClassStudent> page = new Page<>(pageNo, pageSize);
        IPage<OpenClassStudent> pageList = openClassStudentService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 查询未开班的学员
     */
    @GetMapping(value = "/queryStu")
    public Result<?> queryStu(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                              @RequestParam(value = "idcard", required = false) String idcard) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
        Page<Studentinfo> page = new Page<>(pageNo, pageSize);
        IPage<Studentinfo> pageList = studentInfoService.page(page,
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(StringUtils.isNotBlank(idcard), Studentinfo::getIdcard, idcard)
                        .eq(Studentinfo::getOpenClass, 0)
                        .isNotNull(Studentinfo::getStunum)
                        .in(Studentinfo::getInscode, insCodes)
        );
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @Transactional
    @PostMapping(value = "/add")
    @RedissonLock(key = "#rec.openClassId", keyPrefix = "openClass:id:", waitTime = 1, lockTime = 5)
    public Result<String> add(@RequestBody OpenClassStudent rec) {
        List<String> split = Arrays.asList(rec.getStunum().split(","));
        // 校验人数
        OpenClass byId = openClassService.getById(rec.getOpenClassId());
        if (byId.getCurAmount().equals(byId.getStuAmount())) {
            return Result.error("当前开班人数已到上限!");
        }
        if (byId.getCurAmount() + split.size() > byId.getStuAmount()) {
            return Result.error("人数超过限制");
        }
        // 校验重复添加
        long count = openClassStudentService.count(
                Wrappers.<OpenClassStudent>lambdaQuery()
                        .eq(OpenClassStudent::getDeleted,0)
                        .in(OpenClassStudent::getStunum, split)
        );
        if (count > 0) {
            return Result.error("重复添加");
        }
        for (String stunum : split) {
            OpenClassStudent target = new OpenClassStudent();
            // 保存预约记录
            Studentinfo stu = studentInfoService.getOneByStuNum(stunum);
            target.setOpenClassId(rec.getOpenClassId()).setStunum(stu.getStunum()).setInscode(stu.getInscode());
            target.setStuname(stu.getName()).setPhone(stu.getPhone());
            openClassStudentService.save(target);
            stu.setOpenClass(1);
            studentInfoService.updateById(stu);
            // 推送计时
            if (EnvControl.Js_SWITCH) {
                CommonResponse resp = PushJsUtil.pushOpenClass(byId, target);
                if (resp.getErrorcode() != 0) {
                    throw new RuntimeException(resp.getMessage());
                }
                target.setPushJs(1);
                openClassStudentService.updateById(rec);
            }
        }
        byId.setCurAmount(byId.getCurAmount() + split.size());
        openClassService.updateById(byId);
        return Result.ok("添加成功！");
    }

    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        // 回滚可约人数
        OpenClassStudent openClassStudent = openClassStudentService.getById(id);
        String key = "openClass:id:" + openClassStudent.getOpenClassId();
        if (!redissonLock.tryLock(key, 2, 5)) {
            return Result.error("系统繁忙");
        }
        OpenClass byId = openClassService.getById(openClassStudent.getOpenClassId());
        byId.setCurAmount(byId.getCurAmount() - 1);
        openClassService.updateById(byId);
        // 删除预约记录
        openClassStudentService.removeById(id);
        // 学员状态变更
        studentInfoService.update(
                Wrappers.<Studentinfo>lambdaUpdate()
                        .set(Studentinfo::getOpenClass, 0)
                        .eq(Studentinfo::getStunum, openClassStudent.getStunum())
        );
        redissonLock.unlock(key);
        return Result.ok("删除成功!");
    }
}
