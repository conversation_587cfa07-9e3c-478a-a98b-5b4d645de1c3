package com.jky.boot.core.module.sxgzpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.core.module.sxgzpt.entity.Device;
import com.jky.boot.core.module.sxgzpt.mapper.DeviceMapper;
import com.jky.boot.core.module.sxgzpt.service.IDeviceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 终端信息Service业务层处理
 *
 * <AUTHOR>
 * @since 2022-09-02
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {
}
