package com.jky.boot.zlb.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.jky.boot.zlb.entity.TMTrainInfo;
import com.jky.boot.zlb.service.ITMTrainInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: t_m_train_info
 * @Author: jeecg-boot
 * @Date:   2023-06-26
 * @Version: V1.0
 */
@Api(tags="t_m_train_info")
@RestController
@RequestMapping("/zlb/ttra/tMTrainInfo")
@Slf4j
public class TMTrainInfoController extends JeecgController<TMTrainInfo, ITMTrainInfoService> {
	@Autowired
	private ITMTrainInfoService tMTrainInfoService;

	/**
	 * 分页列表查询
	 *
	 * @param tMTrainInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "t_m_train_info-分页列表查询")
	@ApiOperation(value="t_m_train_info-分页列表查询", notes="t_m_train_info-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<TMTrainInfo>> queryPageList(TMTrainInfo tMTrainInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TMTrainInfo> queryWrapper = QueryGenerator.initQueryWrapper(tMTrainInfo, req.getParameterMap());
		Page<TMTrainInfo> page = new Page<TMTrainInfo>(pageNo, pageSize);
		IPage<TMTrainInfo> pageList = tMTrainInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param tMTrainInfo
	 * @return
	 */
	@AutoLog(value = "t_m_train_info-添加")
	@ApiOperation(value="t_m_train_info-添加", notes="t_m_train_info-添加")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_train_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody TMTrainInfo tMTrainInfo) {
		tMTrainInfoService.save(tMTrainInfo);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param tMTrainInfo
	 * @return
	 */
	@AutoLog(value = "t_m_train_info-编辑")
	@ApiOperation(value="t_m_train_info-编辑", notes="t_m_train_info-编辑")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_train_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody TMTrainInfo tMTrainInfo) {
		tMTrainInfoService.updateById(tMTrainInfo);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "t_m_train_info-通过id删除")
	@ApiOperation(value="t_m_train_info-通过id删除", notes="t_m_train_info-通过id删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_train_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		tMTrainInfoService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "t_m_train_info-批量删除")
	@ApiOperation(value="t_m_train_info-批量删除", notes="t_m_train_info-批量删除")
	//@RequiresPermissions("org.jeecg.modules.demo:t_m_train_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.tMTrainInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "t_m_train_info-通过id查询")
	@ApiOperation(value="t_m_train_info-通过id查询", notes="t_m_train_info-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<TMTrainInfo> queryById(@RequestParam(name="id",required=true) String id) {
		TMTrainInfo tMTrainInfo = tMTrainInfoService.getById(id);
		if(tMTrainInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(tMTrainInfo);
	}
//
//    /**
//    * 导出excel
//    *
//    * @param request
//    * @param tMTrainInfo
//    */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_train_info:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, TMTrainInfo tMTrainInfo) {
//        return super.exportXls(request, tMTrainInfo, TMTrainInfo.class, "t_m_train_info");
//    }
//
//    /**
//      * 通过excel导入数据
//    *
//    * @param request
//    * @param response
//    * @return
//    */
//    //@RequiresPermissions("t_m_train_info:importExcel")
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
//        return super.importExcel(request, response, TMTrainInfo.class);
//    }

}
