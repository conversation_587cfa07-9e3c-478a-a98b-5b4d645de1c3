package com.jky.boot.core.module.gzpt.service.impl;

import com.jky.boot.core.module.gzpt.entity.Appoint;
import com.jky.boot.core.module.gzpt.mapper.AppointMapper;
import com.jky.boot.core.module.gzpt.service.IAppointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: t_m_appoint
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class AppointServiceImpl extends ServiceImpl<AppointMapper, Appoint> implements IAppointService {
    @Autowired
    private AppointMapper appointMapper;

    /**
     * 查询约定
     *
     * @param id 约定主键
     * @return 约定
     */
    @Override
    public Appoint selectAppointById(Long id) {
        return appointMapper.selectAppointById(id);
    }

    /**
     * 查询约定列表
     *
     * @param appoint 约定
     * @return 约定
     */
    @Override
    public List<Appoint> selectAppointList(Appoint appoint) {
        return appointMapper.selectAppointList(appoint);
    }

    /**
     * 新增约定
     *
     * @param appoint 约定
     * @return 结果
     */
    @Override
    public int insertAppoint(Appoint appoint) {
        return appointMapper.insertAppoint(appoint);
    }

    /**
     * 修改约定
     *
     * @param appoint 约定
     * @return 结果
     */
    @Override
    public int updateAppoint(Appoint appoint) {
        return appointMapper.updateAppoint(appoint);
    }

    /**
     * 批量删除约定
     *
     * @param ids 需要删除的约定主键
     * @return 结果
     */
    @Override
    public int deleteAppointByIds(Long[] ids) {
        return appointMapper.deleteAppointByIds(ids);
    }

    /**
     * 删除约定信息
     *
     * @param id 约定主键
     * @return 结果
     */
    @Override
    public int deleteAppointById(Long id) {
        return appointMapper.deleteAppointById(id);
    }

    @Override
    public Appoint getByInscode(String inscode) {
        return appointMapper.selectAppointByInscode(inscode);
    }


}
