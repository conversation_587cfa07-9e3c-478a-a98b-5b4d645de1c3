<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.AppointMapper">

    <resultMap type="com.jky.boot.core.module.gzpt.entity.Appoint" id="AppointResult">
        <result property="id" column="id"/>
        <result property="inscode" column="inscode"/>
        <result property="insname" column="insname"/>
        <result property="classroomplace" column="classroomplace"/>
        <result property="remotenetwork" column="remotenetwork"/>
        <result property="virtualdirveplace" column="virtualdirveplace"/>
        <result property="basicdriveplace" column="basicdriveplace"/>
        <result property="roaddriveplace" column="roaddriveplace"/>
        <result property="appointnetwork" column="appointnetwork"/>
        <result property="appointphone" column="appointphone"/>
        <result property="otherappointway" column="otherappointway"/>
        <result property="stumoneyto" column="stumoneyto"/>
        <result property="otherappoint" column="otherappoint"/>
        <result property="totalcontract" column="totalcontract"/>
        <result property="signendtime" column="signendtime"/>
    </resultMap>

    <sql id="selectAppointVo">
        select id,
               inscode,
               insname,
               classroomplace,
               remotenetwork,
               virtualdirveplace,
               basicdriveplace,
               roaddriveplace,
               appointnetwork,
               appointphone,
               otherappointway,
               stumoneyto,
               otherappoint,
               totalcontract,
               signendtime
        from t_m_appoint
    </sql>

    <select id="selectAppointList" parameterType="com.jky.boot.core.module.gzpt.entity.Appoint" resultMap="AppointResult">
        <include refid="selectAppointVo"/>
        <where>
            <if test="inscode != null  and inscode != ''">and inscode in (select LEADER from sys_dept start with
                DEPT_ID=#{inscode} connect by prior DEPT_ID = PARENT_ID AND DEL_FLAG = 0 AND STATUS = 0)
            </if>
            <if test="insname != null  and insname != ''">and insname like concat(concat('%', #{insname}), '%')</if>
        </where>
    </select>

    <select id="selectAppointById" parameterType="Long" resultMap="AppointResult">
        <include refid="selectAppointVo"/>
        where id = #{id}
    </select>

    <select id="selectAppointByInscode" resultType="com.jky.boot.core.module.gzpt.entity.Appoint">
        <include refid="selectAppointVo"/>
        where inscode = #{inscode}
    </select>

    <insert id="insertAppoint" parameterType="com.jky.boot.core.module.gzpt.entity.Appoint">
        insert into t_m_appoint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inscode != null">inscode,</if>
            <if test="insname != null">insname,</if>
            <if test="classroomplace != null">classroomplace,</if>
            <if test="remotenetwork != null">remotenetwork,</if>
            <if test="virtualdirveplace != null">virtualdirveplace,</if>
            <if test="basicdriveplace != null">basicdriveplace,</if>
            <if test="roaddriveplace != null">roaddriveplace,</if>
            <if test="appointnetwork != null">appointnetwork,</if>
            <if test="appointphone != null">appointphone,</if>
            <if test="otherappointway != null">otherappointway,</if>
            <if test="stumoneyto != null">stumoneyto,</if>
            <if test="otherappoint != null">otherappoint,</if>
            <if test="totalcontract != null">totalcontract,</if>
            <if test="signendtime != null">signendtime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inscode != null">#{inscode},</if>
            <if test="insname != null">#{insname},</if>
            <if test="classroomplace != null">#{classroomplace},</if>
            <if test="remotenetwork != null">#{remotenetwork},</if>
            <if test="virtualdirveplace != null">#{virtualdirveplace},</if>
            <if test="basicdriveplace != null">#{basicdriveplace},</if>
            <if test="roaddriveplace != null">#{roaddriveplace},</if>
            <if test="appointnetwork != null">#{appointnetwork},</if>
            <if test="appointphone != null">#{appointphone},</if>
            <if test="otherappointway != null">#{otherappointway},</if>
            <if test="stumoneyto != null">#{stumoneyto},</if>
            <if test="otherappoint != null">#{otherappoint},</if>
            <if test="totalcontract != null">#{totalcontract},</if>
            <if test="signendtime != null">#{signendtime},</if>
        </trim>
    </insert>

    <update id="updateAppoint" parameterType="com.jky.boot.core.module.gzpt.entity.Appoint">
        update t_m_appoint
        <trim prefix="SET" suffixOverrides=",">
            <if test="inscode != null">inscode = #{inscode},</if>
            <if test="insname != null">insname = #{insname},</if>
            <if test="classroomplace != null">classroomplace = #{classroomplace},</if>
            <if test="remotenetwork != null">remotenetwork = #{remotenetwork},</if>
            <if test="virtualdirveplace != null">virtualdirveplace = #{virtualdirveplace},</if>
            <if test="basicdriveplace != null">basicdriveplace = #{basicdriveplace},</if>
            <if test="roaddriveplace != null">roaddriveplace = #{roaddriveplace},</if>
            <if test="appointnetwork != null">appointnetwork = #{appointnetwork},</if>
            <if test="appointphone != null">appointphone = #{appointphone},</if>
            <if test="otherappointway != null">otherappointway = #{otherappointway},</if>
            <if test="stumoneyto != null">stumoneyto = #{stumoneyto},</if>
            <if test="otherappoint != null">otherappoint = #{otherappoint},</if>
            <if test="totalcontract != null">totalcontract = #{totalcontract},</if>
            <if test="signendtime != null">signendtime = #{signendtime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppointById" parameterType="Long">
        delete
        from t_m_appoint
        where id = #{id}
    </delete>

    <delete id="deleteAppointByIds" parameterType="String">
        delete from t_m_appoint where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>