package com.jky.boot.zlb.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.zlb.entity.TMCoach;
import com.jky.boot.zlb.entity.TMInstitution;
import com.jky.boot.zlb.mapper.TMCoachMapper;
import com.jky.boot.zlb.service.ITMCoachService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: t_m_coach
 * @Author: jeecg-boot
 * @Date:   2023-06-14
 * @Version: V1.0
 */
@Service
public class TMCoachServiceImpl extends ServiceImpl<TMCoachMapper, TMCoach> implements ITMCoachService {

    @Override
    public Page<TMCoach> list(Page<TMCoach> page, String inscode) {
        return baseMapper.driver(page,inscode);
    }

    @Override
    public Page<TMCoach> coach(Page<TMCoach> page, String idcard,String name) {
        return baseMapper.coach(page,idcard,name);
    }


    @Override
    public Page<TMCoach> selectcoach(Page<TMCoach> page, String inscode,String teachpermitted) {
        return baseMapper.selectcoach(page,inscode,teachpermitted);
    }
}
