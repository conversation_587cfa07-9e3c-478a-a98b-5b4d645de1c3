package com.jky.boot.core.module.sxgzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.beanCopy.BeanConvertUtils;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IClassRecordDetailService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.PushJgUtils;
import com.jky.boot.core.module.sxgzpt.entity.ClassEvaluation;
import com.jky.boot.core.module.sxgzpt.event.CalLabelEvent;
import com.jky.boot.core.module.sxgzpt.service.IClassEvaluationService;
import com.jky.boot.core.module.sxgzpt.vo.ClassEvaluationVo;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 课后评价
 *
 * <AUTHOR>
 * @version 2024-05-13 11:22
 */
@RestController
@RequestMapping("/gzpt/classEva")
@Slf4j
public class ClassEvaluationController {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final IClassRecordDetailService classRecordDetailService;
    private final IClassEvaluationService classEvaluationService;
    private final IStudentinfoService stuInfoService;

    public ClassEvaluationController(ApplicationEventPublisher applicationEventPublisher,
                                     IClassRecordDetailService classRecordDetailService,
                                     IClassEvaluationService classEvaluationService,
                                     IStudentinfoService stuInfoService) {
        this.applicationEventPublisher = applicationEventPublisher;
        this.classRecordDetailService = classRecordDetailService;
        this.classEvaluationService = classEvaluationService;
        this.stuInfoService = stuInfoService;
    }

    /**
     * 学员电子教学日志查询
     */
    @GetMapping(value = "/class/list")
    public Result<IPage<ClassRecordDetail>> queryClassList(ClassRecordDetail classRecordDetail,
                                                           @RequestParam(name = "pageNo", defaultValue = "1")
                                                           Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10")
                                                           Integer pageSize,
                                                           HttpServletRequest req) {
        if (StringUtils.isBlank(classRecordDetail.getStunum())) {
            return Result.ok();
        }
        List<ClassEvaluation> list = classEvaluationService.list(
                Wrappers.<ClassEvaluation>lambdaQuery()
                        .eq(ClassEvaluation::getStunum, classRecordDetail.getStunum())
        );
        List<String> recordIds = list.stream().map(ClassEvaluation::getRecordId).collect(Collectors.toList());
        QueryWrapper<ClassRecordDetail> queryWrapper = QueryGenerator.initQueryWrapper(classRecordDetail, req.getParameterMap());
        queryWrapper.notIn(CollectionUtils.isNotEmpty(recordIds), "id", recordIds);
        queryWrapper.likeRight("subjcode", "1");
        queryWrapper.orderByDesc("create_time");
        Page<ClassRecordDetail> page = new Page<>(pageNo, pageSize);
        IPage<ClassRecordDetail> pageList = classRecordDetailService.page(page, queryWrapper);
        for (ClassRecordDetail detail : pageList.getRecords()) {
            detail.setKm(detail.getSubjcode().substring(3, 4));
            detail.setType(detail.getSubjcode().substring(0, 1));
            detail.setStarttime(DateUtils.parseStringToString(detail.getStarttime(), DateUtils.YYYYMMDDHHMMSS));
            detail.setEndtime(DateUtils.parseStringToString(detail.getEndtime(), DateUtils.YYYYMMDDHHMMSS));
        }
        return Result.ok(pageList);
    }

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/zlb/list")
    public Result<?> queryZlbList(ClassEvaluation classEvaluation,
                                  @RequestParam(name = "pageNo", defaultValue = "1")
                                  Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10")
                                  Integer pageSize,
                                  HttpServletRequest req) {
        QueryWrapper<ClassEvaluation> queryWrapper = QueryGenerator.initQueryWrapper(classEvaluation, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<ClassEvaluation> page = new Page<>(pageNo, pageSize);
        IPage<ClassEvaluation> pageList = classEvaluationService.page(page, queryWrapper);
        List<ClassEvaluation> temp = pageList.getRecords();
        if (CollectionUtils.isEmpty(temp)) {
            return Result.ok(pageList);
        }
        List<ClassEvaluationVo> vos = classEvaluationService.getClassVos(temp);
        IPage<ClassEvaluationVo> res = new Page<>();
        res.setRecords(vos);
        res.setPages(pageList.getPages());
        res.setCurrent(pageList.getCurrent());
        res.setSize(pageList.getSize());
        res.setTotal(pageList.getTotal());
        return Result.ok(res);
    }

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(ClassEvaluation classEvaluation,
                                   @RequestParam(name = "pageNo", defaultValue = "1")
                                   Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10")
                                   Integer pageSize,
                                   HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(classEvaluation.getInscode());
        classEvaluation.setInscode(null);
        QueryWrapper<ClassEvaluation> queryWrapper = QueryGenerator.initQueryWrapper(classEvaluation, req.getParameterMap());
        queryWrapper.in(CollectionUtils.isNotEmpty(insCodes), "inscode", insCodes);
        queryWrapper.orderByDesc("create_time");
        Page<ClassEvaluation> page = new Page<>(pageNo, pageSize);
        IPage<ClassEvaluation> pageList = classEvaluationService.page(page, queryWrapper);
        List<ClassEvaluation> temp = pageList.getRecords();
        if (CollectionUtils.isEmpty(temp)) {
            return Result.ok(pageList);
        }
        List<ClassEvaluationVo> vos = classEvaluationService.getClassVos(temp);
        IPage<ClassEvaluationVo> res = new Page<>();
        res.setRecords(vos);
        res.setPages(pageList.getPages());
        res.setCurrent(pageList.getCurrent());
        res.setSize(pageList.getSize());
        res.setTotal(pageList.getTotal());
        return Result.ok(res);
    }

    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody ClassEvaluation rec) {
        Studentinfo one = stuInfoService.getOneByStuNum(rec.getStunum());
        if (Objects.isNull(one)) {
            return Result.error("该学员不存在");
        }
        ClassEvaluation evaluation = classEvaluationService.getOne(
                Wrappers.<ClassEvaluation>lambdaQuery()
                        .eq(ClassEvaluation::getStunum, one.getStunum())
                        .eq(ClassEvaluation::getRecordId, rec.getRecordId())
        );
        if (Objects.nonNull(evaluation)) {
            return Result.error("请勿重复评价");
        }
        ClassRecordDetail byId = classRecordDetailService.getById(rec.getRecordId());
        rec.setCoachnum(byId.getCoachnum());
        rec.setCoachname(byId.getCoachname());
        Double ta = rec.getTeachingAttitude();
        Double tq = rec.getTeachingQuality();
        Double te = rec.getTeachingEnv();
        rec.setOverall(BigDecimal.valueOf((ta + tq + te) / 3).setScale(2, RoundingMode.HALF_UP).doubleValue());
        rec.setInscode(one.getInscode());
        rec.setInsname(one.getInsname());
        rec.setStunum(one.getStunum());
        rec.setStuname(one.getName());
        rec.setCreateTime(new Date());
        rec.setEditFlag(0);
        rec.setCreateBy(null);
        rec.setUpdateTime(null);
        rec.setUpdateBy(null);
        // 同步至监管
        if (EnvControl.JG_SWITCH) {
            Result<?> resp = PushJgUtils.pushJgNew("/jgpt/sync/classEva", rec);
            if (!resp.isSuccess()) {
                return resp;
            }
        }
        // 保存评价
        classEvaluationService.save(rec);
        // 每次新增课后评价都重计算教练员平均分和前三标签
        CalLabelEvent event = new CalLabelEvent(this, byId.getCoachnum());
        applicationEventPublisher.publishEvent(event);
        return Result.ok("评论成功！");
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody ClassEvaluation rec) {
        ClassEvaluation one = classEvaluationService.getById(rec.getId());
        if (one.getEditFlag() == 1) {
            return Result.error("评价只能修改一次!");
        }
        Date createTime = one.getCreateTime();
        Date now = new Date();
        long getTime = now.getTime() - createTime.getTime();
        long num = getTime / (1000 * 60 * 60 * 24);
        if (num > 30) {
            return Result.error("超过三十天的评论不允许修改");
        }
        Double ta = rec.getTeachingAttitude();
        Double tq = rec.getTeachingQuality();
        Double te = rec.getTeachingEnv();
        double oa = BigDecimal.valueOf((ta + tq + te) / 3).setScale(2, RoundingMode.HALF_UP).doubleValue();
        one.setTeachingAttitude(ta);
        one.setTeachingQuality(tq);
        one.setTeachingEnv(te);
        one.setOverall(oa);
        one.setEditFlag(1);
        one.setLabel(rec.getLabel());
        if (EnvControl.JG_SWITCH) {
            Result<?> resp = PushJgUtils.pushJgNew("/jgpt/sync/classEva", one);
            if (!resp.isSuccess()) {
                return resp;
            }
        }
        classEvaluationService.updateById(one);
        return Result.ok("修改成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "t_m_evaluation-通过id删除")
    @RequiresPermissions("gzpt:classEva:remove")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        classEvaluationService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "t_m_evaluation-批量删除")
    @RequiresPermissions("gzpt:classEva:remove")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        classEvaluationService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        ClassEvaluation one = classEvaluationService.getById(id);
        if (one == null) {
            return Result.error("未找到对应数据");
        }
        ClassEvaluationVo res = new ClassEvaluationVo();
        BeanConvertUtils.copyProperties(one, res);
        ClassRecordDetail record = classRecordDetailService.getById(one.getRecordId());
        if (Objects.isNull(record)) {
            return Result.ok(res);
        }
        res.setKm(record.getSubjcode().substring(3, 4));
        res.setRecnum(record.getRecnum());
        res.setCoachname(record.getCoachname());
        res.setDurationY(record.getDurationY());
        res.setMileageY(record.getMileageY());
        res.setStarttime(DateUtils.parseStringToString(record.getStarttime(), DateUtils.YYYYMMDDHHMMSS));
        res.setEndtime(DateUtils.parseStringToString(record.getEndtime(), DateUtils.YYYYMMDDHHMMSS));
        return Result.ok(res);
    }
}
