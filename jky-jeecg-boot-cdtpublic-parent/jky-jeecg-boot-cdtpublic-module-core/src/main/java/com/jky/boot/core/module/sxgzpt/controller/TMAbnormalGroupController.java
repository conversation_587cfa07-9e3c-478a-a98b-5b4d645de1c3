package com.jky.boot.core.module.sxgzpt.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.dto.GroupTDO;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.gzpt.vo.AddAbnormalNoVo;
import com.jky.boot.core.module.sxgzpt.entity.AbnormalGroup;
import com.jky.boot.core.module.sxgzpt.service.IAbnormalGroupService;
import com.jky.boot.core.module.sxgzpt.service.ITrainRecordAbnormalService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Description:异常数据管理
 * @Author: jeecg-boot
 * @Date: 2023-04-14
 * @Version: V1.0
 */
@Api(tags = "异常数据管理")
@RestController
@RequestMapping("/gzpt/abnormalGroup")
@Slf4j
public class TMAbnormalGroupController extends JeecgController<AbnormalGroup, IAbnormalGroupService> {
    @Autowired
    private IAbnormalGroupService abnormalGroupService;
    //	@Autowired
//	private IAbnormalGroupNoService abnormalGroupNoService;
    @Autowired
    private ITrainRecordAbnormalService trainRecordAbnormalService;

//	@Autowired
//	private IMxWorkorderJsonService workorderJsonService;

//	@Autowired
//	private ITrainRecordAbnormalNoService trainRecordAbnormalNoService;

    /**
     * 分页列表查询
     *
     * @param tMAbnormalGroup
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "t_m_abnormal_group-分页列表查询")
    @ApiOperation(value = "t_m_abnormal_group-分页列表查询", notes = "t_m_abnormal_group-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<AbnormalGroup>> queryPageList(AbnormalGroup tMAbnormalGroup,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        List<String> realInscodes;
        if (Objects.nonNull(tMAbnormalGroup.getInscode())) {
            realInscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMAbnormalGroup.getInscode());
            tMAbnormalGroup.setInscode(null);
            tMAbnormalGroup.setRealInscodes(realInscodes);
        }
        Page<AbnormalGroup> page = new Page<>(pageNo, pageSize);
        return Result.OK(abnormalGroupService.selectAbnormalGroupList(tMAbnormalGroup, page));
    }

//	/**
//	 *   电子教学日志加入疑似违规
//	 * @param params
//	 * @return
//	 */
//	@AutoLog(value = "培训过程-异常数据管理-加入疑似违规")
//	@ApiOperation(value="培训过程-异常数据管理-加入疑似违规", notes="培训过程-异常数据管理-加入疑似违规")
//	@Transactional
//	@PostMapping(value = "/addAbnormalNo")
//	public Result<String> addAbnormalNo(@RequestBody AddAbnormalNoVo addAbnormalNoVo) {
//		List<ClassRecordDetail> list = getClassRecordDetailList(addAbnormalNoVo);
//		for (ClassRecordDetail detail : list) {
//			trainRecordAbnormalNoService.addAbnormalNo(detail, addAbnormalNoVo.getSeq());
//		}
//		return Result.OK("加入疑似违规成功！");
//	}


    public List<ClassRecordDetail> getClassRecordDetailList(AddAbnormalNoVo addAbnormalNoVo) {
//		最大值为这么多的最小值，没有就现在时间减 20，
        return trainRecordAbnormalService.getClassRecordDetailListByGroupIds(addAbnormalNoVo.getIds(), addAbnormalNoVo.getMinCrdate(), addAbnormalNoVo.getMaxCrdate());
    }

    /**
     * 添加
     *
     * @param tMAbnormalGroup
     * @return
     */
    @AutoLog(value = "t_m_abnormal_group-添加")
    @ApiOperation(value = "t_m_abnormal_group-添加", notes = "t_m_abnormal_group-添加")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_abnormal_group:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody AbnormalGroup tMAbnormalGroup) {
        abnormalGroupService.save(tMAbnormalGroup);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param tMAbnormalGroup
     * @return
     */
    @AutoLog(value = "t_m_abnormal_group-编辑")
    @ApiOperation(value = "t_m_abnormal_group-编辑", notes = "t_m_abnormal_group-编辑")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_abnormal_group:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody AbnormalGroup tMAbnormalGroup) {
        abnormalGroupService.updateById(tMAbnormalGroup);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "t_m_abnormal_group-通过id删除")
    @ApiOperation(value = "t_m_abnormal_group-通过id删除", notes = "t_m_abnormal_group-通过id删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_abnormal_group:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        abnormalGroupService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "t_m_abnormal_group-批量删除")
    @ApiOperation(value = "t_m_abnormal_group-批量删除", notes = "t_m_abnormal_group-批量删除")
    //@RequiresPermissions("org.jeecg.modules.demo:t_m_abnormal_group:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.abnormalGroupService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "t_m_abnormal_group-通过id查询")
    @ApiOperation(value = "t_m_abnormal_group-通过id查询", notes = "t_m_abnormal_group-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<AbnormalGroup> queryById(@RequestParam(name = "id") String id) {
        AbnormalGroup tMAbnormalGroup = abnormalGroupService.getById(id);
        if (tMAbnormalGroup == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(tMAbnormalGroup);
    }
//
//    /**
//     * 导出excel
//     *
//     * @param request
//     * @param tMAbnormalGroup
//     */
//    //@RequiresPermissions("org.jeecg.modules.demo:t_m_abnormal_group:exportXls")
//    @RequestMapping(value = "/exportXls")
//    public ModelAndView exportXls(HttpServletRequest request, AbnormalGroup tMAbnormalGroup) {
//        List<String> realInscodes;
//        if (Objects.nonNull(tMAbnormalGroup.getInscode())) {
//            realInscodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(tMAbnormalGroup.getInscode());
//            tMAbnormalGroup.setInscode(null);
//            tMAbnormalGroup.setRealInscodes(realInscodes);
//        }
//        Page<AbnormalGroup> page = new Page<>(1, 10000);
//        List<AbnormalGroup> list = abnormalGroupService.selectAbnormalGroupList(tMAbnormalGroup, page).getRecords();
//        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//        mv.addObject(NormalExcelConstants.FILE_NAME, "异常数据管理");
//        mv.addObject(NormalExcelConstants.CLASS, AbnormalGroup.class);
//        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("异常数据管理", "导出人:" + JkSecurityUtils.getUsername(), "异常数据管理"));
//        mv.addObject(NormalExcelConstants.DATA_LIST, list);
//        return mv;
//    }


    /**
     * 查询电子教学日志
     *
     * @param type 1:疑似违规 2.异常组
     * @return
     */
    @GetMapping("/classRecordList")
    public Result<?> classRecordList(String id, int type) {

        Date crdate = null;
        String tablename = "";
        Integer newjgpt = 0;
        if (type == 1) {
//			tablename="newjgpt.t_m_train_record_abnormal_no";
//			AbnormalGroupNo byId = abnormalGroupNoService.getById(id);
//			newjgpt=byId.getIsNewjgpt();
//			crdate=byId.getCrdate();
        } else {
            AbnormalGroup abnormalGroup = abnormalGroupService.getById(id);
            tablename = "sxgzpt.t_m_train_record_abnormal";
            crdate = abnormalGroup.getCrdate();
            newjgpt = abnormalGroup.getIsNewjgpt();
        }

        if (crdate != null) {
            String s = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", crdate);

            Date date = DateUtils.parseDate(s);
            List<ClassRecordDetail> list = null;
            if (null != newjgpt && newjgpt == 1) {
                list = trainRecordAbnormalService.getClassRecordDetailList(id, date, tablename);
            } else {
                String time = DateUtils.getTime(date, 20);
                time = time + " 000000";
                Date starttime = DateUtils.parseDate(time);
                list = trainRecordAbnormalService.getClassRecordDetailList(id, starttime, date, tablename);
            }
            if (list == null || list.size() == 0) {
                return Result.error("该电子教学日志不存在");
            }
            for (ClassRecordDetail classRecordDetail : list) {
                classRecordDetail.setKm(classRecordDetail.getSubjcode().substring(3, 4));
                if (Objects.isNull(classRecordDetail.getType())){
                    classRecordDetail.setType(classRecordDetail.getSubjcode().substring(0, 1));
                }
            }
            return Result.ok(list);
        }
        return Result.ok();
    }

//	/**
//	 * 提交工单
//	 * @param workorderTDO
//	 * id
//	 * workType 1.疑似违规 2.异常组
//	 * file;//附件
//	 * @return
//	 */
//	@PostMapping("/submitWork")
//	@Transactional
//	public Result<?> submitWork(WorkorderTDO workorderTDO){
//		if(workorderTDO.getFile()!=null){
//			workorderTDO.setPath(workorderTDO.getFile());
//		}
//		String s = workorderJsonService.saveWorkorder(workorderTDO);
//		if(StringUtils.isNotEmpty(s)) {
//			return Result.error(s);
//		}
//		return 	Result.ok("提交成功");
//
//	}

    @PostMapping("/audit")
    public Result<?> audit(@RequestBody GroupTDO groupTDO) {
        abnormalGroupService.audit(groupTDO);
        return Result.ok("操作成功!");
    }
}
