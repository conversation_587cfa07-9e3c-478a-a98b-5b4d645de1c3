<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.jky.boot</groupId>
    <artifactId>jky-jeecg-boot-cdtpublic-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <artifactId>jky-jeecg-boot-cdtpublic-web</artifactId>
   <properties>
		<start-class>com.jky.boot.JkyCdtpublicApplication</start-class>
		<!-- 前后端合并部署，指定前端项目路径 -->
<!-- 		<web-project-path>D:\dev_workspace\sts\ant-design-vue-jeecg</web-project-path> -->
	</properties>
	<dependencies>
		<!-- cdtpublic项目后台模块依赖 -->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-cdtpublic-module-system</artifactId>
		</dependency>
		<!-- [core]子模块依赖 -->
		<dependency>
			<groupId>com.jky.boot</groupId>
			<artifactId>jky-jeecg-boot-cdtpublic-module-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.jky.boot</groupId>
			<version>2.0-SNAPSHOT</version>
			<artifactId>jky-jeecg-boot-security-patches-desensitize-starter</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-bootstrap</artifactId>-->
<!--		</dependency>-->

<!--  文档管理依赖，如果不需要可以注释掉 												 -->
<!-- 		<dependency> 														-->
<!-- 			<groupId>com.jky.boot</groupId> 								-->
<!-- 		  	<artifactId>jky-jeecg-boot-module-doc</artifactId> 				-->
<!-- 		</dependency> 														-->



	</dependencies>

	<build>
		<finalName>quzgzpt-1.0</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				 <configuration>
			      <fork>true</fork>
			      <!-- spring-boot:run 中文乱码解决 -->
			      <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
			      </configuration>
			</plugin>
			<plugin>
                 <groupId>org.apache.maven.plugins</groupId>
                 <artifactId>maven-resources-plugin</artifactId>
                 <version>3.1.0</version>
             </plugin>

			<plugin>
				<!-- 打包时去除第三方依赖 -->
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<layout>ZIP</layout>
					<includes>
						<include>
							<groupId>non-exists</groupId>
							<artifactId>non-exists</artifactId>
						</include>
					</includes>
				</configuration>
			</plugin>
			<!-- 拷贝第三方依赖文件到指定目录 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<!--target/lib是依赖jar包的输出目录，根据自己喜好配置-->
							<outputDirectory>target/lib</outputDirectory>
							<excludeTransitive>false</excludeTransitive>
							<stripVersion>false</stripVersion>
							<includeScope>runtime</includeScope>
						</configuration>
					</execution>
				</executions>
			</plugin>





<!-- 	前后端整合为一个发布，执行前端build命令并拷贝dist到后端static下，执行yarn命令 -->
<!-- 			<plugin> -->
<!-- 				<groupId>org.codehaus.mojo</groupId> -->
<!-- 				<artifactId>exec-maven-plugin</artifactId> -->
<!-- 				<executions> -->
<!-- 					<execution> -->
<!-- 						<id>exec-yarn-run-build</id> -->
<!-- 						<phase>prepare-package</phase> -->
<!-- 						<goals> -->
<!-- 							<goal>exec</goal> -->
<!-- 						</goals> -->
<!-- 						<configuration> -->
<!-- 							<executable>yarn</executable> -->
<!-- 							<arguments> -->
<!-- 								<argument>run</argument> -->
<!-- 								<argument>build</argument> -->
<!-- 							</arguments> -->
<!-- 							<workingDirectory>${web-project-path}</workingDirectory> -->
<!-- 						</configuration> -->
<!-- 					</execution> -->

<!-- 				</executions> -->
<!-- 			</plugin> -->

<!-- 			copy文件到指定目录下 -->
<!-- 			<plugin> -->
<!-- 				<groupId>org.apache.maven.plugins</groupId> -->
<!-- 				<artifactId>maven-resources-plugin</artifactId> -->
<!-- 				<configuration> -->
<!-- 					<encoding>${project.build.sourceEncoding}</encoding> -->
<!-- 				</configuration> -->
<!-- 				<executions> -->
<!-- 					<execution> -->
<!-- 						<id>copy-spring-boot-webapp</id> -->
<!-- 						<phase>package</phase> -->
<!-- 						<goals> -->
<!-- 							<goal>copy-resources</goal> -->
<!-- 						</goals> -->
<!-- 						<configuration> -->
<!-- 							<encoding>utf-8</encoding> -->
<!-- 							<outputDirectory>${basedir}/src/main/resources/static</outputDirectory> -->
<!-- 							<resources> -->
<!-- 								<resource> -->
<!-- 									<directory>${web-project-path}/dist</directory> -->
<!-- 								</resource> -->
<!-- 							</resources> -->
<!-- 						</configuration> -->
<!-- 					</execution> -->
<!-- 				</executions> -->
<!-- 			</plugin> -->

		</plugins>
	</build>
</project>
