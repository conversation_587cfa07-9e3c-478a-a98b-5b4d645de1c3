server:
  port: 8083
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /cdtpublic
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    enabled-by-default: false

spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  mail:
    host: smtp.163.com
    username: jeec<PERSON>@163.com
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  aop:
    proxy-target-class: true
  jpa:
    open-in-view: false
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: ***************************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
        bank:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************************************************************************************************************************
          username: root
          password: root
  #redis 配置
  redis:
    database: 6
    host: ************
    port: 56379
    password: '6CSe&Px3'
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,classpath*:com/jky/boot/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao:
  base-package: org.jeecg.modules.jmreport.*
jeecg:
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 签名拦截接口
  signUrls: /sys/dict/getDictItems/*,/sys/dict/loadDict/*,/sys/dict/loadDictOrderByValue/*,/sys/dict/loadDictItem/*,/sys/dict/loadTreeData,/sys/api/queryTableDictItemsByCode,/sys/api/queryFilterTableDictInfo,/sys/api/queryTableDictByKeys,/sys/api/translateDictFromTable,/sys/api/translateDictFromTableByKeys
  # local\minio\alioss
  uploadType: local
  # 前端访问地址
  domainUrl:
    pc: http://localhost:3100
    app: http://localhost:8051
  path:
    #文件上传根目录 设置
    upload: /opt/projects/cdtpublic/upFiles
    #webapp文件路径
    webapp: /opt/projects/cdtpublic/webapp
  shiro:
    excludeUrls: /sys/jky/cas/client/validateLogin/**,/pub/jky/**,/gzpt/classRecordDetail/add,/gzpt/stageTrainningTime/saveAuditstate,/zlb/login/**
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKey: LTAI4GDArYcxPrYXqbVMjXKc
    secretKey: ******************************
    bucketName: jkyqstes
    staticDomain: http://jkyqstes.oss-cn-hangzhou.aliyuncs.com
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: ************:9200
    check-enabled: false
  # 在线预览文件服务器地址配置
  file-view-domain: http://127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: ??
  #大屏报表参数设置
  jmreport:
    mode: prod
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: false
    #必须校验方法
    verify_methods: remove,delete,save,add,update
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  #分布式锁配置
  redisson:
    address: ************:56379
    password: '6CSe&Px3'
    type: STANDALONE
    enabled: true
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper: info
    com.jky.boot: info
#cas单点登录
cas:
  auth:
    appKey: ''
    appSecret: ''
  prefixUrl: http://ywsn.jtyst.zj.gov.cn:7003/auth/oauth/check_token
  userInfo: http://ywsn.jtyst.zj.gov.cn:7003/admin/user/info
  logout: http://ywsn.jtyst.zj.gov.cn:7003/auth/token/logout
  deptDetail: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/dept/getDeptById   #根据组织id获取组织信息
  orgDetail: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/org/getSysOrgById   #根据机构id获取机构信息
  currentAndNextAndMember: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/dept/getCurrentAndNextAndMember   #根据组织id获取本组织信息、直接下属组织列表以及用户列表

#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: true
    username: jky_ad
    password: jky@@admin2022
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: false
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: false
      # appKey
      client-id: ??
      # appSecret
      client-secret: ??
      agent-id: ??


jky:
  showErrorPage: true

# 中国银行地址
CITICurl: http://IP:PORT/jxbank/msgnotify?v=1&ts=1212&user=
# 农商地址
RCBurl: http://IP:PORT/jxbanksdk/msgnotify?v=1&ts=1212&user=

# 宁波银行地址
bankurl: http://IP:PORT/banksdk/msgnotify?v=1&ts=1212&user=
#建行测试
#JHbankurl: http://************:13307/ccbbank/msgsend?v=1&ts=1212&user=
JHbankurl: http://IP:PORT/ccbbank/msgsend?v=1&ts=1212&user=

JHbankurl2: http://IP:PORT/ccbbank
# 推送工行地址
ICBCbankurl: http://IP:PORT/icbbank/msgnotify?v=1&ts=1212&user=

#农行
ABCbankurl: http://IP:PORT/abcmsgnotify?v=1&ts=1212&user=



#服务器ip+端口号
#jhgzurl: http://*************:8081/jhgzpt
jhgzurl: http://************/gzptapi

#appUrl: http://zlb.aptstu.com/zjx
appUrl: https://IP:PORT/prod-api/app


cityCoode: 3304

jgurl: http://IP:PORT/jxjgptjk/gzpt/querysubject

cret: /templates/pfx/hangzhouzhizheng.pfx
passcret: Zhizheng22

# 嘉兴
#zlb:
#  appId: **********
#  secretKey: BCDSGS_51d2629f2f97323a4a91a0999788e435
#  accessKey: BCDSGA_8cb97f8bcca1356e89ae2272a53a69a9

# 绍兴
zlb:
  appId: **********
  secretKey: BCDSGS_eb1c975662708787759ab51f039fb4e0
  accessKey: BCDSGA_1d48a27ee7f0bde288e0a7821805ca01


cityName: 嘉兴
citySeal: /inscode_jh/

#金华-平安银行
PABurl: http://***************:18888/jpapi/msgnotify?v=1&ts=1212&user=
