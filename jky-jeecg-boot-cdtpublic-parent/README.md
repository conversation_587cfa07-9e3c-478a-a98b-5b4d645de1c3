
工程名：cdtpublic
工程名说明：city Driver Training public
工程中文名称：市级机动车驾培服务平台



一、jky-jeecg-boot-***-parent系列集成了Jeecg-Boot 低代码开发平台
- 所有项目划分5块模块

```
- 1.jky-jeecg-boot-***-common         项目通用模块，用于封装Jeecg-Boot通用接口和项目通用代码
- 2.jky-jeecg-boot-***-module-job     项目任务子模块
- 3.jky-jeecg-boot-***-module-system  项目管理子模块
- 4.jky-jeecg-boot-***-module-[core]  项目其他子模块(core名称自行替换)
- 5.jky-jeecg-boot-****-web           项目启动模块（只放配置文件、配置类）
- 6.jky-jeecg-cloud-****-module       微服务中间件模块
  - 6.1.jky-cloud-gateway               路由网关
  - 6.2.jky-cloud-monitor               监控模块
  - 6.3.jky-cloud-nacos                 配置中心(服务注册)
  - 6.4.jky-cloud-xxljob                分布式任务
```
 
二、为了更好的支持Jeecg-Boot后续升级，做以下开发约定：

```
- 1.禁止修改Jeecg-Boot源码，所有Jeecg-Boot依赖以jar形式管理
- 2.禁止直接调用Jeecg-Boot通用方法，必须统一封装后供模块调用,避免Jeecg-Boot升级未向下兼容导致需要大量修改，封装后只要修改封装类进行向下适配
(具体参考jky-jeecg-boot-base-common下的JeecgBootCommonApi类进行简单二次封装)
- 3.后台用户体系管理模块，需要扩展功能，请拷贝Jeecg-Boot源码放到自定义模块中进行二次开发，类名统一加前缀J
- 4.jky-jeecg-boot-****-web 项目启动模块 ,只放配置文件、配置类.
```
