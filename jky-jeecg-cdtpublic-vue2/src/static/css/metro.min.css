.ribbed-lime {
    background: #a4c400 linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent) !important;
    background-size: 40px 40px !important;
}

.bg-cyan {
    background-color: #1ba1e2 !important;
}
.bg-lightRed {
    background-color: #da5a53 !important;
}

.slider {
    height: 2.125rem;
    line-height: 1;
    width: auto;
    position: relative;
}
.slider .marker {
    height: 1rem;
    width: 1rem;
    cursor: pointer;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
    left: 0;
    background-color: #1d1d1d;
    z-index: 2;
}
.slider .marker:focus,
.slider .marker:active,
.slider .markerhover {
    border: 2px #ce352c solid;
}
.slider .slider-backside,
.slider .complete {
    height: .5rem;
    background: #999999;
    width: 100%;
    line-height: 2.125rem;
    top: 50%;
    margin-top: -0.25rem;
    position: absolute;
}
.slider .complete {
    width: auto;
    background-color: #00aba9;
    z-index: 2;
    transition: background .3s ease;
    left: 0;
}
.slider .buffer {
    height: 4px;
    width: auto;
    background-color: #ffffff;
    z-index: 1;
    transition: background .3s ease;
    position: absolute;
    top: 50%;
    margin-top: -2px;
    left: 0;
}
.slider .slider-hint {
    min-width: 1.8rem;
    width: auto;
    height: auto;
    position: absolute;
    z-index: 3;
    border: 1px #ccc solid;
    padding: .25rem;
    top: -1.2rem;
    text-align: center;
    font-size: .625rem;
    display: none;
    background: #fffcc0;
}
.slider .slider-hint:before {
    border: 1px #ccc solid;
    border-left: 0;
    border-top: 0;
    content: "";
    width: .25rem;
    height: .25rem;
    display: block;
    position: absolute;
    background-color: inherit;
    margin-top: -0.125rem;
    margin-left: -0.15625rem;
    top: 100%;
    left: 50%;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
}
.slider.hint-bottom .slider-hint {
    top: 100%;
    margin-top: -0.125rem;
}
.slider.hint-bottom .slider-hint:before {
    top: -0.0625rem;
    -webkit-transform: rotate(-135deg);
    transform: rotate(-135deg);
}
.slider.permanent-hint > .slider-hint {
    display: block;
}
.slider:hover .complete {
    background-color: #45fffd;
}
.slider:active .complete,
.slider:active + .marker:active .complete {
    background-color: #45fffd;
}
.slider.place-left {
    margin-right: 20px;
}
.slider.place-right {
    margin-left: 20px;
}
.slider.ani .complete {
    -webkit-animation: ani-bg-stripes 2s linear infinite;
    animation: ani-bg-stripes 2s linear infinite;
}
.slider.vertical {
    min-height: 100px;
    width: 2.125rem;
    display: inline-block;
}
.slider.vertical .slider-backside,
.slider.vertical .complete {
    position: absolute;
    height: 100%;
    width: .5rem;
    bottom: 0;
    left: 50%;
    margin-left: -0.25rem;
    top: auto;
}
.slider.vertical .marker {
    left: 50%;
    top: auto;
    margin-left: -0.5rem;
}
.slider.vertical .buffer {
    position: absolute;
    height: auto;
    width: 6px ;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
    top: auto;
}
.slider.vertical .slider-hint {
    left: 100%;
    margin-top: 0;
}
.slider.vertical .slider-hint:before {
    height: .25rem;
    width: .25rem;
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
    left: -1px;
    top: 50%;
    margin-top: -0.125rem;
    margin-left: -0.135rem;
}
.slider.vertical.hint-left .slider-hint {
    left: -100%;
    margin-left: .25rem;
}
.slider.vertical.hint-left .slider-hint:before {
    left: 100%;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.slider.cycle-marker .marker {
    border-radius: 50%;
}

