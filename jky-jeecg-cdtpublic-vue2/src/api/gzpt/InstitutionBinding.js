import { axios as request }  from '@/utils/request'

// 查询驾校绑定银行列表
export function listInstitutionBinding(query) {
  return request({
    url: '/gzpt/institutionBinding/list',
    method: 'get',
    params: query
  })
}

// 查询驾校绑定银行详细
export function getInstitutionBinding(inscode) {
  return request({
    url: '/gzpt/institutionBinding/' + inscode,
    method: 'get'
  })
}

// 新增驾校绑定银行
export function addInstitutionBinding(data) {
  return request({
    url: '/gzpt/institutionBinding/add',
    method: 'post',
    data: data
  })
}

// 修改驾校绑定银行
export function updateInstitutionBinding(data) {
  return request({
    url: '/gzpt/institutionBinding/edit',
    method: 'put',
    data: data
  })
}

// 删除驾校绑定银行
export function delInstitutionBinding(inscode) {
  return request({
    url: '/gzpt/institutionBinding/' + inscode,
    method: 'delete'
  })
}

// 导出驾校绑定银行
export function exportInstitutionBinding(query) {
  return request({
    url: '/gzpt/InstitutionBinding/export',
    method: 'get',
    params: query
  })
}