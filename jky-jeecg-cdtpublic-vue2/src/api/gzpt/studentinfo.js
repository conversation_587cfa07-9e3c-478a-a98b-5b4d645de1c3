import { axios as request }  from '@/utils/request'
import {
    httpAction,
    getAction,
    uploadAction
  } from '@/api/manage'

// 查询学员信息列表
export function listStudentinfo(query) {
  return request({
    url: "/gzpt/studentinfo/list",
    method: "get",
    params: query,
  });
}

// 查询学员信息详细
export function getStudentinfo(id) {
  return request({
    url: "/gzpt/studentinfo/queryById?id="+id,
    method: "get",
  });
}

// 新增学员信息
export function addStudentinfo(data) {
  return request({
    url: "/gzpt/studentinfo",
    method: "post",
    data: data,
  });
}

//文件上传并返回保存路径
export function fileDownload(formData) {
  return request({
    url: "/oss/upload",
    method: "post",
    data: formData,
	headers: {
	  'Content-Type': 'multipart/form-data',  // 文件上传
	},
  });
}

// 修改学员信息
export function updateStudentinfo(data) {
  return request({
    url: "/gzpt/studentinfo",
    method: "put",
    data: data,
  });
}

// 删除学员信息
export function delStudentinfo(ids, did) {
  return request({
    url: "/gzpt/studentinfo/deleteBatch?ids=" + ids + "&did=" + did,
    method: "post",
  });
}

// 强制删除学员信息
export function delStudentinfoById(id) {
  return request({
    url: "/gzpt/studentinfo/" + id,
    method: "post",
  });
}

// 导出学员信息
export function exportStudentinfo(query) {
  return request({
    url: "/gzpt/studentinfo/export",
    method: "get",
    params: query,
  });
}

// 导出学员信息
export function exportStudentinfoids(ids) {
  return request({
    url: "/gzpt/studentinfo/export/" + ids,
    method: "get",
  });
}

// 学员签订合同
export function signStudentContract(id) {
  return request({
    url: "/gzpt/studentinfo/contract/" + id,
    method: "get",
  });
}


//查看学员合同
export function viewStudentContract(stunum) {
  return request({
    url: "/gzpt/studentinfo/viewContract/" + stunum,
    method: "get",
  });
}

// 推送全国平台
export function PushQg(id) {
  return request({
    url: "/gzpt/studentinfo/pushQg/" + id,
    method: "get",
  });
}

// 推送计时平台
export function PushJs(id) {
  return request({
    url: "/gzpt/studentinfo/pushJs/" + id,
    method: "get",
  });
}

// 批量推送计时平台
export function BatchPushJs(id) {
  return request({
    url: "/gzpt/studentinfo/batchPushJs/" + id,
    method: "get",
  });
}

// 推送银行
export function PushStuToBank(data) {
  return request({
    url: "/gzpt/studentinfo/PushStuToBank",
    method: "post",
    data: data,
  });
}

// 强制推送银行
export function PushStuToBankForce(data) {
  return request({
    url: "/gzpt/studentinfo/PushStuToBankForce",
    method: "post",
    data: data,
  });
}

// 判断是不是东阳南山或金华交通技师
export function isspecial() {
  return request({
    url: "/gzpt/studentinfo/isspecial",
    method: "get",
  });
}

// 设置黑户
export function setBlackStu(data) {
  return request({
    url: "/gzpt/studentinfo/black",
    method: "get",
    params: data,
  });
}

// 查询各驾校学员数量
export function listStudentCounts(params) {
  return request({
    url: "/gzpt/studentinfo/listStudentCounts",
    method: "get",
    params,
  });
}

// 导出学员信息
export function exportReport(query) {
  return request({
    url: "/gzpt/studentinfo/exportReport",
    method: "get",
    params: query,
  });
}

// 是否是驾校
export function isSchool() {
  return request({
    url: "/gzpt/studentinfo/isSchool",
    method: "get",
  });
}

// 推送计时平台
export function setoldstu(id) {
  return request({
    url: "/gzpt/studentinfo/setoldstudent/" + id,
    method: "get",
  });
}

// 单个同步
export function singleSyncStudent(data){
  return request({
    url: "/gzpt/studentinfo/manualSync",
    method: "post",
    data: data,
  });
}

// 学时同步
export function classRecordSync(id){
  return request({
    url: "/gzpt/studentinfo/classrecordDetailSync/" + id,
    method: "get",
  });
}

export function stusign(data){
  return request({
    url: "/gzpt/studentinfo/stusign",
    method: "post",
    data: data,
  });
}

//根据培训机构获取教练员
export function getCocachList(data){
	return getAction("gzpt/coach/list/sen",data)
}
/**
 * 根据类型获取教练员
 * @param {Object} params 查询参数
 * @param {number} params.teachtype 教练类型：实操-1，课堂-2，模拟-3
 * @param {number} [params.pageSize=10] 每页条数
 * @param {number} [params.pageNo=1] 页码
 * @returns {Promise}
 */
export function getCocachList2(params) {
  return getAction("/gzpt/coach/reservation/list", {
    teachtype: params.teachtype,
    pageSize: params.pageSize || 10,
    pageNo: params.pageNo || 1
  })
}
//
export function applylogout(data){
  return request({
    url: "/gzpt/studentinfo/applylogout",
    method: "post",
    data: data,
  });
}
