import { axios as request }  from '@/utils/request'

// 查询车辆信息列表
export function listCarInfo(query) {
  return request({
    url: '/gzpt/carInfo/list',
    method: 'get',
    params: query
  })
}

// 查询车辆信息详细
export function getCarInfo(id) {
  return request({
    url: '/gzpt/carInfo/' + id,
    method: 'get'
  })
}

//勾选导出
export function exportByIds(ids) {
  return request({
    url: '/gzpt/carInfo/exportByIds/' + ids,
    method: 'get'
  })
}

// 新增车辆信息
export function addCarInfo(data) {
  return request({
    url: '/gzpt/carinfo',
    method: 'post',
    data: data
  })
}

// 修改车辆信息
export function updateCarInfo(data) {
  return request({
    url: '/gzpt/carinfo',
    method: 'put',
    data: data
  })
}

// 删除车辆信息
export function delCarInfo(id) {
  return request({
    url: '/gzpt/carinfo/' + id,
    method: 'delete'
  })
}

// 导出车辆信息
export function exportCarInfo(query) {
  return request({
    url: '/gzpt/carinfo/export',
    method: 'get',
    params: query
  })
}

// 修改状态
export function updateStatus(query) {
  return request({
    url: '/gzpt/carinfo/updateStatus',
    method: 'put',
    params: query
  })
}


// 转校
export function updateTransfer(query) {
  return request({
    url: '/gzpt/carinfo/updateTransfer',
    method: 'put',
    params: query
  })
}
//推送计时
export function pushJs(id) {
  return request({
    url: '/gzpt/carinfo/pushJs/' + id,
    method: 'get'
  })
}
// 推送全国平台
export function PushQg(id) {
  return request({
    url: "/gzpt/carinfo/pushQg/" + id,
    method: "get",
  });
}
// 批量推送计时
export function batchPushJs(id){
  return request({
    url: '/gzpt/carinfo/batchPushJs/' + id,
    method: 'get'
  })
}
