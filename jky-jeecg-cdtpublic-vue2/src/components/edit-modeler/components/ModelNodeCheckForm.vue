<template>
  <a-spin :spinning="confirmLoading" style="z-index:3000">
    <j-form-container>
      <a-form slot="detail">
        <!-- 文档查看权限 -->
        <a-row class="form-row" :gutter="24">
          <a-col :span="24"  >
            <!--<a-form-item label="文档查看权限" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-left: 0px">
              <a-input v-decorator="['searchgroupids']" placeholder="请输入文档查看权限"></a-input>
            </a-form-item>-->
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol"  label="选择审核人" >
              <a-radio-group @change="changeNodeCheckType" v-model="nodeCheckType" >
                <a-radio value="0"> 指定角色 </a-radio>
                <a-radio value="1"> 指定人员 </a-radio>
                <a-radio value="2"> 指定部门负责人 </a-radio>
                <a-radio value="3"> 发布人
                  <a-tooltip placement="topLeft" title="自动获取发布人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-radio>
                <a-radio value="4"> 发布人的部门负责人
                  <a-tooltip placement="topLeft" title="自动获取发布人所在部门的负责人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-radio>
                <a-radio value="5"> 发布人组的负责人
                  <a-tooltip placement="topLeft" title="自动获取发布人组的负责人，没有组即部门负责人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-radio>
                <a-radio value="6"> 发起人部门的分管领导
                  <a-tooltip placement="topLeft" title="自动获取发布人所在部门的分管领导">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-radio>
                <a-radio value="27"> 发布人板块领导
                  <a-tooltip placement="topLeft" title="自动获取发布人板块领导">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-radio>
                <a-radio value="88"> 自定义审核人
                  <!-- <a-tooltip placement="topLeft" title="自定义审核人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip> -->
                </a-radio>
                <a-radio value="77"> 省市县指定审核人
                  <!-- <a-tooltip placement="topLeft" title="自定义审核人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip> -->
                </a-radio>
              </a-radio-group>
              <!--
              <a-checkbox-group @change="changeNodeCheckType" v-model="nodeCheckType" >
                <a-checkbox value="0"> 根据角色 </a-checkbox>
                <a-checkbox value="1"> 根据人员 </a-checkbox>
                <a-checkbox value="2"> 根据部门 </a-checkbox>
                <a-checkbox value="3">
                  发布人
                  <a-tooltip placement="topLeft" title="自动获取发布人">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-checkbox>
                <a-checkbox value="4"> 发布人的部门
                  <a-tooltip placement="topLeft" title="自动获取发布人所在部门">
                    <a-icon type="exclamation-circle" />
                  </a-tooltip>
                </a-checkbox>
              </a-checkbox-group>
            -->
            </a-form-item>
            <!-- 0角色 1人员 2部门-->
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="选择角色" v-if="nodeCheckType == 0" >
              <j-select-role  v-model="nodeCheck.roleIds" id="nodeCheckRoles"/>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="选择人员" v-if="nodeCheckType == 1" >
              <!--  通过部门选择人员控件 -->
              <j-select-user-by-dep v-model="nodeCheck.userIds" :multi="false"></j-select-user-by-dep>
              <!-- <j-select-user-by-dep v-model="nodeCheck.userIds" :multi="false" :elemId="nodeCheckUsers"></j-select-user-by-dep> -->
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="选择部门" v-if="nodeCheckType == 2" >
              <j-select-depart-org v-model="nodeCheck.departmentIds" @change="getDepart" :multi="false" :elemId="nodeCheckDeparts"></j-select-depart-org>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="自定义查询用户登录名SQL" v-if="nodeCheckType == 88" >
              <!-- <j-select-role  v-model="nodeCheck.roleIds" id="nodeCheckRoles"/> -->
              <a-textarea rows="4" v-model="nodeCheck.checkerSql"></a-textarea>
              <div class="checkbtn" @click="checkSql">测试</div>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="行政区划参数" v-if="nodeCheckType == 77" >
                <a-input v-model="nodeCheck.areaCodeParam" placeholder="请输入流程中行政区划参数。为空默认登录人行政区划user.areaCode" value="'user.areaCode'"></a-input>
            </a-form-item>
            <a-form-item :label-col="labelCol" :wrapper-col="wrapperCol" label="省市县指定审核人" v-if="nodeCheckType == 77" >
                <a-radio-group  v-model="allowArea" button-style="solid" @change="changeTreeType">
                    <a-radio-button value="33">省级</a-radio-button>
                    <a-radio-button value="33__">市级</a-radio-button>
                    <a-radio-button value="33____">县级</a-radio-button>
                    <a-radio-button value="33|33__">省级或市级</a-radio-button>
                    <a-radio-button value="33__%">市级或县级</a-radio-button>
                    <a-radio-button value="33%">省市县三级</a-radio-button>
                </a-radio-group>
            </a-form-item>
            <div class="tree_wrap" v-if="nodeCheckType == 77">
              <a-switch checked-children="展开"  un-checked-children="折叠" default-checked @change="changZz" />
              <a-tree :tree-data="treeData" :expandedKeys.sync="expandedKeys" v-if="treeSx"  @select="changePer" >
                <template #title="{ key: treeKey, title ,report ,type,realname}">
                  <a-dropdown :trigger="['contextmenu']">
                    <p>
                      <span>{{ title }} 
                          <b v-show="isShow(type)" style="color:red;margin-left: 20px;" v-if="realname ==''">"点击选择"</b>
                          <b v-show="isShow(type)" style="color:deepskyblue;margin-left: 20px;">{{realname}}</b>
                      </span>                    
                    </p>
                
                  </a-dropdown>
                </template>
              </a-tree>
              <div class="checkPersons" v-show="perShow">
                <h4>选择人员</h4>
                <j-select-user-by-dep v-if="perShow" @change="changPerson" v-model="perChecker" :multi="true" :userIds="userIds" ref="selectUserByDep"></j-select-user-by-dep>
                <a-button class="btn_t" @click="perCancle">取 消</a-button>
                <a-button class="btn_t" type="primary" @click="perOk">确 定</a-button>
              </div>
            </div>
            
          </a-col>
        </a-row>
      </a-form>
      
    </j-form-container>
  </a-spin>
</template>

<script>

  import Vue from 'vue'
  import { httpAction, getAction, postAction} from '@/api/manage'
  import { USER_NAME,USER_INFO } from "@/store/mutation-types"
  import JFormContainer from '@/components/jeecg/JFormContainer'
  import JUpload from '@/components/jeecg/JUpload'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import JTreeSelect from '@/components/jeecg/JTreeSelect'
  import {initDictOptions, filterDictText} from '@/components/dict/JDictSelectUtil'
  import JSelectUserByDep from '@/components/jeecgbiz/JSelectUserByDep'
  import JSelectRole from '@/components/jeecgbiz/JSelectRole'
  import JSelectDepartOrg from '@/components/jeecgbiz/JSelectDepartOrg'
  import { datapro,datavince,datacity } from './province.js'
  //const userInfo = Vue.ls.get(USER_INFO);

  export default {
    name: 'ModelNodeCheckForm',
    components: {
      JFormContainer,
      JUpload,
      JEllipsis,
      JSelectUserByDep,
      JSelectRole,
      JSelectDepartOrg,
      JTreeSelect
    },
    props: {
      //流程表单data
      formData: {
        type: Object,
        default: ()=>{},
        required: false
      },
      //表单模式：true流程表单 false普通表单
      formBpm: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      },
      nodeId: {
        type: String,
        required: false,
      },
      nodeObj:{
        type: Object,
        default: ()=>{},
        required: false
      },
      modelId: {
        type: String,
        required: false,
      },
    },
    //inject: ['nodeId', 'nodeObj'], //当前节点编号，当前节点对象
    data () {
      return {
        model: {},
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        url: {
          //add: "/document/documentInfo/add",
          //edit: "/document/documentInfo/edit",
          //queryById: "/document/documentInfo/queryById"
        },
        nodeCheckType: '', //子节点审核人类型
        allowArea:"33",
        nodeCheck:{
          nodeId: '', //节点编号
          nodeFormId: '', //本节点匹配的表单id
          nodeFormName: '', //本节点匹配的表单名称
          areaCodeParam:'',
          nodeCheckType: '', //审核人类型
          nodeCheckedInfo: '', //已选中的审核人展示信息

          roleIds:'', //选中的角色
          userIds:'', //选中的人员
          otherTypes:'', //其他审核类型
          customChecker:false,
          areaChecker:false,
          checkerSql:'',
          allowArea:"33",
          areaCheckerList:[],
          departmentIds:'', //选中的部门
          chooseSponsor:false, //发起人
          chooseDepHeader:false, //发起人部门负责人
          chooseDepGroup:false, //发起人组负责人
          chooseDepChargeLeader:false, //发起人部门分管领导
        },
        nodeCheckUsers: 'nodeCheckUsers',
        nodeCheckDeparts: 'nodeCheckDeparts',
        perShow: false,
        realname: "",
        expandedKeys: [],
        perChecker: "",
        propCheckdata: {},
        treeSx: true,
        treeData: [],
        arrs: [],
        currentPerson: {}, // 点击地域树时获取负责人信息
        userIds: '', // 点击树时负责人选中IDS
      }
    },
    computed: {

    },
    created () {
        this.initSelectCheckInfo();
        
        
    },
    mounted(){
      // this.changeData(0,0,0,"周树人");
      // this.changeData(1,0,0,"豫山"); 
      // this.changeData(2,0,0,"豫才");
      // this.changeData(1,0,1,"周樟寿");
      // this.changeData(2,1,0,"鲁迅");
    },
    methods: {
      // 折叠展开
      changZz(val){
        // console.log(val);
        if(val==true){
          this.expandedKeys=["33","3301","3302","3303","3304","3305","3306","3307","3308","3309","3310","3311"];
        }else{
          this.expandedKeys=[];
        }
      },
      //  切换人员
      changPerson(e,t){
        // console.log(e,t,11); 
        // @fix wyk 多选人员
        if (t && t.length) {
          this.realname=t.map(item => item.realname).join(',')
          this.userIds = t.map(item => item.username).join(',')
        } else {
          this.realname = ''
          this.userIds = ''
        }       
        // this.realname=t[0].realname;
      },
      changeTreeType(e){
        if(e.target.value=="33"){
          this.expandedKeys=[];
        }else if(e.target.value=="33__" || e.target.value=="33|33__"){
          this.expandedKeys=["33"];
        }else{
          this.expandedKeys=["33","3301","3302","3303","3304","3305","3306","3307","3308","3309","3310","3311"];
        }       
      },
      isShow(type){       
        if(this.allowArea=="33"&&type==0){
          return true
        }else if(this.allowArea=="33__"&&type==1){
          return true
        }else if(this.allowArea=="33____"&&type==2){
          return true
        }else if(this.allowArea=="33|33__"&&(type==0||type==1)){
          return true
        }else if(this.allowArea=="33__%"&&(type==1||type==2)){
          return true
        }else if(this.allowArea=="33%"){
          return true
        }else{
          return false
        }
      },
      changeData(type,parentIndex,index,value,realname){

        this.treeData.map((obj)=>{
          if(type==0){
            obj.report=value;
            obj.realname=realname;
          }else if(type==1){
            obj.children[index].report=value;
            obj.children[index].realname=realname;
          }else if(type==2){
            obj.children[parentIndex].children[index].report=value;
            obj.children[parentIndex].children[index].realname=realname;
          }
        })
      },
      
      perCancle(){
        this.perShow=false;
        this.perChecker="";
      },
      perOk(){           
        // 赋值
        this.changeData(this.propCheckdata.type,this.propCheckdata.parentIndex,this.propCheckdata.index,this.perChecker,this.realname);
        this.perShow=false; 
        this.perChecker="";
      },
      changePer(selectedKeys,e){
        // console.log(selectedKeys,e);
        const dataRef = e.node.$vnode.data.props && e.node.$vnode.data.props.dataRef
        if(dataRef){
        // if(e.selectedNodes.length>0){
          // this.propCheckdata=e.selectedNodes[0].data.props.dataRef;
          this.propCheckdata=dataRef
          if(this.allowArea=="33"&&this.propCheckdata.type==0){
            this.perShow=true;
        
          }else if(this.allowArea=="33__"&&this.propCheckdata.type==1){
            this.perShow=true;
        
          }else if(this.allowArea=="33____"&&this.propCheckdata.type==2){
            this.perShow=true;
        
          }else if(this.allowArea=="33|33__"&&(this.propCheckdata.type==0||this.propCheckdata.type==1)){
            this.perShow=true;
        
          }else if(this.allowArea=="33__%"&&(this.propCheckdata.type==1||this.propCheckdata.type==2)){
            this.perShow=true;
        
          }else if(this.allowArea=="33%"){
            this.perShow=true;
        
          }
        }
        // @fix wyk 2022.11.28 获取当前节点的审核人信息
        this.userIds=""
        this.$nextTick(() => {
          const {realname, report} = e.node.$vnode.data.props
          this.userIds = report
          this.perChecker = report
          this.realname = realname
        })
      },
      getDepart(val){
        console.log(this.departNames,12)
      },
      //初始化
      initAllGroupTypes(){       
        //0角色 1人员 2部门
        if('' != this.model.searchgroupids && null != this.model.searchgroupids){
          this.nodeCheck.nodeCheckedInfo = this.model.searchgroupids;
          let nodeCheckObjIds = this.nodeCheck.nodeCheckedInfo.split(";");
          nodeCheckObjIds.forEach((dItem, index) => {
            let qObjs = dItem.split(":"); //dItem，如 R:RID_ys001,RID_knowledge,为字符串
            if(null != qObjs && 0 < qObjs.length){
              let qKey = qObjs[0]; //qKey，如 R，代表角色
              let qIds = qObjs[1]; //qIds，如 RID_ys001,RID_knowledge,为字符串
              if("R" == qKey){ //角色
                this.nodeCheck.roleIds = qIds.replace(/RID_/g, "");
              }
              else if("U" == qKey){ //人员
                this.nodeCheck.userIds = qIds.replace(/UID_/g, "");
              }
              else if("D" == qKey){ //部门
                this.nodeCheck.departmentIds = qIds.replace(/DID_/g, "");
              }
              /*else if("P" == qKey){ //发布人
                this.nodeCheck.chooseSponsor = true;
              }
              else if("PD" == qKey){ //发布人部门
                this.nodeCheck.chooseDep = true;
              }*/
            }
          })
        }
        if('' != this.model.searchgrouptypes && null != this.model.searchgrouptypes){
          this.nodeCheck.nodeCheckType = this.model.searchgrouptypes.split(",");
        }
        
      },
      changeNodeCheckType(e) {
        // console.log(234);
        // this.clearNodeCheck();
        this.clearNodeInfo();
      },
      clearNodeCheck(){
        this.nodeCheck.nodeId = ''; //本节点id
        this.nodeCheck.nodeFormId = ''; //本节点匹配的表单id
        this.nodeCheck.nodeFormName = ''; //本节点匹配的表单id
        this.nodeCheck.areaCodeParam = '',
        this.nodeCheck.nodeCheckedInfo = '';

        this.nodeCheck.roleIds = '';
        this.nodeCheck.userIds = '';
        this.nodeCheck.departmentIds = '';
        this.nodeCheck.chooseSponsor = false; //发起人
        this.nodeCheck.chooseDepHeader = false; //发起人部门负责人
        this.nodeCheck.chooseDepGroup = false; //发起人组负责人
        this.nodeCheck.chooseDepChargeLeader = false; //发起人部门分管领导
        this.nodeCheck.otherTypes ='';
        this.nodeCheck.checkerSql=''; //自定义内容
        this.nodeCheck.customChecker=false; //自定义内容
        this.nodeCheck.areaChecker=false; //自定义内容
        this.nodeCheck.allowArea = "33";
        this.nodeCheck.areaCheckerList = [];
      },
      clearNodeInfo(){
        this.nodeCheck.roleIds = ''; //选中的角色
        this.nodeCheck.userIds = ''; //选中的人员
        this.nodeCheck.departmentIds = ''; //选中的部门
        this.nodeCheck.chooseSponsor = false; //发起人
        this.nodeCheck.chooseDepHeader = false; //发起人部门负责人
        this.nodeCheck.chooseDepGroup = false; //发起人组负责人
        this.nodeCheck.chooseDepChargeLeader = false; //发起人部门分管领导
        this.nodeCheck.otherTypes ='';
        this.nodeCheck.checkerSql=''; //自定义内容
        this.nodeCheck.customChecker=false; //自定义内容
        this.nodeCheck.nodeCheckedInfo = '';
        this.nodeCheck.areaCodeParam = '';
        this.nodeCheck.areaChecker=false; //自定义内容
        this.nodeCheck.allowArea = "33";
        this.nodeCheck.areaCheckerList = [];
      },
      submitForm () {
        const that = this;
        // 获取省市县指定审核人
        let chooseArray=[];
        this.treeData.map((obj)=>{
          if(obj.report!=""){
            chooseArray.push({
              id: that.nodeCheck.areaCheckerList.id||"",
              modelId: that.modelId,
              nodeKey: that.nodeCheck.nodeId,
              areaCode: obj.key,
              userName: obj.report,
              realname: obj.realname
            })
          }
          if(obj.children){
            obj.children.map((obc)=>{
              if(obc.report!=""){
                chooseArray.push({
                  id: that.nodeCheck.areaCheckerList.id||"",
                  modelId: that.modelId,
                  nodeKey: that.nodeCheck.nodeId,
                  areaCode: obc.key,
                  userName: obc.report,
                  realname: obc.realname
                })
              }
              if(obc.children){
                obc.children.map((obh)=>{
                  if(obh.report!=""){
                    chooseArray.push({
                      id: that.nodeCheck.areaCheckerList.id||"",
                      modelId: that.modelId,
                      nodeKey: that.nodeCheck.nodeId,
                      areaCode: obh.key,
                      userName: obh.report,
                      realname: obh.realname
                    })
                  }
                })
              }
            })
          }
        });
        that.nodeCheck.areaCheckerList=chooseArray;
        if(null != that.nodeObj){
            that.nodeCheck.nodeId = that.nodeObj.nodeId;
            that.nodeCheck.nodeFormId = that.nodeObj.nodeFormId;
            that.nodeCheck.nodeFormName = that.nodeObj.nodeFormName;
        }
        //else{
            //that.nodeCheck.nodeId = that.nodeId;
            //that.nodeCheck.nodeCheckedInfo = '';
        //}
        
        
        that.nodeCheck.nodeCheckType = that.nodeCheckType;
        that.nodeCheck.allowArea = that.allowArea;
        console.log(that.nodeCheck.areaCodeParam);
        //0角色 1人员 2部门
        //构造展示用的审核人信息
        
        switch (that.nodeCheckType) {       
          case "0":
          console.log(that.nodeCheck.roleIds,23);
            if(null == that.nodeCheck.roleIds || '' == that.nodeCheck.roleIds){
              that.$message.warning("请指定对应的角色！");
              
              return;
            }
            that.nodeCheck.nodeCheckedInfo = "已指定角色：";

            let nodeCheckRoleIds = that.nodeCheck.roleIds.split(",");
            let roleIdsLength = nodeCheckRoleIds.length;
            if(0 < roleIdsLength){
              let tempRoleIds = "";
              nodeCheckRoleIds.forEach((r, i1) => {
                if(i1 < (roleIdsLength-1)){
                  tempRoleIds += (r+",")
                }
                else{
                  tempRoleIds += r
                }
              })
              that.nodeCheck.nodeCheckedInfo += tempRoleIds;
            }
            break;
          case "1":
            if(null == that.nodeCheck.userIds || '' == that.nodeCheck.userIds){
              that.$message.warning("请指定对应人员！");
              return;
            }
            that.nodeCheck.nodeCheckedInfo = "已指定人员：";

            let nodeCheckUserIds = that.nodeCheck.userIds.split(",");
            let userIdsLength = nodeCheckUserIds.length;
            if(0 < userIdsLength){
              let tempUserIds = "";
              nodeCheckUserIds.forEach((u, i2) => {
                if(i2 < (userIdsLength-1)){
                  tempUserIds += (u+",")
                }
                else{
                  tempUserIds += u
                }
              })
              that.nodeCheck.nodeCheckedInfo += tempUserIds;
            }
            break;
          case "2":
          console.log(that.nodeCheck.departmentIds,99);
            if(null == that.nodeCheck.departmentIds || '' == that.nodeCheck.departmentIds){
              console.log(1234);
              that.$message.warning("请指定对应部门！");
              return;
            }
            that.nodeCheck.nodeCheckedInfo = "已指定部门负责人：";

            let nodeCheckOrgIds = that.nodeCheck.departmentIds.split(",");
            let departmentCodesLength = nodeCheckOrgIds.length;
            if(0 < departmentCodesLength){
              let tempDepartIds = "";
              nodeCheckOrgIds.forEach((d, i3) => {
                if(i3 < (departmentCodesLength-1)){
                  tempDepartIds += (d+",")
                }
                else{
                  tempDepartIds += d
                }
              })
              that.nodeCheck.nodeCheckedInfo += tempDepartIds;
            }
            break;
          case "3":
            that.nodeCheck.chooseSponsor = true;
            that.nodeCheck.nodeCheckedInfo = "已指定发起人";
            break;
          case "4":
            that.nodeCheck.chooseDepHeader = true;
            that.nodeCheck.nodeCheckedInfo = "已指定发起人部门负责人";
            break;
          case "5":
            that.nodeCheck.chooseDepGroup = true;
            that.nodeCheck.nodeCheckedInfo = "已指定发起人组负责人";
            break;
          case "6":
            that.nodeCheck.chooseDepChargeLeader = true;
            that.nodeCheck.nodeCheckedInfo = "已指定发起人部门分管领导";
            break;
          case "27":
            this.nodeCheck.otherTypes = that.nodeCheckType;
            that.nodeCheck.nodeCheckedInfo = "已指定发起人板块领导";
            break;
          case "88":
              this.nodeCheck.customChecker = true;
              that.nodeCheck.nodeCheckedInfo = "自定义审核人";
              break;
          case "77":
              this.nodeCheck.areaChecker = true;
              that.nodeCheck.nodeCheckedInfo = "省市县审核人";
              break;
        }

        that.$emit('handleModalNodeValue', that.nodeId, that.nodeCheck);
      },
      initSelectCheckInfo(){
        let that=this;
        this.treeData=JSON.parse(JSON.stringify(datapro));
        if(null != this.nodeObj){
          console.log(this.nodeObj);
            this.nodeCheck = this.nodeObj;
            this.nodeCheckType = '' || this.nodeObj.nodeCheckType;
            this.allowArea = this.nodeObj.allowArea || "33";
            if(this.allowArea=="33"){
              this.expandedKeys=[];
            }else if(this.allowArea=="33__"){
              this.expandedKeys=["33"];
            }else{
              this.expandedKeys=["33","3301","3302","3303","3304","3305","3306","3307","3308","3309","3310","3311"];
            } 
            if(this.nodeObj.areaCheckerList){
              this.treeInter(this.nodeObj.areaCheckerList)  
            }
                        
        }
      },
      // tree填入
      treeInter(dataArea){
        let that=this;
        // let dataArea=that.nodeObj.areaCheckerList;
        
        for(var k=0;k<dataArea.length;k++){
          let dataKey=dataArea[k];
          // console.log(dataArea[k],999);
          that.treeData.map((obj)=>{
            if(obj.key==dataKey.areaCode){
              obj.report=dataKey.userName;
              obj.realname=dataKey.realname;
            }
            obj.children.map((obc)=>{
              if(obc.key==dataKey.areaCode){
                obc.report=dataKey.userName;
                obc.realname=dataKey.realname;
              }
              obc.children.map((obk)=>{
                if(obk.key==dataKey.areaCode){
                  obk.report=dataKey.userName;
                  obk.realname=dataKey.realname;
                }              
              })
            })
          })
        }
      },
      // 自定义审核人sql测试
      checkSql(){
        
        let params={
          checkerSql: this.nodeCheck.checkerSql,
          fromId: this.nodeCheck.nodeFormId
        };
        postAction("/activiti/isSelect",params).then(res => {
          if(res.success){
            // this.$message.warning(res.message);
            alert(res.message)
          }else{
            alert(res.message)
            // this.$message.warning("1111");
          }
        });
      },
    }
  }
</script>

<style lang="less">
#nodeCheckRoles,
#nodeCheckUsers,
#nodeCheckDeparts{
  .ant-modal-mask, .ant-modal-wrap{
    z-index: 3010;
  }
}
.checkbtn{
  width: 60px;
  height: 30px;
  background-color: #1890FF;
  color: #fff;
  text-align: center;
  line-height: 30px;
  border-radius: 4px;
  cursor: pointer;
}
.tree_wrap{
  width: 600px;
  height: 400px;
  overflow-y: auto;
  margin: 0 auto;
  padding: 10px;
  border: 1px solid #ccc;
}
.checkPersons{
  width: 500px;
  height: 150px;
  position: absolute;
  left: calc(50% - 250px);
  top: 120px;
  z-index: 100001;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #1890FF;
  padding: 10px;
  .btn_t{
    margin: 20px 10px;
  }
}
</style>