<template>
  <el-dialog
    title="关联业务表单"
    :visible="dialogVisible"
    width="45%"
    style="z-index: 3000"
    :before-close="handleClose">
      <el-table :data="modelFormDatas">
        <el-table-column property="id" label="编号" width="150"></el-table-column>
        <el-table-column property="fromName" label="表单名称" width="200"></el-table-column>
      </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>


<script>
import Vue from 'vue'
import { deleteAction, getAction,downFile } from '@/api/manage'

  export default {
    props:{
      formParams: {
        type: Object,
        require: true,
        default: ()=>{}
      },
      dialogVisible: {
        type: Boolean,
        require: false
      }
    },
    //props: ['dialogVisible', 'params'],
    components: {
    },
    data() {
      return {
        modelFormDatas : null,
        url: {
          list: "/activiti/actFrom/list",
        },
      };
    },
    mounted(){
      this.initModelFormList();
    },
    methods: {
      initModelFormList(){
        getAction(this.url.list, {
          parentId: '',
          name: ''
        }).then((res) => {
          if(res.success){

          }
        });
      },
      handleOk() {
        this.$emit('handleFormVisiable', { visiable: false })
        //this.$refs.modeler.saveXML();
      },
      handleClose(done) {
        Vue.ls.set("modelFormVisible", false, 60 * 60 * 1000);
        this.$emit('handleFormVisiable', { visiable: false })
      },
      handleCreat(mParams){
        if(this.$refs.modeler){
          this.$refs.modeler.handleCreate(mParams);
        }
      },
      handleUpdate(mParams){
        if(this.$refs.modeler){
          this.$refs.modeler.handleUpdate(mParams);
        }
      }
    },
  };
</script>