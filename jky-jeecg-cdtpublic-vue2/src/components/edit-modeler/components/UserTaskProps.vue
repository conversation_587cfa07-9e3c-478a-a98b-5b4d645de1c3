<template>
    <div>
        <el-form-item label="编号" required>
            <el-input v-model= "id"></el-input>
        </el-form-item>
        <el-form-item label="名称" required>
            <el-input v-model= "name"></el-input>
        </el-form-item>
        <el-form-item label="文档" v-show="false">
            <el-input type="textarea" v-model="documentation"></el-input>
        </el-form-item>
        <el-form-item label="多实例类型" v-show="false">
            <el-select v-model= "multiinstance_type"> <!--相当于bpmn2.0标准中的isSequential-->
                <el-option label="非多实例" value="None"></el-option>
                <el-option label="同时进行" value="Parallel"></el-option>
                <el-option label="顺序进行" value="Sequential"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="集合 (多实例)" v-show="false"><!--后端暂时没用到-->
          <el-input v-model= "multiinstance_collection"></el-input>
        </el-form-item>
        <el-form-item label="基数 (多实例)" v-show="false"><!--后端暂时没用到-->
            <el-input v-model= "multiinstance_cardinality" type="number"></el-input>
        </el-form-item>
        <el-form-item label="元素的变量(多实例)" v-show="false"><!--后端暂时没用到-->
            <el-input v-model= "multiinstance_variable" ></el-input>
        </el-form-item>
        <el-form-item label="通过权重[1-100]" v-show="false">
            <el-input v-model= "multiinstance_condition" type="number"></el-input>
        </el-form-item>
        <el-form-item label="表单标识"  v-show="false">
            <el-input v-model= "formKey" readonly></el-input>
        </el-form-item>
        <el-form-item label="关联业务表单" required>
          <el-input v-model="nodeInfo.nodeFormId" style="display: none"></el-input>
          <el-input v-model="nodeInfo.nodeFormName" readonly @click.native="showNodeForm"></el-input>
        </el-form-item>
        <el-form-item label="审核人" required v-show="checkParamShow">
          <el-input v-model="nodeInfo.nodeCheckedInfo" @click.native="handleNodeCheck" readonly v-show="checkParamShow"></el-input>
        </el-form-item>
        <el-form-item label="高级设置" class="advanced_setting">
          <el-switch v-model="nodeInfo.advancedSwitch" active-text="生效" inactive-text="不生效"></el-switch>
        </el-form-item>
        <el-form-item label="重复审核人是否默认通过:" class="advanced_setting" v-show="checkParamShow && nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.avoidCheckerRepeat" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
        <el-form-item label="逾期提醒" class="advanced_setting btn_set"  v-show="checkParamShow &&nodeInfo.advancedSwitch">
          ><el-input type="number" v-model= "nodeInfo.advancedSettings.limitHour" max="3"></el-input>小时         
        </el-form-item>
        <el-form-item label="通过:" class="advanced_setting btn_set" v-show="checkParamShow &&nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.btnPassShow" active-text="显示" inactive-text="隐藏"></el-switch>
          <el-input v-model="nodeInfo.advancedSettings.btnPassName" placeholder="可自定义按钮名称"></el-input>
        </el-form-item>
        <el-form-item label="不通过:" class="advanced_setting btn_set" v-show="checkParamShow &&nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.btnBackShow" active-text="显示" inactive-text="隐藏"></el-switch>
          <el-input v-model="nodeInfo.advancedSettings.btnBackName" placeholder="可自定义按钮名称"></el-input>
        </el-form-item>
        <el-form-item label="退回修改:" class="advanced_setting btn_set" v-show="checkParamShow && nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.btnBackEditShow" active-text="显示" inactive-text="隐藏"></el-switch>
          <el-input v-model="nodeInfo.advancedSettings.btnBackEditName" placeholder="可自定义按钮名称"></el-input>
        </el-form-item>
        <el-radio-group style="margin: 10px;" @change="changeThjd" v-model="nodeInfo.advancedSettings.backNodesType" v-show="nodeInfo.advancedSwitch && checkParamShow  && nodeInfo.advancedSettings.btnBackEditShow">
            <el-radio label="21">退回到申请人</el-radio>
<!--             <el-radio label="22">退回到上个节点</el-radio> -->
            <el-radio label="23">填写允许退回节点(编号多个逗号间隔)</el-radio>
            <el-input style="margin: 4px;" v-model="nodeInfo.advancedSettings.backNodeKeys" v-show="nodeInfo.advancedSettings.backNodesType==23" placeholder=""></el-input>
<!--             <el-radio label="24">选中允许退回的最早节点</el-radio> -->
<!--             <el-input style="margin: 4px;" v-model="nodeInfo.advancedSettings.backNodeKeys"  v-show="nodeInfo.advancedSettings.backNodesType==24" placeholder=""></el-input> -->
        </el-radio-group>
        <el-form-item label="发送邮件通知:" class="advanced_setting" v-show="nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.email" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
        <el-form-item label="发送短信通知:" class="advanced_setting" v-show="nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.sms" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
        <el-form-item label="发送系统通知:" class="advanced_setting" v-show="nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.sys" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
        <el-form-item label="发送钉钉通知:" class="advanced_setting" v-show="nodeInfo.advancedSwitch">
          <el-switch v-model="nodeInfo.advancedSettings.dd" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>

        <el-form-item label="任务派遣" v-show="false">
            <el-select v-model= "candidateGroups" multiple filterable placeholder="请选择">
                <el-option
                v-for=" p in positions"
                :key="p.id"
                :label="p.name"
                :value="p.id">
                </el-option>
            </el-select>
        </el-form-item>
    </div>
</template>
<script>
const forEach = require('lodash/forEach');
import Vue from 'vue'
import {findObjFromArrayByField,isNeedUpdate} from '../js/util/CommonUtils';
import bpmnHelper from '../js/helper/BpmnHelper';
import {NodeTypeMap,TxTypeMap} from '../../../static/js/static';
import { deleteAction, getAction, postAction, downFile } from '@/api/manage'
// 用户任务属性组件
export default {
    props:['element','modelId','mainFormId','mainFormName','nodeCheckObjs'],
    inject: ['bpmnModeler'],
    data(){
      return {
        modeling: null,
        id: this.element.id || '',
        name: '',
        formKey:'',
        documentation: '',
        multiinstance_type: '',
        multiinstance_collection: '',
        multiinstance_condition: '',
        multiinstance_cardinality: null,
        multiinstance_variable: null,
        checkParamShow: false,
        // 原子节点属性
        positions:[],
        priority:'',
        candidateGroups: '没有受让人',

        nodeInfo : {
          nodeId: '', //节点编号
          srcNodeId: '', //老节点编号
          nodeFormId: '', //本节点匹配的表单id
          nodeFormName: '', //本节点匹配的表单名称
          nodeName : '',
          nodeCheckType: '', //审核人类型
          nodeCheckedInfo: '', //已选中的审核人展示信息

          roleIds:'', //选中的角色
          userIds:'', //选中的人员
          departmentIds:'', //选中的部门
          chooseSponsor:false, //发起人
          chooseDepHeader:false, //发起人部门负责人
          chooseDepGroup:false, //发起人组负责人
          chooseDepChargeLeader:false, //发起人部门分管领导

          advancedSwitch: false,
          advancedSettings: {
            backNodesType: "21",
            backNodeKeys: "",
            limitHour: "",
            avoidCheckerRepeat: true, //避免审核人进行重复审核
            btnPassShow: true, //按钮是否展示
            btnPassName: null, //按钮名称，默认为通过
            btnBackShow: true, //按钮是否展示
            btnBackName: null, //按钮名称，默认为不通过
            btnBackEditShow: true, //按钮是否展示
            btnBackEditName: null, //按钮名称，默认为退回修改
            email: false, //是否发送邮件
            sms: false, //是否发送短信
            sys: false, //是否发送系统消息
            dd: false //是否发送dd消息
          }
        },
        url: {
          queryNodeInfo: '/activiti/nodeInfo',
        },
      }
    },
    created(){
      this.applyTaskChange();
      this.initCurNodeInfo(); //初始化当前节点信息
    },
    methods:{
      // 退回节点切换
      changeThjd(val){
        // console.log(val)
      },
      applyTaskChange(){
	      if(!this.name || this.name.indexOf('申请') > -1){
	  	       this.checkParamShow = false;
	  	       this.nodeInfo.nodeCheckType = '3';
               this.nodeInfo.chooseSponsor = true;
               this.nodeInfo.nodeCheckedInfo = "已指定发起人";
	      }else{
	      	   this.checkParamShow = true;
	      }
      },
      initCurNodeInfo(){
          /*
            1、首先从接口中查询该子节点的相关信息
            2、其次判断主节点的表单编号是否存在或已选
            3、最后再判断父组件Modeler.vue的缓存对象nodeCheckObjs中是否存在该子节点的表单编号
           */
          let that = this;
          that.nodeInfo.nodeId = that.id;
          that.nodeInfo.nodeName = that.name;
          console.log("初始化  that.id = ", that.id)

          getAction(that.url.queryNodeInfo, {
              modelId: that.modelId, //流程编号
              nodeId: that.id //节点编号
          }).then((res) => {
              if(res.success){
                  let result = res.result;
                  let areaCheckerList = result.areaCheckerList;
                  let areaCodeParam = "";
                  if(result.areaCodeParam){
                   areaCodeParam = result.areaCodeParam;
                  }
                   
                  if(null != result){
                      if(result.fromId){
                        that.nodeInfo.srcNodeId = that.id;
                        console.log("初始化_从接口  that.nodeInfo.srcNodeId = ", that.nodeInfo.srcNodeId)
                          if(!that.nodeInfo.nodeFormId){
                              that.nodeInfo.nodeFormId = result.fromId;
                              that.nodeInfo.nodeFormName = result.fromName;
                          }
                      }

                      let checker = result.checker;
                      if(null != checker && 0 < checker.length){
                          //that.clearNodeInfo();
                          let ncObj = checker[0];
                          let type = ncObj.type;
                          let relateId = ncObj.relateId;
                          
                          switch (type) {
                              case 0:
                                  that.nodeInfo.nodeCheckType = '0';
                                  that.nodeInfo.roleIds = relateId;
                                  that.nodeInfo.nodeCheckedInfo = "已指定角色："+relateId;
                                  break;
                              case 1:
                                  that.nodeInfo.nodeCheckType = '1';
                                  that.nodeInfo.userIds = relateId;
                                  that.nodeInfo.nodeCheckedInfo = "已指定人员："+relateId;
                                  break;
                              case 2:
                                  that.nodeInfo.nodeCheckType = '2';
                                  that.nodeInfo.departmentIds = relateId;
                                  that.nodeInfo.nodeCheckedInfo = "已指定部门："+relateId;
                                  break;
                              case 3:
                                  that.nodeInfo.nodeCheckType = '3';
                                  that.nodeInfo.chooseSponsor = true;
                                  that.nodeInfo.nodeCheckedInfo = "已指定发起人";
                                  break;
                              case 4:
                                  that.nodeInfo.nodeCheckType = '4';
                                  that.nodeInfo.chooseDepHeader = true;
                                  that.nodeInfo.nodeCheckedInfo = "已指定发起人部门的负责人";
                                  break;
                              case 5:
                                  that.nodeInfo.nodeCheckType = '5';
                                  that.nodeInfo.chooseDepGroup = true;
                                  that.nodeInfo.nodeCheckedInfo = "已指定发起人组负责人";
                                  break;
                              case 6:
                                  that.nodeInfo.nodeCheckType = '6';
                                  that.nodeInfo.chooseDepChargeLeader = true;
                                  that.nodeInfo.nodeCheckedInfo = "已指定发起人部门的分管领导";
                                  break;
                              case 27:
                                  that.nodeInfo.nodeCheckType = '27';
                                  that.nodeInfo.nodeCheckedInfo = "已指定发起人板块领导";
                                  break;
                              case 88:
                                  that.nodeInfo.nodeCheckType = '88';
                                  that.nodeInfo.customChecker = true;
                                  that.nodeInfo.checkerSql = relateId;
                                  that.nodeInfo.nodeCheckedInfo = "自定义审核人";
                                  break;
                              case 77:
                                  that.nodeInfo.nodeCheckType = '77';
                                  that.nodeInfo.areaChecker = true;
                                  that.nodeInfo.allowArea = relateId;
                                  that.nodeInfo.areaCheckerList = areaCheckerList;
                                  that.nodeInfo.areaCodeParam = areaCodeParam;
                                  that.nodeInfo.nodeCheckedInfo = "省市县审核人";
                                  break;
                          }
                      }

                      let adSets = result.advancedSettings; //获取
                      // console.log(adSets,99999);
                      // console.log(22222222);
                      
                      if(null != adSets){
                        that.nodeInfo.advancedSwitch = result.advancedSwitch;
                        that.nodeInfo.advancedSettings= result.advancedSettings;
                        that.nodeInfo.advancedSettings.avoidCheckerRepeat = adSets.avoidCheckerRepeat; //避免审核人进行重复审核
                        that.nodeInfo.advancedSettings.btnPassShow = adSets.btnPassShow; //按钮是否展示
                        that.nodeInfo.advancedSettings.btnPassName = adSets.btnPassName; //按钮名称，默认为通过
                        that.nodeInfo.advancedSettings.btnBackShow = adSets.btnBackShow; //按钮是否展示
                        that.nodeInfo.advancedSettings.btnBackName = adSets.btnBackName; //按钮名称，默认为不通过
                        that.nodeInfo.advancedSettings.btnBackEditShow = adSets.btnBackEditShow; //按钮是否展示
                        that.nodeInfo.advancedSettings.btnBackEditName = adSets.btnBackEditName; //按钮名称，默认为退回修改
                        that.nodeInfo.advancedSettings.email = adSets.email; //是否发送邮件
                        that.nodeInfo.advancedSettings.sms = adSets.sms; //是否发送短信
                        that.nodeInfo.advancedSettings.sys = adSets.sys; //是否发送系统消息
                        that.nodeInfo.advancedSettings.dd = adSets.dd; //是否发送dd消息
                        that.nodeInfo.advancedSettings.backNodesType=result.advancedSettings.backNodesType.toString();
                      }
                      else{
                        that.nodeInfo.advancedSwitch = false;
                      }
                  }
              }
          }).finally(() => {
              if(!that.nodeInfo.nodeFormId){
                  that.nodeInfo.nodeFormId = that.mainFormId;
                  that.nodeInfo.nodeFormName = that.mainFormName;
              }

              let subNodeInfo = that.nodeCheckObjs[that.id];
              console.log("subNodeInfo = ", subNodeInfo)

              if(null != subNodeInfo){
                  that.nodeInfo.srcNodeId = subNodeInfo.srcNodeId;
                  console.log("初始化_从缓存  that.nodeInfo.srcNodeId = ", that.nodeInfo.srcNodeId)

                  that.clearNodeInfo();
                  console.log(subNodeInfo,9999);
                  switch (subNodeInfo.nodeCheckType) {
                      case "0":
                          that.nodeInfo.nodeCheckType = '0';
                          that.nodeInfo.roleIds = subNodeInfo.roleIds;
                          that.nodeInfo.nodeCheckedInfo = "已指定角色："+subNodeInfo.roleIds;
                          break;
                      case "1":
                          that.nodeInfo.nodeCheckType = '1';
                          that.nodeInfo.userIds = subNodeInfo.userIds;
                          that.nodeInfo.nodeCheckedInfo = "已指定人员："+subNodeInfo.userIds;
                          break;
                      case "2":
                          that.nodeInfo.nodeCheckType = '2';
                          that.nodeInfo.departmentIds = subNodeInfo.departmentIds;
                          that.nodeInfo.nodeCheckedInfo = "已指定部门："+subNodeInfo.departmentIds;
                          break;
                      case "3":
                          that.nodeInfo.nodeCheckType = '3';
                          that.nodeInfo.chooseSponsor = true;
                          that.nodeInfo.nodeCheckedInfo = "已指定发起人";
                          break;
                      case "4":
                          that.nodeInfo.nodeCheckType = '4';
                          that.nodeInfo.chooseDepHeader = true;
                          that.nodeInfo.nodeCheckedInfo = "已指定发起人部门的负责人";
                          break;
                      case "5":
                          that.nodeInfo.nodeCheckType = '5';
                          that.nodeInfo.chooseDepGroup = true;
                          that.nodeInfo.nodeCheckedInfo = "已指定发起人组负责人";
                          break;
                      case "6":
                          that.nodeInfo.nodeCheckType = '6';
                          that.nodeInfo.chooseDepChargeLeader = true;
                          that.nodeInfo.nodeCheckedInfo = "已指定发起人部门的分管领导";
                          break;
                      case "27":
                          that.nodeInfo.nodeCheckType = '27';
                          that.nodeInfo.otherTypes = subNodeInfo.nodeCheckType;
                          that.nodeInfo.nodeCheckedInfo = "已指定发起人板块领导";
                          break;
                      case "88":
                          that.nodeInfo.nodeCheckType = '88';
                          that.nodeInfo.customChecker = true;
                          that.nodeInfo.checkerSql = subNodeInfo.checkerSql;
                          that.nodeInfo.nodeCheckedInfo = "自定义审核人";
                          break;
                      case "77":
                          that.nodeInfo.nodeCheckType = '77';
                          that.nodeInfo.areaChecker = true;
                          that.nodeInfo.allowArea = subNodeInfo.allowArea;
                          that.nodeInfo.areaCheckerList = subNodeInfo.areaCheckerList;
                          that.nodeInfo.areaCodeParam = subNodeInfo.areaCodeParam;
                          that.nodeInfo.nodeCheckedInfo = "省市县审核人";
                          break;
                  }

                  that.nodeInfo.advancedSwitch = subNodeInfo.advancedSwitch;
                  that.nodeInfo.advancedSettings = subNodeInfo.advancedSettings;
              }

              that.updateNodeToParent();
          });
      },
      clearNodeInfo(){
        console.log("执行清空重置程序")
          this.nodeInfo.roleIds = ''; //选中的角色
          this.nodeInfo.userIds = ''; //选中的人员
          this.nodeInfo.departmentIds = ''; //选中的部门
          this.nodeInfo.chooseSponsor = false; //发起人
          this.nodeInfo.chooseDepHeader = false; //发起人部门负责人
          this.nodeInfo.chooseDepGroup = false; //发起人组负责人
          this.nodeInfo.chooseDepChargeLeader = false; //发起人部门分管领导

          this.nodeInfo.advancedSwitch = false,
          this.nodeInfo.advancedSettings = {
            avoidCheckerRepeat: true, //避免审核人进行重复审核
            btnPassShow: true, //按钮是否展示
            btnPassName: null, //按钮名称，默认为通过
            btnBackShow: true, //按钮是否展示
            btnBackName: null, //按钮名称，默认为不通过
            btnBackEditShow: true, //按钮是否展示
            btnBackEditName: null, //按钮名称，默认为退回修改
            email: false, //是否发送邮件
            sms: false, //是否发送短信
            sys: false, //是否发送系统消息
            dd: false //是否发送系统消息
          }
      },
      updateNodeToParent(){
        console.log("更新   this.nodeInfo.nodeId = ", this.nodeInfo.nodeId)
        console.log("更新   this.nodeInfo.srcNodeId = ", this.nodeInfo.srcNodeId)
        this.$emit('updateNodeCheckObjs', this.id, this.nodeInfo.srcNodeId, this.nodeInfo);
      },
      updateNodeForm(newFormId, newFormName){
        this.nodeInfo.nodeFormId = newFormId;
        this.nodeInfo.nodeFormName = newFormName;
      },
      showNodeForm(){
        //展示关联表单列表
        this.$emit('handleFormVisiable', true, this.nodeInfo.nodeFormId)
      },
      handleNodeCheck(){
        this.$emit('handleCheckVisible', true)
      },
      handleModalNodeValue(ncInfo){
        this.nodeInfo.nodeCheckedInfo = ncInfo;
      },
    },
    mounted() {
        const bpmnModeler = this.bpmnModeler();
        this.modeling = bpmnModeler.get('modeling');

        this.$emit('updateNodeId', this.id);
    },
    computed: {
      advancedSwitch() {
        return this.nodeInfo.advancedSwitch;
      },
      advancedSettings() {
        return this.nodeInfo.advancedSettings;
      },
    },
    watch:{
        id: {
          handler(newVal, oldVal) {
            console.log("触发监控 id");
            this.modeling.updateProperties(this.element,{
              id: newVal
            });

            console.log("触发监控 oldVal = ", oldVal)
            console.log("触发监控 newVal = ", newVal)
            this.nodeInfo.nodeId = newVal;
            //this.nodeInfo.srcNodeId = oldVal;

            this.updateNodeToParent();
          }
        },
        name: {
            handler(newVal, oldVal) {
    		  this.applyTaskChange();
              console.log("触发监控 name");
                this.modeling.updateProperties(this.element,{
                    name: newVal
                });
            }
        },
        advancedSwitch: {
          deep: true,
          handler(newVal, oldVal) {
            console.log("触发监控 advancedSwitch");
            this.nodeInfo.advancedSwitch = newVal;
            this.updateNodeToParent();
          }
        },
        advancedSettings: {
          deep: true,
          handler(newVal, oldVal) {
            console.log("触发监控 advancedSettings");
            this.nodeInfo.advancedSettings = newVal;
            this.updateNodeToParent();
          }
        },
        //监视元素变化
        element:{
            deep: true,
            immediate: true,
             handler(newVal,oldVal){
               console.log("触发监控 element");
                 if(newVal.type == 'bpmn:UserTask') {
                     const bpmnModeler = this.bpmnModeler();
                     const modeling = bpmnModeler.get('modeling');
                     const businessObject = newVal.businessObject;
                     this.name = businessObject.name;
                     this.formKey = businessObject.get('formKey');
                     // 原子
                     const candidateGroupsTemp = businessObject.get('candidateGroups');
                     // 解决后端反显和切换节点反显candidateGroupsTemp类型不一致问题
                    if(candidateGroupsTemp && candidateGroupsTemp.length > 0) {
                        if(Array.isArray(candidateGroupsTemp)) {
                            //切换节点反显
                            this.candidateGroups = businessObject.get('candidateGroups');
                        } else {
                            //后端反显
                            this.candidateGroups = businessObject.get('candidateGroups').split(',');
                        }
                    }

                     this.multiinstance_collection = businessObject.get('multiinstance_collection') || '';
                     this.multiinstance_type = businessObject.get('multiinstance_type') || 'None';
                     this.multiinstance_condition = businessObject.get('multiinstance_condition') || '';
                     this.multiinstance_variable = businessObject.get('multiinstance_variable') || '';
                     modeling.updateProperties(newVal,{'multiinstance_collection':this.multiinstance_collection});
                     modeling.updateProperties(newVal,{'multiinstance_type':this.multiinstance_type});
                     modeling.updateProperties(newVal,{'multiinstance_condition':this.multiinstance_condition});
                     modeling.updateProperties(newVal,{'multiinstance_variable':this.multiinstance_variable});
                 }
             }
        },
        formKey:{
            handler(newVal,oldVal){
              console.log("触发监控 formKey");
                this.modeling.updateProperties(this.element,{'formKey':newVal});
            }
        },
        multiinstance_type: {
            handler(newVal, oldVal) {
                this.modeling.updateProperties(this.element,{'multiinstance_type':newVal});
            }
        },
        multiinstance_collection: {
            handler(newVal, oldVal) {
                this.modeling.updateProperties(this.element,{'multiinstance_collection':newVal});
            }
        },
        multiinstance_condition: {
            handler(newVal, oldVal) {
                this.modeling.updateProperties(this.element,{'multiinstance_condition':newVal});
            }
        },
        multiinstance_variable: {
          handler(newVal, oldVal) {
            this.modeling.updateProperties(this.element,{'multiinstance_variable':newVal});
          }
        },
        priority: {
            handler(newVal, oldVal) {
                this.modeling.updateProperties(this.element,{'priority':newVal});
            }
        },
        candidateGroups: {
            handler(newVal,oldVal){
                this.modeling.updateProperties(this.element,{'candidateGroups':newVal});

            }
        }
    }

}
</script>
<style lang="less">
  
</style>