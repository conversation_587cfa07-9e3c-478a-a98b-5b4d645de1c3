<template>
  <a-tree
    showLine
    :treeData="treeDataSource"
    @select="handleTreeNodeSelect"
  >
  	<template slot="custom" slot-scope="item" v-if="editable">
      
      <!-- 点击编辑按钮进入编辑状态 -->
  		<div v-if="item.isEdit" class="custom-tree-node">
  			<a-input placeholder="请输入内容" style="height: 24px;" :value="item.title" @change="(v) => {handleInputChange(v, item)}"/>
  			<div>
  				<span>
  					<a-icon type="close-circle" @click="handleEditTreeNodeItemCancel(item)"/>
  				</span>
  				<span>
  					<a-icon type="check-circle" @click="handleEditTreeNodeItemOk(item)"/>
  				</span>
  			</div>
  		</div>
      
      <!-- 点击添加按钮进入新增状态 -->
  		<div v-else-if="item.isAdd" class="custom-tree-node">
  			<a-input placeholder="请输入内容" style="height: 24px;" :value="item.title" @change="(v) => {handleInputChange(v, item)}"/>
  			<div>
  				<span>
  					<a-icon type="close-circle" @click="handleAddTreeNodeItemCancel(item)"/>
  				</span>
  				<span>
  					<a-icon type="check-circle" @click="handleAddTreeNodeItemOk(item)"/>
  				</span>
  			</div>
  		</div>
      
      <!-- 默认展示状态 -->
  		<div v-else>
  			<div class="custom-tree-node">
  				<p>{{ item.title }}</p>
  				<div v-if="item.pos.split('-').length > 0">
  					<span>
  						<a-icon type="plus-circle" @click="handleAddTreeNodeItem(item)"/>
  					</span>
  					<a-popconfirm
  						title="确定删除吗？"
  						ok-text="确定"
  						cancel-text="取消"
  						@confirm="handleDeleteTreeNodeItemOk(item)"
  					>
  						<span v-if="!item.disEditable">
  							<a-icon type="delete" />
  						</span>
  					</a-popconfirm>
  					<span v-if="!item.disEditable">
  						<a-icon type="edit" @click="handleEditTreeNodeItem(item)"/>
  					</span>
  				</div>
  			</div>
  		</div>
  	</template>
  </a-tree>
</template>

<script>
  import { ATreeEditableMixin } from '@/mixins/ATreeEditableMixin'
  
  export default {
    props: ['editable'],
    mixins: [ATreeEditableMixin],
    mounted() {
      this.getTreeData();
    },
  }
</script>

<style lang="less" scoped>
  .custom-tree-node {
  	display: flex;
  	justify-content: space-between;
  	
  	span {
  		margin-left: 10px;
  	}
  }
</style>