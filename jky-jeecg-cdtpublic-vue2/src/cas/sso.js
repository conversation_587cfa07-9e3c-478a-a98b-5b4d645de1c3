import Vue from 'vue'
import { ACCESS_TOKEN } from "@/store/mutation-types"
import store from '@/store'
/**
 * 单点登录
 */
const init = (callback) => {
  // 2022-08-18 shenws 通过url参数指定是否要单点登录 start
  let toSSO = getUrlParam("sso");
//  alert("Vue.prototype.VUE_APP_SSO="+Vue.prototype.VUE_APP_SSO)
  if (toSSO && toSSO == 'false') {
     Vue.prototype.VUE_APP_SSO = 'false';
  }
  
//  alert("Vue.prototype.VUE_APP_SSO="+Vue.prototype.VUE_APP_SSO)
  // 2022-08-18 shenws end
  if (Vue.prototype.VUE_APP_SSO == 'true') {
    let token = Vue.ls.get(ACCESS_TOKEN);
    let st = getUrlParam("ticket");
    let sevice = "http://" + window.location.host + "/?ticket=";
    if (st) {
      validateSt(st, sevice, callback);
    } else {
      if (token) {
        loginSuccess(callback);
      } else {
        let serviceUrl = encodeURIComponent(sevice);
        window.location.href = window._CONFIG['casPrefixUrl'] + "/managePage/login?redirectUrl=" + serviceUrl;
      }
    }
  }else{
    callback && callback()
  }
};
const SSO = {
  init: init
};

function getUrlParam(paraName) {
  let url = document.location.toString();
  let arrObj = url.split("?");

  if (arrObj.length > 1) {
    let arrPara = arrObj[1].split("&");
    let arr;

    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split("=");

      if (arr != null && arr[0] == paraName) {
        return arr[1];
      }
    }
    return "";
  }
  else {
    return "";
  }
}

function validateSt(ticket,service,callback){
  let params = {
    ticket: ticket,
    service:service
  };
  store.dispatch('ValidateLogin',params).then(res => {
    //this.departConfirm(res)
    if(res.success){
      loginSuccess(callback);
    }else{
      let sevice = "http://"+window.location.host+"/?ticket=";
      let serviceUrl = encodeURIComponent(sevice);
      window.location.href = window._CONFIG['casPrefixUrl']+"/managePage/login?redirectUrl="+serviceUrl;
    }
  }).catch((err) => {
    console.log(err);
    //that.requestFailed(err);
  });
}

function loginSuccess (callback) {
  callback();
}
export default SSO;