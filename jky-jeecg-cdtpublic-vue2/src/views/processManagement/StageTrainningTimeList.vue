<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper" style="margin-bottom:10px">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :lg="6" :md="8" :sm="24">
						<a-form-item label="培训机构">
							<!--							<j-dict-select-tag type="list" v-model="queryParam.cblb" dictCode="tjcb_cblb"-->
							<!--								placeholder="请选择培训机构编号" @change="changeCblb" />-->
							<j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
							</j-tree-select-depart>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-input-group compact>
							<a-select v-model="selectQuery" style="width: 100px">
								<a-select-option value="1">
									身份证号
								</a-select-option>
								<a-select-option value="2">
									学员编号
								</a-select-option>
							</a-select>
							<a-input style="width: calc(100% - 100px)" v-model="inputQuery" />
						</a-input-group>
					</a-col>

					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="姓名">
							<a-input v-model="queryParam.stuname" placeholder="请输入姓名" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="科目">
							<j-dict-select-tag type="list" v-model="queryParam.subject" dictCode="sys_km"
								placeholder="请选择科目" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="教练员">
							<a-input v-model="queryParam.coachname" placeholder="请输入教练员" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="审核状态">
							<!--							<a-select placeholder="请选择审核状态" v-model="queryParam.auditstate">
								<a-select-option :value="0">未审核</a-select-option>
								<a-select-option :value="1">审核通过</a-select-option>
								<a-select-option :value="2">审核不通过</a-select-option>
							</a-select>-->
							<j-dict-select-tag type="list" v-model="queryParam.auditstate" dictCode="sys_general_audit"
								placeholder="请选择审核状态" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="是否校验">
							<!--							<a-select placeholder="请选择是否校验" v-model="queryParam.ispushsj">
								<a-select-option :value="0">否</a-select-option>
								<a-select-option :value="1">是</a-select-option>
							</a-select>-->
							<j-dict-select-tag type="list" v-model="queryParam.ispushsj" dictCode="sys_check"
								placeholder="请选择是否校验" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="推送银行">
							<a-select placeholder="请选择是否推送银行" v-model="queryParam.isbank">
								<a-select-option :value="0">未推送</a-select-option>
								<a-select-option :value="1">已推送</a-select-option>
								<a-select-option :value="2">推送失败</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="6" :md="8" :sm="24">
						<a-form-item label="推送计时">
							<a-select placeholder="请选择推送计时" v-model="queryParam.ispushjs">
								<a-select-option :value="0">未推送</a-select-option>
								<a-select-option :value="1">已推送</a-select-option>
								<a-select-option :value="2">推送失败</a-select-option>
							</a-select>
						</a-form-item>
					</a-col>
					<a-col :xl="8" :lg="8" :md="8" :sm="24">
						<a-form-item label="推送计时时间">
							<j-date placeholder="请选择开始日期" class="query-group-cust"
								v-model="queryParam.pushjsdate_begin">
							</j-date>
							<span class="query-group-split-cust"></span>
							<j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.pushjsdate_end">
							</j-date>
						</a-form-item>
					</a-col>
					<a-col :xl="8" :lg="8" :md="8" :sm="24">
						<a-form-item label="审核时间">
							<j-date placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.auditdate_begin">
							</j-date>
							<span class="query-group-split-cust"></span>
							<j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.auditdate_end">
							</j-date>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
			<a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:time:add'">新增</a-button>
			<a-button type="primary" icon="download" @click="handleExportXls('报审申请管理数据')" v-has="'gzpt:time:export'">导出</a-button>
<!--			<a-button type="primary" icon="download" :disabled="multiple" @click="handleExportXlsIds('报审申请管理数据')"-->
<!--				v-has="'gzpt:time:export'">勾选导出</a-button>-->
			<a-button @click="timeCheck" :disabled="!clickable || single" type="primary" icon="radius-setting"
				v-has="'gzpt:time:checkStage'">检验学时</a-button>
			<a-button @click="sendJS" :disabled="single" type="primary" icon="radius-setting"
				v-has="'gzpt:time:pushJs'">推送计时</a-button>
<!--			<a-dropdown :disabled="single" v-has="'gzpt:time:pushJs:special'">-->
<!--				<a-menu slot="overlay">-->
<!--					<a-menu-item @click.native="sendJS">-->
<!--						多伦-->
<!--					</a-menu-item>-->
<!--					<a-menu-item @click.native="sendJS">-->
<!--						星唯-->
<!--					</a-menu-item>-->
<!--				</a-menu>-->
<!--				<a-button style="margin-left: 8px" type="primary"> 推送计时-->
<!--					<a-icon type="down" />-->
<!--				</a-button>-->
<!--			</a-dropdown>-->
			<a-button @click="sendBank" :disabled="single" type="primary" icon="radius-setting"
				v-has="'gzpt:time:pushBank'">推送银行</a-button>

			<a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:time:remove'">
				<a-menu slot="overlay">
					<a-menu-item key="1" @click="batchDel">
						<a-icon type="delete" />删除
					</a-menu-item>
				</a-menu>
				<a-button style="margin-left: 8px"> 批量操作
					<a-icon type="down" />
				</a-button>
			</a-dropdown>
		</div>

		<!-- table区域-begin -->
		<div>
			<div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
				<i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
					style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
				<a style="margin-left: 24px" @click="onClearSelected">清空</a>
			</div>

			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="max-width:80px;font-size: 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>

				<span slot="action" slot-scope="text, record">
					<a @click="handleEdit(record)" v-has="'gzpt:time:edit'">编辑</a>

					<a-divider type="vertical" v-has="'gzpt:time:edit'" />
					<a @click="handleDetail(record)">详情</a>
					<a-divider type="vertical"  />
					<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
						<a>删除</a>
					</a-popconfirm>
					<!-- <a-dropdown>
						<a class="ant-dropdown-link">更多
							<a-icon type="down" />
						</a>
						<a-menu slot="overlay">
							<a-menu-item>
								<a @click="handleDetail(record)">详情</a>
							</a-menu-item>
							<a-menu-item v-has="'gzpt:time:remove'">
								<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
									<a>删除</a>
								</a-popconfirm>
							</a-menu-item>
						</a-menu>
					</a-dropdown> -->
				</span>
				<div slot="message" slot-scope="text, record">
					<div style="width: 150px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;"
						:title="record.message">{{ record.message }}</div>
				</div>
			</a-table>
		</div>
		<stage-trainning-time-modal ref="modalForm" @ok="modalFormOk"></stage-trainning-time-modal>
	</a-card>
</template>

<script>
	import {
		listTime,
		getTime,
		delTime,
		addTime,
		updateTime,
		exportTime,
		checkStage,
		pushJs,
		pushBank,
		exportTimeByid,
	} from "@/api/gzpt/time";
	import {
		deleteAction,
		getAction,
		downFile,
		getFileAccessHttpUrl,
		postAction
	} from '@/api/manage'
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
	import StageTrainningTimeModal from './modules/StageTrainningTimeModal'
	import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
	export default {
		name: 'StageTrainningTimeList',
		mixins: [JeecgListMixin, mixinDevice],
		components: {
			JTreeSelectDepart,
			StageTrainningTimeModal
		},
		data() {
			return {
				description: '阶段报审申请',

				labelCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 5
					},
				},
				wrapperCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 16
					},
				},
				dataSource: [{
					stuname: '刘一',
					idcard: '**************',
					sex: '女',
				}],
				//人脸对比
				isface: null,
				//省局校验
				ispushsj: null,
				isbank: null,
				//是否审核
				audit: null,
				message: null,
				subject: null,
				stunum: null,
				// 部门树选项

				deptOptions: undefined,
				// 遮罩层
				loading: true,
				// 导出遮罩层
				exportLoading: false,
				// 选中数组
				ids: [],
				// 非单个禁用
				single: true,
				// 非多个禁用
				multiple: true,
				// 显示搜索条件
				showSearch: true,
        clickable: true,
				// 表头
				columns: [{
						title: '序号',
						dataIndex: '',
						key: 'rowIndex',
						width: 60,
						align: "center",
						customRender: function(t, r, index) {
							return parseInt(index) + 1;
						}
					},
					{
						title: '姓名',
						align: "center",
						dataIndex: 'stuname'
					},
          {
            title: '学员编号',
            align: "center",
            dataIndex: 'stunum'
          },
					{
						title: '身份证',
						align: "center",
						dataIndex: 'idcard'
					},
					{
						title: '性别',
						align: "center",
						dataIndex: 'sex_dictText'
					},
					{
						title: '科目',
						align: "center",
						dataIndex: 'subject_dictText'
					},
					{
						title: '培训机构名称',
						align: "center",
						dataIndex: 'insname'
					},
					{
						title: '教练员',
						align: "center",
						dataIndex: 'coachname'
					},
					{
						title: '车型',
						align: "center",
						dataIndex: 'traintype'
					},
					{
						title: '是否校验', //0=未校验,1=已校验
						align: "center",
						dataIndex: 'ispushsj_dictText'
					},

					{
						title: '监管校验',
						align: "center",
						dataIndex: 'message',
						scopedSlots: {
							customRender: 'message'
						}
					},
					{
						title: '推送计时', // 0=未推送,1=推送成功,2=推送失败
						align: "center",
						dataIndex: 'ispushjs_dictText'
					},
					{
						title: '总学时',
						align: "center",
						dataIndex: 'totaltime'
					},
					{
						title: '审核状态', // 0=未审核,1=审核通过,2=推送失败
						align: "center",
						dataIndex: 'auditstate_dictText'
					},
					{
						title: '审核人',
						align: "center",
						dataIndex: 'operatorname'
					},
					{
						title: '审核日期',
						align: "center",
						dataIndex: 'auditdate',
						customRender: function(text) {
							return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
						}
					},
					{
						title: '申请日期',
						align: "center",
						dataIndex: 'crdate',
						customRender: function(text) {
							return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
						}
					},
					{
						title: '推送计时日期',
						align: "center",
						dataIndex: 'pushjsdate',
						customRender: function(text) {
							return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
						}
					},
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/gzpt/stageTrainningTime/list",
					delete: "/gzpt/stageTrainningTime/delete",
					deleteBatch: "/gzpt/stageTrainningTime/deleteBatch",
					exportXlsUrl: "/gzpt/stageTrainningTime/exportXls",
					exportXlsUrlIds: '/gzpt/stageTrainningTime/exportByIds',
					importExcelUrl: "gzpt/stageTrainningTime/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
				inputQuery: '',
				selectQuery: '1',
				queryParams: {
					idcard: '',
					stunum: '',

				},
			}
		},
		created() {
			this.getSuperFieldList();
		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			initDictConfig() {},
			getSuperFieldList() {
				let fieldList = [];
				fieldList.push({
					type: 'string',
					value: 'inscode',
					text: '培训机构编号'
				})
				fieldList.push({
					type: 'string',
					value: 'insname',
					text: '培训机构名称'
				})
				fieldList.push({
					type: 'string',
					value: 'coachname',
					text: '教练员'
				})
				fieldList.push({
					type: 'string',
					value: 'stunum',
					text: '学员编号'
				})
				fieldList.push({
					type: 'string',
					value: 'idcard',
					text: '身份证'
				})
				fieldList.push({
					type: 'string',
					value: 'stuname',
					text: '姓名'
				})
				fieldList.push({
					type: 'int',
					value: 'subject',
					text: '科目'
				})
				fieldList.push({
					type: 'number',
					value: 'totaltime',
					text: '总学时(分)'
				})
				fieldList.push({
					type: 'number',
					value: 'vehicletime',
					text: '实操学时(分)'
				})
				fieldList.push({
					type: 'number',
					value: 'classtime',
					text: '课堂学时(分)'
				})
				fieldList.push({
					type: 'number',
					value: 'simulatortime',
					text: '模拟学时(分)'
				})
				fieldList.push({
					type: 'number',
					value: 'networktime',
					text: '远程学时(分)'
				})
				fieldList.push({
					type: 'number',
					value: 'mileage',
					text: '里程(h/km)'
				})
				fieldList.push({
					type: 'string',
					value: 'applydate',
					text: '报名时间'
				})
				fieldList.push({
					type: 'string',
					value: 'auditstate',
					text: '审核状态 0=未审核,1=审核通过,2=推送失败'
				})
				fieldList.push({
					type: 'string',
					value: 'operatorname',
					text: '审核人'
				})
				fieldList.push({
					type: 'date',
					value: 'auditdate',
					text: '审核时间'
				})
				fieldList.push({
					type: 'string',
					value: 'district',
					text: '市区编号'
				})
				fieldList.push({
					type: 'string',
					value: 'traintype',
					text: '车型'
				})
				fieldList.push({
					type: 'int',
					value: 'ispushsj',
					text: '是否校验 0=未校验,1=已校验'
				})
				fieldList.push({
					type: 'int',
					value: 'ispushjs',
					text: '是否推送计时 0=未推送,1=推送成功,2=推送失败'
				})
				fieldList.push({
					type: 'int',
					value: 'isface',
					text: '是否人脸对比 0=未对比,1=已对比'
				})
				fieldList.push({
					type: 'date',
					value: 'crdate',
					text: '创建时间'
				})
				fieldList.push({
					type: 'date',
					value: 'pushjsdate',
					text: '推送计时时间'
				})
				fieldList.push({
					type: 'date',
					value: 'pushsjdate',
					text: '校验时间'
				})
				fieldList.push({
					type: 'number',
					value: 'duration',
					text: '有效学时'
				})
				fieldList.push({
					type: 'int',
					value: 'isbank',
					text: '是否推送银行 0 未推送 1 推送成功 2 推送失败'
				})
				fieldList.push({
					type: 'string',
					value: 'resultmeg',
					text: '说明'
				})
				fieldList.push({
					type: 'string',
					value: 'recnums',
					text: '电子教学日志+培训时间'
				})
				this.superFieldList = fieldList
			},

			loadData(arg) {
				if (!this.url.list) {
					this.$message.error("请设置url.list属性!")
					return
				}
				//加载数据 若传入参数1则加载第一页的内容
				if (arg === 1) {
					this.ipagination.current = 1;
				}
				var params = this.getQueryParams(); //查询条件
				if (this.selectQuery != "") {
					if (this.selectQuery == 1) {
						params.idcard = this.inputQuery;
						params.stunum = null;
					}
					if (this.selectQuery == 2) {
						params.stunum = this.inputQuery;
						params.idcard = null;
					}
				}
				this.loading = true;
				getAction(this.url.list, params).then((res) => {
					if (res.success) {
						//update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
						this.dataSource = res.result.records || res.result;
						if (res.result.total) {
							this.ipagination.total = res.result.total;
						} else {
							this.ipagination.total = 0;
						}
						//update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
					} else {
						this.$message.warning(res.message)
					}
				}).finally(() => {
					this.loading = false
				})
			},

			searchReset() {
				this.queryParam = {}
				this.inputQuery = "";
				this.selectQuery = "1";
				this.loadData(1);
			},
			onSelectChange(selectedRowKeys, selectionRows) {
				this.selectedRowKeys = selectedRowKeys;
				this.selectionRows = selectionRows;
				let selection = selectionRows;
				this.ids = selection.map((item) => item.id);
				this.single = selection.length !== 1;
				this.multiple = !selection.length;
				this.isface = selection.map((item) => item.isface);
				this.ispushsj = selection.map((item) => item.ispushsj);
				this.message = selection.map((item) => item.message);
				this.audit = selection.map((item) => item.auditstate);
				this.isbank = selection.map((item) => item.isbank);
				this.subject = selection.map((item) => item.subject);
				this.stunum = selection.map((item) => item.stunum)
			},
			//学时校验
			timeCheck(row) {
        this.clickable = false;
        this.single = false;
				var audit1 = parseInt(this.audit.indexOf("1"));
				var audit2 = parseInt(this.audit.indexOf("2"));
				if (audit1 == 0 || audit2 == 0) {
          this.clickable = true;
          this.single = true;
					return this.$message.error("报审已审核无需再推送");
				}
				const data = {
					id: this.ids,
				};
				console.log(data);
				// this.bigLoading = true;
				checkStage(this.ids)
					.then((response) => {
					  if(response.success){
					    this.$message.success(response.message);
              this.loadData();
              this.onSelectChange([],[])
            }else {
              this.$message.error(response.message);
            }
					})
					.catch((e) => {
						this.loadData();
						// this.bigLoading = false;
					});
        this.clickable = true;
        this.single = true;
			},
			sendJS(bankCode) {
				const isface = this.isface.find((item) => item === 0);
				// if (isface === 0) {
				// 	return this.$message.error("人脸未对比");
				// }
				const id = this.ids;
				const ispushsj = this.ispushsj.join(",");
				// if (ispushsj == 0) {
				// 	return this.$message.error("未推送省局校验");
				// }
				// console.log(this.message);
				const meg = this.message.join(",");

				const audit = this.audit.join(",");
				// console.log("111", audit == 1);
				// if (audit == 1 || audit == 2) {
				// 	return this.$message.error("报审已审核无需再推送");
				// }
				const data = {
					id: id.join(","),
					bankCode: bankCode,
				};
				console.log(data, "=====");
				this.bigLoading = true;

				pushJs(data).then((res) => {
					if (res.success) {
						console.log('success')
						this.$message.success("推送成功");
						// this.bigLoading = false;
						this.loadData();
					} else {
						this.$message.error(res.message);
					}
				}).catch((e) => {
					console.log(e)
					console.log('error')
					return this.$message.error("推送失败");
					// this.bigLoading = false;
					this.loadData();
				});
			},
			//推送银行
			sendBank(row) {
				const id = row.id || this.ids;

				//const subject = this.subject.join(",");
				//  console.log('111',subject)
				/*  if(subject == 1){
				         return this.$message.error("第一部分不能手动推送银行!");
				      } */
				const audit = this.audit.join(",");
				if (audit == 0) {
					return this.$message.error("审核通过后才能推送银行!");
				}
				/* const isbank = this.isbank.join(",");
				if (isbank == 1) {
				  return this.$message.error("已推送过,无需再次推送银行!");
				} */
				const data = {
					id: id.join(","),
				};
				// this.bigLoading = true;
				pushBank(data)
					.then((response) => {
						if (response.success) {
							this.$message.success("推送成功");
						} else {
							this.$message.error(response.message);
						}

						// this.bigLoading = false;
						this.loadData();
					})
					.catch((e) => {
						// this.bigLoading = false;
						this.loadData();
					});
			},
			//勾选导出
			handleExportXlsIds(fileName) {
				if (!fileName || typeof fileName != "string") {
					fileName = "导出文件"
				}
				let param = {} //this.getQueryParams();
				if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
					param['selections'] = this.selectedRowKeys.join(",")
					// param = {
					//   selections: this.selectedRowKeys.join(",")
					// }
				}
				console.log("导出参数", param)
				downFile(this.url.exportXlsUrlIds, param).then((data) => {
					if (!data) {
						this.$message.warning("文件下载失败")
						return
					}
					if (typeof window.navigator.msSaveBlob !== 'undefined') {
						window.navigator.msSaveBlob(new Blob([data], {
							type: 'application/vnd.ms-excel'
						}), fileName + '.xls')
					} else {
						let url = window.URL.createObjectURL(new Blob([data], {
							type: 'application/vnd.ms-excel'
						}))
						let link = document.createElement('a')
						link.style.display = 'none'
						link.href = url
						link.setAttribute('download', fileName + '.xls')
						document.body.appendChild(link)
						link.click()
						document.body.removeChild(link); //下载完成移除元素
						window.URL.revokeObjectURL(url); //释放掉blob对象
					}
				})
			},



		}
	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
