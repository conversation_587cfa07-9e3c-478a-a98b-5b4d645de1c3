<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="异常组对象" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="objnum">
              <a-input v-model="model.objnum" placeholder="请输入异常组对象"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="异常组对象名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="objname">
              <a-input v-model="model.objname" placeholder="请输入异常组对象名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
<!--            <a-form-model-item label="1-业户 2-学员 3-教练员 4-教练车" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <a-input-number v-model="model.type" placeholder="请输入1-业户 2-学员 3-教练员 4-教练车" style="width: 100%" />
            </a-form-model-item>-->
            <a-form-model-item label="对象类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag v-model="model.type" placeholder="类型" dictCode="sys_group_type" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入培训机构编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入培训机构名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="异常类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="abnormalSeq">
              <a-input v-model="model.abnormalSeq" placeholder="请输入异常类型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="异常类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="abnormalName">
              <a-input v-model="model.abnormalName" placeholder="请输入异常类型名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
<!--            <a-form-model-item label="处理状态 0-未处理 1-已处理" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <a-input-number v-model="model.status" placeholder="请输入处理状态 0-未处理 1-已处理" style="width: 100%" />
            </a-form-model-item>-->
            <a-form-model-item label="处理状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag v-model="model.status" placeholder="请选择处理状态" dictCode="sys_group_status" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="生成时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择生成时间" v-model="model.crdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="处理时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="disposeDate">
              <j-date placeholder="请选择处理时间" v-model="model.disposeDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="县区" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
              <a-input v-model="model.district" placeholder="请输入县区"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否提交工单" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isWorkorder">
<!--              <a-input-number v-model="model.isWorkorder" placeholder="请输入是否提交工单 0-未提交 1-提交" style="width: 100%" />-->
              <j-dict-select-tag v-model="model.isWorkorder" placeholder="请选择处理状态" dictCode="sys_is_workorder" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="提交时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="submitDate">
              <j-date placeholder="请选择提交时间" v-model="model.submitDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="标识工单提交后的状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orderstatus">
              <a-input-number v-model="model.orderstatus" placeholder="请输入标识工单提交后的状态" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="原因" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reason">
              <a-input v-model="model.reason" placeholder="请输入原因"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="操作人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="operator">
              <a-input v-model="model.operator" placeholder="请输入操作人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="从电子教学日志,异常组推疑似违规操作人id" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="userId">
              <a-input-number v-model="model.userId" placeholder="请输入从电子教学日志,异常组推疑似违规操作人id" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="1-电子教学日志添加,2-异常组添加疑似违规" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="addWay">
              <a-input-number v-model="model.addWay" placeholder="请输入1-电子教学日志添加,2-异常组添加疑似违规" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否是新监管添加" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isNewjgpt">
<!--              <a-input-number v-model="model.isNewjgpt" placeholder="请输入是否是新监管添加 0.否 1.是" style="width: 100%" />-->
              <a-select v-model="model.isNewjgpt" placeholder="请选择是否是新监管添加">
                <a-select-option :value="0">
                  否
                </a-select-option>
                <a-select-option :value="1">
                  是
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'TMAbnormalGroupNoForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 12 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/jgpt/abnormalGroupNo/add",
          edit: "/jgpt/abnormalGroupNo/edit",
          queryById: "/jgpt/abnormalGroupNo/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>