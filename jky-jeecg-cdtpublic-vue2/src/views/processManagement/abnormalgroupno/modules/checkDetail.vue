<template>
  <div>
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" tab="电子教学日志">
        <a-table
          :loading="loading"
          id="StatisticalReport_table"
          :columns="columns"
          :data-source="tableDataSource"
          bordered
          :pagination="false"
        >
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
// import { getStudent,ClassRecordStatistics} from "@/api/jgpt/student";
import { getAction } from '@/api/manage'
export default {
  name: 'checkDetail',
  data() {
    return {
      stunum: '',
      insname: '',
      detailInfo: {},
      classRecordDetail: [
        {
          //培训部分
          training: null,
          trainingType: null,
          crditeTime: 0,
          validTime: 0,
          trainingTotalTime: 0,
        },
      ],
      columns: [
        {
          title: '编号',
          align: 'center',
          dataIndex: 'recnum',
        },
        {
          title: '学员',
          align: 'center',
          dataIndex: 'stuname',
        },
        {
          title: '教练员',
          align: 'center',
          dataIndex: 'coachname',
        },
        {
          title: '车牌号',
          align: 'center',
          dataIndex: 'licnum',
        },
        {
          title: '培训部分',
          align: 'center',
          dataIndex: 'km',
          customRender: function (t, r, index) {
            switch (t) {
              case '1':
                return '第一部分'
                break
              case '2':
                return '第二部分'
                break
              case '3':
                return '第三部分'
                break
              case '4':
                return '第四部分'
                break
              default:
                break
            }
          },
        },
        {
          title: '培训类型',
          align: 'center',
          dataIndex: 'calssType',
          customRender: function (t, r, index) {
            switch (t) {
              case '1':
                return '实操'
                break
              case '2':
                return '课堂'
                break
              case '3':
                return '远程'
                break
              case '4':
                return '模拟'
                break
              default:
                break
            }
          },
        },
        // {
        //   title:'培训类型',
        //   align:'center',
        //   dataIndex:'licnum',
        // },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'starttime',
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'endtime',
        },
        {
          title: '学时',
          align: 'center',
          dataIndex: 'duration',
        },
        {
          title: '里程',
          align: 'center',
          dataIndex: 'mileage',
        },
        {
          title: '均速',
          align: 'center',
          dataIndex: 'avevelocity',
        },
        {
          title: '生成时间',
          align: 'center',
          dataIndex: 'crdate',
        },
      ],
      tableDataSource: [],
      loading: false,
    }
  },
  methods: {
    previewImg(url) {
      this.$hevueImgPreview(url)
    },
    getData(record) {
      this.stunum = record.id
      this.insname = record.insname
      this.getList()
    },
    getList() {
      let data = { id: this.stunum, type: 1 }
      // this.loading = true;
      // classRecordList(data).then(response => {
      //   console.log('data:',response.data)
      //   this.details = response.data;
      //   /* this.total = response.total;*/
      //   this.loading = false;
      // });
      this.loading = true
      getAction('/jgpt/abnormalGroup/classRecordList', data)
        .then((res) => {
          if (res.success) {
            this.tableDataSource = res.result.records || res.result
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="less" scoped>
.img {
  width: 80px;
  height: 80px;
  border: none;
  overflow: hidden;
}
</style>