<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="学员姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuName">
              <a-input v-model="model.stuName" placeholder="请输入学员姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuNum">
              <a-input v-model="model.stuNum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tel">
              <a-input v-model="model.tel" placeholder="请输入手机号码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idCard">
              <a-input v-model="model.idCard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学驾车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carType">
<!--              <a-input-number v-model="model.carType" placeholder="请输入学驾车型" style="width: 100%" />-->
              <j-dict-select-tag type="list" v-model="model.carType" dictCode="sys_stu_traintype"
                                 placeholder="请选择培训车型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="考试科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="examSub">
<!--              <a-input-number v-model="model.examSub" placeholder="请输入考试科目" style="width: 100%" />-->
              <j-dict-select-tag type="list" v-model="model.examSub" dictCode="	sys_km"
                                 placeholder="请选择培训车型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="考试场地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="field">
              <a-input v-model="model.field" placeholder="请输入考试场地"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="考试场次" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="session">
              <a-input v-model="model.session" placeholder="请输入考试场次"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="考试时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="examTime">
              <j-date placeholder="请选择考试时间" v-model="model.examTime"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachNum">
              <a-input v-model="model.coachNum" placeholder="请输入教练"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="考试结果" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="examRes">
              <a-input-number v-model="model.examRes" placeholder="请输入考试结果" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'TMExamAppointForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           stuName: [
              { required: true, message: '请输入学员姓名!'},
           ],
           stuNum: [
              { required: true, message: '请输入学员编号!'},
           ],
           tel: [
              { required: true, message: '请输入手机号码!'},
           ],
           idCard: [
              { required: true, message: '请输入身份证号!'},
           ],
           carType: [
              { required: true, message: '请输入学驾车型!'},
           ],
           examSub: [
              { required: true, message: '请输入考试科目!'},
           ],
           examTime: [
              { required: true, message: '请输入考试时间!'},
           ],
           coachNum: [
              { required: true, message: '请输入教练!'},
           ],
           examRes: [
              { required: true, message: '请输入考试结果!'},
           ],
        },
        url: {
          add: "/gzpt/tMExamAppoint/add",
          edit: "/gzpt/tMExamAppoint/edit",
          queryById: "/gzpt/tMExamAppoint/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>