<template>
  <a-tabs>
    <a-tab-pane key="1" tab="学员基本信息">
      <a-spin :spinning="confirmLoading">
        <j-form-container :disabled="formDisabled">
          <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
            <a-row>
              <a-col :span="12">
                <a-form-model-item
                  label="姓名"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="name"
                  v-if="isFromEnter"
                >
                  <a-input v-model="model.name" placeholder="请输入姓名" :disabled="model.id"></a-input>
                </a-form-model-item>
                <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name" v-else>
                  <a-input
                    v-model="model.name"
                    placeholder="请输入姓名"
                    :disabled="!isFromEnter && !!model.id"
                  ></a-input>
                </a-form-model-item>

                <a-form-model-item
                  label="性别"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="sex"
                  v-if="isFromEnter"
                >
                  <j-dict-select-tag
                    type="list"
                    v-model="model.sex"
                    dictCode="sys_user_sex"
                    placeholder="请选择性别"
                    :disabled="model.id"
                  />
                </a-form-model-item>
                <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex" v-else>
                  <j-dict-select-tag type="list" v-model="model.sex" dictCode="sys_user_sex" placeholder="请选择性别" />
                </a-form-model-item>

                <a-form-model-item
                  label="证件类型"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="cardtype"
                  style="margin-bottom: 0;"
                >
                  <j-dict-select-tag
                    type="list"
                    v-model="model.cardtype"
                    dictCode="sys_stu_cardtype"
                    placeholder="请选择证件类型"
                    :disabled="!isFromEnter && !!model.id"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="头像" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photopath">
                  <span style="color: crimson;">（请上传小于2M的图片）</span>
                  <j-upload
                    v-model="model.photopath"
                    fileType="image"
                    :multiple="true"
                    :number="1"
                    :beforeUpload="beforeUpload"
                  >
                  </j-upload>
                  <a-button type="primary" @click="openCamer">开启摄像头拍照</a-button>
                </a-form-model-item>
              </a-col>

              <a-col :span="12" v-if="title == '详情'">
                <a-form-model-item label="学员统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cardtype">
                  <a-input v-model="model.stunum" placeholder="请输入学员统一编号"></a-input>
                </a-form-model-item>
              </a-col>

              <a-col :span="12" v-if="isFromEnter">
                <a-form-model-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cardtype">
                  <a-input v-model="model.idcard" placeholder="请输入证件号码" :disabled="model.id"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-else>
                <a-form-model-item label="证件号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cardtype">
                  <a-input
                    v-model="model.idcard"
                    placeholder="请输入证件号码"
                    :disabled="!isFromEnter && !!model.id"
                  ></a-input>
                </a-form-model-item>
              </a-col>

              <a-col :span="12">
                <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
                  <j-tree-select-depart
                    placeholder="请选择培训机构"
                    v-model="model.inscode"
                    v-if="title == '新增'"
                    @change="getCocachList"
                    @getData="getTreeData"
                  ></j-tree-select-depart>
                  <a-input v-model="model.insname" placeholder="" v-if="title != '新增'" disabled> </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="报名时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="applydate">
                  <!--                  <j-date placeholder="请选择报名时间" class="query-group-cust" :value="model.applydate" dateFormat="YYYYMMDD"
                    style="width:100%">
                  </j-date>-->
                  <j-date
                    placeholder="请选择报名时间"
                    class="query-group-cust"
                    v-model="model.applydate"
                    dateFormat="YYYYMMDD"
                    style="width:100%"
                    readOnly
                  >
                  </j-date>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
                  <a-input v-model="model.phone" placeholder="请输入手机号码"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="联系地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
                  <a-input v-model="model.address" placeholder="请输入联系地址"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="isFromEnter">
                <a-form-model-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.nationality"
                    dictCode="sys_common_country"
                    placeholder="请选择国籍"
                    disabled
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-else>
                <a-form-model-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.nationality"
                    dictCode="sys_common_country"
                    placeholder="请选择国籍"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="业务类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="busitype">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.busitype"
                    dictCode="sys_stu_busitype"
                    placeholder="请选择业务类型"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="驾驶证号"
                  v-if="model.busitype == '1'"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="drilicnum"
                >
                  <a-input v-model="model.drilicnum" placeholder="请输入驾驶证号"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="驾驶证初领日期"
                  v-if="model.busitype == '1'"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="fstdrilicdate"
                >
                  <j-date
                    placeholder="请选择驾驶证初领日期"
                    class="query-group-cust"
                    v-model="model.fstdrilicdate"
                    style="width:100%"
                  >
                  </j-date>
                </a-form-model-item>
              </a-col>

              <a-col :span="12">
                <a-form-model-item
                  label="原准驾车型"
                  v-if="model.busitype == '1'"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="perdritype"
                >
                  <j-dict-select-tag
                    type="list"
                    v-model="model.perdritype"
                    dictCode="sys_stu_traintype"
                    placeholder="请选择原准驾车型"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="isFromEnter">
                <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
                  <j-dict-select-tag
                    type="list"
                    @input="selectTrainType"
                    v-model="model.traintype"
                    dictCode="sys_stu_traintype"
                    placeholder="请选择培训车型"
                    disabled
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="!isFromEnter">
                <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
                  <j-dict-select-tag
                    type="list"
                    @input="selectTrainType"
                    v-model="model.traintype"
                    dictCode="sys_stu_traintype"
                    placeholder="请选择培训车型"
                  />
                </a-form-model-item>
              </a-col>
              <!--              <a-col :span="12">-->
              <!--                <a-form-model-item-->
              <!--                  label="学时单价（元/学时）"-->
              <!--                  :labelCol="labelCol"-->
              <!--                  :wrapperCol="wrapperCol"-->
              <!--                  prop="unitPrice"-->
              <!--                >-->
              <!--                  <a-input-number-->
              <!--                    :decimalSeparator="accuracy"-->
              <!--                    :precision="1"-->
              <!--                    v-model="model.unitPrice"-->
              <!--                    :min="minprice"-->
              <!--                    placeholder="请输入学时单价"-->
              <!--                    style="width:100%"-->
              <!--                    @change="unitPriceChange"-->
              <!--                  ></a-input-number>-->
              <!--                </a-form-model-item>-->
              <!--              </a-col>-->
              <!--              <a-col :span="12">-->
              <!--                <a-form-model-item label="第二部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="secondCost">-->
              <!--                  <a-input-number-->
              <!--                    :decimalSeparator="accuracy"-->
              <!--                    v-model="model.secondCost"-->
              <!--                    placeholder="请输入第二部分费用"-->
              <!--                    style="width:100%"-->
              <!--                    :disabled="isFromEnter"-->
              <!--                  ></a-input-number>-->
              <!--                </a-form-model-item>-->
              <!--              </a-col>-->
              <!--              <a-col :span="12">-->
              <!--                <a-form-model-item label="第三部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="thirdCoast">-->
              <!--                  <a-input-number-->
              <!--                    :decimalSeparator="accuracy"-->
              <!--                    v-model="model.thirdCoast"-->
              <!--                    placeholder="请输入第三部分费用"-->
              <!--                    style="width:100%"-->
              <!--                    :disabled="isFromEnter"-->
              <!--                  ></a-input-number>-->
              <!--                </a-form-model-item>-->
              <!--              </a-col>-->
              <!--              <a-col :span="12">-->
              <!--                <a-form-model-item label="服务费等" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="archiveCost">-->
              <!--                  <a-input-number-->
              <!--                    :decimalSeparator="accuracy"-->
              <!--                    :min="0"-->
              <!--                    :max="1000"-->
              <!--                    v-model="model.archiveCost"-->
              <!--                    placeholder="按运管要求，服务费1000元以内"-->
              <!--                    style="width:100%"-->
              <!--                  ></a-input-number>-->
              <!--                </a-form-model-item>-->
              <!--              </a-col>-->
              <a-col :span="12">
                <a-form-model-item label="冻结金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="frozenAmount">
                  <a-input v-model="model.frozenAmount" placeholder="请输入冻结金额" :disabled="true"> </a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  v-if="title !== '新增'"
                  label="是否老学员"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="isold"
                >
                  <a-radio-group v-model="model.isold" :disabled="isFromEnter">
                    <a-radio value="0">
                      否
                    </a-radio>
                    <a-radio value="1">
                      是
                    </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="实操教练" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
                  <a-select :value="model.coachnum" show-search @change="handleSearch" optionFilterProp="title">
                    <a-select-option
                      :value="item.coachnum"
                      :key="item.coachnum"
                      :title="item.name"
                      v-for="(item, index) in cocachList"
                    >
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="是否为跨地市转校"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="iscrosscity"
                >
                  <a-radio-group v-model="model.iscrosscity" @change="crosscityChange" :disabled="!!model.isEnter">
                    <a-radio value="2">
                      否
                    </a-radio>
                    <a-radio value="1">
                      是
                    </a-radio>
                  </a-radio-group>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="model.iscrosscity == '1'">
                <a-form-model-item
                  label="外地转入前所在地市"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="fromarea"
                >
                  <a-input
                    v-model="model.fromarea"
                    placeholder="请输入转入前所在地市  如：杭州市"
                    :disabled="!!model.isEnter"
                  ></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="model.iscrosscity == '1'">
                <a-form-model-item
                  label="转校后学习科目"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="crossType"
                >
                  <a-select v-model="model.crossType" @change="transferSubject" :disabled="!!model.isEnter">
                    <a-select-option :value="1">
                      科目二和科目三
                    </a-select-option>
                    <a-select-option :value="2">
                      科目二
                    </a-select-option>
                    <a-select-option :value="3">
                      科目三
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </j-form-container>
        <a-modal v-model="cameraVisible" @ok="handleOk" :width="700">
          <div class="box">
            <video id="videoCamera" class="canvas" :width="videoWidth" :height="videoHeight" autoPlay></video>
            <canvas id="canvasCamera" class="canvas" :width="videoWidth" :height="videoHeight"></canvas>
          </div>
          <div class="bommen" style="margin-top: 50px;">
            <a-button type="primary" @click="openCamera()">打开摄像头</a-button>
            <a-button type="primary" @click="stopCamera()" style="margin:0 5px">关闭摄像头</a-button>
            <a-button type="primary" @click="setImage()">拍照</a-button>
          </div>
        </a-modal>
      </a-spin>
    </a-tab-pane>
    <a-tab-pane key="2" tab="驾训明细" v-if="formDisabled && !isFromEnter" forceRender>
      <a-table
        :dataSource="trainTableList.dataSource"
        :columns="trainTableList.columns"
        :pagination="{ total: trainTableList.total, current: trainTableList.pageIndex }"
        @change="pagination => getTrainTableList(pagination.current)"
      ></a-table>
    </a-tab-pane>
    <a-tab-pane key="3" tab="消费结算明细" v-if="formDisabled && !isFromEnter">
      <a-table
        :columns="orderTableList.columns"
        :dataSource="orderTableList.dataSource"
        :pagination="{ total: orderTableList.total, current: orderTableList.pageIndex }"
        @change="pagination => getOrderTableList(pagination.current)"
      ></a-table>
    </a-tab-pane>
    <a-tab-pane key="4" tab="学时汇总" v-if="formDisabled && !isFromEnter">
      <a-table
        :columns="timeTableList.columns"
        :dataSource="timeTableList.dataSource"
        :pagination="{ total: timeTableList.total, current: timeTableList.pageIndex }"
        @change="pagination => getTimeTableList(pagination.current)"
      ></a-table>
    </a-tab-pane>
    <a-tab-pane key="5" tab="合同" v-if="formDisabled && !isFromEnter">
      <p v-if="!pdfSrc">学员尚未签订合同,无法查看!</p>
      <iframe v-if="pdfSrc" style="width: 100%;height: 700px;border: 0;" :src="pdfSrc"></iframe>
    </a-tab-pane>
    <a-tab-pane key="6" tab="学时报审" v-if="formDisabled && !isFromEnter">
      <a-table
        :scroll="{ x: true }"
        :columns="ClassHourApprovalTableList.columns"
        :dataSource="ClassHourApprovalTableList.dataSource"
        :pagination="{ total: ClassHourApprovalTableList.total, current: ClassHourApprovalTableList.pageIndex }"
        @change="pagination => getTimeTableList(pagination.current)"
      ></a-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import fetchJsonp from 'fetch-jsonp'
import { httpAction, getAction, uploadAction, postAction } from '@/api/manage'
import { fileDownload } from '@/api/gzpt/studentinfo'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import { getCocachList } from '@/api/gzpt/studentinfo'
import { formMixins } from '@/mixins/formMixins'
import $ from 'jquery'
let moment = require('moment')
let Base64 = require('js-base64').Base64
export default {
  name: 'StudentinfoForm',
  components: {
    JTreeSelectDepart
  },
  mixins: [formMixins],
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      title: '',
      cocachList: [],
      cameraVisible: false,
      limitMoney: {
        basicDownlimit: 0,
        basicUplimit: 1000,
        secondDownlimit: 0,
        secondUplimit: 1000,
        thirdDownlimit: 0,
        thirdUplimit: 1000
      },
      accuracy: '.',
      os: false, //控制摄像头开关
      thisVideo: null,
      thisContext: null,
      thisCancas: null,
      imgSrc: '',
      videoWidth: 300,
      videoHeight: 200,
      model: {
        basicCost: 0,
        secondCost: 0,
        thirdCoast: 0,
        name: null,
        cardtype: 1,
        sex: null,
        address: null,
        idcard: null,
        photopath: null,
        applydate: null,
        isold: '0',
        iscrosscity: '2',
        unitPrice: 0,
        nationality: '中国',
        busitype: 0,
        coachnum: '',
        frozenAmount: undefined
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      validatorRules: {
        inscode: [
          {
            required: true,
            message: '请输入培训机构编号!'
          }
        ],
        cardtype: [
          {
            required: true,
            message: '请输入证件类型'
          }
        ],
        idcard: [
          {
            required: true,
            message: '请输入身份证号!'
          }
        ],
        nationality: [
          {
            required: true,
            message: '请输入国籍!'
          }
        ],
        sex: [
          {
            required: true,
            message: '请输入性别!'
          }
        ],
        photopath: [
          {
            required: true,
            message: '请上传头像!'
          }
        ],
        busitype: [
          {
            required: true,
            message: '请输入业务类型'
          }
        ],
        traintype: [
          {
            required: true,
            message: '请输入培训车型'
          },
          {
            validator: (rule, value, callback) => {
              if (!this.model.inscode) {
                this.model.traintype = ''
                callback(new Error('请先选择培训机构'))
              } else {
                callback()
                // this.judgeLimit(callback)
                //callback()
              }
            },
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入姓名!'
          }
        ],
        phone: [
          {
            required: true,
            message: '请输入手机号码!'
          },
          {
            pattern: /^1[0-9]{10}$/,
            message: '手机号码格式不对!'
          }
        ],
        address: [
          {
            required: true,
            message: '请输入联系地址!'
          }
        ],
        basicCost: [
          {
            required: true,
            message: '请输入基础费用!'
          }
        ],
        secondCost: [
          {
            required: true,
            message: '请输入第二部分费用!'
          }
        ],
        thirdCoast: [
          {
            required: true,
            message: '请输入第三部分费用!'
          }
        ],
        frozenAmount: [
          {
            required: true,
            message: '请输入冻结金额!'
          }
        ],
        coachnum: [
          {
            required: true,
            message: '请输入实操教练!'
          }
        ],
        fromarea: [
          {
            required: true,
            message: '请输入转入前所在地市!'
          }
        ],
        crossType: [
          {
            required: true,
            message: '转校后学习科目不能为空!'
          }
        ],
        unitPrice: [
          {
            required: true,
            message: '学时单价不能为空!'
          }
        ],
        archiveCost: [
          {
            required: true,
            message: '服务费不能为空!'
          }
        ]
      },
      trainTableList: {
        pageIndex: 1,
        total: 0,
        dataSource: [],
        columns: [
          {
            title: '设备编号',
            align: 'center',
            dataIndex: 'simunum'
          },
          {
            title: '学时编号',
            align: 'center',
            dataIndex: 'recnum'
          },
          {
            title: '科目',
            align: 'center',
            dataIndex: 'km_dictText'
          },
          {
            title: '开始时间',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '结束时间',
            align: 'center',
            dataIndex: 'endtime'
          },
          {
            title: '有效学时',
            align: 'center',
            dataIndex: 'duration_y'
          },
          {
            title: '有效里程',
            align: 'center',
            dataIndex: 'mileage_y'
          },
          {
            title: '学时',
            align: 'center',
            dataIndex: 'duration'
          },
          {
            title: '里程',
            align: 'center',
            dataIndex: 'mileage'
          },
          {
            title: '培训平均速度km/h',
            align: 'center',
            dataIndex: 'avevelocity'
          },
          {
            title: '教练员',
            align: 'center',
            dataIndex: 'coachname'
          },
          {
            title: '车牌号',
            align: 'center',
            dataIndex: 'licnum'
          }
        ]
      },
      orderTableList: {
        pageIndex: 1,
        total: 0,
        dataSource: [],
        columns: [
          {
            title: '学员姓名',
            align: 'center',
            dataIndex: 'stuname'
          },
          {
            title: '驾校名称',
            align: 'center',
            dataIndex: 'insname'
          },
          {
            title: '银行名称',
            align: 'center',
            dataIndex: 'bankname'
          },
          {
            title: '划转金额',
            align: 'center',
            dataIndex: 'transferAmount'
          },
          {
            title: '冻结金额余额',
            align: 'center',
            dataIndex: 'balanceAmount'
          },
          {
            title: '订单类型',
            align: 'center',
            dataIndex: 'ordertype'
          },
          {
            title: '订单状态',
            align: 'center',
            dataIndex: 'status'
          },
          {
            title: '说明',
            align: 'center',
            dataIndex: 'remark'
          },
          {
            title: '银行操作时间',
            align: 'center',
            dataIndex: 'bankdate'
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime'
          }
        ]
      },
      timeTableList: {
        dataSource: [],
        columns: [
          {
            title: '科目',
            align: 'center',
            dataIndex: 'subject',
            // dataIndex: 'subject_dictText',
            customRender: function(t, r, index) {
              if (t == 1) {
                return '科目一'
              } else if (t == 2) {
                return '科目二'
              } else if (t == 3) {
                return '科目三'
              } else if (t == 4) {
                return '科目四'
              }
            }
          },
          {
            title: '实操学时',
            align: 'center',
            dataIndex: 'vehicletime'
          },
          {
            title: '实操额定学时',
            align: 'center',
            dataIndex: 'vehicleCreditration'
          },
          {
            title: '课堂',
            align: 'center',
            dataIndex: 'classtime'
          },
          {
            title: '课堂额定学时',
            align: 'center',
            dataIndex: 'classCreditration'
          },
          {
            title: '模拟',
            align: 'center',
            dataIndex: 'simulatortime'
          },
          {
            title: '模拟额定学时',
            align: 'center',
            dataIndex: 'simulatorCreditration'
          },
          {
            title: '远程',
            align: 'center',
            dataIndex: 'networktime'
          },
          {
            title: '远程额定学时',
            align: 'center',
            dataIndex: 'networkCreditration'
          },
          {
            title: '是否报审',
            align: 'center',
            dataIndex: 'isBaoshen',
            // dataIndex: 'isBaoshen_dictText',
            customRender: function(t, r, index) {
              if (t == 0) {
                return '未报审'
              } else if (t == 1) {
                return '已报审'
              }
            }
          },
          {
            title: '创建时间',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '里程',
            align: 'center',
            dataIndex: 'mileage'
          },
          {
            title: '额定里程',
            align: 'center',
            dataIndex: 'ratedmileage'
          },
          {
            title: '车型',
            align: 'center',
            dataIndex: 'traintype'
          }
        ]
      },
      ClassHourApprovalTableList: {
        dataSource: [],

        // 表头
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function(t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: '姓名',
            width: 120,
            align: 'center',
            dataIndex: 'stuname'
          },
          {
            title: '学员编号',
            width: 140,
            align: 'center',
            dataIndex: 'stunum'
          },
          {
            title: '身份证',
            width: 140,
            align: 'center',
            dataIndex: 'idcard'
          },
          {
            title: '性别',
            width: 60,
            align: 'center',
            dataIndex: 'sex_dictText'
          },
          {
            title: '科目',
            width: 100,
            align: 'center',
            dataIndex: 'subject_dictText'
          },
          {
            title: '培训机构名称',
            width: 100,
            align: 'center',
            dataIndex: 'insname'
          },
          {
            title: '教练员',
            width: 100,
            align: 'center',
            dataIndex: 'coachname'
          },
          {
            title: '车型',
            width: 100,
            align: 'center',
            dataIndex: 'traintype'
          },
          {
            title: '是否校验', //0=未校验,1=已校验
            width: 100,
            align: 'center',
            dataIndex: 'ispushsj_dictText'
          },

          {
            title: '监管校验',
            width: 100,
            align: 'center',
            dataIndex: 'message',
            scopedSlots: {
              customRender: 'message'
            }
          },
          {
            title: '推送计时', // 0=未推送,1=推送成功,2=推送失败
            width: 100,
            align: 'center',
            dataIndex: 'ispushjs_dictText'
          },
          {
            title: '总学时',
            width: 100,
            align: 'center',
            dataIndex: 'totaltime'
          },
          {
            title: '审核状态', // 0=未审核,1=审核通过,2=推送失败
            width: 100,
            align: 'center',
            dataIndex: 'auditstate_dictText'
          },
          {
            title: '审核人',
            width: 100,
            align: 'center',
            dataIndex: 'operatorname'
          },
          {
            title: '审核日期',
            width: 100,
            align: 'center',
            dataIndex: 'auditdate',
            customRender: function(text) {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            }
          },
          {
            title: '申请日期',
            width: 100,
            align: 'center',
            dataIndex: 'crdate',
            customRender: function(text) {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            }
          },
          {
            title: '推送计时日期',
            width: 100,
            align: 'center',
            dataIndex: 'pushjsdate',
            customRender: function(text) {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            }
          }
          // {
          //   title: '操作',
          //   dataIndex: 'action',
          //   align: "center",
          //   fixed: "right",
          //   width: 147,
          //   scopedSlots: {
          //     customRender: 'action'
          //   }
          // }
        ]
      },
      url: {
        add: '/gzpt/studentinfo/add',
        edit: '/gzpt/studentinfo/edit',
        editEnter: '/gzpt/studentinfoEnter/edit',
        queryById: '/gzpt/studentinfo/queryById'
      },
      wrStatus: {},
      pdfSrc: '',
      thirdCoast: '',
      secondCost: '',
      minprice: ''
    }
  },
  watch: {
    // model: {
    //   handler(val) {
    //     let sum = 0
    //     for (let key in this.model) {
    //       // if (['unitPrice'].includes(key) && !isNaN(this.model[key] * 1)) {
    //       // 	this.$set(this.model, 'secondCost', this.model.unitPrice*this.model.secendTime)
    //       // 	this.$set(this.model, 'thirdCoast', this.model.unitPrice*this.model.thirdTime)
    //       // }
    //       if (['basicCost', 'secondCost', 'thirdCoast'].includes(key) && !isNaN(this.model[key] * 1)) {
    //         sum += this.model[key] * 1
    //       }
    //     }
    //     this.$set(this.model, 'frozenAmount', parseFloat(sum.toFixed(1)))
    //   },
    //   deep: true
    // }
  },
  computed: {
    isFromEnter() {
      return window.location.href.includes('Enter')
    },
    formDisabled() {
      return this.disabled
    }
    // getFrozenAmount() {
    //   return this.model.secondCost * 1 + this.model.thirdCoast * 1
    // }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        const limit = 1024 * 1024 * 2
        if (file.size > limit) {
          this.$message.error('上传图片大小不能超过 2M ！')
          return reject(false)
        }
        return resolve(true)
      })
    },
    getTreeData(treeData) {
      if (treeData.length == 1 && !(treeData[0].children instanceof Array)) {
        this.$set(this.model, 'inscode', treeData[0].id)
        this.getCocachList(this.model.inscode)
      }
    },
    crosscityChange(e) {
      console.log(e.target.value, '城市切换')
      if (e.target.value == 1) {
        //1--是
        this.thirdCoast = this.model.thirdCoast
        this.secondCost = this.model.secondCost
        if (this.isFromEnter) {
          this.model.crossType = 1
        }
      } else {
        //2--否
        // this.model.secondCost = parseFloat((this.model.unitPrice * this.secendTime).toFixed(1))
        // this.model.thirdCoast = parseFloat((this.model.unitPrice * this.thirdTime).toFixed(1))
      }
    },
    transferSubject(e) {
      console.log(this.secondCost, this.thirdCoast, e, '转校后学习科目')
      if (e == 2) {
        // this.model.thirdCoast = 0
        // this.model.secondCost = parseFloat((this.model.unitPrice * this.secendTime).toFixed(1))
      } else if (e == 3) {
        // this.model.secondCost = 0
        // this.model.thirdCoast = parseFloat((this.model.unitPrice * this.thirdTime).toFixed(1))
      } else if (e == 1) {
        // this.model.secondCost = parseFloat((this.model.unitPrice * this.secendTime).toFixed(1))
        // this.model.thirdCoast = parseFloat((this.model.unitPrice * this.thirdTime).toFixed(1))
      }
    },
    // unitPriceChange(e) {
    //   this.$set(this.model, 'crossType', '')
    //   console.log(this.model.unitPrice, '学时价格')
    //   this.model.secondCost = parseFloat((this.model.unitPrice * this.secendTime).toFixed(1))
    //   this.model.thirdCoast = parseFloat((this.model.unitPrice * this.thirdTime).toFixed(1))
    //   this.thirdCoast = this.model.thirdCoast
    //   this.secondCost = this.model.secondCost
    // },
    getData111() {
      if (window.location.href.includes('Enter')) {
        getAction('/gzpt/studentinfoEnter/queryById', {
          id: this.model.id
        }).then(res => {
          if (res.success) {
            this.model = res.result
          }
        })
      } else {
        getAction('/gzpt/studentinfo/queryById', {
          id: this.model.id
        }).then(res => {
          if (res.success) {
            this.model = res.result
          }
        })
      }
    },
    //选择车型 给三个费用默认值
    selectTrainType() {
      // if (this.model.traintype == 'C1') {
      //   this.minprice = 72
      //   this.model.unitPrice = this.model.unitPrice < 72 ? 72 : this.model.unitPrice
      // }
      // if (this.model.traintype == 'C2') {
      //   this.minprice = 81.6
      //   this.model.unitPrice = this.model.unitPrice < 81.6 ? 81.6 : this.model.unitPrice
      // }
      this.$set(this.model, 'crossType', '')
      if (this.model.inscode) {
        getAction('/gzpt/cost/list', {
          inscode: this.model.inscode,
          traintype: this.model.traintype
        }).then(res => {
          let frozenAmount = res.result.records.length > 0 ? Number(res.result.records[0].frozenAmount) : 0
          this.$set(this.model, 'frozenAmount', frozenAmount)
          // this.model.unitPrice = res.result.records.length > 0 ? Number(res.result.records[0].oprCostPer) : 0
          // this.model.archiveCost = res.result.records.length > 0 ? Number(res.result.records[0].archiveCost) : 0
          // this.getTotalTime()
        })
        // this.getAmount()
      }
    },
    //获取学时
    getTotalTime(type) {
      getAction('/gzpt/trainSubjectCredit/list', {
        classtype: 1,
        subject: 2,
        type: 6,
        trainCarType: this.model.traintype
      }).then(res => {
        this.secendTime = res.result.records[0].creditration / 60
        if (type !== 1) {
          // this.model.secondCost = parseFloat((this.secendTime * this.model.unitPrice).toFixed(1))
        }
      })
      getAction('/gzpt/trainSubjectCredit/list', {
        classtype: 1,
        subject: 3,
        type: 6,
        trainCarType: this.model.traintype
      }).then(res => {
        this.thirdTime = res.result.records[0].creditration / 60
        if (type !== 1) {
          // this.model.thirdCoast = parseFloat((this.thirdTime * this.model.unitPrice).toFixed(1))
        }
      })
    },
    //获取合同
    getContract(stunum) {
      getAction(`/gzpt/studentinfo/viewContract/${stunum}`).then(res => {
        if (res.success) {
          this.pdfSrc = res.result
        } else {
        }
      })
    },
    getTrainTableList(page) {
      getAction('/gzpt/classRecordDetail/list', {
        stunum: this.model.stunum,
        pageNo: page
      }).then(res => {
        if (res.success && res.result) {
          this.trainTableList.dataSource = res.result.records || []
          this.trainTableList.pageIndex = res.result.current
          this.trainTableList.total = res.result.total
        }
      })
    },
    getOrderTableList(page) {
      console.log(this.model.stunum)
      getAction('/gzpt/bankOrder/list', {
        stunum: this.model.stunum,
        pageNo: page
      }).then(res => {
        if (res.success) {
          this.orderTableList.dataSource = res.result.records || []
          this.orderTableList.pageIndex = res.result.current
          this.orderTableList.total = res.result.total
        }
      })
    },
    getTimeTableList(page) {
      if (this.model.stunum) {
        getAction('/gzpt/studentinfo/totalTimeList', {
          stunum: this.model.stunum
          // pageNo: page
        }).then(res => {
          if (res.success) {
            this.timeTableList.dataSource = res.result || []
            this.timeTableList.pageIndex = res.result.current
            this.timeTableList.total = res.result.total
          }
        })
      } else {
        this.timeTableList.dataSource = []
        this.timeTableList.pageIndex = 1
        this.timeTableList.total = 0
      }
    },
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.model.applydate = record.applydate ? record.applydate : moment().format('YYYYMMDD')
      this.model.isold = record.hasOwnProperty('isold') ? record.isold : '0'
      this.model.iscrosscity = record.hasOwnProperty('iscrosscity') ? record.iscrosscity : '2'

      if (this.title == '详情') {
        this.getContract(this.model.stunum)
      }
      this.secondCost = this.model.secondCost
      this.thirdCoast = this.model.thirdCoast
      this.visible = true
      if (this.model.stunum) {
        this.getTrainTableList(1)
        this.getOrderTableList(1)
        this.getTimeTableList(1)
      }
      //获取学时报审数据
      this.getClassHourApproval()
      this.getData111()
      this.getTotalTime(1)
      if (this.model.inscode) {
        this.getCocachList(this.model.inscode, 1)
      }
      if (!window.location.href.includes('Enter') && this.title === '新增') {
        getAction('/gzpt/studentinfo/getApplyDate').then(res => {
          this.model.applydate = res.result
        })
      }
    },
    // getAmount() {
    //   getAction('/gzpt/cost/list', {
    //     inscode: this.model.inscode,
    //     traintype: this.model.traintype
    //   }).then(res => {
    //     if (res.success && res.result.records.length > 0) {
    //       let data = res.result.records[0]
    //       this.model.basicCost = Number(data.basicCost)
    //       this.model.secondCost = Number(data.secondCost)
    //       this.model.thirdCoast = Number(data.thirdCoast)
    //       this.model.frozenAmount = Number(data.frozenAmount)
    //     }
    //   })
    // },
    // judgeLimit(callback) {
    //   getAction('/gzpt/costLimit/list', {
    //     inscode: this.model.inscode,
    //     traintype: this.model.traintype
    //   }).then(res => {
    //     if (res.success) {
    //       if (res.result.records == 0) {
    //         // this.$message.error(`该驾校的${this.model.traintype}车型尚未添加资金上下限,无法报名!`)
    //         this.model.traintype = ''
    //         callback()
    //       } else {
    //         // this.getAmount()
    //         this.limitMoney = res.result.records[0]
    //         callback()
    //       }
    //     } else {
    //       this.$message.error(res.message)
    //     }
    //   })
    // },
    getClassHourApproval(pageNo = 1) {
      getAction('/gzpt/stageTrainningTime/list', {
        stunum: this.model.stunum,
        pageNo: 1
      })
        .then(res => {
          if (res.success) {
            // this.ClassHourApprovalTableList.dataSource = res.result.records || []
            this.ClassHourApprovalTableList.dataSource = [
              {
                id: '1849277981963501570',
                inscode: '0584624956351101',
                insname: '江山市通达机动车驾驶员培训有限公司',
                stunum: '6646894733251696',
                idcard: '362322********0719',
                stuname: '维尔测试学员',
                subject: 1,
                totaltime: 360,
                vehicletime: 0,
                classtime: 360,
                simulatortime: 0,
                networktime: 0,
                mileage: 0,
                applydate: '********',
                auditstate: '0',
                operatorname: null,
                auditdate: null,
                district: '330881',
                traintype: 'C1',
                ispushsj: 1,
                ispushjs: 1,
                isface: 1,
                crdate: '2024-10-24',
                pushjsdate: '2024-10-31',
                pushsjdate: '2024-10-24',
                duration: 360,
                isbank: 0,
                resultmeg: null,
                recnums: null,
                message: '查询成功',
                coachnum: '****************',
                coachname: '维尔测试教练员',
                sex: '1',
                ids: null,
                inscodes: null,
                pushjsdate_begin: null,
                pushjsdate_end: null,
                auditdate_begin: null,
                auditdate_end: null,
                pushsjdate_begin: null,
                pushsjdate_end: null,
                createBy: 'zhujp',
                updateTime: '2024-10-29 10:21:12',
                createTime: '2024-10-24 10:33:23',
                updateBy: null,
                subject_dictText: '科目一',
                auditstate_dictText: '未审核',
                ispushsj_dictText: '是',
                ispushjs_dictText: '已推送',
                isbank_dictText: '未推送',
                sex_dictText: '男'
              },
              {
                id: '1849274157710360578',
                inscode: '****************',
                insname: '开化县益兴驾驶员培训有限公司',
                stunum: '****************',
                idcard: '362322********0719',
                stuname: '安运测试学员',
                subject: 1,
                totaltime: 360,
                vehicletime: 0,
                classtime: 360,
                simulatortime: 0,
                networktime: 0,
                mileage: 0,
                applydate: '********',
                auditstate: '0',
                operatorname: null,
                auditdate: null,
                district: '330824',
                traintype: 'C1',
                ispushsj: 1,
                ispushjs: 1,
                isface: 1,
                crdate: '2024-10-24',
                pushjsdate: '2024-10-31',
                pushsjdate: '2024-10-24',
                duration: 360,
                isbank: 0,
                resultmeg: null,
                recnums: null,
                message: '查询成功',
                coachnum: '****************',
                coachname: '安运测试教练员',
                sex: '1',
                ids: null,
                inscodes: null,
                pushjsdate_begin: null,
                pushjsdate_end: null,
                auditdate_begin: null,
                auditdate_end: null,
                pushsjdate_begin: null,
                pushsjdate_end: null,
                createBy: 'zhujp',
                updateTime: '2024-10-24 10:18:12',
                createTime: '2024-10-24 10:18:12',
                updateBy: null,
                subject_dictText: '科目一',
                auditstate_dictText: '未审核',
                ispushsj_dictText: '是',
                ispushjs_dictText: '已推送',
                isbank_dictText: '未推送',
                sex_dictText: '男'
              }
            ]
            this.ClassHourApprovalTableList.pageIndex = res.result.current
            this.ClassHourApprovalTableList.total = res.result.total
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getCocachList(code, tag) {
      getCocachList({
        inscode: this.model.inscode,
        pageSize: 999
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
          if (!tag) {
            this.model.coachnum = this.cocachList[0].coachnum
          }
        } else {
          this.$message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    handleSearch(e, option) {
      this.$set(this.model, 'coachnum', e)
    },
    submitForm() {
      const that = this
      // 触发表单验证
      // if (!this.isFromEnter && !this.model.id && !this.model.archiveCost) {
      //   that.$message.warning('服务费不能为空！')
      //   return
      // }
      // if (!this.isFromEnter && !this.model.id && this.model.archiveCost > 1000) {
      //   that.$message.warning('按运管要求，服务费须在1000元以内！')
      //   return
      // }
      if (!this.model.frozenAmount) {
        that.$message.warning('冻结金额为0,请添加合同收费内容')
        return
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            if (window.location.href.includes('Enter')) {
              httpurl += this.url.editEnter
            } else {
              httpurl += this.url.edit
            }
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    openCamer() {
      this.cameraVisible = true
    },
    openCamera() {
      const _this = this
      this.os = false
      this.thisVideo = document.getElementById('videoCamera')
      // 旧版本浏览器可能根本不支持mediaDevices，我们首先设置一个空对象
      if (navigator.mediaDevices === undefined) {
        navigator.mediaDevices = {}
      }
      // 一些浏览器实现了部分mediaDevices，我们不能只分配一个对象
      // 使用getUserMedia，因为它会覆盖现有的属性。
      // 这里，如果缺少getUserMedia属性，就添加它。
      if (navigator.mediaDevices.getUserMedia === undefined) {
        navigator.mediaDevices.getUserMedia = function(constraints) {
          // 首先获取现存的getUserMedia(如果存在)
          let getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.getUserMedia
          // 有些浏览器不支持，会返回错误信息
          // 保持接口一致
          if (!getUserMedia) {
            return Promise.reject(new Error('getUserMedia is not implemented in this browser'))
          }
          // 否则，使用Promise将调用包装到旧的navigator.getUserMedia
          return new Promise(function(resolve, reject) {
            getUserMedia.call(navigator, constraints, resolve, reject)
          })
        }
      }
      const constraints = {
        audio: false,
        video: {
          width: _this.videoWidth,
          height: _this.videoHeight,
          transform: 'scaleX(-1)'
        }
      }
      navigator.mediaDevices
        .getUserMedia(constraints)
        .then(function(stream) {
          // 旧的浏览器可能没有srcObject
          if ('srcObject' in _this.thisVideo) {
            _this.thisVideo.srcObject = stream
          } else {
            // 避免在新的浏览器中使用它，因为它正在被弃用。
            _this.thisVideo.src = window.URL.createObjectURL(stream)
          }
          _this.thisVideo.onloadedmetadata = function(e) {
            _this.thisVideo.play()
          }
        })
        .catch(err => {
          this.$message({
            message: '没有开启摄像头权限或浏览器版本不兼容',
            type: 'warning'
          })
        })
    },
    setImage() {
      var thisCancas = document.getElementById('canvasCamera')

      var thisContext = thisCancas.getContext('2d')

      thisContext.drawImage(this.thisVideo, 0, 0, this.videoWidth, this.videoHeight)

      //展示在form表单中

      // 获取图片base64链接
      var image = thisCancas.toDataURL('image/png')
      this.imgSrc = this.dataURLtoFile(image, 'file')
      this.uploadImage(this.imgSrc)
    },
    stopCamera() {
      if (this.thisVideo && this.thisVideo !== null) {
        this.thisVideo.srcObject.getTracks()[0].stop()
        this.os = true //切换成打开摄像头
      }
    },
    submit() {
      console.log(this.dataURLtoFile(this.imgSrc, 'file'), 133367788)
    },
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
      const suffix = mime.split('/')[1]
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      let files = new File([u8arr], `${filename}.${suffix}`, {
        type: mime
      })
      return files
    },
    handleOk() {
      this.stopCamera()
      this.cameraVisible = false
    },
    //上传图片
    uploadImage(str) {
      let formData = new FormData()
      formData.append('file', str)
      uploadAction('/oss/upload', formData).then(res => {
        if (res.success) {
          this.model.photopath = res.message
        }
      })
    },

    getData() {
      // postAction('http://127.0.0.1:18889/api/readCert', ).then((res) => {
      //   if (res.success) {
      //     this.model.cardtype=res.resultContent.certTYpe;
      //     this.model.name=res.resultContent.partyName;
      //     this.model.sex=res.resultContent.gender;
      //     this.model.address=res.resultContent.certAddress;
      //     this.model.idcard=res.resultContent.certNumber
      //     this.model.photopath=Base64(res.resultContent.identityPic)
      //
      //   }
      // })
      // $.ajax({
      //   type: "POST",
      //   url: "http://127.0.0.1:18889/api/readCert",
      //   dataType: "jsonp",
      //   // jsonp: "callback",
      //   // jsonpCallback: "f",
      //   success: function (res) {
      //     console.log(res);
      //   },
      // });

      let that = this
      fetch('http://127.0.0.1:18889/api/readCert', {
        method: 'post'
      })
        .then(function(data) {
          return data.text()
        })
        .then(function(data) {
          let result = JSON.parse(data)
          console.log(result)
          if (that.title === '新增') {
            that.model.cardtype = result.resultContent.certType
            that.model.name = result.resultContent.partyName
            that.model.sex = result.resultContent.gender == 0 ? '2' : result.resultContent.gender
            that.model.address = result.resultContent.certAddress
            that.model.idcard = result.resultContent.certNumber
            // that.model.photopath="data:image/jpeg;base64,"+result.resultContent.identityPic
            let base64 = 'data:image/png;base64,' + result.resultContent.identityPic
            // let base64=result.resultContent.identityPic
            that.postImg(base64)
          } else {
            if (that.model.idcard !== result.resultContent.certNumber) {
              that.$message.error('身份证号不一致，请更换身份证！')
            } else {
              that.model.address = result.resultContent.certAddress
              let base64 = 'data:image/png;base64,' + result.resultContent.identityPic
              // let base64=result.resultContent.identityPic
              that.postImg(base64)
            }
          }

          console.log(that.model)
        })
    },
    base64ToFile(dataurl, filename) {
      // // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
      // let data = base64.split(',');
      // // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
      // let type = data[0].match(/:(.*?);/)[1];
      // // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
      // let suffix = type.split('/')[1];
      // // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
      // const bstr = window.atob(data[1]);
      // // 获取解码结果字符串的长度
      // let n = bstr.length
      // // 根据解码结果字符串的长度创建一个等长的整形数字数组
      // // 但在创建时 所有元素初始值都为 0
      // const u8arr = new Uint8Array(n)
      // // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
      // while (n--) {
      //   // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
      //   u8arr[n] = bstr.charCodeAt(n)
      // }
      // // 利用构造函数创建File文件对象
      // // new File(bits, name, options)
      // const file =  new File([u8arr], `${fileName}.${suffix}`, {
      //   type: type
      // })
      // // 将File文件对象返回给方法的调用者
      // return file;
      // console.log(file)
      var arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], filename, {
        type: mime
      })
    },
    //上传身份证照片
    postImg(base64) {
      let randnum = Math.random() * 10000000000000
      randnum = Math.floor(randnum)
      let fileName = randnum + '.png'
      let file = this.base64ToFile(base64, fileName)
      let fd = new FormData()
      fd.append('file', file)
      fileDownload(fd).then(res => {
        console.log(res)
        if (res.success) {
          this.model.photopath = res.message
        }
      })
    },
    getData2() {
      let that = this
      fetchJsonp('http://127.0.0.1:8099/?cmd=readcardnomal&charset=utf8', {
        method: 'get',
        headers: {
          'Content-Type': 'text/html'
        },
        // jsonpCallback: "fnCallback",
        mode: 'no-cors' // 以CORS的形式跨域
        // cache: "force-cache"
      }).then(response => {
        response
          .json()
          .then(data => {
            let result = data
            if (that.title === '新增') {
              that.model.cardtype = result.resultContent.certType
              that.model.name = result.resultContent.partyName
              that.model.sex = result.resultContent.gender == 0 ? '2' : result.resultContent.gender
              that.model.address = result.resultContent.certAddress
              that.model.idcard = result.resultContent.certNumber
              // that.model.photopath="data:image/jpeg;base64,"+result.resultContent.identityPic
              let base64 = 'data:image/png;base64,' + result.resultContent.identityPic
              that.postImg(base64)
            } else {
              if (that.model.idcard !== result.resultContent.certNumber) {
                that.$message.error('身份证号不一致，请更换身份证！')
              } else {
                that.model.address = result.resultContent.certAddress
                let base64 = 'data:image/png;base64,' + result.resultContent.identityPic
                that.postImg(base64)
              }
            }
          })
          .catch(err => {
            console.log(err)
          })
      })
    },

    //维尔
    // 第一步：开启端口
    startPort() {
      let that = this
      fetch('http://127.0.0.1:12321/WlSdt_api/OpenPort?Port=1001', {
        method: 'get'
      })
        .then(function(data) {
          return data.text()
        })
        .then(function(data) {
          that.wrStatus = JSON.parse(data)
          // console.log(that.wrStatus)
          // return result
        })
    },
    // 第二步：读取身份信息
    getData3() {
      let that = this
      that.startPort()
      // if(that.wrStatus.result===false){
      //   return  that.$message.warning("插件未安装或服务未启动");
      // }
      // if(that.wrStatus.return!==0){
      //   return  that.$message.warning("端口未开启");
      // }
      fetch('http://127.0.0.1:12321/WlSdt_api/ReadBaseMsg?Port=1001&ReadType=3', {
        method: 'get'
      })
        .then(function(data) {
          return data.text()
        })
        .then(function(data) {
          let result = JSON.parse(data)
          if (that.title === '新增') {
            that.model.cardtype = '1'
            that.model.name = result.data.name
            that.model.sex = result.data.sex == '男' ? '1' : '2'
            that.model.address = result.data.address
            that.model.idcard = result.data.idNo
            // // that.model.photopath="data:image/jpeg;base64,"+result.resultContent.identityPic
            // let base64="data:image/png;base64,"+result.resultContent.identityPic
            let base64 = result.photo
            that.postImg(base64)
            // console.log(that.model)
          } else {
            if (that.model.idcard !== result.data.idNo) {
              that.$message.error('身份证号不一致，请更换身份证！')
            } else {
              that.model.address = result.data.address
              let base64 = result.photo
              that.postImg(base64)
            }
          }
          that.closePort()
        })
    },
    // 第三步：关闭端口
    closePort() {
      fetch('http://127.0.0.1:12321/WlSdt_api/ClosePort?Port=1001', {
        method: 'get'
      })
        .then(function(data) {
          return data.text()
        })
        .then(function(data) {
          let result = JSON.parse(data)
          // return result
          // console.log(result)
        })
    },

    //金华  浙江中正身份证读卡器
    getData4() {
      this.readSAMID()
    },

    // 读取读卡器设备编号 并验证是否属于'被支持的设备'
    readSAMID() {
      var _this = this
      var ws = new WebSocket('ws://127.0.0.1:8009/idcard')
      ws.onopen = function(evt) {
        var command = 'CmdSAMID|' + 0 + '|' + ''
        ws.send(command)
      }
      ws.onmessage = function(evt) {
        ws.close()
        var jsontext = evt.data
        var resp = JSON.parse(jsontext)
        if (resp.result == '0') {
          // listReadcarddevice({ deviceid: resp.data }).then((res) => {
          //   if (res.rows.length >= "1") {
          //     _this.readIdCard();
          //   } else {
          //     return _this.$message.error(
          //       "无法读取该设备编号或该设备型号不支持,请录入设备编号或更换读卡器!"
          //     );
          //   }
          // });
          _this.readIdCard()
        } else {
          return _this.$message.error('读取设备编号失败,请检查设备是否异常!')
        }
      }

      ws.onclose = function(evt) {}
    },
    // 调用身份证阅读器驱动程序 获取身份证信息
    readIdCard() {
      var _this = this
      var ws = new WebSocket('ws://127.0.0.1:8009/idcard')
      ws.onopen = function(evt) {
        var command = 'CmdReadCard|' + 0 + '|' + ''
        ws.send(command)
      }

      ws.onmessage = function(evt) {
        ws.close()
        var jsontext = evt.data
        _this.resp = JSON.parse(jsontext)

        _this.readIDCardParam()
      }

      ws.onclose = function(evt) {}
    },
    // 身份证参数传递给form表单
    readIDCardParam() {
      if (this.resp.result == 0) {
        // this.form.address = this.resp.data.Address.trim();
        // this.form.name = this.resp.data.Name.trim();
        // this.form.sex = this.resp.data.Sex;
        // this.form.idcard = this.resp.data.ID;
        // this.form.checkIdcard = this.resp.data.ID;
        // this.form.cardtype =
        //   this.resp.data.CardType == " " ? "1" : this.resp.data.CardType;
        // this.form.nationality = "中国";
        if (this.title === '新增') {
          this.model.cardtype = this.resp.data.CardType == ' ' ? '1' : this.resp.data.CardType
          this.model.name = this.resp.data.Name.trim()
          this.model.sex = this.resp.data.Sex
          this.model.address = this.resp.data.Address.trim()
          this.model.idcard = this.resp.data.ID
          // this.handleMakeIDcard();
          this.mxGetPhotoData()
        } else {
          if (this.model.idcard !== this.resp.data.ID) {
            this.$message.error('身份证号不一致，请更换身份证！')
          } else {
            this.model.address = this.resp.data.Address.trim()
            this.mxGetPhotoData()
          }
        }
      } else {
        this.$message.error('读卡失败,清检查设备或卡片是否可用!')
      }
    },
    // 调用身份证阅读器驱动程序 获取身份证人脸信息
    mxGetPhotoData() {
      var _this = this
      var ws = new WebSocket('ws://127.0.0.1:8009/idcard')
      ws.onopen = function(evt) {
        var command = 'CmdGetPhoto|' + 0 + '|' + ''
        ws.send(command)
      }

      ws.onmessage = function(evt) {
        ws.close()
        var jsontext = evt.data
        var resp = JSON.parse(jsontext)
        var img = 'data:image/png;base64,' + resp.data
        // _this.imgStr = "data:image/png;base64," + resp.data;
        _this.postImg(img)
      }
      ws.onclose = function(evt) {}
    }
    // 读取身份证芯片序列号
    // handleMakeIDcard() {
    //   var _this = this
    //   var ws = new WebSocket('ws://127.0.0.1:8009/idcard')
    //   ws.onopen = function(evt) {
    //     var command = 'CmdReadIDMsg|' + 0 + '|' + ''
    //     ws.send(command)
    //   }
    //   ws.onmessage = function(evt) {
    //     ws.close()
    //     var jsontext = evt.data
    //     var resp = JSON.parse(jsontext)
    //     _this.form.cardid = resp.data.replace(/\s*/g, '')
    //   }
    // },
    // // 在线学员报名表单 读卡器按钮
    // handleFromEnterReadIDCard() {}
  }
}
</script>
<style lang="less" scoped="scoped">
.ant-input-disabled {
  // background-color: transparent;
  // color: rgba(0, 0, 0, 0.8);
}
</style>
