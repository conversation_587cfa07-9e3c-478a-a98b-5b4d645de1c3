<template>
	<a-spin :spinning="confirmLoading">
		<j-form-container :disabled="formDisabled">
			<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
				<a-row>
					<a-col :span="12">
						<a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
							<a-input v-model="model.stunum" placeholder="请输入学员编号"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="身份证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
							<a-input v-model="model.idcard" placeholder="请输入身份证"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subject">
							<j-dict-select-tag type="list" v-model="model.subject" dictCode="sys_km"
								placeholder="请选择科目" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="实操学时" :labelCol="labelCol" :wrapperCol="wrapperCol"
							prop="vehicletime">
							<a-input-number v-model="model.vehicletime" placeholder="请输入实操学时" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="课程" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classtime">
							<a-input-number v-model="model.classtime" placeholder="请输入课程" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="模拟" :labelCol="labelCol" :wrapperCol="wrapperCol"
							prop="simulatortime">
							<a-input-number v-model="model.simulatortime" placeholder="请输入模拟" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="远程" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="networktime">
							<a-input-number v-model="model.networktime" placeholder="请输入远程" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="是否报审" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isBaoshen">
<!--							<a-select placeholder="请选择是否报审" v-model="model.isBaoshen">
								<a-select-option :value="1">未报审</a-select-option>
								<a-select-option :value="2">已报审</a-select-option>
							</a-select>-->
              <j-dict-select-tag type="list" v-model="model.isBaoshen" dictCode="sys_baoshen"
                                 placeholder="请选择是否报审" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="里程" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mileage">
							<a-input-number v-model="model.mileage" placeholder="请输入里程" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
							<j-date placeholder="请选择创建时间" v-model="model.crdate" style="width: 100%" />
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
							<a-input v-model="model.traintype" placeholder="请输入车型"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
							<a-input v-model="model.insname" placeholder="请输入培训机构"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
							<a-input v-model="model.inscode" placeholder="请输入培训机构编号"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="市区" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
							<a-input v-model="model.district" placeholder="请输入市区"></a-input>
						</a-form-model-item>
					</a-col>
				</a-row>
			</a-form-model>
		</j-form-container>
	</a-spin>
</template>

<script>
	import {
		httpAction,
		getAction
	} from '@/api/manage'
	import {
		validateDuplicateValue
	} from '@/utils/util'
  import {
    formMixins
  } from "@/mixins/formMixins";

	export default {
		name: 'TotalTimeForm',
		components: {},
    mixins: [formMixins],
		props: {
			//表单禁用
			disabled: {
				type: Boolean,
				default: false,
				required: false
			}
		},
		data() {
			return {
				model: {},
				labelCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 7
					},
				},
				wrapperCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 16
					},
				},
				confirmLoading: false,
				validatorRules: {
					stunum: [{
						required: true,
						message: '请输入学员编号!'
					}, ],
					idcard: [{
						required: true,
						message: '请输入身份证!'
					}, ],
				},
				url: {
					add: "/gzpt/totalTime/add",
					edit: "/gzpt/totalTime/edit",
					queryById: "/gzpt/totalTime/queryById"
				}
			}
		},
		computed: {
			formDisabled() {
				return this.disabled
			},
		},
		created() {
			//备份model原始值
			this.modelDefault = JSON.parse(JSON.stringify(this.model));
		},
		methods: {
      getData2() {
        getAction('/gzpt/totalTime/queryById',{
          id: this.model.id
        }).then((res)=>{
          if(res.success){
            this.model = res.result
          } 
        })
      },
			add() {
				this.edit(this.modelDefault);
			},
			edit(record) {
				this.model = Object.assign({}, record);
				this.visible = true;
        this.getData2()
			},
			submitForm() {
				const that = this;
				// 触发表单验证
				this.$refs.form.validate(valid => {
					if (valid) {
						that.confirmLoading = true;
						let httpurl = '';
						let method = '';
						if (!this.model.id) {
							httpurl += this.url.add;
							method = 'post';
						} else {
							httpurl += this.url.edit;
							method = 'put';
						}
						httpAction(httpurl, this.model, method).then((res) => {
							if (res.success) {
								that.$message.success(res.message);
								that.$emit('ok');
							} else {
								that.$message.warning(res.message);
							}
						}).finally(() => {
							that.confirmLoading = false;
						})
					}

				})
			},
		}
	}
</script>
