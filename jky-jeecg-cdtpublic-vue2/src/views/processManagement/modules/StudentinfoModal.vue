<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <studentinfo-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></studentinfo-form>
    <template slot="footer">
      <a-button  @click="getInfoData" v-if="title=='新增'"  v-has="'stu:add:jl'">精伦读卡器</a-button>
      <a-button  @click="getInfoData2" v-if="title=='新增'"  v-has="'stu:add:ynws'" >因纳伟盛</a-button>
      <a-button  @click="getInfoData3" v-if="title=='新增'"   v-has="'stu:add:wer'" >维尔读卡器</a-button>
      <a-button  @click="getInfoData4" v-if="title=='新增'"   v-has="'stu:add:zjzz'">浙江中正</a-button>
      <a-button  @click="handleOk" type="primary" v-if="!disableSubmit">确定</a-button>
      <a-button  @click="handleCancel">关闭</a-button>
    </template>
  </j-modal>
</template>

<script>

  import StudentinfoForm from './StudentinfoForm'
  export default {
    name: 'StudentinfoModal',
    components: {
      StudentinfoForm
    },
    data () {
      return {
        title:'',
        width:1200,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
		 this.$refs.realForm.title='新增'
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
			this.$refs.realForm.title=this.title
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      },
      getInfoData(){
        this.$refs.realForm.getData();
      },
      getInfoData2(){
        this.$refs.realForm.getData2();
      },
      getInfoData3(){
        this.$refs.realForm.getData3();
      },
      getInfoData4(){
        this.$refs.realForm.getData4();
      },
    }
  }
</script>