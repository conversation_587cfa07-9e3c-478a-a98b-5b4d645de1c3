<template>
	<a-modal v-model="stuSignOpen" title="学员签名" @ok="handleGenerate" :width="800">
		<vue-esign class="esign" ref="esign" style="border: 1px solid black;" :isCrop="isCrop" :height="450"
			:lineWidth="lineWidth" :lineColor="lineColor" :bgColor.sync="bgColor"></vue-esign>
		<a-button type="primary" style="margin-top: 10px;margin-left: 90%;" @click="handleReset">清除</a-button>
	</a-modal>
</template>

<script>
	import {
		fileDownload,
		stusign
	} from "@/api/gzpt/studentinfo";
	export default {
		name: '',
		props: {
			stunum: {
				type: String,
				require: true
			},
		},
		data() {
			return {
				stuSignOpen: false,
				lineWidth: 6,
				lineColor: '#000000',
				bgColor: '',
				resultImg: '',
				isCrop: false,
				//图片地址
				imageUrl: '',
				signurl: null,
				show: false
			}
		},

		methods: {
			//接收父组件传递的id值
			init() {
				this.stuSignOpen = true
				let that = this
				this.$nextTick(() => {
					that.$refs.esign.reset();
					that.resultImg = ''
				})

			},
			//清空画板..
			handleReset() {
				console.log(this.$refs.esign)
				this.$refs.esign.reset();
				this.resultImg = ''
			},
			//生成签名图片..
			handleGenerate() {
				this.$refs.esign.generate().then(res => {
					let randnum = Math.random() * 10000000000000
					randnum = Math.floor(randnum)
					let fileName = "dianziqianming/" + randnum + '.png'
					let file = this.dataURLtoFile(res, fileName)

					let fd = new FormData();
					fd.append("file", file);
					let that = this
					fileDownload(fd).then(res => {
						console.log(res)
						if (res.success) {
							console.log(that)
							that.signurl = res.message
							that.submit()
						}

					})
					// 调接口
				}).catch(err => {
					this.$message.error('请签名之后提交！')
				})
			},
			//将base64转换为文件..
			dataURLtoFile(dataurl, filename) {
				var arr = dataurl.split(','),
					mime = arr[0].match(/:(.*?);/)[1],
					bstr = atob(arr[1]),
					n = bstr.length,
					u8arr = new Uint8Array(n);
				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}
				return new File([u8arr], filename, {
					type: mime
				});
			},
			submit() {
				let data = {
					url: this.signurl,
					stunum: this.stunum
				}
				stusign(data).then(res => {
					if (res.success) {
						this.$message.success('签名成功');
						this.handleReset()
						this.stuSignOpen = false
						this.$emit('signResult')
					} else {
						this.$message.error(res.message);
					}
				})

			}
		}
	}
</script>

<style scoped>
	button {
		height: 40px;
	}

	.show-info {
		margin-bottom: 20px;
		font-size: 25px;
		width: 100%;
		text-align: center;
	}

	.contro-container {
		width: 100%;
		height: 50px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-around;
		background-color: #D3D3D3;
		/* position: absolute; */
		margin-top: 10px;
		bottom: 0px;
	}

	::v-deep .el-dialog__body {
		padding-bottom: 0 !important;
		width: 100%;
	}

	.qianming-container {
		background: blanchedalmond;
		margin: 20px auto;
		position: relative;
	}

	.text {
		font-size: 14px;
	}

	.item {
		margin-bottom: 18px;
	}

	.clearfix:before,
	.clearfix:after {
		display: table;
		content: "";
	}

	.clearfix:after {
		clear: both
	}

	.box-card {
		width: 95%;
		margin-left: 2.5%;
		margin-top: 20px;
	}

	canvas {
		margin: 0 auto;
	}
</style>
