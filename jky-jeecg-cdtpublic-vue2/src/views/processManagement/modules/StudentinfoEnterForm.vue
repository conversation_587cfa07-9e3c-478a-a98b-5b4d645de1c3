<template>
	<a-spin :spinning="confirmLoading">
		<j-form-container :disabled="formDisabled">
			<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
				<a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="model.inscode" v-if="title=='新增'"
                                    @change="getCocachList"></j-tree-select-depart>
              <a-input v-model="model.insname" placeholder="" v-if="title!='新增'" disabled></a-input>
            </a-form-model-item>
          </a-col>
					<a-col :span="24">
						<a-form-model-item label="报名时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="applydate">
							<j-date placeholder="请选择报名时间" class="query-group-cust" v-model="model.applydate" style="width:100%">
							</j-date>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
							<a-input v-model="model.name" placeholder="请输入姓名"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
							<a-select placeholder="请选择性别" v-model="model.sex">
								<a-select-option value="1">男</a-select-option>
								<a-select-option value="2">女</a-select-option>
							</a-select>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="证件类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cardtype">
<!--							<a-input v-model="model.cardtype" placeholder="请输入证件类型   1:身份证 2:护照 3:军官证 4:其他"></a-input>-->
              <j-dict-select-tag type="list" v-model="model.cardtype" dictCode="sys_stu_cardtype"
                                 placeholder="请选择证件类型" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
							<a-input v-model="model.idcard" placeholder="请输入身份证号"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
							<a-input v-model="model.phone" placeholder="请输入手机号码"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="联系地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
							<a-input v-model="model.address" placeholder="请输入联系地址"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
							<a-input v-model="model.nationality" placeholder="请输入国籍"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="业务类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="busitype">
<!--							<a-input v-model="model.busitype" placeholder="请输入业务类型   0:初领 1:增领 9:其他"></a-input>-->
              <j-dict-select-tag type="list" v-model="model.busitype" dictCode="sys_stu_busitype"
                                 placeholder="请选择业务类型" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
<!--							<a-input v-model="model.traintype" placeholder="请输入培训车型"></a-input>-->
              <j-dict-select-tag type="list" v-model="model.traintype" dictCode="sys_stu_traintype"
                                 placeholder="请选择培训车型" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="实操教练员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
							<a-input v-model="model.coachnum" placeholder="请输入实操教练员"></a-input>
						</a-form-model-item>
					</a-col>

				</a-row>
			</a-form-model>
		</j-form-container>
	</a-spin>
</template>

<script>
	import {
		httpAction,
		getAction
	} from '@/api/manage'
	import {
		validateDuplicateValue
	} from '@/utils/util'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'

	export default {
		name: 'StudentinfoEnterForm',
		components: {JTreeSelectDepart},
		props: {
			//表单禁用
			disabled: {
				type: Boolean,
				default: false,
				required: false
			}
		},
		data() {
			return {
				model: {},
				labelCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 5
					},
				},
				wrapperCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 16
					},
				},
				confirmLoading: false,
				validatorRules: {
					inscode: [{
						required: true,
						message: '请输入培训机构!'
					}, ],
					cardtype: [{
						required: true,
						message: '请输入证件类型'
					}, ],
					idcard: [{
						required: true,
						message: '请输入身份证号!'
					}, ],
					nationality: [{
						required: true,
						message: '请输入国籍!'
					}, ],
					sex: [{
						required: true,
						message: '请输入性别'
					}, ],
					busitype: [{
						required: true,
						message: '请输入业务类型 '
					}, ],
					traintype: [{
						required: true,
						message: '请输入培训车型 '
					}, ],
					name: [{
						required: true,
						message: '请输入姓名!'
					}, ],
					phone: [{
						required: true,
						message: '请输入手机号码!'
					}, {
						pattern: /^1[0-9]{10}$/,
						message: "手机号码格式不对!",
					}],
					address: [{
						required: true,
						message: '请输入联系地址!'
					}, ],
					applydate: [{
						required: true,
						message: '请输入报名时间!'
					}, ],
				},
				url: {
					add: "/gzpt/studentinfoEnter/add",
					edit: "/gzpt/studentinfoEnter/edit",
					queryById: "/gzpt/studentinfoEnter/queryById"
				}
			}
		},
		computed: {
			formDisabled() {
				return this.disabled
			},
		},
		created() {
			//备份model原始值
			this.modelDefault = JSON.parse(JSON.stringify(this.model));
		},
		methods: {
			add() {
				this.edit(this.modelDefault);
			},
			edit(record) {
				this.model = Object.assign({}, record);
				this.visible = true;
			},
			submitForm() {
				const that = this;
				// 触发表单验证
				this.$refs.form.validate(valid => {
					if (valid) {
						that.confirmLoading = true;
						let httpurl = '';
						let method = '';
						if (!this.model.id) {
							httpurl += this.url.add;
							method = 'post';
						} else {
							httpurl += this.url.edit;
							method = 'put';
						}
						httpAction(httpurl, this.model, method).then((res) => {
							if (res.success) {
								that.$message.success(res.message);
								that.$emit('ok');
							} else {
								that.$message.warning(res.message);
							}
						}).finally(() => {
							that.confirmLoading = false;
						})
					}

				})
			},
		}
	}
</script>
