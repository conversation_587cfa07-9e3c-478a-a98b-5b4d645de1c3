<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入培训机构编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
              <a-input v-model="model.coachnum" placeholder="请输入教练员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练车" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carnum">
              <a-input v-model="model.carnum" placeholder="请输入教练车"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="simunum">
              <a-input v-model="model.simunum" placeholder="请输入设备编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="厂商编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="platnum">
              <a-input v-model="model.platnum" placeholder="请输入厂商编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学时编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recnum">
              <a-input v-model="model.recnum" placeholder="请输入学时编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subjcode">
<!--              <a-input v-model="model.subjcode" placeholder="请输入科目"  ></a-input>-->
              <j-dict-select-tag type="list" v-model="model.km" dictCode="sys_km"
                                 placeholder="请选择科目" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="开始时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="starttime">
<!--              <a-input v-model="model.starttime" placeholder="请输入开始时间"  ></a-input>-->
              <j-date placeholder="请输入开始时间" v-model="model.starttime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="结束时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="endtime">
<!--              <a-input v-model="model.endtime" placeholder="请输入结束时间"  ></a-input>-->
              <j-date placeholder="请输入结束时间" v-model="model.endtime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="有效学时" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="durationY">
              <a-input-number v-model="model.durationY" placeholder="请输入有效学时" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="有效里程" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mileageY">
              <a-input-number v-model="model.mileageY" placeholder="请输入有效里程" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="duration">
              <a-input-number v-model="model.duration" placeholder="请输入学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="里程(h/km)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mileage">
              <a-input-number v-model="model.mileage" placeholder="请输入里程(h/km)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训平均速度 km/h" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="avevelocity">
              <a-input-number v-model="model.avevelocity" placeholder="请输入培训平均速度 km/h" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuname">
              <a-input v-model="model.stuname" placeholder="请输入学员名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuidcard">
              <a-input v-model="model.stuidcard" placeholder="请输入身份证"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachname">
              <a-input v-model="model.coachname" placeholder="请输入教练员"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入培训机构名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licnum">
              <a-input v-model="model.licnum" placeholder="请输入车牌号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="市区编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
              <a-input v-model="model.district" placeholder="请输入市区编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择创建时间" v-model="model.crdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'ClassRecordDetailForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           starttime: [
              { required: true, message: '请输入开始时间!'},
           ],
           endtime: [
              { required: true, message: '请输入结束时间!'},
           ],
		   inscode: [
		      { required: true, message: '请输入培训机构编号!'},
		   ],
		   stunum: [
		      { required: true, message: '请输入学员编号!'},
		   ],
		   coachnum: [
		      { required: true, message: '请输入教练员编号!'},
		   ],
		   simunum: [
		      { required: true, message: '请输入设备编号!'},
		   ],
		   platnum: [
		      { required: true, message: '请输入厂商编号!'},
		   ],
        },
        url: {
          add: "/gzpt/classRecordDetail/add",
          edit: "/gzpt/classRecordDetail/edit",
          queryById: "/gzpt/classRecordDetail/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>