<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <!--					<j-dict-select-tag type="list" v-model="queryParam.cblb" dictCode="tjcb_cblb"-->
              <!--						placeholder="请选择培训机构编号" @change="changeCblb" />-->
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
              </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员编号">
              <a-input v-model="queryParam.stunum" placeholder="请输入学员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="日期范围">
              <j-date placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.beginTime">
              </j-date>
              <span class="query-group-split-cust"></span>
              <j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.endTime">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="handleSearch" icon="search">查询</a-button>
            <a-button type="primary" @click="handleReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('资金动态监管')" >导出</a-button>

      <!-- 高级查询区域 -->
      <!--      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <a-menu slot="overlay">
                <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
              </a-menu>
              <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>-->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange">
        <!-- :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" -->

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:studentinfoEnter:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:studentinfoEnter:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:studentinfoEnter:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
  </a-card>
</template>
<script>

import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import { getAction,postAction} from '@/api/manage'
export default {
  name: 'CapitalCountList',
  mixins:[JeecgListMixin, mixinDevice],
  components: {
    JTreeSelectDepart,
  },
  data () {
    return {
      description: '资金动态监管',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key:'rowIndex',
          width:60,
          align:"center",
          customRender:function (t,r,index) {
            return r.inscode ? parseInt(index)+1 : '合计';
          }
        },
        {
          title:'驾校名称',
          align:"center",
          dataIndex: 'insname'
        },
        {
          title:'合同金额',
          align:"center",
          dataIndex: 'frozenmoney'
        },
        {
          title:'冻结资金',
          align:"center",
          dataIndex: 'frozenmoney'
        },
        {
          title:'基础费用',
          align:"center",
          dataIndex: 'km1'
        },
        {
          title:'第二部分',
          align:"center",
          dataIndex: 'km2'
        },
        {
          title:'第三部分',
          align:"center",
          dataIndex: 'km3'
        },
        {
          title:'当前冻结资金',
          align:"center",
          dataIndex: 'currentFrozen'
        },

        // {
        //   title: '操作',
        //   dataIndex: 'action',
        //   align:"center",
        //   fixed:"right",
        //   width:147,
        //   scopedSlots: { customRender: 'action' }
        // }
      ],
      url: {
        list: "/gzpt/bankOrder/moneyTotal",
        exportXlsUrl: "/gzpt/bankOrder/exportMoneyTotal",
      },
      dictOptions:{},
      superFieldList:[],
      disableMixinCreated:true,
      queryParam:{
        beginTime:'',
        endTime:'',
      },
    }
  },
  created() {
    this.setTimeRange();
    this.ipagination.pageSize = 100
    this.loadData()
    // this.getSuperFieldList();
  },
  computed: {
    importExcelUrl: function(){
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    },
  },
  methods: {
    loadData(arg) {
      if(!this.url.list){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      this.loading = true;
      getAction(this.url.list, params).then((res) => {
        if (res.success) {
          //update-begin---author:zhangyafei    Date:********  for：适配不分页的数据列表------------
          this.dataSource = res.result.records||res.result;
          if(res.result.total)
          {
            this.ipagination.total = res.result.total;
          }else{
            this.ipagination.total = 0;
          }
          this.getTotal()
          //update-end---author:zhangyafei    Date:********  for：适配不分页的数据列表------------
        }else{
          this.$message.warning(res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    getTotal() {
      getAction('/gzpt/bankOrder/totalAmount', {
        ...this.queryParam,
      }).then((res) => {
        if (res.success) {
          this.dataSource.push({frozenmoney: res.result.allFrozenmoney, currentFrozen: res.result.allCurrentFrozen})
          this.allCurrentFrozen = res.result.allCurrentFrozen;
          this.allFrozenmoney = res.result.allFrozenmoney;
        }
      })
    },
    setTimeRange() {
      let  date = new Date();
      let seperator = "-";
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      let currentdate = year + "-" + month + "-" + strDate;
      let currentdatemin = year + "-" + month + "-" + "01";
      // this.formParams.timeRange = [currentdatemin, currentdate];
      this.queryParam.beginTime=currentdatemin
      this.queryParam.endTime=currentdate
      console.log(this.queryParam)
    },
    handleSearch() {
      this.searchQuery();
    },
    handleReset() {
      this.searchReset();
    },
    initDictConfig(){
    },
    getSuperFieldList(){
      let fieldList=[];
      fieldList.push({type:'string',value:'inscode',text:'培训机构编号'})
      fieldList.push({type:'string',value:'cardtype',text:'证件类型   1:身份证 2:护照 3:军官证 4:其他'})
      fieldList.push({type:'string',value:'idcard',text:'身份证号'})
      fieldList.push({type:'string',value:'nationality',text:'国籍'})
      fieldList.push({type:'string',value:'name',text:'姓名'})
      fieldList.push({type:'string',value:'sex',text:'性别 1:男性;2:女性'})
      fieldList.push({type:'string',value:'phone',text:'手机号码'})
      fieldList.push({type:'string',value:'address',text:'联系地址'})
      fieldList.push({type:'int',value:'photo',text:'成功上传的学员头像照片文件ID'})
      fieldList.push({type:'string',value:'busitype',text:'业务类型   0:初领 1:增领 9:其他'})
      fieldList.push({type:'string',value:'drilicnum',text:'驾驶证号'})
      fieldList.push({type:'string',value:'fstdrilicdate',text:'驾驶证初领日期'})
      fieldList.push({type:'string',value:'traintype',text:'培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P'})
      fieldList.push({type:'string',value:'applydate',text:'报名时间'})
      fieldList.push({type:'string',value:'photopath',text:'图片路径'})
      fieldList.push({type:'string',value:'insname',text:'驾校名称'})
      fieldList.push({type:'string',value:'stunum',text:'学员统一编号'})
      fieldList.push({type:'string',value:'bankcode',text:'银行code'})
      fieldList.push({type:'string',value:'bankname',text:'银行名称'})
      fieldList.push({type:'string',value:'perdritype',text:'原准驾车型'})
      fieldList.push({type:'string',value:'basicCost',text:'基础费用'})
      fieldList.push({type:'string',value:'secondCost',text:'第二部分费用'})
      fieldList.push({type:'string',value:'thirdCoast',text:'第三部分费用'})
      fieldList.push({type:'string',value:'frozenAmount',text:'冻结金额'})
      fieldList.push({type:'string',value:'coachnum',text:'教练编号'})
      fieldList.push({type:'int',value:'isEnter',text:'是否入学'})
      this.superFieldList = fieldList
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>