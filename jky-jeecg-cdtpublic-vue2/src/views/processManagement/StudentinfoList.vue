<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper" style="margin-bottom:10px">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <!-- <a-tree-select checkable  :treeData="departTree" placeholder="请选择培训机构" /> -->
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode"> </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-input v-model="inputQuery">
              <a-select slot="addonBefore" v-model="selectQuery">
                <a-select-option value="1">
                  身份证号
                </a-select-option>
                <a-select-option value="2">
                  学员编号
                </a-select-option>
                <a-select-option value="3">
                  手机号码
                </a-select-option>
              </a-select>
            </a-input>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="带教教练">
              <a-input v-model="queryParam.coachname" placeholder="请输入带教教练" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="推送银行">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.bankcode"
                dictCode="sys_bank"
                placeholder="请选择推送银行"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员姓名">
              <a-input v-model="queryParam.name" placeholder="请输入学员姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="证件类型">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.cardtype"
                dictCode="sys_stu_cardtype"
                placeholder="请选择证件类型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="性别">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.sex"
                dictCode="sys_user_sex"
                placeholder="请选择性别"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="业务类型">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.busitype"
                dictCode="sys_stu_busitype"
                placeholder="请选择业务类型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训车型">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="推送计时">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.istojs"
                dictCode="sys_push"
                placeholder="请选择推送计时"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="8" :lg="8" :md="8" :sm="24">
            <a-form-item label="报名日期">
              <j-date placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.applydateBegin">
              </j-date>
              <span class="query-group-split-cust"></span>
              <j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.applydateEnd"> </j-date>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员来源">
              <a-select v-model="queryParam.baseType" placeholder="请选择学员来源">
                <a-select-option value="0">企业端</a-select-option>
                <a-select-option value="1">浙里办</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="签订合同">
              <a-select v-model="queryParam.issigncontract" placeholder="请选择是否签订合同">
                <a-select-option value="0">否</a-select-option>
                <a-select-option value="1">是</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:studentinfo:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('学员信息')" v-has="'gzpt:studentinfo:export'">
        导出</a-button
      >
      <a-button
        @click="handleExportXls('t_m_studentinfo')"
        type="primary"
        :disabled="selectedRowKeys.length > 0 ? false : true"
        v-has="'gzpt:studentinfo:export'"
      >
        勾选导出</a-button
      >
      <a-dropdown :disabled="selectedRowKeys.length == 1 ? false : true">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="viewStuContract" v-has="'gzpt:studentinfo:viewContract'">
            查看合同
          </a-menu-item>
          <a-menu-item key="2" @click="signStuContract" v-has="'gzpt:studentinfo:sign'">
            签订合同
          </a-menu-item>
        </a-menu>
        <a-button>
          学员合同
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-dropdown :disabled="selectedRowKeys.length == 1 ? false : true">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="PushQgPt" v-has="'gzpt:studentinfo:pushQg'">
            全国平台
          </a-menu-item>
          <a-menu-item key="2" @click="PushJsPt" v-has="'gzpt:studentinfo:pushJs'">
            计时平台
          </a-menu-item>
        </a-menu>
        <a-button>
          推送
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-dropdown :disabled="selectedRowKeys.length > 0 ? false : true" v-has="'gzpt:studentinfo:pushJsBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="2" @click="BatchPushJsPt">
            计时平台
          </a-menu-item>
        </a-menu>
        <a-button>
          批量推送
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <!--			<a-button @click="JxPushbank" type="primary" :disabled="selectedRowKeys.length>0?false:true" v-has="'gzpt:studentinfo:PushStuToBank'">推送银行</a-button>-->
      <a-dropdown v-has="'gzpt:studentinfo:PushStuToBank'">
        <a-menu slot="overlay" :disabled="selectedRowKeys.length == 1 ? false : true">
          <a-menu-item
            key="1"
            v-for="item in bankList"
            :key="item.bankCode"
            :value="item.bankCode"
            @click="Pushbankceshi(item)"
          >
            {{ item.bankName }}
          </a-menu-item>
        </a-menu>
        <a-button>
          推送银行
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-button
        @click="handleDropoutDelete"
        type="primary"
        :disabled="selectedRowKeys.length > 0 ? false : true"
        v-has="'gzpt:studentinfo:drop'"
        >退学
      </a-button>
      <a-button
        @click="handleLogoutDelete"
        type="primary"
        :disabled="selectedRowKeys.length > 0 ? false : true"
        v-has="'gzpt:studentinfo:logOff'"
        >注销
      </a-button>
      <a-button
        @click="handleSetBlack"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:black'"
        >设置黑户
      </a-button>
      <a-button
        @click="setoldstudent"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:setoldstudent'"
        >设置老学员
      </a-button>
      <a-button
        @click="setAssistance"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:assistance'"
        >设置智能机器人
      </a-button>
      <a-button
        @click="forceDelete"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:remove'"
        >强制删除
      </a-button>
      <a-dropdown v-has="'gzpt:studentinfo:PushStuToBankForce'">
        <a-menu slot="overlay" :disabled="selectedRowKeys.length == 1 ? false : true">
          <a-menu-item
            key="1"
            v-for="item in bankList"
            :key="item.bankCode"
            :value="item.bankCode"
            @click="Pushbankforce(item)"
          >
            {{ item.bankName }}
          </a-menu-item>
        </a-menu>
        <a-button>
          强制推送银行
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-button
        @click="handleStuSign"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:stuSign'"
        >学员签名
      </a-button>
      <a-button
        @click="handleStuSignNew"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:stuSignQzb'"
        >签字板
      </a-button>
      <a-button @click="handleSingleSync" type="primary" v-has="'gzpt:studentinfo:manualsync'">学员同步 </a-button>
      <a-button
        @click="handleClassRecordSync"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:classrecordsync'"
      >
        学时同步</a-button
      >
      <a-button
        @click="handleSummary"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:totaltime:summaryTotalTime'"
      >
        汇总学时</a-button
      >
      <a-button
        @click="handleTransSchool"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:studentinfo:trans'"
        >转校申请</a-button
      >
      <!--      <a-button @click="() => unregisterVisible = true" :disabled="selectedRowKeys.length > 0 ?false:true" type="primary">-->
      <!--        注销申请</a-button>-->
      <a-button
        @click="passSubject1"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:passSubject1'"
        >通过科目一
      </a-button>
      <a-button
        @click="passSubject2"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        >科目二费用划转
      </a-button>
      <a-button
        @click="handleAccident('0')"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:releaseStuCoach:add'"
        >人脸比对取消
      </a-button>
      <a-button
        @click="handleAccident('1')"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:releaseStuCoach:add'"
        >定位判断取消
      </a-button>
      <a-button
        @click="handleAccident('2')"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:releaseStuCoach:add'"
        >衢学车特殊学员设置
      </a-button>
      <a-button
        @click="releaseOcrCheck"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:releaseOcrCheck'"
        >衢学车学员签合同免验证
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:studentinfo:remove'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"> <a-icon type="delete" />删除 </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-button
        @click="syncVisible = true"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:studentinfo:StageTrainningTimeReview'"
        >同步阶段报审</a-button
      >
      <a-button
        @click="handleFreeOrder"
        type="primary"
        icon="check-circle"
        :disabled="selectedRowKeys.length !== 1"
        >学员免单</a-button>
      <!-- 添加按钮 -->
      <a-button v-if="selectedRowKeys.length === 1" type="primary" icon="file-pdf" @click="viewApplicationForm">
        查看机动车驾驶人申请表
      </a-button>
      <a-button v-if="selectedRowKeys.length === 1" type="primary" icon="file" @click="viewStudyProof">
        查看学习证明
      </a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          selectionRows: selectionRows,
          onChange: handleSelectionChange
        }"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="text"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
            @click="showBig(text)"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:studentinfo:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />

          <a v-if="!record.issigncontract" @click="handleDeleteJx(record)" v-has="'gzpt:studentinfo:delete'">删除</a>
          <a-divider v-if="!record.issigncontract" type="vertical" />

          <a @click="handleEditPhoto(record)" v-has="'gzpt:studentinfo:insEdit'">驾校编辑</a>

          <a-divider type="vertical" v-has="'gzpt:studentinfo:insEdit'" />

          <a @click="handleEditTraintype(record)" v-has="'gzpt:studentinfo:carEdit'">修改车型</a>
          <a-divider type="vertical" v-has="'gzpt:studentinfo:carEdit'" />
          <!--					<a @click="handleEditApplyDate(record)" v-has="'gzpt:studentinfo:applyDateEdit'">修改报名时间</a>-->
          <!--					<a-divider type="vertical" v-has="'gzpt:studentinfo:applyDateEdit'" />-->

          <a-dropdown>
            <a class="ant-dropdown-link"
              >更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:studentinfo:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:studentinfo:logoutapply'">
                <a @click="handleLogoutapply(record)">注销申请</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <studentinfo-modal ref="modalForm" @ok="modalFormOk"></studentinfo-modal>
    <studentinfo-photo-modal ref="modalForm2" @ok="modalFormOk"></studentinfo-photo-modal>
    <stu-cost-change-modal ref="modalForm3" @ok="modalFormOk"></stu-cost-change-modal>
    <a-modal v-model="openBlack" title="设置黑户" @ok="handleSetBlack" :width="800">
      <a-form-model ref="form" :model="form" :rules="validatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="代管人身份证号" prop="otherIdcard" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="form.otherIdcard" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="代管人手机号码" prop="otherPhone" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="form.otherPhone" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="syncOpen" title="学员同步" @ok="submitSyncForm" :width="800">
      <a-form-model ref="syncForm" :model="syncForm" :rules="syncFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="证件号码" prop="idcard" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="syncForm.idcard" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训车型" prop="traintype" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="syncForm.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="summaryOpen" title="学时汇总" @ok="submitSummaryForm" :width="800">
      <a-form-model ref="summaryForm" :model="summaryForm" :rules="summaryFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="科目" prop="subject" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-model="summaryForm.subject" dictCode="sys_km" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="logoutapplyOpen" title="注销申请" @ok="submitLogoutapplyForm" :width="800">
      <a-form-model ref="summaryForm" :model="summaryForm" :rules="summaryFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="注销理由" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="syncForm.reason" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <StuInfoSign :stunum="stunum" ref="stuInfoSign" @signResult="signResult"></StuInfoSign>
    <a-modal v-model="isshowBig" :footer="null">
      <div class="showBig">
        <img :src="bigUrl" />
      </div>
    </a-modal>
    <a-modal v-model="syncVisible" title="同步" @ok="handleSync" :width="800">
      <a-form-model ref="syncForm" :model="syncParams" :rules="syncvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="科目" prop="subject" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-model="syncParams.subject" dictCode="sys_km" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="setCostChangeVisiable" title="金额修改" :width="800">
      <stu-cost-change ref="stuCostChange" :stunum="stunum"></stu-cost-change>
    </a-modal>
    <a-modal v-model="stuSignVisible" title="学员签名" @ok="handleStuSignNewOk" :width="800">
      <stu-sign-new ref="stuSignNew" :stunum="stunum" @signResultNew="signResultNew"></stu-sign-new>
    </a-modal>
    <a-modal v-model="EditTraintypeVisible" title="修改车型" @ok="handleEditTraintypeOk" :width="800">
      <a-form-model ref="traintypeForm" :model="traintypeParams" :rules="traintypevalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="车型" prop="traintype" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-model="traintypeParams.traintype">
                <a-select-option value="C1">C1</a-select-option>
                <a-select-option value="C2">C2</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
<!--          <a-col :span="24">-->
<!--            <a-form-model-item label="科目一技能证明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="filePath">-->
<!--              <j-upload v-model="traintypeParams.filePath" :multiple="true" :number="1"></j-upload>-->
<!--            </a-form-model-item>-->
<!--          </a-col>-->
        </a-row>
      </a-form-model>
    </a-modal>

    <a-modal v-model="EditApplyDateVisible" title="修改车型" @ok="handleEditApplyDateOk" :width="800">
      <a-form-model ref="applyDateForm" :model="applyDateParams" :rules="applyDateValidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="新报名时间" prop="newApplyDate" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择新报名时间"
                class="query-group-cust"
                v-model="applyDateParams.newApplyDate"
              ></j-date>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item
              label="修改报名时间申请文件"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              prop="filePath"
            >
              <j-upload v-model="applyDateParams.filePath" :multiple="true" :number="1"></j-upload>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>

    <a-modal v-model="unregisterVisible" title="注销理由" @ok="handleUnregister" :width="800">
      <a-form>
        <a-form-item label="注销理由">
          <a-textarea :model="unregisterReason" placeholder="请输入注销理由" row="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model="transferSchoolVisible" title="转校申请" @ok="submitTransSchool" :width="800">
      <a-form>
        <a-form-item label="目标驾校">
          <j-tree-select-depart placeholder="请选择培训机构" v-model="targetInscode"> </j-tree-select-depart>
        </a-form-item>
      </a-form>
      <div v-if="priceTip">
        <div style="display: flex;justify-content:space-around">
          <p>科二实操已学学时：{{ transferData.part2Minutes || 0 }}</p>
          <p>科二实操额定学时：{{ transferData.total2Minutes || 0 }}</p>
          <p>实操学时单价：{{ transferData.unitPrice || 0 }}</p>
          <p>科二预计划转：{{ transferData.part2Price || 0 }}</p>
        </div>
        <div style="display: flex;justify-content:space-around">
          <p>科三实操已学学时：{{ transferData.part3Minutes || 0 }}</p>
          <p>科三实操额定学时：{{ transferData.total3Minutes || 0 }}</p>
          <p>实操学时单价：{{ transferData.unitPrice || 0 }}</p>
          <p>科三预计划转：{{ transferData.part3Price || 0 }}</p>
        </div>
        <div style="display: flex;justify-content:center">
          预计划转到<span style="font-size: 16px;font-weight: bold;">{{ chooseStudent.insname }}</span
          >总额：{{ transferData.totalPrice || 0 }}
        </div>
      </div>
    </a-modal>
    <a-modal :visible="previewVisible" :footer="null" @cancel="handlePreviewCancel">
      <img style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import StudentinfoModal from './modules/StudentinfoModal'
import StuInfoSign from './modules/StuInfoSign.vue'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import { getAction, postAction } from '@/api/manage'
import { listBank } from '@/api/gzpt/bank'
import {
  listStudentinfo,
  getStudentinfo,
  delStudentinfo,
  delStudentinfoById,
  addStudentinfo,
  updateStudentinfo,
  exportStudentinfo,
  exportStudentinfoids,
  fileDownload,
  signStudentContract,
  viewStudentContract,
  PushQg,
  PushJs,
  PushStuToBank,
  PushStuToBankForce,
  isspecial,
  setBlackStu,
  isSchool,
  setoldstu,
  BatchPushJs,
  singleSyncStudent,
  applylogout,
  classRecordSync
} from '@/api/gzpt/studentinfo'
import StudentinfoPhotoModal from './modules/StudentinfoPhotoModal'
import StuSignNew from './modules/StuSignNew'
import StuCostChange from "@views/processManagement/modules/StuCostChange.vue";
import StuCostChangeModal from "@views/processManagement/modules/StuCostChangeModal.vue";
export default {
  name: 'StudentinfoList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    StuCostChangeModal,
    StuCostChange,
    StuSignNew,
    StudentinfoPhotoModal,
    StudentinfoModal,
    JTreeSelectDepart,
    StuInfoSign
  },
  data() {
    return {
      description: '学员信息管理',
      selectedRowKeys: [],
      syncVisible: false,
      stuSignVisible: false,
      setCostChangeVisiable: false,
      unregisterVisible: false,
      unregisterReason: '',
      syncParams: {},
      syncvalidatorRules: {
        subject: [
          {
            required: true,
            message: '请选择科目!'
          }
        ]
      },
      traintypeParams: {},
      applyDateParams: {},
      traintypevalidatorRules: {
        traintype: [
          {
            required: true,
            message: '请选择车型!'
          }
        ],
        filePath: [
          {
            required: true,
            message: '请上传科目一技能证明!'
          }
        ]
      },
      applyDateValidatorRules: {
        newApplyDate: [
          {
            required: true,
            message: '请选择新报名时间!'
          }
        ],
        filePath: [
          {
            required: true,
            message: '请上传修改报名时间申请文件!'
          }
        ]
      },
      openBlack: false,
      syncOpen: false,
      logoutapplyOpen: false,
      stunum: '',
      stuObj: {},
      form: {},
      syncForm: {},
      departTree: [],
      queryParam: {
        selectQuery: 1
      },
      dataSource: [
        {
          name: '刘一',
          sex: '女',
          phone: '18768278956'
        }
      ],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '图片',
          align: 'center',
          dataIndex: 'photopath',
          scopedSlots: {
            customRender: 'imgSlot'
          }
        },
        {
          title: '学员统一编号',
          align: 'center',
          dataIndex: 'stunum'
        },
        {
          title: '证件号码',
          align: 'center',
          dataIndex: 'idcard'
        },
        {
          title: '手机号码',
          align: 'center',
          dataIndex: 'phone'
        },
        {
          title: '培训车型 ',
          align: 'center',
          dataIndex: 'traintype'
        },
        {
          title: '带教教练',
          align: 'center',
          dataIndex: 'coachname'
        },
        {
          title: '报名日期',
          align: 'center',
          dataIndex: 'applydate'
        },
        {
          title: '计时厂商',
          align: 'center',
          dataIndex: 'platform_dictText'
        },
        {
          title: '推送计时',
          align: 'center',
          dataIndex: 'istojs_dictText'
        },
        {
          title: '推送银行',
          align: 'center',
          dataIndex: 'istobank_dictText'
        },
        {
          title: '是否智能机器人',
          align: 'center',
          dataIndex: 'isassistance',
          customRender: function(t) {
            return t === 0 ? '否' : '是'
          }
        },
        {
          title: '驾校名称',
          align: 'center',
          dataIndex: 'insname'
        },
        {
          title: '学员来源',
          align: 'center',
          dataIndex: 'baseType',
          customRender: function(t) {
            return t === 1 ? '浙里办' : '企业端'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/gzpt/studentinfo/list',
        delete: '/gzpt/studentinfo/delete',
        deleteBatch: '/gzpt/studentinfo/deleteBatch',
        exportXlsUrl: '/gzpt/studentinfo/exportXls',
        importExcelUrl: 'gzpt/studentinfo/importExcel',
        freeOrder: '/gzpt/studentinfo/freeOrder', // 添加免单接口URL
      },
      validatorRules: {
        otherIdcard: [
          {
            required: true,
            message: '请输入代管人身份证号!'
          },
          {
            pattern: /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/,
            message: '身份证号格式不对!'
          }
        ],
        otherPhone: [
          {
            required: true,
            message: '请输入代管人手机号码'
          },
          {
            pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
            message: '手机号码格式不对!'
          }
        ]
      },
      syncFormvalidatorRules: {
        idcard: [
          {
            required: true,
            message: '请输入证件号码!'
          },
          {
            pattern: /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/,
            message: '证件号码格式不对!'
          }
        ],
        traintype: [
          {
            required: true,
            message: '请输入培训车型'
          }
        ]
      },
      summaryFormvalidatorRules: {
        subject: [
          {
            required: true,
            message: '请选择科目'
          }
        ]
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      dictOptions: {},
      superFieldList: [],
      bankList: [],
      flag: '',
      inscodes: [],
      selectQuery: '1',
      inputQuery: '',
      queryParams: {
        idcard: '',
        mobile: '',
        stunum: ''
      },
      bigUrl: '',
      isshowBig: false,
      summaryOpen: false,
      summaryForm: {},
      EditTraintypeVisible: false,
      EditApplyDateVisible: false,
      targetInscode: '',
      transferSchoolVisible: false,
      transferData: {},
      chooseStudent: {},
      priceTip: false,
      previewVisible: false,
      previewImage: ''
    }
  },
  created() {
    this.getbanklist()
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleTransSchool() {
      let id = this.selectedRowKeys[0]
      let target = this.dataSource.find(item => item.id == id)
      if (!target.stunum) {
        this.$message.error('该生不符合转校条件!')
        return false
      }
      this.transferSchoolVisible = true
      this.chooseStudent = target
      if (this.chooseStudent.unitPrice) {
        this.priceTip = true
        getAction('/gzpt/stuTransfer/notice', { stunum: target.stunum }).then(res => {
          if (res.success) {
            this.transferData = res.result
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.priceTip = false
      }
    },
    handleAccident(operateType) {
      this.$confirm({
        title: '提示',
        content: '确认进行豁免操作吗？',
        onOk: () => {
          postAction('/gzpt/releaseStuCoach/add', {
            operateId: this.stunum,
            roleType: '0',
            operateType
          }).then(res => {
            if (res.success) {
              this.$message.success('操作成功!')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    releaseOcrCheck() {
      this.$confirm({
        title: '提示',
        content: '确认对该学员进行衢学车签订合同免除 签名验证 操作吗？',
        onOk: () => {
          postAction('/gzpt/studentinfo/releaseOcrCheck', {
            id: this.selectedRowKeys[0]
          }).then(res => {
            if (res.success) {
              this.$message.success('操作成功!')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    submitTransSchool() {
      if (!this.targetInscode) {
        this.$message.error('目标驾校不能为空!')
        return false
      }
      let id = this.selectedRowKeys[0]
      let target = this.dataSource.find(item => item.id == id)
      //return false
      postAction('/gzpt/stuTransfer/apply', {
        inscode: this.targetInscode,
        stunum: target.stunum
      }).then(res => {
        if (res.success) {
          this.$message.success('转校申请成功!')
          this.transferSchoolVisible = false
        } else {
          this.$message.error(res.message)
        }
      })
    },
    showBig(text) {
      this.isshowBig = true
      this.bigUrl = text
    },
    batchDel() {
      let ids = this.selectedRowKeys.join(',')
      this.handleDelete(ids)
    },
    handleSync() {
      this.$refs.syncForm.validate(valid => {
        if (valid) {
          postAction('/gzpt/stageTrainningTime/StageTrainningTimeReview', {
            stunum: this.stunum,
            subject: this.syncParams.subject
          }).then(res => {
            if (res.success) {
              this.$message.success('同步成功')
            } else {
              this.$message.error(res.message)
            }
            this.syncVisible = false
            this.$refs.syncForm.resetFields()
          })
        }
      })
    },
    handleDelete(id) {
      let that = this
      this.loading = true
      delStudentinfo(id, 0)
        .then(res => {
          this.loading = false
          if (res.code == 200) {
            that.loadData()
            that.$message.success(res.message)
          } else {
            that.$message.error(res.message)
          }
        })
        .catch(err => {
          that.$message.error(err)
        })
    },
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      if (this.selectQuery != '') {
        if (this.selectQuery == 1) {
          params.idcard = this.inputQuery
          params.stunum = null
          params.mobile = null
        }
        if (this.selectQuery == 2) {
          params.stunum = this.inputQuery
          params.idcard = null
          params.phone = null
        }
        if (this.selectQuery == 3) {
          params.phone = this.inputQuery
          params.idcard = null
          params.stunum = null
        }
      }
      console.log(params, 11111)
      this.loading = true
      getAction(this.url.list, params)
        .then(res => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (res.result.total) {
              this.ipagination.total = res.result.total
            } else {
              this.ipagination.total = 0
            }
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    searchReset() {
      this.queryParam = {}
      this.selectQuery = '1'
      this.inputQuery = ''
      this.loadData(1)
    },
    initDictConfig() {},
    //查看学员合同
    viewStuContract() {
      this.pdfUrl = ''
      this.pageNum = 1
      const id = this.selectedRowKeys[0]
      getStudentinfo(id).then(res => {
        viewStudentContract(res.result.stunum).then(response => {
          if (response.result) {
            javascipt: window.open(response.result)
          } else {
            this.$message.error('学员尚未签订合同,无法查看!')
          }
        })
      })
    },
    // 学员签订合同
    signStuContract() {
      this.pdfUrl = ''
      this.pageNum = 1
      const id = this.selectedRowKeys[0]
      getStudentinfo(id).then(response => {
        if (response.result.stunum === null) {
          this.$message.error('学员尚未获取统一编号,无法签订合同!')
        } else {
          if (response.result.issigncontract === 0 || response.result.issigncontract === null) {
            signStudentContract(id).then(response => {
              if (response.success) {
                this.$message.success('合同签订成功！')
              } else {
                this.$message.error(response.message)
              }
            })
          } else {
            let that = this
            this.$confirm({
              title: '该学员已经签订合同，确认重新签订吗？',
              content: '',
              onOk() {
                signStudentContract(id)
                  .then(response => {
                    if (response.success) {
                      that.$message.success('合同已重新签订！')
                    } else {
                      that.$message.error(response.message)
                    }
                  })
                  .catch(err => {
                    that.$message.error(err)
                  })
              },
              onCancel() {}
            })
          }
        }
      })
    },

    // 推送全国
    PushQgPt() {
      const id = this.selectedRowKeys[0]
      PushQg(id).then(response => {
        if (response.code == 200) {
          this.$message.success('推送成功')
          this.loadData()
          this.onClearSelected()
        } else {
          this.$message.error(response.message)
        }
      })
    },

    // 推送计时
    PushJsPt() {
      const id = this.selectedRowKeys[0]
      PushJs(id).then(response => {
        if (response.code == 200) {
          this.$message.success('推送成功')
          this.loadData()
          this.onClearSelected()
        } else {
          this.$message.error(response.message)
        }
      })
    },

    // 调试推送银行
    Pushbankceshi(item) {
      if (item.bankCode == null) {
        this.$message.error('没有选择银行')
        return null
      }
      this.form.id = this.selectedRowKeys.join(',')
      this.form.bankcode = item.bankCode
      this.form.bankname = item.bankName
      PushStuToBank(this.form).then(response => {
        if (response.success) {
          this.$message.success('推送成功')
        } else {
          this.$message.error(response.message)
        }
      })
    },

    /** 退学按钮操作 */
    handleDropoutDelete() {
      const ids = this.selectedRowKeys.join(',')
      let that = this
      this.$confirm({
        title: '是否确认删除学员信息编号为"' + ids + '"的数据项？',
        content: '',
        onOk() {
          delStudentinfo(ids, 1)
            .then(res => {
              if (res.code == 200) {
                that.loadData()
                that.$message.success(res.message)
              } else {
                that.$message.error(res.message)
              }
            })
            .catch(err => {
              that.$message.error(err)
            })
        },
        onCancel() {}
      })
    },
    /** 注销按钮操作 */
    handleLogoutDelete(row) {
      const ids = this.selectedRowKeys.join(',')
      let that = this
      this.$confirm({
        title: '是否确认注销学员信息编号为"' + ids + '"的数据项？',
        content: '',
        onOk() {
          delStudentinfo(ids, 2)
            .then(res => {
              if (res.success) {
                that.$message.success('注销成功')
                that.loadData()
              } else {
                that.$message.error('注销失败')
              }
            })
            .catch(err => {
              that.$message.error(err)
            })
        },
        onCancel() {}
      })
    },

    // 设置黑户
    handleSetBlack() {
      let that = this
      this.form.id = this.selectedRowKeys[0]
      setBlackStu(that.form)
      that.openBlack = false
      that.$message.success('设置黑户成功')
      this.loadData()
      // 触发表单验证
      // let that = this
      // this.$refs.form.validate(valid => {
      // 	if (valid) {
      // 		setBlackStu(that.form);
      // 		that.openBlack = false;
      // 		that.$message.success("设置黑户成功");
      // 		that.$refs.form.resetFields()
      // 		this.loadData();
      // 	}
      // })
    },
    // 设置黑户
    passSubject1() {
      postAction('/gzpt/studentinfo/passSubjectOne', {
        id: this.selectedRowKeys[0]
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    passSubject2() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning('请选择一个学员进行操作！')
        return
      }
      const id = this.selectedRowKeys[0]
      const student = this.selectionRows[0] // 获取选中的学员信息
      let that = this
      this.$confirm({
        title: `该学员科目二是否已上车培训？`,
        onOk() {
          postAction('/gzpt/studentinfo/passSubjectTwo', {
            id: this.selectedRowKeys[0]
          }).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
            } else {
              this.$message.error(res.message)
            }
          })
        },
        onCancel() {}
      })
    },
    //设置智能机器人
    setAssistance() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning('请选择一个学员进行操作！')
        return
      }
      const id = this.selectedRowKeys[0]
      const student = this.selectionRows[0] // 获取选中的学员信息
      let that = this
      this.$confirm({
        title: `确认要为学员【${student.name}】设置智能机器人吗？`,
        onOk() {
          if(student.stunum == null){
            that.$message.error('学员编号不能为空!')
            return
          }
          if(student.issigncontract != 0){
            that.$message.error('学员已签订合同!')
            return
          }
          that.$refs.modalForm3.edit(student);
          that.$refs.modalForm3.title = "设置智能机器人";
          that.$refs.modalForm3.disableSubmit = false;
        },
        onCancel() {}
      })

      /*postAction('/gzpt/studentinfo/assistance', {
        id: this.selectedRowKeys[0],
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('设置成功')
          this.open = false
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })*/
    },
    //设置老学员
    setoldstudent() {
      const id = this.selectedRowKeys[0]
      setoldstu(id).then(res => {
        if (res.code == 200) {
          this.$message.success('设置成功')
          this.open = false
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    /** 强制删除按钮操作 */
    forceDelete() {
      const ids = this.selectedRowKeys.join(',')
      let that = this
      this.$confirm({
        title: '是否确认删除学员信息编号为"' + ids + '"的数据项？',
        content: '',
        onOk() {
          delStudentinfoById(ids)
            .then(res => {
              if (res.code == 200) {
                that.$message.success('删除成功')
                that.loadData()
              } else {
                that.$message.error(res.message)
              }
            })
            .catch(err => {
              that.$message.error(err)
            })
        },
        onCancel() {}
      })
    },
    // 判断是否为东阳南山或金华交通技师
    ispecial() {
      isspecial().then(response => {
        this.flag = response.result
      })
    },
    /** 单个同步按钮操作 */
    handleSingleSync() {
      this.ispecial()
      this.syncOpen = true
      this.isSchool = false
    },
    handleLogoutapply(record) {
      this.ispecial()
      this.logoutapplyOpen = true
      this.stuObj = record
    },
    //学时同步
    handleClassRecordSync(row) {
      this.loading = true
      const id = this.selectedRowKeys[0]
      classRecordSync(id)
        .then(res => {
          this.loading = false
          this.$message.success(res.message)
          this.loadData()
        })
        .catch(() => {
          this.loading = false
        })
    },
    submitSyncForm() {
      this.$refs['syncForm'].validate(valid => {
        this.loading = true
        if (valid) {
          //避免异步问题，暂时把第二个请求嵌在第一个里面
          console.log(this.syncForm)
          singleSyncStudent(this.syncForm)
            .then(response => {
              if (response.success) {
                this.$message.success(response.message)
                this.syncOpen = false
                this.loading = false
                this.loadData()
              } else {
                this.$message.error(response.message)
              }
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    submitLogoutapplyForm() {
      this.loading = true
      this.syncForm.id = this.stuObj.id
      applylogout(this.syncForm)
        .then(response => {
          if (response.success) {
            this.$message.success(response.message)
            this.logoutapplyOpen = false
            this.loading = false
            this.loadData()
          } else {
            this.$message.error(response.message)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    //学时汇总
    handleSummary() {
      this.summaryOpen = true
      this.summaryForm = {}
    },
    handleUnregister() {
      postAction('', {
        unregisterReason: this.unregisterReason,
        ids: this.selectedRowKeys.join(',')
      })
        .then(response => {
          this.loading = false
          this.unregisterReason = ''
        })
        .catch(() => {
          this.loading = false
        })
    },
    submitSummaryForm() {
      this.$refs.summaryForm.validate(valid => {
        this.loading = true
        if (valid) {
          //避免异步问题，暂时把第二个请求嵌在第一个里面
          console.log(this.summaryForm)
          postAction('/gzpt/totalTime/summaryTotalTime', {
            stunum: this.stunum,
            subject: this.summaryForm.subject
          })
            .then(response => {
              if (response.success) {
                this.$message.success(response.message)
                this.summaryOpen = false
                this.loading = false
                this.loadData()
                this.handleSelectionChange([], [])
              } else {
                this.loading = false
                this.$message.error(response.message)
              }
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },

    // 强制推送银行
    Pushbankforce(item) {
      if (item.bankCode == null) {
        this.$message.error('没有选择银行')
        return null
      }
      this.form.id = this.selectedRowKeys.join(',')
      this.form.bankCode = item.bankCode
      this.form.bankName = item.bankName
      PushStuToBankForce(this.form, item.bankCode).then(response => {
        if (response.success) {
          this.$message.success('推送成功')
        } else {
          this.$message.error(response.message)
        }
      })
      this.reset()
    },
    // 嘉兴推送银行
    JxPushbank() {
      this.form.id = this.selectedRowKeys[0]
      this.form.inscode = this.inscodes

      PushStuToBank(this.form)
        .then(response => {
          if (response.success) {
            this.$message.success('推送成功')
          } else {
            this.$message.error(response.message)
          }
        })
        .catch(err => {})
      this.reset()
    },
    // 获取银行列表
    getbanklist() {
      listBank(null).then(response => {
        this.bankList = response.result.records
        // getUserProfile().then(userRes => {
        // 	let user = userRes.data;
        // 	let {
        // 		dept: deptName
        // 	} = user
        // 	let banklist = response.result.records.filter(item => {
        // 		if (item.bankCode === 'pabank' &&
        // 			!['金华杰正机动车驾驶员培训有限公司', '金华职业技术学院理工汽车驾驶培训学校', '金华市公运中心'].concat(
        // 				deptName)
        // 		) {
        // 			return false;
        // 		}
        // 		return true;
        // 	})
        // 	console.log(banklist)
        // 	this.bankList = banklist
        // });
      })
    },
    //嘉兴学员签名
    handleStuSign() {
      if (this.signpath !== null && this.signpath !== '' && this.signpath !== undefined) {
        let that = this
        this.$confirm({
          title: '该学员已签名, 是否确定重新签名?',
          content: '',
          onOk() {
            that.$refs.stuInfoSign.init()
          },
          onCancel() {}
        })
      } else {
        this.$refs.stuInfoSign.init()
      }
    },
    signResult() {
      this.loadData()
      this.onClearSelected()
    },

    //签字板
    handleStuSignNew() {
      this.stuSignVisible = true
    },

    handleStuSignNewOk() {
      // this.stuSignVisible=false;
      this.$refs.stuSignNew.handleGenerate()
    },
    signResultNew() {
      this.stuSignVisible = false
      this.loadData()
      this.onClearSelected()
    },
    // 多选框选中数据
    handleSelectionChange(selectedRowKeys, record) {
      this.selectedRowKeys = selectedRowKeys
      this.selectionRows = record
      console.log(selectedRowKeys, '选中', record)
      // this.ids = selection.map(item => item.id)
      // this.stunum = selection.map(item => item.stunum)
      // this.inscodes = selection.map(item => item.inscodes)
      this.stunum = record[0].stunum
      this.signpath = record[0].signpath
      this.inscodes = record[0].inscodes
      // this.single = selection.length !== 1;
      // this.multiple = !selection.length;
    },
    // 批量推送计时
    BatchPushJsPt() {
      const id = this.selectedRowKeys.join(',')
      this.loading = true
      BatchPushJs(id)
        .then(res => {
          if (res.code === 200) {
            this.loading = false
            this.$message.success('批量推送成功')
            this.loadData()
          } else {
            this.$message.error('批量推送失败')
            this.loadData()
          }
        })
        .catch(error => {
          this.loading = false
          this.loadData()
        })
    },

    handleEditPhoto(record) {
      this.$refs.modalForm2.edit(record)
      this.$refs.modalForm2.title = '编辑'
      this.$refs.modalForm2.disableSubmit = false
    },
    //驾校删除
    handleDeleteJx(record) {
      let that = this
      this.$confirm({
        title: '是否确定删除?',
        content: '',
        onOk() {
          that.jxDeleteOk(record)
        },
        onCancel() {}
      })
    },
    jxDeleteOk(record) {
      this.loading = true
      postAction('gzpt/studentinfo/deleteOne', {
        id: record.id
      })
        .then(response => {
          if (response.success) {
            this.$message.success(response.message)
            this.loadData()
          } else {
            this.loading = false
            this.$message.error(response.message)
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 修改车型
    handleEditTraintype(record) {
      this.EditTraintypeVisible = true
      this.traintypeParams.id = record.id
    },
    //
    handleEditTraintypeOk() {
      console.log(this.traintypeParams)
      this.$refs.traintypeForm.validate(valid => {
        if (valid) {
          postAction('/gzpt/studentinfo/carEdit', this.traintypeParams).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              this.EditTraintypeVisible = false
              this.$refs.traintypeForm.resetFields()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },

    // 修改报名时间
    handleEditApplyDate(record) {
      this.EditApplyDateVisible = true
      this.applyDateParams.id = record.id
    },
    //修改报名时间
    handleEditApplyDateOk() {
      console.log(this.applyDateParams)
      this.$refs.applyDateForm.validate(valid => {
        if (valid) {
          postAction('/gzpt/studentinfo/applyDateEdit', this.applyDateParams).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              this.EditApplyDateVisible = false
              this.$refs.applyDateForm.resetFields()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 查看申请表方法
    viewApplicationForm() {
      const id = this.selectedRowKeys[0]
      if (!id) {
        this.$message.warning('请选择要查看的学员')
        return
      }

      getAction('/gzpt/studentinfoEnter/viewApplicationForm', {
        id: id
      }).then(res => {
        if (res.success) {
          if (res.result) {
            this.handlePreview(res.result)
          } else {
            this.$message.warning('未找到驾驶证申请表文件!')
          }
        } else {
          this.$message.error(res.message || '获取文件失败')
        }
      })
    },
    // 查看学习证明方法
    viewStudyProof() {
      const id = this.selectedRowKeys[0]
      if (!id) {
        this.$message.warning('请选择要查看的学员')
        return
      }

      getAction('/zlb/studentStudyProof/getByStudentInfoId', {
        id: id
      }).then(res => {
        if (res.success) {
          if (res.result && res.result.studyUrl) {
            this.handlePreview(res.result.studyUrl)
          } else {
            this.$message.warning('未上传学习证明!')
          }
        } else {
          this.$message.error(res.message || '获取文件失败')
        }
      })
    },
    // 处理文件预览
    handlePreview(url) {
      if (!url) return
      // 获取文件扩展名 - 先去除查询参数
      const baseUrl = url.split('?')[0]
      const fileExt = baseUrl.substring(baseUrl.lastIndexOf('.') + 1).toLowerCase()
      if (fileExt === 'pdf') {
        window.open(url)
      } else if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
        this.previewImage = url
        this.previewVisible = true
      } else {
        window.open(url) // 其他类型文件默认用浏览器打开
      }
    },

    // 关闭预览
    handlePreviewCancel() {
      this.previewVisible = false
      this.previewImage = ''
    },
    // 学员免单处理
    handleFreeOrder() {
      if (this.selectedRowKeys.length !== 1) {
        this.$message.warning('请选择一个学员进行操作！')
        return
      }
      const id = this.selectedRowKeys[0]
      const student = this.selectionRows[0] // 获取选中的学员信息
      let that = this
      this.$confirm({
        title: `确认要为学员【${student.name}】进行免单操作吗？`,
        content: '免单有效期1天！',
        onOk() {
          that.loading = true
          postAction(that.url.freeOrder, student)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message || '学员免单操作成功！')
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.error(res.message || '学员免单操作失败！')
              }
            })
            .catch(err => {
              that.$message.error('请求失败: ' + err)
            })
            .finally(() => {
              that.loading = false
            })
        },
        onCancel() {}
      })
    },

  }
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';

/deep/.ant-table-row {
  height: 50px !important;
}

.showBig {
  height: 500px;
  width: 500px;

  img {
    height: 100%;
    width: 90%;
    object-fit: fill;
    margin: 0 auto;
  }
}
</style>
