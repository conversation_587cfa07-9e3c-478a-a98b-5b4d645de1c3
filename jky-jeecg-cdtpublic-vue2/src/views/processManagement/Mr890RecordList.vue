<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="设备编号">
							<a-input v-model="queryParam.deviceId" placeholder="请输入设备编号" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="身份证号码">
							<a-input v-model="queryParam.idcard" placeholder="请输入身份证号码" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="姓名">
							<a-input v-model="queryParam.name" placeholder="请输入姓名" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="认证状态">
							<j-dict-select-tag type="list" v-model="queryParam.status" dictCode="sys_mr890_face_status"
								placeholder="请选择证件类型" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="签到类型">
							<j-dict-select-tag type="list" v-model="queryParam.signType" dictCode="sys_mr890_sign_type"
								placeholder="请选择签到类型" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
<!--			<a-button type="primary" icon="download" @click="handleExportXls('统一实人认证')" v-has="'gzpt:totaltime:export'" >导出</a-button>-->
		</div>

		<!-- table区域-begin -->
		<div>
			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="width:120px;height:120px; 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>


        <span slot="action" slot-scope="text, record" v-has="'gzpt:mr890:pass'">
					<a v-if="record.status==0"  @click="handlePass(record)"  >设置通过</a>
				</span>
			</a-table>
		</div>

	</a-card>
</template>

<script>
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
  import {  postAction, putAction } from '@/api/manage'
	export default {
		name: 'Mr890RecordList',
		mixins: [JeecgListMixin, mixinDevice],
		components: {

		},
		data() {
			return {
				description: '统一实人认证',
				// 表头
				columns: [
					{
						title: 'ID',
						align: "center",
						dataIndex: 'id'
					},
					{
						title: '设备编号',
						align: "center",
						dataIndex: 'deviceId'
					},
					{
						title: '培训中心名称',
						align: "center",
						dataIndex: 'centerName'
					},
					{
						title: '身份证号码',
						align: "center",
						dataIndex: 'idcard'
					},
					{
						title: '姓名',
						align: "center",
						dataIndex: 'name'
					},
					{
						title: '照片',
						align: "center",
						dataIndex: 'ssoUrlPhoto',
						scopedSlots: {
							customRender: 'imgSlot'
						}
					},
					{
						title: '认证状态',
						align: "center",
						dataIndex: 'status_dictText'
					},
					{
						title: '签到类型',
						align: "center",
						dataIndex: 'signType_dictText'
					},
					{
						title: '创建时间',
						align: "center",
						dataIndex: 'createTime'
					},
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/gzpt/mr890Record/list",
					delete: "/gzpt/mr890Record/delete",
					deleteBatch: "/gzpt/mr890Record/deleteBatch",
					exportXlsUrl: "/gzpt/mr890Record/export",
					importExcelUrl: "gzpt/mr890Record/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
			}
		},
		created() {

		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			initDictConfig() {},
			handleStage() {
				let that = this
				this.$confirm({
					title: '是否生成报审？',
					content: '',
					onOk() {
						that.loading = true;
						craStage(that.selectedRowKeys[0]).then((response) => {
							that.loading = false;
							if(response.success){
								that.$message.success("操作成功");
								that.loadData();
							} else {
								that.$message.error(response.message);
							}

						});
					},
					onCancel() {

					},
				});

			},
      handlePass(record) {
        let that = this
        console.log(record)
        this.$confirm({
          title: '是否设置认证状态通过？',
          content: '',
          onOk() {
            that.loading = true;
            postAction('/gzpt/mr890Record/edit'+'?id='+record.id).then((response) => {
              that.loading = false;
              if (response.success) {
                that.$message.success("操作成功");
                that.loadData();
              } else {
                that.$message.error(response.message);
              }

            });
          },
          onCancel() {

          },
        });
      },
		},


	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
