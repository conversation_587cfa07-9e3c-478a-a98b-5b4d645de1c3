<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart
                dict="treeselect"
                v-model="queryParam.inscode"
                placeholder="请选择培训机构"
              ></j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教练员编号">
              <a-input v-model="queryParam.coachNum" placeholder="请输入教练员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教练员姓名">
              <j-input v-model="queryParam.coachName" placeholder="请输入教练员姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="身份证">
              <a-input v-model="queryParam.idCard" placeholder="请输入身份证" />
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="日期">
              <a-range-picker
                style="width: 300px"
                v-model="queryParam.date"
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
                @change="onDateChange"
                showTime
              />
            </a-form-item>
          </a-col>

          <a-button type="primary" icon="search" style="margin-right: 8px" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- <a-button type="primary" icon="download" @click="handleExportXls('coach_rest')">导出</a-button> -->

      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a @click="handleDelete(record.id)">删除</a>
        </span>
      </a-table>
    </div>

    <coach-rest-modal ref="modalForm" @ok="modalFormOk"></coach-rest-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CoachRestModal from './modules/CoachRestModal'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'

export default {
  name: 'CoachRestList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CoachRestModal,
    JTreeSelectDepart,
  },
  data() {
    return {
      description: 'coach_rest管理页面',
      // 表头
      columns: [
        {
          title: '申请人',
          align: 'center',
          dataIndex: 'coachName',
        },
        {
          title: '教练员证件号',
          align: 'center',
          dataIndex: 'idCard',
        },
        {
          title: '教练员编号',
          align: 'center',
          dataIndex: 'coachNum',
        },
        {
          title: '请假类型',
          // title: '类型 4：休息，5：请假',
          align: 'center',
          dataIndex: 'type_dictText',
        },
        {
          title: '请假天数',
          // title: '类型 4：休息，5：请假',
          align: 'center',
          dataIndex: 'days',
        },
        {
          title: '请假事由',
          align: 'center',
          dataIndex: 'reason',
        },
        {
          title: '开始时间',
          align: 'center',
          dataIndex: 'startDate',
        },
        {
          title: '结束时间',
          align: 'center',
          dataIndex: 'endDate',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      isorter: {
        column: 'createTime',
        order: 'desc',
      },
      url: {
        list: '/coachRest/list',
        delete: '/coachRest/delete',
        deleteBatch: '/coachRest/deleteBatch',
        exportXlsUrl: '/coachRest/exportXls',
        importExcelUrl: '/coachRest/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    onDateChange: function (value, dateString) {
      this.$set(this.queryParam, 'startDate', dateString[0])
      this.$set(this.queryParam, 'endDate', dateString[1])
    },
    initDictConfig() {},
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>