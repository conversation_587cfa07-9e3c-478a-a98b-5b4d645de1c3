<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <coach-config-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></coach-config-form>
    <template slot="footer">
      <a-button v-if="!disableSubmit" @click="handleOk(false)" type="primary">保存</a-button>
      <a-button v-if="!disableSubmit" @click="handleOk(true)" type="primary">保存并生效</a-button>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </j-modal>
</template>

<script>
import CoachConfigForm from './SchedulingConfigForm'
export default {
  name: 'CoachConfigModal',
  components: {
    CoachConfigForm,
  },
  data() {
    return {
      title: '',
      width: 800,
      visible: false,
      disableSubmit: false,
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleOk(isAssert) {
      this.$refs.realForm.submitForm(isAssert)
    },
    submitCallback() {
      this.$emit('ok')
      this.visible = false
    },
    handleCancel() {
      this.close()
    },
  },
}
</script>