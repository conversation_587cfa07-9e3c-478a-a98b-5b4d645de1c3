<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="设备编号">
<!--							<a-input v-model="queryParam.versionName" placeholder="请输入版本名称" />-->
              <a-input v-model="queryParam.id" placeholder="请输入设备编号" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="单位名称">
							<a-input v-model="queryParam.centerName" placeholder="请输入单位名称" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="设备状态">
							<j-dict-select-tag type="list" v-model="queryParam.status" dictCode="sys_mr890_device_status"
								placeholder="请选择设备状态" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="版本号">
							<a-input v-model="queryParam.version" placeholder="请输入版本号" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="是否使用人脸">
							<j-dict-select-tag type="list" v-model="queryParam.useFace" dictCode="sys_mr890_use_face"
								placeholder="请选择是否使用人脸" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
			<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
			<a-button type="primary" icon="download" @click="handleExportXls('mr890设备管理')" v-has="'gzpt:totaltime:export'" >导出</a-button>
			<a-button @click="upgradeVersion" type="primary" >全部升级最新版本</a-button>
		</div>

		<!-- table区域-begin -->
		<div>
			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="max-width:80px;font-size: 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>
				<span slot="action" slot-scope="text, record">
					<a @click="handleEdit(record)" v-has="'gzpt:studentinfo:edit'">编辑</a>
					<!-- <a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />
					<a @click="handleDetail(record)">详情</a> -->
					<a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />
					<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
						<a>删除</a>
					</a-popconfirm>
				</span>
			</a-table>
		</div>
		<Mr890InfoModal ref="modalForm" @ok="modalFormOk"></Mr890InfoModal>
	</a-card>
</template>

<script>
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
	import {
		craStage
	} from "@/api/gzpt/totaltime";
	import Mr890InfoModal from './modules/Mr890InfoModal'
  import {
    postAction
  } from '@/api/manage'
	export default {
		name: 'Mr890Info',
		mixins: [JeecgListMixin, mixinDevice],
		components: {
			Mr890InfoModal
		},
		data() {
			return {
				description: 'mr890设备管理',
				dataSource2: [{
					deviceId: '123888',
					centerName: '东阳-恒通',
					status: '在线',
					isUseFace: '启用'
				}],
				// 表头
				columns: [
					{
						title: '设备编号',
						align: "center",
						dataIndex: 'id'
					},
					{
						title: '单位名称',
						align: "center",
						dataIndex: 'centerName'
					},
					{
						title: '设备状态',
						align: "center",
						dataIndex: 'status_dictText'
					},
					{
						title: '是否使用人脸',
						align: "center",
						dataIndex: 'useFace_dictText'
					},
					{
						title: '是否启用',
						align: "center",
						dataIndex: 'enable_dictText'
					},
					{
						title: '版本号',
						align: "center",
						dataIndex: 'version'
					},
					{
						title: '最后在线时间',
						align: "center",
						dataIndex: 'lastOnlineTime'
					},
					{
						title: '创建时间',
						align: "center",
						dataIndex: 'createTime'
					},
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/gzpt/mr890Info/list",
					delete: "/gzpt/mr890Info/delete",
					deleteBatch: "/gzpt/mr890Info/deleteBatch",
					exportXlsUrl: "/gzpt/mr890Info/export",
					importExcelUrl: "gzpt/mr890Info/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
			}
		},
		created() {
			this.getSuperFieldList();
		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			initDictConfig() {},
			getSuperFieldList() {
				let fieldList = [];
				fieldList.push({
					type: 'string',
					value: 'stunum',
					text: '学员编号'
				})
				fieldList.push({
					type: 'string',
					value: 'idcard',
					text: '身份证'
				})
				fieldList.push({
					type: 'int',
					value: 'subject',
					text: '科目'
				})
				fieldList.push({
					type: 'number',
					value: 'vehicletime',
					text: '实操学时'
				})
				fieldList.push({
					type: 'number',
					value: 'classtime',
					text: '课程'
				})
				fieldList.push({
					type: 'number',
					value: 'simulatortime',
					text: '模拟'
				})
				fieldList.push({
					type: 'number',
					value: 'networktime',
					text: '远程'
				})
				fieldList.push({
					type: 'number',
					value: 'isBaoshen',
					text: 'IS_BAOSHEN'
				})
				fieldList.push({
					type: 'number',
					value: 'mileage',
					text: '里程'
				})
				fieldList.push({
					type: 'date',
					value: 'crdate',
					text: '创建时间'
				})
				fieldList.push({
					type: 'string',
					value: 'traintype',
					text: '车型'
				})
				fieldList.push({
					type: 'string',
					value: 'inscode',
					text: '培训机构'
				})
				fieldList.push({
					type: 'string',
					value: 'insname',
					text: '培训机构编号'
				})
				fieldList.push({
					type: 'string',
					value: 'district',
					text: '市区'
				})
				fieldList.push({
					type: 'string',
					value: 'stuname',
					text: '姓名'
				})
				this.superFieldList = fieldList
			},
			upgradeVersion(){
				let that = this
				this.$confirm({
					title: '是否确认全部设备升级最新版本？',
					content: '',
					onOk() {
						that.loading = true;
						postAction('/gzpt/mr890Info/upgradeAll').then((response) => {
							that.loading = false;
							if (response.success) {
								that.$message.success("操作成功");
								that.loadData();
							} else {
								that.$message.error(response.message);
							}

						});
					},
					onCancel() {

					},
				});
			},
		}
	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
