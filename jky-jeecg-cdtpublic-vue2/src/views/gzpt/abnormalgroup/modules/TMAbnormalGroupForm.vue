<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="异常组对象" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="objnum">
              <a-input v-model="model.objnum" placeholder="请输入异常组对象"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="异常组对象名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="objname">
              <a-input v-model="model.objname" placeholder="请输入异常组对象名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="对象类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <j-dict-select-tag v-model="model.type" placeholder="类型" dictCode="sys_group_type" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入培训机构编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训机构名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入培训机构名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="异常类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="abnormalSeq">
              <a-input v-model="model.abnormalSeq" placeholder="请输入异常类型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="异常类型名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="abnormalName">
              <a-input v-model="model.abnormalName" placeholder="请输入异常类型名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="处理状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag v-model="model.status" placeholder="请选择处理状态" dictCode="sys_group_status" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="生成时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择生成时间" v-model="model.crdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="处理时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="disposeDate">
              <j-date placeholder="请选择处理时间" v-model="model.disposeDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="县区" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
              <a-input v-model="model.district" placeholder="请输入县区"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否提交工单" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isWorkorder">
              <a-input-number v-model="model.isWorkorder" placeholder="请输入是否提交工单 " style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="提交时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="submitDate">
              <j-date placeholder="请选择提交时间" v-model="model.submitDate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="标识工单提交后的状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="orderstatus">
              <a-input-number v-model="model.orderstatus" placeholder="请输入标识工单提交后的状态" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="理由" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reason">
              <a-input v-model="model.reason" placeholder="请输入理由"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="操作人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="operator">
              <a-input v-model="model.operator" placeholder="请输入操作人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否显示该异常组" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isShow">
              <a-input-number v-model="model.isShow" placeholder="请输入是否显示该异常组 不影响审核设置9 默认空" style="width: 100%" />
            </a-form-model-item>
          </a-col>

        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'TMAbnormalGroupForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/gzpt/abnormalGroup/add",
          edit: "/gzpt/abnormalGroup/edit",
          queryById: "/gzpt/abnormalGroup/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>