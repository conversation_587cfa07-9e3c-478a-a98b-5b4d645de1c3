<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper" style="margin-bottom:10px">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构" >
              <!-- <a-tree-select checkable  :treeData="departTree" placeholder="请选择培训机构" /> -->
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
              </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-input v-model="inputQuery">
              <a-select slot="addonBefore" v-model="selectQuery" class="sfType">
                <a-select-option value="1">
                  身份证号
                </a-select-option>
                <a-select-option value="2">
                  学员编号
                </a-select-option>
                <a-select-option value="3">
                  手机号码
                </a-select-option>
              </a-select>
            </a-input>
          </a-col>

          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员姓名">
              <a-input v-model="queryParam.stuname" placeholder="请输入学员姓名" />
            </a-form-item>
          </a-col>



          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训车型">
              <j-dict-select-tag type="list" v-model="queryParam.traintype" dictCode="sys_stu_traintype"
                                 placeholder="请选择培训车型" />
            </a-form-item>
          </a-col>

          <a-col :xl="8" :lg="8" :md="8" :sm="24">
            <a-form-item label="预约时间">
              <j-date placeholder="请选择开始时间" class="query-group-cust" v-model="queryParam.startTime">
              </j-date>
              <span class="query-group-split-cust"></span>
              <j-date placeholder="请选择结束结束" class="query-group-cust" v-model="queryParam.endTime">
              </j-date>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('stu_appointment')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <stu-appointment-modal ref="modalForm" @ok="modalFormOk"></stu-appointment-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import StuAppointmentModal from './modules/StuAppointmentModal'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'


  export default {
    name: 'StuAppointmentList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      StuAppointmentModal,
      JTreeSelectDepart,
    },
    data () {
      return {
        description: 'stu_appointment管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'学员名字',
            align:"center",
            dataIndex: 'stuname'
          },
          {
            title:'学员身份证',
            align:"center",
            dataIndex: 'idcard'
          },
          {
            title:'手机号',
            align:"center",
            dataIndex: 'phone'
          },
          {
            title:'学员编号',
            align:"center",
            dataIndex: 'stunum'
          },
          {
            title:'驾校编号',
            align:"center",
            dataIndex: 'inscode'
          },
          {
            title:'驾校名字',
            align:"center",
            dataIndex: 'insname'
          },
          {
            title:'预约编号',
            align:"center",
            dataIndex: 'orderNo'
          },
          {
            title:'培训车型',
            align:"center",
            dataIndex: 'traintype'
          },
          {
            title:'培训类型',
            align:"center",
            dataIndex: 'classType_dictText'
          },
          {
            title:'培训预约状态',
            align:"center",
            dataIndex: 'status_dictText'
          },
          {
            title:'培训的模拟点名称',
            align:"center",
            dataIndex: 'address'
          },
          {
            title:'培训时⻓',
            align:"center",
            dataIndex: 'classHour'
          },
          // {
          //   title:'培训内容',
          //   align:"center",
          //   dataIndex: 'content'
          // },
          {
            title:'培训开始时间',
            align:"center",
            dataIndex: 'startTime',
            // customRender:function (text) {
            //   return !text?"":(text.length>10?text.substr(0,10):text)
            // }
          },
          {
            title:'培训结束时间',
            align:"center",
            dataIndex: 'endTime',
            // customRender:function (text) {
            //   return !text?"":(text.length>10?text.substr(0,10):text)
            // }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/gzpt/tMStuAppointment/list",
          delete: "/gzpt/tMStuAppointment/delete",
          deleteBatch: "/gzpt/tMStuAppointment/deleteBatch",
          exportXlsUrl: "/gzpt/tMStuAppointment/exportXls",
          importExcelUrl: "/gzpt/tMStuAppointment/importExcel",

        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'stuname',text:'学员名字'})
        fieldList.push({type:'string',value:'idcard',text:'学员身份证'})
        fieldList.push({type:'string',value:'phone',text:'手机号'})
        fieldList.push({type:'string',value:'stunum',text:'学员编号'})
        fieldList.push({type:'string',value:'inscode',text:'驾校编号'})
        fieldList.push({type:'string',value:'insname',text:'驾校名字'})
        fieldList.push({type:'string',value:'orderNo',text:'预约编号（唯⼀）'})
        fieldList.push({type:'string',value:'traintype',text:'培训车型'})
        fieldList.push({type:'int',value:'classType',text:'培训类型（1，课堂 2，模拟 3， 实操科⽬⼆ 4，实操科⽬三)'})
        fieldList.push({type:'int',value:'status',text:'培训状态（4取消， 9，超时取消 1 成功）'})
        fieldList.push({type:'string',value:'address',text:'培训的模拟点名称或地址，如：（xxxx模拟中⼼）'})
        fieldList.push({type:'number',value:'classHour',text:'培训时⻓'})
        fieldList.push({type:'string',value:'content',text:'培训内容'})
        fieldList.push({type:'date',value:'startTime',text:'培训开始时间'})
        fieldList.push({type:'date',value:'endTime',text:'培训结束时间'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped lang="less">
  @import '~@assets/less/common.less';
  .sfType{
	  /deep/.ant-select-selection--single{
	  	width: 100px;
	  }
  }

</style>