<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12" v-if="disabled">
            <a-form-model-item label="考核员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="examnum">
              <a-input v-model="model.examnum" placeholder="请输入考核员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
              <j-dict-select-tag type="list" v-model="model.sex" dictCode="sys_user_sex"
              	placeholder="请选择性别" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
              <a-input v-model="model.idcard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mobile">
              <a-input v-model="model.mobile" placeholder="请输入手机号码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.insname" placeholder="请输入培训机构" v-if="title!='新增'" disabled></a-input>
			  <j-tree-select-depart placeholder="请选择培训机构" v-model="model.inscode" v-if="title=='新增'" >
			  </j-tree-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="联系地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input v-model="model.address" placeholder="请输入联系地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="照片文件" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photo" style="margin-bottom:0">
              <j-upload v-model="model.photo" fileType="image" :multiple="true"></j-upload>
            </a-form-model-item>
            <a-form-model-item label="准驾车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="dripermitted">
              <j-dict-select-tag type="list" v-model="model.dripermitted" dictCode="sys_stu_traintype"
                                 placeholder="请选择准驾车型" />
            </a-form-model-item>
          </a-col>

          <a-col :span="12">
            <a-form-model-item label="职业资格证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="occupationno">
              <a-input v-model="model.occupationno" placeholder="请输入职业资格证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="职业资格等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="occupationlevel">
              <a-select  v-model="model.occupationlevel" style="width:100%">
              	<a-select-option :value="1">
              		1
              	</a-select-option>
              	<a-select-option :value="2">
              		2
              	</a-select-option>
              	<a-select-option :value="3">
              		3
              	</a-select-option>
              	<a-select-option :value="4">
              		4
              	</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
<!--          <a-col :span="12">

          </a-col>-->
          <a-col :span="12">
            <a-form-model-item label="准教车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="teachpermitted">
              <j-dict-select-tag type="list" v-model="model.teachpermitted" dictCode="sys_stu_traintype"
              	placeholder="请选择准驾车型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="供职状态 " :labelCol="labelCol" :wrapperCol="wrapperCol" prop="employstatus">
			  <j-dict-select-tag type="list" v-model="model.employstatus" dictCode="sys_Serve_status"
			  	placeholder="请输入供职状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="入职日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="hiredate">
			   <j-date placeholder="请输入入职日期" v-model="model.hiredate"  style="width: 100%" dateFormat="YYYYMMDD"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="离职日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="leavedate">
			   <j-date placeholder="请输入离职日期" v-model="model.leavedate"  style="width: 100%" dateFormat="YYYYMMDD"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="备案状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
			  <j-dict-select-tag type="list" v-model="model.status" dictCode="sys_general_status"
			  	placeholder="请输入状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="备案时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择备案时间" v-model="model.crdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="区县编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
              <a-input v-model="model.district" placeholder="请输入区县编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="isplatform">
            <a-form-model-item label="备案平台" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="platform">
							 <j-dict-select-tag type="list" v-model="model.platform" dictCode="sys_platform" placeholder="请选择备案平台"></j-dict-select-tag>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="审核状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditstate">
			  <j-dict-select-tag type="list" v-model="model.auditstate" dictCode="sys_general_audit"
			  	placeholder="请输入审核状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditdate">
              <j-date placeholder="请选择审核时间" v-model="model.auditdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="审核原因" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditreason">
              <a-input v-model="model.auditreason" placeholder="请输入审核原因"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="驾驶证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="drilicence">
              <a-input v-model="model.drilicence" placeholder="请输入驾驶证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="驾驶证初领日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fstdrilicdate">
			  <j-date placeholder="请输入驾驶证初领日期" v-model="model.fstdrilicdate"  style="width: 100%" dateFormat="YYYYMMDD"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
  import {
    formMixins
  } from "@/mixins/formMixins";
  export default {
    name: 'TMExaminerForm',
    components: {
		JTreeSelectDepart
    },
    mixins: [formMixins],
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 8 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           examnum: [
              { required: true, message: '请输入考核员编号!'},
           ],
           name: [
              { required: true, message: '请输入姓名!'},
           ],
           sex: [
              { required: true, message: '请输入1:男性;2:女性!'},
           ],
           idcard: [
              { required: true, message: '请输入身份证号!'},
           ],
           mobile: [
              { required: true, message: '请输入手机号码!'},
           ],
           inscode: [
              { required: true, message: '请输入培训机构编号!'},
           ],
           photo: [
              { required: true, message: '请输入照片文件url!'},
           ],
           dripermitted: [
              { required: true, message: '请输入准驾车型'},
           ],
           employstatus: [
              { required: true, message: '请输入供职状态: 0:在职 1:离职 !'},
           ],
           hiredate: [
              { required: true, message: '请输入入职日期!'},
           ],
           status: [
              { required: true, message: '请输入	状态'},
           ],
           crdate: [
              { required: true, message: '请输入备案时间!'},
           ],
           district: [
              { required: true, message: '请输入区县编号!'},
           ],
           auditdate: [
              { required: true, message: '请输入审核时间!'},
           ],
           examid: [
              { required: true, message: '请输入ID!'},
           ],
					 drilicence: [
					    { required: true, message: '请输入驾驶证号!'},
					 ],
					 fstdrilicdate: [
					    { required: true, message: '请输入驾驶证初领日期!'},
					 ],
        },
        url: {
          add: "/gzpt/tMExaminer/add",
          edit: "/gzpt/tMExaminer/edit",
          queryById: "/gzpt/tMExaminer/queryById"
        },
        isplatform:true,
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      getData2() {
        getAction('/gzpt/tMExaminer/queryById',{
          id: this.model.id
        }).then((res)=>{
          if(res.success){
            this.model = res.result
          } 
        })
      },
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
        this.getData2()
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>