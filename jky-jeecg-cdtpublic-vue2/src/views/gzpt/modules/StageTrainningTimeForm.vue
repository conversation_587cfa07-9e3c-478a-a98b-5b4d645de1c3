<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入培训机构编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入培训机构名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
              <a-input v-model="model.idcard" placeholder="请输入身份证"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuname">
              <a-input v-model="model.stuname" placeholder="请输入姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subject">
              <a-input-number v-model="model.subject" placeholder="请输入科目" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="总学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totaltime">
              <a-input-number v-model="model.totaltime" placeholder="请输入总学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="实操学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="vehicletime">
              <a-input-number v-model="model.vehicletime" placeholder="请输入实操学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="课堂学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classtime">
              <a-input-number v-model="model.classtime" placeholder="请输入课堂学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="模拟学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="simulatortime">
              <a-input-number v-model="model.simulatortime" placeholder="请输入模拟学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="远程学时(分)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="networktime">
              <a-input-number v-model="model.networktime" placeholder="请输入远程学时(分)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="里程(h/km)" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="mileage">
              <a-input-number v-model="model.mileage" placeholder="请输入里程(h/km)" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="报名时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="applydate">
              <a-input v-model="model.applydate" placeholder="请输入报名时间"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核状态 0=未审核,1=审核通过,2=推送失败" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditstate">
              <a-input v-model="model.auditstate" placeholder="请输入审核状态 0=未审核,1=审核通过,2=推送失败"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="operatorname">
              <a-input v-model="model.operatorname" placeholder="请输入审核人"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditdate">
              <j-date placeholder="请选择审核时间" v-model="model.auditdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="市区编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="district">
              <a-input v-model="model.district" placeholder="请输入市区编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <a-input v-model="model.traintype" placeholder="请输入车型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否校验 0=未校验,1=已校验" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ispushsj">
              <a-input-number v-model="model.ispushsj" placeholder="请输入是否校验 0=未校验,1=已校验" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否推送计时 0=未推送,1=推送成功,2=推送失败" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="ispushjs">
              <a-input-number v-model="model.ispushjs" placeholder="请输入是否推送计时 0=未推送,1=推送成功,2=推送失败" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否人脸对比 0=未对比,1=已对比" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isface">
              <a-input-number v-model="model.isface" placeholder="请输入是否人脸对比 0=未对比,1=已对比" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择创建时间" v-model="model.crdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="推送计时时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pushjsdate">
              <j-date placeholder="请选择推送计时时间" v-model="model.pushjsdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="校验时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pushsjdate">
              <j-date placeholder="请选择校验时间" v-model="model.pushsjdate"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="有效学时" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="duration">
              <a-input-number v-model="model.duration" placeholder="请输入有效学时" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否推送银行 0 未推送 1 推送成功 2 推送失败" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isbank">
              <a-input-number v-model="model.isbank" placeholder="请输入是否推送银行 0 未推送 1 推送成功 2 推送失败" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="说明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="resultmeg">
              <a-input v-model="model.resultmeg" placeholder="请输入说明"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="电子教学日志+培训时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="recnums">
              <a-textarea v-model="model.recnums" rows="4" placeholder="请输入电子教学日志+培训时间" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StageTrainningTimeForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/gzpt/stageTrainningTime/add",
          edit: "/gzpt/stageTrainningTime/edit",
          queryById: "/gzpt/stageTrainningTime/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>