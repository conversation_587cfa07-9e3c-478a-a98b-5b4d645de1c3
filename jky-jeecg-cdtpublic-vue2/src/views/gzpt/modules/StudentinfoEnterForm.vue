<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入培训机构编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="证件类型   1:身份证 2:护照 3:军官证 4:其他" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="cardtype">
              <a-input v-model="model.cardtype" placeholder="请输入证件类型   1:身份证 2:护照 3:军官证 4:其他"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
              <a-input v-model="model.idcard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="国籍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="nationality">
              <a-input v-model="model.nationality" placeholder="请输入国籍"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="性别 1:男性;2:女性" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
              <a-input v-model="model.sex" placeholder="请输入性别 1:男性;2:女性"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
              <a-input v-model="model.phone" placeholder="请输入手机号码"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="联系地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
              <a-input v-model="model.address" placeholder="请输入联系地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="成功上传的学员头像照片文件ID" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photo">
              <a-input-number v-model="model.photo" placeholder="请输入成功上传的学员头像照片文件ID" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="业务类型   0:初领 1:增领 9:其他" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="busitype">
              <a-input v-model="model.busitype" placeholder="请输入业务类型   0:初领 1:增领 9:其他"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾驶证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="drilicnum">
              <a-input v-model="model.drilicnum" placeholder="请输入驾驶证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾驶证初领日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="fstdrilicdate">
              <a-input v-model="model.fstdrilicdate" placeholder="请输入驾驶证初领日期"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <a-input v-model="model.traintype" placeholder="请输入培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="报名时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="applydate">
              <a-input v-model="model.applydate" placeholder="请输入报名时间"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="图片路径" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photopath">
              <a-input v-model="model.photopath" placeholder="请输入图片路径"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾校名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入驾校名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员统一编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员统一编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="银行code" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="bankcode">
              <a-input v-model="model.bankcode" placeholder="请输入银行code"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="银行名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="bankname">
              <a-input v-model="model.bankname" placeholder="请输入银行名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="原准驾车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="perdritype">
              <a-input v-model="model.perdritype" placeholder="请输入原准驾车型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="基础费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="basicCost">
              <a-input v-model="model.basicCost" placeholder="请输入基础费用"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第二部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="secondCost">
              <a-input v-model="model.secondCost" placeholder="请输入第二部分费用"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第三部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="thirdCoast">
              <a-input v-model="model.thirdCoast" placeholder="请输入第三部分费用"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="冻结金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="frozenAmount">
              <a-input v-model="model.frozenAmount" placeholder="请输入冻结金额"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
              <a-input v-model="model.coachnum" placeholder="请输入教练编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="是否入学" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isEnter">
              <a-input-number v-model="model.isEnter" placeholder="请输入是否入学" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StudentinfoEnterForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           inscode: [
              { required: true, message: '请输入培训机构编号!'},
           ],
           cardtype: [
              { required: true, message: '请输入证件类型   1:身份证 2:护照 3:军官证 4:其他!'},
           ],
           idcard: [
              { required: true, message: '请输入身份证号!'},
           ],
           nationality: [
              { required: true, message: '请输入国籍!'},
           ],
           sex: [
              { required: true, message: '请输入性别 1:男性;2:女性!'},
           ],
           busitype: [
              { required: true, message: '请输入业务类型   0:初领 1:增领 9:其他!'},
           ],
           traintype: [
              { required: true, message: '请输入培训车型  下列编码单选：  A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P!'},
           ],
           applydate: [
              { required: true, message: '请输入报名时间!'},
           ],
           isEnter: [
              { required: true, message: '请输入是否入学!'},
           ],
        },
        url: {
          add: "/gzpt/studentinfoEnter/add",
          edit: "/gzpt/studentinfoEnter/edit",
          queryById: "/gzpt/studentinfoEnter/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>