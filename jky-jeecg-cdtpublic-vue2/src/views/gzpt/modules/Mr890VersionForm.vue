<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="版本名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="versionName">
              <a-input v-model="model.versionName" placeholder="请输入版本名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="版本url" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="versionUrl">
			  <j-upload v-model="model.versionUrl" fileTypeList="apk" :multiple="true" :number="1" fileType="apk">
			  </j-upload>
			  <p>请上传大小不超过200MB格式为apk的文件</p>
              <a-input v-model="model.versionUrl" placeholder="请输入版本url"  ></a-input>
            </a-form-model-item>
          </a-col>
          
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'Mr890VersionForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           versionName: [
              { required: true, message: '请输入版本名称!'},
           ],
           versionUrl: [
              { required: true, message: '请输入版本url!'},
           ],
        },
        url: {
          add: "/api/tMMr890Version/add",
          edit: "/api/tMMr890Version/edit",
          queryById: "/api/tMMr890Version/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>