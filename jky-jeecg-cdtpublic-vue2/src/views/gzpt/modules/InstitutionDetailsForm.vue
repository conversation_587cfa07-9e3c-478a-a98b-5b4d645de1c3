<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container >
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24" v-if="!model.id">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <j-tree-select-depart placeholder="请选择培训机构" :disabled="formDisabled" v-model="model.inscode"> </j-tree-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.id">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="model.inscode_dictText" placeholder="" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.id">
            <a-form-model-item label="地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="model.address" placeholder=""  :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24" v-if="model.id">
            <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="model.phone" placeholder=""  :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构首页图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="logo">
              <span style="color: crimson;">（请上传小于1M的图片）</span>
              <j-upload
                :disabled="formDisabled"
                v-model="model.logo"
                fileType="image"
                :multiple="true"
                :beforeUpload="beforeUpload"
              ></j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="轮播图" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carouselIds">
              <span style="color: crimson;">（请上传小于1M的图片）</span>
              <j-upload
                :disabled="formDisabled"
                v-model="model.carouselIds"
                fileType="image"
                :multiple="true"
                :beforeUpload="beforeUpload"
              ></j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="简介说明" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="introduction">
              <a-textarea
                v-model="model.introduction"
                placeholder="请输入简介说明"
                autosize
                :disabled="formDisabled"
                :auto-size="{ minRows: 4, maxRows: 6 }"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="经度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lng">
              <a-input
                :disabled="formDisabled"
                v-model="model.lng"
                placeholder="请输入经度，例如：120.000000"
                style="width: 50%; margin-right: 10px;"
              ></a-input
              ><a-button type="primary"
                ><a target="_blank" href="https://api.map.baidu.com/lbsapi/getpoint/index.html"
                  >前往百度地图选取精准坐标</a
                ></a-button
              >
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="纬度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="lat">
              <a-input
                :disabled="formDisabled"
                v-model="model.lat"
                placeholder="请输入纬度，例如：30.000000"
                style="width: 50%;"
              ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="投诉电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="complaintsHotline">
              <a-input  :disabled="formDisabled" v-model="model.complaintsHotline" placeholder="请输入投诉电话"></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import axios from 'axios'
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
export default {
  name: 'InstitutionDetailsForm',
  components: { JTreeSelectDepart },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      model: {},
      valName: null,
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      validatorRules: {
        id: [
          {
            required: true,
            message: '请输入培训机构编号!'
          }
        ]
      },
      url: {
        add: '/gzpt/institutionDetails/add',
        edit: '/gzpt/institutionDetails/edit',
        queryById: '/gzpt/institutionDetails/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    handleAddressPosition(v) {
      getAction('/gzpt/institutionDetails/getLongitudeAndLatitude', {
        address: v.target.value
      }).then(res => {
        if (res.success) {
          this.model.lng = res.result.lng
          this.model.lat = res.result.lat
        }
      })
    },
    getLocation(b) {
      console.log(b, 'bbbbbbbb')
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        const limit = 1024 * 1024
        if (file.size > limit) {
          this.$message.error('上传图片大小不能超过 1M ！')
          return reject(false)
        }
        return resolve(true)
      })
    },
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          if (!this.model.id) {
            httpAction(httpurl, this.model, method)
              .then(res => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          } else {
            that.$confirm({
              title: '提示',
              content:
                '修改驾校信息需要管理部门审核，未通过之前·衢州市学驾一件事综合服务系统显示旧信息，确定提交修改？',
              onOk() {
                httpAction(httpurl, that.model, method)
                  .then(res => {
                    if (res.success) {
                      that.$message.success(res.message)
                      that.$emit('ok')
                    } else {
                      that.$message.warning(res.message)
                    }
                  })
                  .finally(() => {
                    that.confirmLoading = false
                  })
              },
              onCancel() {
                that.confirmLoading = false
              }
            })
          }
        }
      })
    }
  }
}
</script>
