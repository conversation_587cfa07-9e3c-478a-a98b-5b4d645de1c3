<template>
  <div>
    <a-tabs>
      <a-tab-pane key="1" tab="教练车基本信息">
        <a-spin :spinning="confirmLoading">
          <j-form-container :disabled="formDisabled">
            <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
              <a-row>
                <a-col :span="12">
                  <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
                    <j-tree-select-depart placeholder="请选择培训机构" v-model="model.inscode" v-if="title == '新增'">
                    </j-tree-select-depart>
                    <a-input
                      v-model="model.insname"
                      placeholder=""
                      v-if="title != '新增'"
                      :disabled="title != '新增'"
                    ></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="车架号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="franum">
                    <a-input v-model="model.franum" placeholder="请输入车架号"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="发动机号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="engnum">
                    <a-input v-model="model.engnum" placeholder="请输入发动机号"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12" v-if="disabled">
                  <a-form-model-item label="车辆编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carnum">
                    <a-input v-model="model.carnum" placeholder="请输入车辆编号"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="车辆牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licnum">
                    <a-input v-model="model.licnum" placeholder="请输入车辆牌号"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="车牌颜色" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="platecolor">
                    <j-dict-select-tag
                      type="list"
                      v-model="model.platecolor"
                      dictCode="sys_car_color"
                      placeholder="请选择车牌颜色"
                    />
                  </a-form-model-item>
                  <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="perdritype">
                    <j-dict-select-tag
                      type="list"
                      v-model="model.perdritype"
                      dictCode="sys_stu_traintype"
                      placeholder="请选择培训车型"
                    />
                  </a-form-model-item>
                </a-col>
                <!-- <a-col :span="12">
                <a-form-model-item label="图片地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photo">
                  <a-input v-model="model.photo" placeholder="请输入图片地址"  ></a-input>
                </a-form-model-item>
              </a-col> -->
                <a-col :span="12">
                  <a-form-model-item label="车辆照片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photourl">
                    <!--              <a-input v-model="model.photourl" placeholder="请输入图片路径"  ></a-input> -->
                    <span style="color: crimson;">（请上传小于2M的车辆照片）</span>
                    <j-upload
                      v-model="model.photourl"
                      fileType="image"
                      :multiple="true"
                      :number="1"
                      :beforeUpload="beforeUpload"
                    ></j-upload>
                  </a-form-model-item>
                </a-col>

                <a-col :span="12">
                  <a-form-model-item label="生产厂家" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="manufacture">
                    <a-textarea v-model="model.manufacture" rows="4" placeholder="请输入生产厂家" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="车辆品牌" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="brand">
                    <a-input v-model="model.brand" placeholder="请输入车辆品牌"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="车辆型号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="model">
                    <a-input v-model="model.model" placeholder="请输入车辆型号"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="购买日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="buydate">
                    <j-date
                      v-model="model.buydate"
                      placeholder="请输入购买日期"
                      style="width: 100%"
                      date-format="YYYYMMDD"
                    ></j-date>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="报废时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="scrapdate">
                    <j-date placeholder="请选择报废时间" v-model="model.scrapdate" style="width: 100%" />
                  </a-form-model-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item
                    label="上传行驶证"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    props="drivingLicensePath"
                    required
                  >
                    <span style="color: crimson;">（请上传小于2M的图片）</span>
                    <j-upload
                      v-model="model.drivingLicensePath"
                      :multiple="true"
                      fileType="image"
                      :number="1"
                      :beforeUpload="beforeUpload"
                    >
                    </j-upload>
                  </a-form-item>
                </a-col>
                <a-col :span="24">
                  <a-form-item
                    label="上传其他证件"
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    props="attachmentPath"
                  >
                    <j-upload v-model="model.attachmentPath" fileType="image" :number="1"> </j-upload>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form-model>
          </j-form-container>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane key="2" tab="车辆保险信息" v-if="formDisabled" forceRender>
        <a-button
          @click="
            () => {
              insureModalVisible = true
              insureModalInfo = model
            }
          "
          type="primary"
          >新增</a-button
        >
        <a-table
          :dataSource="tableList2.dataSource"
          :columns="tableList1.columns"
          :pagination="{ total: tableList2.total, current: tableList2.pageIndex }"
          @change="pagination => getTableList2(pagination.current)"
        ></a-table>
      </a-tab-pane>
      <a-tab-pane key="3" tab="车辆年检信息" v-if="formDisabled">
        <a-button @click="() => (checkModalVisible = true)" type="primary">新增</a-button>
        <a-table
          :dataSource="tableList1.dataSource"
          :columns="tableList2.columns"
          :pagination="{ total: tableList1.total, current: tableList1.pageIndex }"
          @change="pagination => getTableList1(pagination.current)"
        ></a-table>
      </a-tab-pane>
      <a-tab-pane key="4" tab="车辆过户报废信息" v-if="formDisabled">
        <a-button @click="() => (transferModalVisible = true)" type="primary">新增</a-button>
        <a-table
          :dataSource="tableList3.dataSource"
          :columns="tableList3.columns"
          :pagination="{ total: tableList3.total, current: tableList3.pageIndex }"
          @change="pagination => getTableList3(pagination.current)"
        ></a-table>
      </a-tab-pane>
      <a-tab-pane key="5" v-if="carinfoFormTitle !== '新增'"  tab="行驶证">
        <el-button @click="addModel('drivingLicense')" type="primary">新增</el-button>
        <el-table
          :data="drivingLicenseTableData"
          style="width: 100%;"
          :default-sort="{ prop: 'uploadTime', order: 'descending' }"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="uploadFile" label="行驶证">
            <template slot-scope="scope"> 查看行驶证:<a @click="viewFile(scope.row)">点击查看</a> </template>
          </el-table-column>
          <el-table-column prop="annualInspectionIsValidUntil" label="年检有效期"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleView(scope.row)" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row, 'drivingLicense')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </a-tab-pane>
      <a-tab-pane key="6"  v-if="carinfoFormTitle !== '新增'"  tab="维修保养记录单">
        <el-button @click="addModel('maintenance')" type="primary">新增</el-button>
        <el-table
          :data="maintenanceTableData"
          style="width: 100%;"
          :default-sort="{ prop: 'uploadTime', order: 'descending' }"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="uploadFile" label="维修保养记录单">
            <template slot-scope="scope"> 查看维修保养记录单:<a @click="viewFile(scope.row)">点击查看</a> </template>
          </el-table-column>
          <el-table-column prop="maintenanceTime" label="保养日期"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleView(scope.row)" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row, 'maintenance')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </a-tab-pane>
      <a-tab-pane key="7" v-if="carinfoFormTitle !== '新增'"  tab="行驶里程记录">
        <el-button @click="addModel('mileage')" type="primary">新增</el-button>
        <el-table
          :data="mileageTableData"
          style="width: 100%;"
          :default-sort="{ prop: 'uploadTime', order: 'descending' }"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="车牌号">
            <template slot-scope="scope">
              <span>{{ model.licnum }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="mileage" label="里程">
            <template slot-scope="scope">
              <span>{{ scope.row.mileage }} km</span>
            </template>
          </el-table-column>
          <el-table-column prop="uploadYear" label="年份">
            <template slot-scope="scope">
              <span>{{ scope.row.uploadYear }} 年</span>
            </template>
          </el-table-column>
          <el-table-column prop="quarter" :formatter="quarterFormatter" label="季度"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="view('mileage', scope.row)" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row, 'mileage')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </a-tab-pane>
      <a-tab-pane key="8"  v-if="carinfoFormTitle !== '新增'"  tab="保单">
        <el-button @click="addModel('policy')" type="primary">新增</el-button>
        <el-table
          :data="policyTableData"
          border
          style="width: 100%;"
          :default-sort="{ prop: 'uploadTime', order: 'descending' }"
        >
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="车牌号" width="200">
            <template slot-scope="scope">
              <span>{{ model.licnum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="保险期限">
            <template slot-scope="scope">
              <el-date-picker
                disabled
                type="daterange"
                format="yyyy-MM-dd"
                :value="[scope.row.effectiveTime, scope.row.endTime]"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
              <!--            <el-date-picker :value='scope.row.endTime'></el-date-picker>-->
            </template>
          </el-table-column>
          <!--        <el-table-column prop='quarter' :formatter='quarterFormatter' label='季度'></el-table-column>-->
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleView(scope.row)" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row, 'policy')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </a-tab-pane>
    </a-tabs>
    <a-modal
      title="新增保险信息"
      :visible="insureModalVisible"
      @cancel="
        () => {
          insureModalVisible = false
          insureModalInfo = {}
        }
      "
      @ok="handleAddInsure"
    >
      <a-form>
        <a-row>
          <a-col :span="24">
            <a-form-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="insureModalInfo.insname" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车辆编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="insureModalInfo.carnum" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车架号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="transferModalInfo.franum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="保险公司" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="insureModalInfo.insuranceCompany" placeholder="请输入保险公司"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="保险单号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="insureModalInfo.policyNo" placeholder="请输入保险单号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="交强险到期时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker
                v-model="insureModalInfo.compulsoryInsDate"
                placeholder="请选择交强险到期时间"
              ></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="商业险到期时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker
                v-model="insureModalInfo.commercialInsDate"
                placeholder="请选择商业险到期时间"
              ></a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <a-modal
      title="新增年检信息"
      :visible="checkModalVisible"
      @cancel="
        () => {
          checkModalVisible = false
          checkModalInfo = {}
        }
      "
      @ok="handleAddInspection"
    >
      <a-form>
        <a-row>
          <a-col :span="24">
            <a-form-item label="车辆牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="checkModalInfo.licnum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车架号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="transferModalInfo.franum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车辆编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="checkModalInfo.carnum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车辆年检状态" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-select
                v-model="checkModalInfo.inspectionStatus"
                placeholder="请选择车辆年检状态"
                :options="[
                  {
                    value: 1,
                    label: '正常'
                  },
                  {
                    value: 2,
                    label: '逾期未检验'
                  },
                  {
                    value: 3,
                    label: '无法判断'
                  }
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="年检到期时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker v-model="checkModalInfo.inspectionDate" placeholder="请选择年检到期时间"></a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <a-modal
      title="新增报废信息"
      :visible="transferModalVisible"
      @cancel="
        () => {
          transferModalVisible = false
          transferModalInfo = {}
        }
      "
      @ok="handleAddTransfer"
    >
      <a-form>
        <a-row>
          <a-col :span="24">
            <a-form-item label="车辆牌号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="transferModalInfo.licnum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车架号" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-input v-model="transferModalInfo.franum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="车辆编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="transferModalInfo.carnum" placeholder="" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="过户原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="transferModalInfo.transferReason" placeholder="请输入过户原因"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="过户地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="transferModalInfo.transferAddress" placeholder="请输入过户地址"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="过户时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker v-model="transferModalInfo.transferDate" placeholder="请选择过户时间"></a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="报废" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-select
                v-model="transferModalInfo.isScrapped"
                placeholder="请选择报废状态"
                :options="[
                  {
                    value: 0,
                    label: '未报废'
                  },
                  {
                    value: 1,
                    label: '已报废'
                  }
                ]"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="报废原因" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="transferModalInfo.scrappingReason" placeholder="请输入报废原因"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="报废时间" :labelCol="labelCol" :wrapperCol="wrapperCol" required>
              <a-date-picker v-model="transferModalInfo.scrappingDate" placeholder="请选择报废时间"></a-date-picker>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <!--   行驶证弹窗-->
    <el-dialog
      :visible.sync="drivingLicenseVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="drivingLicenseModel" :rules="saveRules" ref="drivingLicenseModelRef">
        <el-row>
          <el-col>
            <el-form-item label="车牌号" disabled>
              <el-input disabled v-model="model.licnum"></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="年审有效期" prop="annualInspectionIsValidUntil">
              <el-date-picker
                placeholder="请选择年审有效期"
                value-format="yyyy-MM-dd"
                v-model="drivingLicenseModel.annualInspectionIsValidUntil"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="上传行驶证" prop="uploadFile">
              <j-upload v-model="drivingLicenseModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1">
                <el-button size="small" icon="el-icon-upload2">点击上传</el-button>
              </j-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="drivingLicenseVisible = false">关闭</el-button>
        <el-button type="primary" @click="saveAttachment('drivingLicense', drivingLicenseModel)">保存</el-button>
      </span>
    </el-dialog>
    <!--维修保养记录弹窗-->
    <el-dialog
      :visible.sync="maintenanceVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="maintenanceModel" :rules="saveRules" ref="maintenanceModelRef">
        <el-row>
          <el-col>
            <el-form-item label="保养日期" prop="maintenanceTime">
              <el-date-picker
                placeholder="请选择保养日期"
                value-format="yyyy-MM-dd"
                v-model="maintenanceModel.maintenanceTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="上传维修保养记录单扫描件" prop="uploadFile">
              <j-upload v-model="maintenanceModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1">
                <el-button size="small" icon="el-icon-upload2">点击上传</el-button>
              </j-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="maintenanceVisible = false">关闭</el-button>
        <el-button type="primary" @click="saveAttachment('maintenance', maintenanceModel)">保存</el-button>
      </span>
    </el-dialog>
    <!--行驶里程记录-->
    <el-dialog
      :visible.sync="mileageVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="mileageModel" :rules="saveRules" ref="mileageModelRef">
        <el-row>
          <el-col>
            <el-form-item label="车牌号:" disabled>
              <el-input disabled style="width: 250px;" v-model="model.licnum"></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="上传年份" prop="uploadYear">
              <el-select :disabled="title === '查看'" v-model="mileageModel.uploadYear" placeholder="请选择上传年份">
                <el-option v-for="year in years" :key="year" :label="year" :value="year"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="里程:" prop="mileage">
              <el-input
                placeholder="请填写里程"
                style="width: 200px;"
                :disabled="title === '查看'"
                v-model="mileageModel.mileage"
              >
                <template slot="append">km</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="季度:" prop="quarter">
              <el-select placeholder="请选择季度" v-model="mileageModel.quarter" :disabled="title === '查看'">
                <el-option label="第一季度" :value="1"></el-option>
                <el-option label="第二季度" :value="2"></el-option>
                <el-option label="第三季度" :value="3"></el-option>
                <el-option label="第四季度" :value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="mileageVisible = false">关闭</el-button>
        <el-button v-if='title!=="查看"' type="primary" @click="saveAttachment('mileage', mileageModel)">保存</el-button>
      </span>
    </el-dialog>
    <!--保单-->
    <el-dialog
      :visible.sync="policyVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="policyModel" :rules="saveRules" ref="policyModelRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号:" disabled>
              <el-input disabled style="width: 250px;" v-model="model.licnum"></el-input>
            </el-form-item>
          </el-col>
          <el-col span="12">
            <el-form-item label="上传保单:" prop="uploadFile">
              <j-upload v-model="policyModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1"></j-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col span="12">
            <el-form-item label="生效时间:" prop="effectiveTime">
              <el-date-picker
                value-format="yyyy-MM-dd"
                placeholder="请选择生效日期"
                v-model="policyModel.effectiveTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col span="12">
            <el-form-item label="结束时间:" prop="endTime">
              <el-date-picker
                value-format="yyyy-MM-dd"
                placeholder="请选择结束日期"
                v-model="policyModel.endTime"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="policyVisible = false">关闭</el-button>
        <el-button type="primary" @click="saveAttachment('policy', policyModel)">保存</el-button>
      </span>
    </el-dialog>
    <!--    图片预览-->
    <el-dialog
      :visible.sync="previewVisible"
      title="预览"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-image style="width: 100%;height:100%" :src="imgUrl" :preview-src-list="imgUrls"> </el-image>
    </el-dialog>
  </div>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import moment from 'moment'
import { Base64 } from 'js-base64'
import dayjs from 'dayjs'
export default {
  name: 'CarinfoForm',
  components: {
    JTreeSelectDepart
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      carinfoFormTitle:"",
      insureModalVisible: false,
      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 7
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      confirmLoading: false,
      validatorRules: {
        carnum: [
          {
            required: true,
            message: '请输入车辆编号!'
          }
        ],
        inscode: [
          {
            required: true,
            message: '请输入培训机构编号!'
          }
        ],
        franum: [
          {
            required: true,
            message: '请输入车架号!'
          }
        ],
        licnum: [
          {
            required: true,
            message: '请输入车辆牌号!',
            pattern:
              '^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$'
          }
        ],
        drivingLicensePath: [
          {
            required: true,
            message: '请上传行驶证!'
          }
        ],
        scrapdate: [
          {
            required: true,
            message: '请选择报废时间'
          }
        ],
        platecolor: [
          {
            required: true,
            message: '请输入车牌颜色!'
          }
        ],
        manufacture: [
          {
            required: true,
            message: '请输入生产厂家!'
          }
        ],
        brand: [
          {
            required: true,
            message: '请输入车辆品牌!'
          }
        ],
        perdritype: [
          {
            required: true,
            message: '请选择培训车型!'
          }
        ],
        photourl: [
          {
            required: true,
            message: '请上传车辆照片!'
          }
        ],
        buydate: [
          {
            required: true,
            message: '请输入购买时间!'
          }
        ]
      },
      url: {
        save: '/gzpt/attachmentInfo/saveAttachment',
        del: '/gzpt/attachmentInfo/delAttachment',
        get: '/gzpt/attachmentInfo/findTypeList',
        add: '/gzpt/carinfo/add',
        edit: '/gzpt/carinfo/edit',
        queryById: '/gzpt/carinfo/queryById'
      },
      tableList1: {
        pageIndex: 1,
        total: 0,
        dataSource: [],
        columns: [
          {
            title: '车辆牌号',
            align: 'center',
            dataIndex: 'licnum'
          },
          {
            title: '车架号',
            align: 'center',
            dataIndex: 'franum'
          },
          {
            title: '车辆编号',
            align: 'center',
            dataIndex: 'carnum'
          },
          {
            title: '保险公司',
            align: 'center',
            dataIndex: 'insuranceCompany'
          },
          {
            title: '保险单号',
            align: 'center',
            dataIndex: 'policyNo'
          },
          {
            title: '交强险到期时间',
            align: 'center',
            dataIndex: 'compulsoryInsDate'
          },
          {
            title: '商业险到期时间',
            align: 'center',
            dataIndex: 'commercialInsDate'
          }
        ]
      },
      tableList2: {
        pageIndex: 1,
        total: 0,
        dataSource: [],
        columns: [
          {
            title: '车辆牌号',
            align: 'center',
            dataIndex: 'licnum'
          },
          {
            title: '车架号',
            align: 'center',
            dataIndex: 'franum'
          },
          {
            title: '车辆编号',
            align: 'center',
            dataIndex: 'carnum'
          },
          {
            title: '车辆年检状态',
            align: 'center',
            dataIndex: 'inspectionStatus',
            customRender: function(t, r, index) {
              if (t == 1) {
                return '正常'
              } else if (t == 2) {
                return '逾期未检验'
              } else if (t == 3) {
                return '无法判断'
              }
            }
          },
          {
            title: '年检到期时间',
            align: 'center',
            dataIndex: 'inspectionDate'
          }
        ]
      },
      tableList3: {
        pageIndex: 1,
        total: 0,
        dataSource: [],
        columns: [
          {
            title: '车辆牌号',
            align: 'center',
            dataIndex: 'licnum'
          },
          {
            title: '车架号',
            align: 'center',
            dataIndex: 'franum'
          },
          {
            title: '车辆编号',
            align: 'center',
            dataIndex: 'carnum'
          },
          {
            title: '过户原因',
            align: 'center',
            dataIndex: 'transferReason'
          },
          {
            title: '过户地址',
            align: 'center',
            dataIndex: 'transferAddress'
          },
          {
            title: '过户时间',
            align: 'center',
            dataIndex: 'transferDate'
          },
          {
            title: '报废',
            align: 'center',
            dataIndex: 'isScrapped',
            customRender: function(t, r, index) {
              if (t == 0) {
                return '未报废'
              } else if (t == 1) {
                return '报废'
              }
            }
          },
          {
            title: '报废原因',
            align: 'center',
            dataIndex: 'scrappingReason'
          },
          {
            title: '报废时间',
            align: 'center',
            dataIndex: 'scrappingDate'
          }
        ]
      },
      title: '',
      drivingLicenseTableData: [],
      drivingLicenseModel: {},
      drivingLicenseVisible: false,
      attachmentModel: {
        drivingLicense: {}
      },
      maintenanceVisible: false,
      maintenanceModel: {},
      maintenanceTableData: {},

      mileageVisible: false,
      mileageModel: {},
      mileageTableData: {},

      policyVisible: false,
      policyModel: {},
      policyTableData: {},
      //保险期限
      durationOfInsurance: '',
      checkModalVisible: false,
      insureModalInfo: {},
      checkModalInfo: {},
      transferModalVisible: false,
      transferModalInfo: {},
      saveRules: {
        annualInspectionIsValidUntil: [{ required: true, message: '请选择年审有效期', trigger: 'change' }],
        uploadFile: [{ required: true, message: '请上传材料', trigger: 'change' }],
        uploadYear: [{ required: true, message: '请选择上传年份', trigger: 'change' }],
        maintenanceTime: [{ required: true, message: '请选择保养日期', trigger: 'change' }],
        mileage: [{ required: true, message: '请填写里程', trigger: 'change' }],
        quarter: [{ required: true, message: '请选择季度', trigger: 'change' }],
        effectiveTime: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
      },

      previewVisible: false,
      imgUrl: '',
      imgUrls: [],
      years: [2022, 2023, 2024, 2025, 2026] // 可选择的年份
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    setTimeout(() => {
      this.getAttachments()
    }, 100)

    // 获取当前年份
    const currentYear = dayjs().year()
    this.years = new Array(10).fill(undefined).map((item, index) => currentYear + 1 - index)
  },
  methods: {
    view(type, row) {
      this.title = '查看'
      if (type === 'mileage') {
        let jsonRow = JSON.stringify(row)
        this.mileageModel = JSON.parse(jsonRow)
        this.mileageVisible = true
      }
    },
    viewFile(data) {
      if (data.uploadFile !== undefined) {
        let end = data.uploadFile.lastIndexOf('?')
        let start = data.uploadFile.lastIndexOf('.')
        let type = data.uploadFile.substring(start + 1, end)
        if (type !== 'pdf') {
          this.previewVisible = true
          this.imgUrl = data.uploadFile
          this.imgUrls = [data.uploadFile]
          return
        }
        window.open(data.uploadFile, '_blank', 'location=no')
      }
    },
    //保存附件数据
    saveAttachment(subType, contentData) {
      let flag = true
      if (subType === 'drivingLicense') {
        this.$refs.drivingLicenseModelRef.validate(valid => {
          flag = valid
        })
      } else if (subType === 'maintenance') {
        this.$refs.maintenanceModelRef.validate(valid => {
          flag = valid
        })
      } else if (subType === 'mileage') {
        this.$refs.mileageModelRef.validate(valid => {
          flag = valid
        })
      } else if (subType === 'policy') {
        this.$refs.policyModelRef.validate(valid => {
          flag = valid
        })
      }

      if (flag) {
        let data = {
          id: contentData.id, //编辑或覆盖文件的时候此id必传
          associateId: this.model.id,
          type: 3, //1驾培机构，2教练员 3教练车
          subType: subType, //小项的类型

          content: Base64.encode(JSON.stringify(contentData)) //base64加密一下内容
        }
        let that = this
        postAction(this.url.save, data).then(res => {
          if (res.code === 200) {
            that.$message.success(res.message)

            this.drivingLicenseVisible = false
            this.maintenanceVisible = false
            this.mileageVisible = false
            this.policyVisible = false
            this.getAttachments()
          } else {
            that.$message.error(res.message)
          }
        })
        this.getAttachments()
      }
    },
    addModel(type) {
      this.title = '新增'
      if (type === 'drivingLicense') {
        this.drivingLicenseVisible = true
        this.$refs.drivingLicenseModelRef?.resetFields()
        this.drivingLicenseModel = {
          annualInspectionIsValidUntil: '',
          uploadFile: ''
        }
      } else if (type === 'maintenance') {
        this.maintenanceVisible = true
        this.$refs.maintenanceModelRef?.resetFields()
        this.maintenanceModel = {
          maintenanceTime: '',
          uploadFile: ''
        }
      } else if (type === 'mileage') {
        this.mileageVisible = true
        this.$refs.mileageModelRef?.resetFields()
        this.mileageModel = {
          uploadYear: '',
          mileage: '',
          quarter: ''
        }
      } else if (type === 'policy') {
        this.policyVisible = true
        this.$refs.policyModelRef?.resetFields()
        this.policyModel = {
          uploadFile: '',
          effectiveTime: '',
          endTime: ''
        }
      }
    },
    handleView(row) {
      this.viewFile(row)
    },
    handleEdit(row, type) {
      this.title = '编辑'
      let jsonRowStr = JSON.stringify(row)
      if (type === 'drivingLicense') {
        this.drivingLicenseModel = JSON.parse(jsonRowStr)
        this.drivingLicenseVisible = true
      } else if (type === 'maintenance') {
        this.maintenanceModel = JSON.parse(jsonRowStr)
        this.maintenanceVisible = true
      } else if (type === 'mileage') {
        this.mileageModel = JSON.parse(jsonRowStr)
        this.mileageVisible = true
      } else if (type === 'policy') {
        this.policyModel = JSON.parse(jsonRowStr)
        this.policyVisible = true
      }
    },
    handleDel(row) {
      let params = {
        id: row.id
      }
      getAction(this.url.del, params).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.getAttachments()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    getAttachments() {
      let params = {
        associateId: this.model.id,
        type: 3
      }
      getAction(this.url.get, params).then(res => {
        //this.attachmentModel = res.result
        //行驶证
        if (res.result.drivingLicense != undefined) {
          if (!(res.result.drivingLicense instanceof Array)) {
            this.drivingLicenseTableData = [res.result.drivingLicense]
          } else {
            this.drivingLicenseTableData = res.result.drivingLicense
          }
        } else {
          this.drivingLicenseTableData = []
        }
        //维修保养记录
        if (res.result.maintenance != undefined) {
          if (!(res.result.maintenance instanceof Array)) {
            this.maintenanceTableData = [res.result.maintenance]
          } else {
            this.maintenanceTableData = res.result.maintenance
          }
        } else {
          this.maintenanceTableData = []
        }

        //行驶里程记录
        if (res.result.mileage != undefined) {
          if (!(res.result.mileage instanceof Array)) {
            this.mileageTableData = [res.result.mileage]
          } else {
            this.mileageTableData = res.result.mileage
          }
        } else {
          this.mileageTableData = []
        }
        //保单
        if (res.result.policy != undefined) {
          if (!(res.result.policy instanceof Array)) {
            this.policyTableData = [res.result.policy]
          } else {
            this.policyTableData = res.result.policy
          }
        } else {
          this.policyTableData = []
        }
      })
    },
    quarterFormatter(row, column) {
      console.log(row)
      if (row.quarter === 1) {
        return '第一季度'
      } else if (row.quarter === 2) {
        return '第二季度'
      } else if (row.quarter === 3) {
        return '第三季度'
      } else if (row.quarter === 4) {
        return '第四季度'
      }
    },
    handleAddTransfer() {
      if (
        !this.transferModalInfo.transferDate ||
        !this.transferModalInfo.scrappingDate ||
        !this.transferModalInfo.scrappingReason ||
        !this.transferModalInfo.transferReason
      ) {
        this.$message.warning('请将信息补充完整！')
        return
      }
      postAction('/gzpt/carinfo/saveTransfer', {
        licnum: this.transferModalInfo.licnum,
        carnum: this.transferModalInfo.carnum,
        transferReason: this.transferModalInfo.transferReason,
        transferAddress: this.transferModalInfo.transferAddress,
        isScrapped: this.transferModalInfo.isScrapped,
        scrappingReason: this.transferModalInfo.scrappingReason,
        scrappingDate: moment(this.transferModalInfo.scrappingDate).format('YYYY-MM-DD'),
        transferDate: moment(this.transferModalInfo.transferDate).format('YYYY-MM-DD')
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || '提交成功！')
          this.transferModalVisible = false
          this.transferModalInfo = {}
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    beforeUpload(file) {
      return new Promise((resolve, reject) => {
        const limit = 1024 * 1024 * 2
        if (file.size > limit) {
          this.$message.error('上传车辆照片大小不能超过 2M ！')
          return reject(false)
        }
        return resolve(true)
      })
    },
    handleAddInsure() {
      if (
        !this.insureModalInfo.commercialInsDate ||
        !this.insureModalInfo.compulsoryInsDate ||
        !this.insureModalInfo.insuranceCompany ||
        !this.insureModalInfo.policyNo
      ) {
        this.$message.warning('请将信息补充完整！')
        return
      }
      postAction('/gzpt/carinfo/saveInsurance', {
        inscode: this.insureModalInfo.inscode,
        carnum: this.insureModalInfo.carnum,
        insuranceCompany: this.insureModalInfo.insuranceCompany,
        policyNo: this.insureModalInfo.policyNo,
        commercialInsDate: moment(this.insureModalInfo.commercialInsDate).format('YYYY-MM-DD'),
        compulsoryInsDate: moment(this.insureModalInfo.compulsoryInsDate).format('YYYY-MM-DD')
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || '提交成功！')
          this.insureModalVisible = false
          this.insureModalInfo = {}
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleAddInspection() {
      if (!this.checkModalInfo.inspectionStatus || !this.checkModalInfo.inspectionDate) {
        this.$message.warning('请将信息补充完整！')
        return
      }
      postAction('/gzpt/carinfo/saveInspection', {
        licnum: this.checkModalInfo.inscode,
        carnum: this.checkModalInfo.carnum,
        inspectionStatus: this.checkModalInfo.inspectionStatus,
        inspectionDate: moment(this.checkModalInfo.inspectionDate).format('YYYY-MM-DD')
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || '提交成功！')
          this.checkModalVisible = false
          this.checkModalInfo = {}
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    add() {
      this.carinfoFormTitle = '新增'
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.record = record
      this.model = Object.assign({}, record)
      this.insureModalInfo = {
        ...this.model
      }
      this.checkModalInfo = this.model
      this.transferModalInfo = this.model
      this.visible = true
      if (this.formDisabled) {
        this.getTableList1()
        this.getTableList2()
        this.getTableList3()
      }
      // this.model.attachmentPath = [record.attachmentPath]
    },
    handleSubLicense() {
      postAction('/gzpt/carinfo/saveDrivingLicense', {
        id: this.model.id,
        drivingLicensePath: this.model.drivingLicensePath
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || '提交成功！')
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleSubOther() {
      postAction('/gzpt/carinfo/saveAttachment', {
        id: this.model.id,
        attachmentPath: this.model.attachmentPath
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message || '提交成功！')
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (!this.model.drivingLicensePath) {
          this.$message.warning('请将行驶证图片信息补充完整！')
          return
        }
        if (this.model.franum !== this.record.franum && this.model.id) {
          this.$confirm({
            title: '提示',
            content:
              '发现车架号发生变化，将删除该车辆的保险信息，年检信息，行驶证信息，车辆过户报废信息，需驾校重新上传',
            onOk() {
              if (valid) {
                that.confirmLoading = true
                let httpurl = ''
                let method = ''
                if (!that.model.id) {
                  httpurl += that.url.add
                  method = 'post'
                } else {
                  httpurl += that.url.edit
                  method = 'put'
                }
                httpAction(httpurl, that.model, method)
                  .then(res => {
                    if (res.success) {
                      that.$message.success(res.message)
                      that.$emit('ok')
                    } else {
                      that.$message.warning(res.message)
                    }
                  })
                  .finally(() => {
                    that.confirmLoading = false
                  })
              }
            },
            onCancel() {}
          })
        } else {
          if (valid) {
            that.confirmLoading = true
            let httpurl = ''
            let method = ''
            if (!that.model.id) {
              httpurl += that.url.add
              method = 'post'
            } else {
              httpurl += that.url.edit
              method = 'put'
            }
            httpAction(httpurl, that.model, method)
              .then(res => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.$message.success(res.message)
                  that.$emit('ok')
                } else {
                  that.$message.warning(res.message)
                }
              })
              .finally(() => {
                that.confirmLoading = false
              })
          }
        }
      })
    },
    getTableList1() {
      getAction('/gzpt/carinfo/queryInspectionByCarnum', {
        carnum: this.model.carnum
        // pageNo: page
      }).then(res => {
        if (res.success) {
          this.tableList1.dataSource = res.result
          // this.trainTableList.pageIndex = res.result.current;
          // this.trainTableList.total = res.result.total;
        }
      })
    },
    getTableList2() {
      getAction('/gzpt/carinfo/queryInsuranceByCarnum', {
        carnum: this.model.carnum
        // pageNo: page
      }).then(res => {
        if (res.success) {
          this.tableList2.dataSource = res.result
          // this.trainTableList.pageIndex = res.result.current;
          // this.trainTableList.total = res.result.total;
        }
      })
    },
    getTableList3() {
      getAction('/gzpt/carinfo/queryTransferByCarnum', {
        carnum: this.model.carnum
        // pageNo: page
      }).then(res => {
        if (res.success) {
          this.tableList3.dataSource = res.result
          // this.trainTableList.pageIndex = res.result.current;
          // this.trainTableList.total = res.result.total;
        }
      })
    }
  }
}
</script>
