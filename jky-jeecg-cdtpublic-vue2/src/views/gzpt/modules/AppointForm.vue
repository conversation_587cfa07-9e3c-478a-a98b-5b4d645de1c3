<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <j-tree-select-depart  v-if="!model.id" dict="treeselect" v-model="model.inscode" placeholder="请选择培训机构"></j-tree-select-depart>
              <a-input v-else :value="model.insname" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="课堂教学地点" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classroomplace">
              <a-input v-model="model.classroomplace" placeholder="请输入课堂教学地点"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="远程网络教学地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remotenetwork">
              <a-input v-model="model.remotenetwork" placeholder="请输入远程网络教学地址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾驶模拟培训地点" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="virtualdirveplace">
              <a-input v-model="model.virtualdirveplace" placeholder="请输入驾驶模拟培训地点"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="基础和场地驾驶教练场地" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="basicdriveplace">
              <a-input v-model="model.basicdriveplace" placeholder="请输入基础和场地驾驶教练场地"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="道路驾驶路线时间区域" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="roaddriveplace">
              <a-input v-model="model.roaddriveplace" placeholder="请输入道路驾驶路线时间区域"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="预约网址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="appointnetwork">
              <a-input v-model="model.appointnetwork" placeholder="请输入预约网址"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="预约电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="appointphone">
              <a-input v-model="model.appointphone" placeholder="请输入预约电话"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="其它预约方式" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="otherappointway">
              <a-input v-model="model.otherappointway" placeholder="请输入其它预约方式"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学驾人交付给" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stumoneyto">
              <a-input v-model="model.stumoneyto" placeholder="请输入学驾人交付给"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="其它预定" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="otherappoint">
              <a-input v-model="model.otherappoint" placeholder="请输入其它预定"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="合同总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="totalcontract">
              <a-input v-model="model.totalcontract" placeholder="请输入合同总数"  ></a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'

  export default {
    name: 'AppointForm',
    components: {
      JTreeSelectDepart
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/gzpt/appoint/add",
          edit: "/gzpt/appoint/edit",
          queryById: "/gzpt/appoint/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>