<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <j-tree-select-depart
                :disabled="title == '编辑'"
                placeholder="请选择培训机构"
                v-model="model.inscode"
                @change="e => getClassPrice(e, '培训机构')"
              >
              </j-tree-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <!-- 修改 prop 属性，避免与“培训机构”重复 -->
            <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <j-dict-select-tag
                type="list"
                v-model="model.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
                @change="e => getClassPrice(e, '培训车型')"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="套餐图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="photopath">
              <j-upload v-model="model.photopath" fileType="image" :multiple="true" :number="1"> </j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="班级名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classname">
              <a-input v-model="model.classname" placeholder="请输入班级名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="班级简介" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classintro">
              <a-input v-model="model.classintro" placeholder="请输入班级简介"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="班级价格" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="claprice">
              <a-input
                v-model="model.claprice"
                placeholder="请输入班级价格"
                disabled
                style="width: 40%; margin-right: 5px;"
              ></a-input>
              <span>此价格取自合同，如想修改，请修改合同</span>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-model-item label="基础费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="claprice">
              <a-input v-model="model.basicCost" placeholder="请输入基础费用"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第二部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="claprice">
              <a-input v-model="model.secondCost" placeholder="请输入第二部分费用"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第三部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="claprice">
              <a-input v-model="model.thirdCoast" placeholder="请输入第三部分费用"></a-input>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="24">
            <a-form-model-item label="班级备注" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="claremark">
              <a-input v-model="model.claremark" placeholder="请输入班级备注"></a-input>
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="24">
            <a-form-model-item label="班级教练员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coach">
              <a-input v-model="model.coach" placeholder="请输入班级教练员"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="课程编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classno">
              <a-input v-model="model.classno" placeholder="请输入课程编号"></a-input>
            </a-form-model-item>
          </a-col> -->
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JSearchSelectTag from '@/components/dict/JSearchSelectTag'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
export default {
  name: 'TMInstitutionClassForm',
  components: {
    JSearchSelectTag,
    JTreeSelectDepart
  },
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        claprice: [
          {
            required: true,
            message: '请输入班级价格！'
          }
        ],
        inscode: [
          {
            required: true,
            message: '请输入培训机构编码!'
          }
        ],
        classno: [
          {
            required: true,
            message: '请输入课程编号!'
          }
        ]
      },
      url: {
        add: '/zlb/tmc/tMInstitutionClass/add',
        edit: '/zlb/tmc/tMInstitutionClass/edit',
        queryById: '/zlb/tmc/tMInstitutionClass/queryById'
      },
      title: ''
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    getClassPrice(e, type) {
      if (!this.model.inscode) {
        this.$message.warn('请先选择培训机构！')
        return
      }
      let traintype
      if (type === '培训车型') {
        traintype = e
      } else {
        traintype = this.model.traintype
      }
      if (traintype) {
        getAction('/gzpt/cost/getOne', {
          traintype,
          inscode: this.model.inscode
        }).then(res => {
          if (res.success) {
            this.model = {
              ...this.model,
              claprice: res.result.classTuitionFees
            }
            this.$set(this.model, 'claprice', res.result.classTuitionFees)
            this.model.claprice = res.result.classTuitionFees
          } else {
            this.$message.warning(res.message)
            this.model.claprice = null
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
