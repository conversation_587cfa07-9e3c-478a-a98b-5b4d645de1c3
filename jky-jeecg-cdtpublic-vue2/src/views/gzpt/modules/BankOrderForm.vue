<template>
	<a-tabs>
		<a-tab-pane key="1" tab="基本信息">
			<a-spin :spinning="confirmLoading">
				<j-form-container :disabled="formDisabled">
					<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
						<a-row>
							<a-col :span="24">
								<a-form-model-item label="学员姓名" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="stuname">
									<a-input v-model="model.stuname" placeholder="请输入学员姓名"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="身份证" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="idcard">
									<a-input v-model="model.idcard" placeholder="请输入身份证"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="stunum">
									<a-input v-model="model.stunum" placeholder="请输入学员编号"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="驾校编号" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="inscode">
									<a-input v-model="model.inscode" placeholder="请输入驾校编号"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="驾校名称" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="insname">
									<a-input v-model="model.insname" placeholder="请输入驾校名称"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="对应银行编号" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="bankcode">
									<a-input v-model="model.bankcode" placeholder="请输入对应银行编号"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="对应银行名称" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="bankname">
									<a-input v-model="model.bankname" placeholder="请输入对应银行名称"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="订单类型" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="ordertype">
									<j-dict-select-tag type="list" v-model="model.ordertype" dictCode="sys_ordertype"
										placeholder="请选择订单类型" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="划转金额" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="transferAmount">
									<a-input-number v-model="model.transferAmount" placeholder="请输入划转金额"
										style="width: 100%" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="冻结金额余额" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="balanceAmount">
									<a-input-number v-model="model.balanceAmount" placeholder="请输入冻结金额余额"
										style="width: 100%" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="订单状态" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="status">
									<j-dict-select-tag type="list" v-model="model.status" dictCode="sys_order_status"
										placeholder="请选择订单状态" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="圈存id 银行冻结后返回" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="serialno">
									<a-input v-model="model.serialno" placeholder="请输入圈存id 银行冻结后返回"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="createdate">
									<j-date placeholder="请选择创建时间" v-model="model.createdate" style="width: 100%" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="更新时间" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="updatedate">
									<j-date placeholder="请选择更新时间" v-model="model.updatedate" style="width: 100%" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="bosid 对应的阶段报审记录" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="bosid">
									<a-input v-model="model.bosid" placeholder="请输入bosid 对应的阶段报审记录"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="说明" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="remark">
									<a-input v-model="model.remark" placeholder="请输入说明"></a-input>
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="银行扣款或操作时间" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="bankdate">
									<j-date placeholder="请选择银行扣款或操作时间" v-model="model.bankdate" style="width: 100%" />
								</a-form-model-item>
							</a-col>
							<a-col :span="24">
								<a-form-model-item label="失败原因" :labelCol="labelCol" :wrapperCol="wrapperCol"
									prop="reasonerr">
									<a-input v-model="model.reasonerr" placeholder="请输入失败原因"></a-input>
								</a-form-model-item>
							</a-col>
						</a-row>
					</a-form-model>
				</j-form-container>
			</a-spin>
		</a-tab-pane>
		<a-tab-pane key="2" tab="资金划转教练员明细" v-if="formDisabled">
			<a-table :columns="tableList.columns" :dataSource="tableList.dataSource"
				></a-table>
		</a-tab-pane>
	</a-tabs>
</template>

<script>
	import {
		httpAction,
		getAction
	} from '@/api/manage'
	import {
		validateDuplicateValue
	} from '@/utils/util'
	import {
		formMixins
	} from "@/mixins/formMixins";
	export default {
		name: 'BankOrderForm',
		components: {},
		mixins: [formMixins],
		props: {
			//表单禁用
			disabled: {
				type: Boolean,
				default: false,
				required: false
			}
		},
		data() {
			return {
				model: {},
				labelCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 6
					},
				},
				wrapperCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 15
					},
				},
				confirmLoading: false,
				validatorRules: {
					stunum: [{
						required: true,
						message: '请输入学员编号!'
					}, ],
					ordertype: [{
						required: true,
						message: '请输入订单类型'
					}],
				},
				url: {
					add: "/gzpt/bankOrder/add",
					edit: "/gzpt/bankOrder/edit",
					queryById: "/gzpt/bankOrder/queryById"
				},
				tableList:{
					columns:[
						{
							title: '教练名称',
							dataIndex: 'coachName',
							align: 'center',
						},
            {
              title: '有效学时(分钟)',
              dataIndex: 'durationY',
              align: 'center',
            },
						{
							title: '折算学时',
							dataIndex: 'durationYH',
							align: 'center',
						},
						{
							title: '实操单价(元/学时)',
							dataIndex: 'unitPrice',
							align: 'center',
						},
						{
							title: '总金额',
							dataIndex: 'price',
							align: 'center',
						},
					],
					dataSource:[],
					total: 0,
					pageNo: 1,
				},
			}
		},
		computed: {
			formDisabled() {
				return this.disabled
			},
		},
		created() {
			//备份model原始值
			this.modelDefault = JSON.parse(JSON.stringify(this.model));
		},
		methods: {
			getTableList() {
			  getAction('/gzpt/bankOrder/coach', {
			    id: this.model.id,
			  }).then((res) => {
			    this.tableList.dataSource = res.result || [];
			  })
			},
			getData() {
				getAction('/gzpt/bankOrder/queryById', {
					id: this.model.id
				}).then((res) => {
					if (res.success) {
						this.model = res.result
					}
				})
			},
			add() {
				this.edit(this.modelDefault);
			},
			edit(record) {
				this.model = Object.assign({}, record);
				this.visible = true;
				this.getData();
				// this.getTableList()
			},
			submitForm() {
				const that = this;
				// 触发表单验证
				this.$refs.form.validate(valid => {
					if (valid) {
						that.confirmLoading = true;
						let httpurl = '';
						let method = '';
						if (!this.model.id) {
							httpurl += this.url.add;
							method = 'post';
						} else {
							httpurl += this.url.edit;
							method = 'put';
						}
						httpAction(httpurl, this.model, method).then((res) => {
							if (res.success) {
								that.$message.success(res.message);
								that.$emit('ok');
							} else {
								that.$message.warning(res.message);
							}
						}).finally(() => {
							that.confirmLoading = false;
						})
					}

				})
			},
		}
	}
</script>
