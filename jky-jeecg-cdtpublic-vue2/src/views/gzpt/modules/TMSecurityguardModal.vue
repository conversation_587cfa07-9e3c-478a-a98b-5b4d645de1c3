<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <t-m-securityguard-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></t-m-securityguard-form>
  </j-modal>
</template>

<script>

  import TMSecurityguardForm from './TMSecurityguardForm'
  export default {
    name: 'TMSecurityguardModal',
    components: {
      TMSecurityguardForm
    },
    data () {
      return {
        title:'',
        width:900,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.isplatform=false
					this.$refs.realForm.title = this.title
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
					this.$refs.realForm.title = this.title
          if(this.title=='编辑'){
            this.$refs.realForm.isplatform=false
          }else {
            this.$refs.realForm.isplatform=true
          }
          this.$refs.realForm.edit(record);

        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>