<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <coach-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></coach-form>


    <template slot="footer">
      <a-button  @click="getInfoData" v-if="title=='新增'" v-has="'coach:add:zjzz'">浙江中正</a-button>
      <a-button  @click="handleOk" type="primary" v-if="!disableSubmit">确定</a-button>
      <a-button  @click="handleCancel">关闭</a-button>
    </template>
  </j-modal>
</template>

<script>

  import CoachForm from './CoachForm'
  export default {
    name: 'CoachModal',
    components: {
      CoachForm
    },
    data () {
      return {
        title:'',
        width:900,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
			this.$refs.realForm.title = this.title
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
			this.$refs.realForm.title = this.title
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        this.close()
      },
      getInfoData(){
        this.$refs.realForm.getData();
      },
    }
  }
</script>