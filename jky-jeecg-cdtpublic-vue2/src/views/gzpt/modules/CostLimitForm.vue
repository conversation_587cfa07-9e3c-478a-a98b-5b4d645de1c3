<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <j-tree-select-depart  v-if="!model.id" dict="treeselect" v-model="model.inscode" placeholder="请选择培训机构"></j-tree-select-depart>
              <a-input v-else :value="model.insname" disabled></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <j-dict-select-tag type="list" v-model="model.traintype" dictCode="sys_stu_traintype"
              	placeholder="请选择培训车型" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="基础费用上限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="basicUplimit">
              <a-input-number v-model="model.basicUplimit" placeholder="请输入基础费用上限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="基础费用下限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="basicDownlimit">
              <a-input-number v-model="model.basicDownlimit" placeholder="请输入基础费用下限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第二部分上限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="secondUplimit">
              <a-input-number v-model="model.secondUplimit" placeholder="请输入第二部分上限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第二部分下限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="secondDownlimit">
              <a-input-number v-model="model.secondDownlimit" placeholder="请输入第二部分下限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第三部分上限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="thirdUplimit">
              <a-input-number v-model="model.thirdUplimit" placeholder="请输入第三部分上限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第三部分下限" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="thirdDownlimit">
              <a-input-number v-model="model.thirdDownlimit" placeholder="请输入第三部分下限" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'

  export default {
    name: 'CostLimitForm',
    components: {
      JTreeSelectDepart,
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           inscode: [
              { required: true, message: '请选择培训机构!'},
           ],
           basicUplimit: [
              { required: true, message: '请输入基础费用上限!'},
           ],
           basicDownlimit: [
              { required: true, message: '请输入基础费用下限!'},
           ],
           secondUplimit: [
              { required: true, message: '请输入第二部分上限!'},
           ],
           secondDownlimit: [
              { required: true, message: '请输入第二部分下限!'},
           ],
           thirdUplimit: [
              { required: true, message: '请输入第三部分上限!'},
           ],
           thirdDownlimit: [
              { required: true, message: '请输入第三部分下限!'},
           ],
           traintype: [
              { required: true, message: '请输入车型!'},
           ],
        },
        url: {
          add: "/gzpt/costLimit/add",
          edit: "/gzpt/costLimit/edit",
          queryById: "/gzpt/costLimit/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>