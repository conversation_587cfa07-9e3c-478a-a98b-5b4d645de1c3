<template>
	<a-spin :spinning="confirmLoading">
		<j-form-container :disabled="formDisabled">
			<a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
				<a-row>
					<a-col :span="24">
						<a-form-model-item label="设备编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="deviceId">
							<a-input v-model="model.id" placeholder="请输入设备编号" :disabled="title=='edit'"></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="单位名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="centerName">
							<a-select v-model="model.centerName" style="width:100%">
								<a-select-option v-for="(item,index) in deviceList" :key="index"
									:value="item.companyName">
									{{item.companyName}}
								</a-select-option>
							</a-select>
						</a-form-model-item>
					</a-col>
					<a-col :span="24">
						<a-form-model-item label="版本名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="version">
							<a-select v-model="model.version" style="width:100%">
								<a-select-option v-for="(item,index) in versionList" :key="index" :value="item.id">
									{{item.versionName}}
								</a-select-option>
							</a-select>
						</a-form-model-item>
					</a-col>

					<a-col :span="24">
						<a-form-model-item label="是否使用人脸" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="useFace">
							<j-dict-select-tag type="list" v-model="model.useFace" dictCode="sys_mr890_use_face"
								placeholder="请选择是否使用人脸" />
						</a-form-model-item>
					</a-col>
					<a-col :span="24" v-show="title=='edit'">
						<a-form-model-item label="是否启用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="enable">
							<j-dict-select-tag type="list" v-model="model.enable" dictCode="sys_mr890_use_face"
								placeholder="请选择是否启用" />
						</a-form-model-item>
					</a-col>
				</a-row>
			</a-form-model>
		</j-form-container>
	</a-spin>
</template>

<script>
	import {
		httpAction,
		getAction
	} from '@/api/manage'
	import {
		validateDuplicateValue
	} from '@/utils/util'

	export default {
		name: 'Mr890VersionForm',
		components: {},
		props: {
			//表单禁用
			disabled: {
				type: Boolean,
				default: false,
				required: false
			}
		},
		data() {
			return {
				model: {},
				labelCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 5
					},
				},
				wrapperCol: {
					xs: {
						span: 24
					},
					sm: {
						span: 16
					},
				},
				confirmLoading: false,
				validatorRules: {
					centerName: [{
						required: true,
						message: '请输入单位名称!'
					}, ],
					version: [{
						required: true,
						message: '请输入版本号!'
					}, ],
					useFace: [{
						required: true,
						message: '请输入是否使用人脸!'
					}, ],
				},
				url: {
					add: "/gzpt/mr890Info/add",
					edit: "/gzpt/mr890Info/edit",
					queryById: "/gzpt/mr890Info/queryById"
				},
				title: '',
				versionList: [],
				deviceList: [],
        versionId:[],

			}
		},
		computed: {
			formDisabled() {
				return this.disabled
			},
		},
		created() {
			//备份model原始值
			this.modelDefault = JSON.parse(JSON.stringify(this.model));
			this.getDeviceList()
			this.getVersionList()
		},
		methods: {
			add() {
				this.edit(this.modelDefault);
			},
			edit(record) {
				if (record.hasOwnProperty('deviceId')) {
					this.title = 'edit'
				} else {
					this.title = 'add'
				}
				this.model = Object.assign({}, record);
				// console.log(this.versionList)
				// console.log(this.model.version)
				this.visible = true;
			},
			//获取单位名称
			getDeviceList() {
				getAction('/gzpt/insDevice/list/all').then((res) => {
					if (res.success) {
						this.deviceList = res.result
					} else {
						this.$message.error(res.message)
					}
				})
			},
			//获取版本号
			getVersionList() {
				getAction('/api/tMMr890Version/list/all').then((res) => {
					if (res.success) {
						this.versionList = res.result
            if(this.model.version){
              for(let i=0;i<this.versionList.length;i++){
                if(this.model.version==this.versionList[i].versionName){
                  this.model.version=this.versionList[i].id
                }
              }
            }
					} else {
						this.$message.error(res.message)
					}
				})
			},
			submitForm() {
				const that = this;
				// 触发表单验证
				this.$refs.form.validate(valid => {
					if (valid) {
						that.confirmLoading = true;
						let httpurl = '';
						let method = '';
            if (this.model.status==null) {
						// if (!this.model.name) {
							httpurl += this.url.add;
							method = 'post';
						} else {
							httpurl += this.url.edit;
							method = 'put';
						}
						if (this.model.centerName) {
							let obj = this.deviceList.find(item => item.companyName == this.model.centerName)
							this.model.insDeviceId = obj.id
						}
						httpAction(httpurl, this.model, method).then((res) => {
							if (res.success) {
								that.$message.success(res.message);
								that.$emit('ok');
							} else {
								that.$message.warning(res.message);
							}
						}).finally(() => {
							that.confirmLoading = false;
						})
					}

				})
			},
		}
	}
</script>
