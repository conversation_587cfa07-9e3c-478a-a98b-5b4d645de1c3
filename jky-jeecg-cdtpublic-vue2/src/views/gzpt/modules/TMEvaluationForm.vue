<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评价对象" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="evalobject">
              <a-input v-model="model.evalobject" placeholder="请输入评价对象"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评价类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="type">
              <a-input v-model="model.type" placeholder="请输入评价类型"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="总体满意度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overall">
<!--              <a-input-number v-model="model.overall" placeholder="请输入总体满意度 1:一星2:二星3:三星4:四星5:五星（最满意）" style="width: 100%" />-->
              <a-select v-model="model.overall" placeholder="请输入总体满意度">
                <a-select-option value="1">一星</a-select-option>
                <a-select-option value="2">二星</a-select-option>
                <a-select-option value="3">三星</a-select-option>
                <a-select-option value="4">四星</a-select-option>
                <a-select-option value="5">五星</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训部分" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="part">
<!--              <a-input-number v-model="model.part" placeholder="请输入培训部分 1:第一部分2:第二部分3:第三部分4:第四部分" style="width: 100%" />-->
              <a-select v-model="model.part" placeholder="请输入培训部分">
                <a-select-option value="1">第一部分</a-select-option>
                <a-select-option value="2">第二部分</a-select-option>
                <a-select-option value="3">第三部分</a-select-option>
                <a-select-option value="4">第四部分</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评价时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="evaluatetime">
<!--              <a-input v-model="model.evaluatetime" placeholder="请输入评价时间"  ></a-input>-->
                <j-date v-model="model.evaluatetime" placeholder="请输入评价时间"  style="width:100%"></j-date>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评价用语列表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="srvmanner">
<!--              <a-textarea v-model="model.srvmanner" rows="4" placeholder="请输入评价用语列表" />-->
              <a-textarea v-model="model.srvmanner" placeholder="请输入评价用语列表"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="个性化评价" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="personaleval">
<!--              <a-textarea v-model="model.personaleval" rows="4" placeholder="请输入个性化评价" />-->
              <a-textarea v-model="model.personaleval" placeholder="请输入个性化评价"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'TMEvaluationForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/gzpt/tMEvaluation/add",
          edit: "/gzpt/tMEvaluation/edit",
          queryById: "/gzpt/tMEvaluation/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>