<template>
  <a-modal
    title="资金划转"
    :visible="visible"
    @cancel="handleClose"
    @ok="submitForm"
  >
    <a-form-model ref="form" :model="model" :rules="validatorRules">
      <a-row>
        <a-col :span="24">
          <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
            <a-input v-model="model.stunum" placeholder="请输入学员编号"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="学员身份证" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
            <a-input v-model="model.idcard" placeholder="请输入学员身份证"></a-input>
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="订单类型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="remark">
            <j-dict-select-tag type="list" v-model="model.remark" dictCode="sys_ordertype"
              placeholder="请选择订单类型" />
          </a-form-model-item>
        </a-col>
        <a-col :span="24">
          <a-form-model-item label="划转资金" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="transferamount">
            <a-input v-model="model.transferamount" placeholder="请输入划转资金"></a-input>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </a-modal>
</template>

<script>
  import {
    httpAction,
    getAction
  } from '@/api/manage'
  import {
    validateDuplicateValue
  } from '@/utils/util'

  export default {
    name: 'BankRegTransferModal',
    components: {},
    props: {
      visible: {
        type: Boolean,
        default: false,
        required: false
      },
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data() {
      return {
        model: {},
        labelCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 5
          },
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 16
          },
        },
        confirmLoading: false,
        validatorRules: {
          stunum: [{
            required: true,
            message: '请输入学员编号!'
          }],
          bankcode: [{
            required: true,
            message: '请选择银行!'
          }],
        },
        url: {
          queryById: "/gzpt/totalTime/queryById"
        }
      }
    },
    computed: {
      formDisabled() {
        return this.disabled
      },
    },
    created() {
      //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      handleClose() {
        this.$emit('close');
      },
      submitForm() {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if (!this.model.id) {
              httpurl += this.url.add;
              method = 'post';
            } else {
              httpurl += this.url.edit;
              method = 'put';
            }
            this.model.type = 3;
            httpAction(httpurl, this.model, method).then((res) => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
              } else {
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
          this.$emit('reload');
          this.handleClose();
        })
      },
    }
  }
</script>