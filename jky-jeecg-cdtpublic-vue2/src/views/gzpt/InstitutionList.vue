<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode"> </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构全称">
              <a-input v-model="queryParam.name" placeholder="请输入培训机构全称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="分类等级">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.levels"
                dictCode="sys_school_level"
                placeholder="请选择分类等级"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="经营状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.busistatus"
                dictCode="sys_school_status"
                placeholder="请选择经营状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="备案状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.isBankruptcy"
                dictCode="sys_isBankruptcy"
                placeholder="请选择备案状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="计时厂商">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.platform"
                dictCode="sys_platform"
                placeholder="请选择计时厂商"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:institution:add'">新增</a-button>
      <!--      <a-button @click="handleDetailPop" type="primary" icon="plus">详情</a-button>-->
      <a-button type="primary" icon="download" @click="handleExportXls('培训机构表')" v-has="'gzpt:institution:export'"
        >导出</a-button
      >
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <a-button
        @click="handleQueryTransaction"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:institution:queryTransaction'"
        >查询交易记录</a-button
      >
      <a-dropdown :disabled="selectedRowKeys.length == 1 ? false : true">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="PushQgPt" v-has="'gzpt:institution:pushQg'">
            全国平台
          </a-menu-item>
          <a-menu-item key="2" @click="PushJsPt" v-has="'gzpt:institution:pushJsBatch'">
            计时平台
          </a-menu-item>
        </a-menu>
        <a-button v-has="'gzpt:institution:pushJsBatch'">
          推送
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <!--			<a-button @click="handleLock" type="primary" :disabled="selectedRowKeys.length==1?false:true"-->
      <!--				v-has="'gzpt:institution:lockOrUnlock'">解锁</a-button>-->
      <!--			<a-button @click="lock" type="primary" :disabled="selectedRowKeys.length==1?false:true"-->
      <!--				v-has="'gzpt:institution:lockOrUnlock'">锁定</a-button>-->
      <j-dict-select-tag
        type="list"
        dictCode="sys_bank"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:institution:lockBank'"
        placeholder="绑定银行"
        :labelInValue="true"
        @change="bingdingBank"
        v-model="yHcode"
        style="width:120px"
      />
      <!-- 高级查询区域 -->
      <a-button
        @click="handleSyncStatus"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:ins:syncStatus'"
      >
        同步状态
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:institution:deleteBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"> <a-icon type="delete" />删除 </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, selectionRows: selectionRows, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:institution:edit'">编辑</a>
          <a-divider type="vertical" v-has="'gzpt:institution:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link"
              >更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:institution:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <institution-modal ref="modalForm" @ok="modalFormOk"></institution-modal>
    <a-modal v-model="recordVisible" title="查询交易记录" @ok="queryTran" :width="800">
      <a-form-model ref="queryTranForm" :model="queryTranForms" :rules="validatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="驾校编号" prop="inscode" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="queryTranForms.inscode" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾校名称" prop="name" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="queryTranForms.name" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="开始时间" prop="startdate" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryTranForms.startdate"
                style="width:100%"
              >
              </j-date>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="结束时间" prop="enddate" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryTranForms.enddate"
                style="width:100%"
              >
              </j-date>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="银行" prop="bankcode" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="queryTranForms.bankcode"
                dictCode="sys_bank"
                placeholder="请选择银行"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InstitutionModal from './modules/InstitutionModal'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import {
  listInstitution,
  getInstitution,
  delInstitution,
  addInstitution,
  updateInstitution,
  exportInstitution,
  PushQg,
  PushJs,
  queryTransaction,
  importInstitution,
  BatchPushJs
} from '@/api/gzpt/institution'
import { addInstitutionBinding } from '@/api/gzpt/InstitutionBinding'
import { getAction } from '@api/manage'
import dayjs from 'dayjs'
export default {
  name: 'InstitutionList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    InstitutionModal,
    JTreeSelectDepart
  },
  data() {
    return {
      tableData: [
        {
          index: 1,
          uploadTime: ''
        }
      ],
      description: '培训机构表管理页面',
      queryTranForms: {},
      yHcode: '',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '培训机构简称',
          align: 'center',
          dataIndex: 'shortname'
        },
        {
          title: '经营许可证编号',
          align: 'center',
          dataIndex: 'licnum'
        },

        {
          title: '培训机构地址',
          align: 'center',
          dataIndex: 'address'
        },
        {
          title: '法人代表',
          align: 'center',
          dataIndex: 'legal'
        },
        {
          title: '经营范围', // (A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P)
          align: 'center',
          dataIndex: 'busiscope'
        },
        {
          title: '经营状态 ',
          align: 'center',
          dataIndex: 'busistatus_dictText'
        },
        {
          title: '备案状态 ',
          align: 'center',
          dataIndex: 'isBankruptcy',
          customRender: function(text) {
            if (text === 0) {
              return '已备案'
            } else if (text === 1) {
              return '未备案'
            }
          }
        },
        {
          title: '分类等级 ',
          align: 'center',
          dataIndex: 'levels_dictText'
        },

        {
          title: '驾校锁定',
          align: 'center',
          dataIndex: 'islock_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/gzpt/institution/list',
        delete: '/gzpt/institution/delete',
        deleteBatch: '/gzpt/institution/deleteBatch',
        exportXlsUrl: '/gzpt/institution/exportXls',
        importExcelUrl: 'gzpt/institution/importExcel'
      },
      recordVisible: false,
      form: {},
      validatorRules: {
        startdate: [
          {
            required: true,
            message: '请输入开始时间!'
          }
        ],
        enddate: [
          {
            required: true,
            message: '请输入结束时间'
          }
        ],
        bankcode: [
          {
            required: true,
            message: '请输入银行'
          }
        ]
      },
      dictOptions: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      dataSource: [
        {
          shortname: '驾校一',
          licnum: '456789',
          address: '的回复建立数据库差距'
        }
      ],
      queryParam: {
        isBankruptcy: 0
      }
    }
  },
  created() {},
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    //查看交易记录按钮
    handleQueryTransaction() {
      const id = this.selectedRowKeys[0]
      getInstitution(id).then(response => {
        console.log(response.result.inscode, 111)
        this.recordVisible = true
        this.queryTranForms.inscode = response.result.inscode
        this.queryTranForms.name = response.result.name
      })
    },
    queryTranreset() {
      this.queryTranForms = {
        inscode: null,
        name: null,
        startdate: null,
        enddate: null,
        bankcode: null
      }
      this.resetForm('queryTranForm')
    },
    queryTran() {
      this.$refs['queryTranForm'].validate(valid => {
        if (valid) {
          queryTransaction(this.queryTranForms).then(response => {
            if (response.success) {
              this.$message.success('查询中,请到【交易记录查询】中查看结果')
              this.recordVisible = false
            } else {
              this.$message.error(response.message)
            }
          })
        }
      })
    },

    // 推送全国
    PushQgPt(row) {
      const id = this.selectedRowKeys[0]
      PushQg(id).then(response => {
        console.log(response)
        if (response.code == 200) {
          this.$message.success('推送成功')
          this.openpush = false
        } else {
          this.$message.error(response.message)
        }
      })
    },
    // 推送计时
    PushJsPt(row) {
      const id = this.selectedRowKeys[0]
      PushJs(id).then(response => {
        if (response.success) {
          this.$message.success('推送成功')
          this.openpush = false
        } else {
          this.$message.error(response.message)
        }
      })
      this.loadData()
    },

    /** 解锁按钮 */
    handleLock() {
      let that = this
      const id = this.selectedRowKeys[0]
      if (this.selectionRows[0].id == id) {
        if (this.selectionRows[0].islock == 1) {
          this.$confirm({
            title: '提示',
            content: '是否确认解锁该驾校?',
            onOk() {
              that.selectionRows[0].islock = 0
              console.log('islock:', that.selectionRows[0].islock)
              updateInstitution(that.selectionRows[0]).then(response => {
                if (response.success) {
                  that.$message.success('驾校已解锁')
                  that.loadData()
                } else {
                  that.$message.error(response.message)
                }
              })
            },
            onCancel() {}
          })
        } else {
          console.log(22)
          this.$message.warning('该驾校已是解锁状态')
        }
      }
    },

    /** 锁定按钮 */
    lock() {
      let that = this
      const id = this.selectedRowKeys[0]
      if (this.selectionRows[0].id == id) {
        if (this.selectionRows[0].islock == 0) {
          this.$confirm({
            title: '提示',
            content: '是否确认锁定该驾校?',
            onOk() {
              that.selectionRows[0].islock = 1
              console.log('lock:', that.selectionRows[0].islock)
              updateInstitution(that.selectionRows[0]).then(response => {
                that.$message.success('驾校已锁定')
                that.loadData()
              })
            },
            onCancel() {}
          })
        } else {
          this.$message.warning('该驾校已是锁定状态!')
        }
      }
    },
    //绑定银行
    bingdingBank(item, val) {
      let that = this
      // this.yHcode=item;
      console.log(item, this.selectionRows, 1)
      const data = {
        id: this.selectionRows[0].inscode,
        bankcode: item.key,
        bankname: item.label[0].data.attrs.title
      }
      // console.log(data,1);
      addInstitutionBinding(data).then(response => {
        if (response.success) {
          that.$message.success('操作成功')
          that.open = false
          that.loadData()
        } else {
          that.$message.error(response.message)
        }
      })
    },
    // 同步状态
    handleSyncStatus() {
      const id = this.selectedRowKeys[0]
      getAction('/gzpt/institution/syncStatus', {
        id: id
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
