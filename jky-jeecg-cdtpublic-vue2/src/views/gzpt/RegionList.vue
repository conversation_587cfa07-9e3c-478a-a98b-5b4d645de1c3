<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart dict="treeselect" ref="departTree" v-model="queryParam.inscode"
                placeholder="请选择培训机构"></j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教学区域名称">
              <j-input v-model="queryParam.name" placeholder="请输入教学区域名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训车型">
              <a-input v-model="queryParam.vehicletype" placeholder="请输入培训车型" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="备案状态">
              <j-dict-select-tag type="list" v-model="queryParam.status" dictCode="sys_keep" placeholder="请选择备案状态" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="审核状态">
              <j-dict-select-tag type="list" v-model="queryParam.cityAuditstate" dictCode="sys_audit_status"
                                 placeholder="请选择审核状态" />
            </a-form-item>
          </a-col>
<!--          <a-col :xl="6" :lg="7" :md="8" :sm="24">-->
<!--            <a-form-item label="市级审核状态">-->
<!--              <j-dict-select-tag type="list" v-model="queryParam.flag" dictCode="sys_audit_status" placeholder="请选择市级审核状态" />-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-button icon="search" style="margin-right: 8px" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="margin-top:10px">
       <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
<!--      <a-button type="primary" icon="download" @click="handleExportXls('教学区域(电子围栏)管理')">导出</a-button>-->
<!--      <a-dropdown :disabled="selectedRowKeys.length !== 1">
        <a-button>县级审核<a-icon type="down" /></a-button>
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="handleUpdateStatus('countdown', 1)">启用</a-menu-item>
          <a-menu-item key="2" @click="openModal('countdown', 2)">停用</a-menu-item>
        </a-menu>
      </a-dropdown>
      <a-dropdown :disabled="selectedRowKeys.length !== 1">
        <a-button>市级审核<a-icon type="down" /></a-button>
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="handleUpdateStatus('city', 1)">启用</a-menu-item>
          <a-menu-item key="2" @click="openModal('city', 2)">停用</a-menu-item>
        </a-menu>
      </a-dropdown>-->
      <a-button :disabled="selectedRowKeys.length !== 1" @click="handlePush" type="primary"  v-has="'gzpt:regionList:pushjs'">推送计时</a-button>
      <a-dropdown :disabled="selectedRowKeys.length !== 1">
        <a-button>备案设置<a-icon type="down" /></a-button>
        <a-menu slot="overlay">
          <a-menu-item @click="handleBeian(0)">解除备案</a-menu-item>
          <a-menu-item @click="handleBeian(1)">恢复备案</a-menu-item>
        </a-menu>
      </a-dropdown>
      <a-button :disabled="selectedRowKeys.length === 0" type="danger" ghost @click="batchDel">批量删除</a-button>
      <a-button  type="primary" v-has="'region:syncStatus'" ghost @click="() => { syncStatusVisible = true}">同步教学区域</a-button>
      <a-button @click="handleAbolish" type="primary" :disabled="selectedRowKeys.length !== 1" v-has="'gzpt:region:abolish'">教学区域解除备案</a-button>

    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
          style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />

          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>

        </span>

      </a-table>
    </div>

    <region-modal ref="modalForm" @ok="modalFormOk"></region-modal>
    <a-modal
      title="停用原因"
      :visible="reasonModalVisible"
      @ok="handleUpdateStatus()"
      @cancel="() => reasonModalVisible = false"
    >
      <a-textarea v-model="modalReason"></a-textarea>
    </a-modal>
    <a-modal v-model="syncStatusVisible" title="同步电子教学区域" @ok="handleSyncStatus" @cancel="() => { syncStatusVisible = false; syncForm = {}; }" :width="800">
    	<a-form-model ref="syncForm">
    		<a-row>
          <a-col :span="24">
            <a-form-model-item label="培训机构" prop="inscode" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="syncForm.inscode">
              </j-tree-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教学区域编号" prop="seq" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input style="width: 50%" v-model="syncForm.seq"/>
            </a-form-model-item>
          </a-col>
        </a-row>
    	</a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import {mixinDevice} from '@/utils/mixin'
import {JeecgListMixin} from '@/mixins/JeecgListMixin'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'
import RegionModal from './modules/RegionModal'
import {postAction, putAction,getAction} from "@api/manage";

export default {
    name: 'RegionList',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,
      RegionModal
    },
    data() {
      return {
        description: 'region管理页面',
        statusMap: ['未备案', '已备案'],
        flagMap: ['待审核', '同意启用', '不同意启用'],
        cityAuditMap: ['未审核', '审核通过', '审核不通过'],
        // 表头
        columns: [{
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: "center",
            customRender: function(t, r, index) {
              return parseInt(index) + 1;
            }
          },
          {
            title: '驾校名称',
            align: "center",
            dataIndex: 'inscode_dictText'
          },
          {
            title: '教学区域编号',
            align: "center",
            dataIndex: 'seq'
          },
          {
            title: '教学区域名称',
            align: "center",
            dataIndex: 'name'
          },
          {
            title: '培训车型',
            align: "center",
            dataIndex: 'vehicletype'
          },
          {
            title: '审核状态',
            align: "center",
            dataIndex: 'flag_dictText',
          },
          {
            title: '备案状态',
            align: "center",
            dataIndex: 'status',
            customRender: (value) => {
              return this.statusMap[value];
            }
          },
          {
            title: '备案时间',
            align: "center",
            dataIndex: 'crdate',
            customRender: function(text) {
              return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
            }
          },
          // {
          //   title: '审核状态',
          //   align: "center",
          //   dataIndex: 'cityAuditstate_dictText',
          // },
          {
            title: '审核时间',
            align: "center",
            dataIndex: 'auditDate',
            customRender: function(text) {
              return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
            }
          },
          // {
          //   title: '市级审核状态',
          //   align: "center",
          //   dataIndex: 'flag_dictText',
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align: "center",
            fixed: "right",
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: "/gzpt/region/list",
          delete: "/gzpt/region/delete",
          deleteBatch: "/gzpt/region/deleteBatch",
          exportXlsUrl: "/gzpt/region/exportXls",
          importExcelUrl: "gzpt/region/importExcel",

        },
        syncForm: {},
        updateTag: null,
        updateStatus: 0,
        reasonModalVisible: false,
        modalReason: '',
        dictOptions: {},
        superFieldList: [],
        syncStatusVisible: false,
        labelCol: {
        	xs: {
        		span: 24
        	},
        	sm: {
        		span: 5
        	},
        },
        wrapperCol: {
        	xs: {
        		span: 24
        	},
        	sm: {
        		span: 16
        	},
        },
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      handleAbolish(){
       const id =this.selectedRowKeys[0]
        this.$confirm({
          title: '提示',
          content: '确认进行解除备案操作吗？',
          onOk: () => {
            getAction("/gzpt/region/abolish/"+id).then((response) => {
              if (response.success) {
                this.$message.success(response.message);
                this.loadData();
              } else {
                this.$message.error(response.message);
              }
            });
          }
        })

      },
      handleSyncStatus() {
        if (!this.syncForm.inscode || !this.syncForm.seq) {
          this.$message.error('请将信息输入完整！');
          return
        }
        postAction('/gzpt/region/syncStatus', {
          ...this.syncForm
        }).then((res) => {
          if (res.success) {
            this.$message.success('操作成功！');
            this.syncStatusVisible = false;
            this.syncForm = {};
            this.loadData()
          } else {
            this.$message.error(res.message);
          }
        })
      },
      initDictConfig() {},
      getSuperFieldList() {
        let fieldList = [];
        fieldList.push({
          type: 'string',
          value: 'inscode',
          text: '培训机构编号'
        })
        fieldList.push({
          type: 'string',
          value: 'seq',
          text: '教学区域编号 计时平台顺序编号'
        })
        fieldList.push({
          type: 'string',
          value: 'name',
          text: '教学区域名称'
        })
        fieldList.push({
          type: 'string',
          value: 'address',
          text: '教学区域地址'
        })
        fieldList.push({
          type: 'number',
          value: 'area',
          text: '教学区域面积'
        })
        fieldList.push({
          type: 'number',
          value: 'type',
          text: '教学区域类型 1：第二部分；2：第三部分'
        })
        fieldList.push({
          type: 'string',
          value: 'vehicletype',
          text: '培训车型'
        })
        fieldList.push({
          type: 'string',
          value: 'polygon',
          text: '多边形坐标序列 经度1，纬度1；经度2，纬度2；经度3，纬度3；……'
        })
        fieldList.push({
          type: 'number',
          value: 'totalvehnum',
          text: '可容纳车辆数'
        })
        fieldList.push({
          type: 'number',
          value: 'curvehnum',
          text: '已投放车辆数'
        })
        fieldList.push({
          type: 'number',
          value: 'status',
          text: '备案状态： 0：未备案 1：已备案'
        })
        fieldList.push({
          type: 'date',
          value: 'crdate',
          text: '备案时间'
        })
        fieldList.push({
          type: 'number',
          value: 'flag',
          text: '审核状态： 0：待审核 1：同意启用 2：不同意启用'
        })
        fieldList.push({
          type: 'date',
          value: 'auditDate',
          text: '审核时间'
        })
        fieldList.push({
          type: 'string',
          value: 'district',
          text: '区县编号'
        })
        fieldList.push({
          type: 'string',
          value: 'platform',
          text: '备案平台'
        })
        this.superFieldList = fieldList
      },
      handleBeian(status) {
        postAction('/gzpt/region/updataStatus', {
          id: this.selectionRows[0].id,
          status
        }).then((res) => {
          if (res.success) {
            this.loadData(1);
          } else {
            this.$message.error(res.message)
          }
        }).catch((error) => {
          this.$message.error(error)
        })
      },
      openModal(tag, status) {
        this.updateTag = tag;
        this.updateStatus = status;
        this.reasonModalVisible = true;
      },
      handleUpdateStatus(tag, status) {
        let url = '';
        let currentTag = tag || this.updateTag;
        switch(currentTag) {
          case 'countdown':
            url = '/gzpt/region/staAndStop';
            break;
          case 'city':
            url = '/gzpt/region/sjauditstate';
            break;
          default:
            break;
        };
        if (currentTag === 'city') {
          putAction(url, {
            id: this.selectedRowKeys.join(','),
            reason: this.modalReason,
            status: status || this.updateStatus,
          }).then((res) => {
            if (res.success) {
              this.loadData(1);
            } else {
              this.$message.error(res.message)
            }
            this.selectedRowKeys = [];
          }).catch((error) => {
            this.$message.error(error)
          }).finally(() => {
            this.reasonModalVisible = false;
          })
        } else {
          postAction(url, {
            status: status || this.updateStatus,
            id: this.selectedRowKeys.join(','),
            reason: this.modalReason,
          }).then((res) => {
            if (res.success) {
              this.loadData(1);
            } else {
              this.$message.error(res.message)
            }
            this.selectedRowKeys = [];
          }).catch((error) => {
            this.$message.error(error)
          }).finally(() => {
            this.reasonModalVisible = false;
          })
        }
      },
      handlePush(){
        let that = this
        let id=that.selectedRowKeys[0]
        let url='/gzpt/region/pushJs/'+id
        this.$confirm({
          title: '是否确定推送计时？',
          content: '',
          onOk() {
            getAction(url).then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.loadData()
                that.onClearSelected()
              } else {
                that.$message.error(res.message)
              }
            })
          },
          onCancel() {},
        })
      },
    }
  }
</script>
<style lang="less" scoped>
  @import '~@assets/less/common.less';
  /deep/.ant-modal-body {
    padding-top: 0!important;
  }
</style>
