<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
              </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训部分">
              <a-select placeholder="请选择培训部分" v-model="queryParam.part">
                <a-select-option value="1">第一部分</a-select-option>
                <a-select-option value="2">第二部分</a-select-option>
                <a-select-option value="3">第三部分</a-select-option>
                <a-select-option value="4">第四部分</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
<!--          <a-col :xl="4" :lg="7" :md="8" :sm="24">-->
<!--            <a-form-item label="学员编号">-->
<!--             <a-input v-model="queryParam.stunum"></a-input>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
<!--          <a-col :xl="4" :lg="7" :md="8" :sm="24">-->
<!--            <a-form-item label="评价对象">-->
<!--                <a-select placeholder="请选择评价对象" v-model="queryParam.evalobject">-->
<!--                  <a-select-option value="1">教练</a-select-option>-->
<!--                  <a-select-option value="2">驾校</a-select-option>-->
<!--                </a-select>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
<!--          <a-col :xl="4" :lg="7" :md="8" :sm="24">-->
<!--            <a-form-item label="评价类型">-->
<!--              <a-select placeholder="请选择评价类型" v-model="queryParam.type">-->
<!--                <a-select-option value="1">课后评价</a-select-option>-->
<!--                <a-select-option value="2">考后评价</a-select-option>-->
<!--              </a-select>-->
<!--            </a-form-item>-->
<!--          </a-col>-->
          <a-col :xl="8" :lg="8" :md="8" :sm="24">
            <a-form-item label="评价日期">
              <a-date-picker placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.evaluatetimeBegin" valueFormat="YYYY-MM-DD">
              </a-date-picker>
              <span class="query-group-split-cust"></span>
              <a-date-picker placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.evaluatetimeEnd" valueFormat="YYYY-MM-DD">
              </a-date-picker>
            </a-form-item>
          </a-col>
          <a-button type="primary" icon="search" style="margin-right: 8px; margin-bottom: 8px;" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('t_m_evaluation')">导出</a-button>
<!--      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">-->
<!--        <a-button type="primary" icon="import">导入</a-button>-->
<!--      </a-upload>-->
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <t-m-evaluation-modal ref="modalForm" @ok="modalFormOk"></t-m-evaluation-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import TMEvaluationModal from '../modules/TMEvaluationModal'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
  import moment from 'moment'
  export default {
    name: 'TMEvaluationList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,
      TMEvaluationModal
    },
    data () {
      return {
        description: 't_m_evaluation管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'名称',
            align:"center",
            dataIndex: 'name'
          },
          {
            title:'统一编号',
            align:"center",
            dataIndex: 'stunum'
          },
          // {
          //   title:'评价对象',
          //   align:"center",
          //   dataIndex: 'evalobject_dictText'
          // },
          // {
          //   title:'评价类型',
          //   align:"center",
          //   dataIndex: 'type_dictText'
          // },
          {
            title:'驾校名称',
            align:"center",
            dataIndex: 'insname'
          },
          {
            title:'培训部分',
            align:"center",
            dataIndex: 'part_dictText'
          },
          {
            title:'总体满意度',
            align:"center",
            dataIndex: 'overall'
          },
          {
            title:'教学质量评分',
            align:"center",
            dataIndex: 'teachingQuality'
          },
          {
            title:'教学态度评分',
            align:"center",
            dataIndex: 'teachingAttitude'
          },
          {
            title:'车辆状况评分',
            align:"center",
            dataIndex: 'vehicleCondition'
          },
          {
            title:'教学环境评分',
            align:"center",
            dataIndex: 'trainingEnvironment'
          },
          {
            title:'标签',
            align:"center",
            dataIndex: 'srvmanner'
          },
          {
            title:'评价时间',
            align:"center",
            dataIndex: 'evaluatetime'
          },
          // {
          //   title:'个性化评价',
          //   align:"center",
          //   dataIndex: 'personaleval'
          // },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        queryParam: {
          evaluatetimeBegin:  moment().startOf('month').format('YYYY-MM-DD'),
          evaluatetimeEnd: moment().format('YYYY-MM-DD')
        },
        url: {
          list: "/gzpt/tMEvaluation/list",
          delete: "/gzpt/tMEvaluation/delete",
          deleteBatch: "/gzpt/tMEvaluation/deleteBatch",
          exportXlsUrl: "/gzpt/tMEvaluation/exportXls",
          importExcelUrl: "gzpt/tMEvaluation/importExcel",

        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      // this.setTimeRange();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      setTimeRange() {
        let  date = new Date();
        let seperator = "-";
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let strDate = date.getDate();
        if (month >= 1 && month <= 9) {
          month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
        }
        let currentdate = year + "-" + month + "-" + strDate;
        let currentdatemin = year + "-" + month + "-" + "01";
        this.queryParam.evaluatetimeBegin=moment(currentdatemin).format('YYYY-MM-DD')
        this.queryParam.evaluatetimeEnd=moment(currentdate).format('YYYY-MM-DD')
      },
      initDictConfig(){
      },
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>