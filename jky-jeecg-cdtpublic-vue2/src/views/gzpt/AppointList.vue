<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart dict="treeselect" v-model="queryParam.inscode" placeholder="请选择培训机构"></j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-button type="primary" icon="search" style="margin-right: 8px;" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:appoint:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('t_m_appoint')" v-has="'gzpt:appoint:export'">导出</a-button>
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
        @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:appoint:deleteBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
          style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:appoint:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:appoint:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:appoint:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <appoint-modal ref="modalForm" @ok="modalFormOk"></appoint-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'
  import AppointModal from './modules/AppointModal'

  export default {
    name: 'AppointList',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,
      AppointModal
    },
    data() {
      return {
        description: 't_m_appoint管理页面',
        // 表头
        columns: [{
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: "center",
            customRender: function(t, r, index) {
              return parseInt(index) + 1;
            }
          },
          {
            title: '机构全称',
            align: "center",
            dataIndex: 'insname'
          },
          {
            title: '课堂教学地点',
            align: "center",
            dataIndex: 'classroomplace'
          },
          {
            title: '远程网络教学地址',
            align: "center",
            dataIndex: 'remotenetwork'
          },
          {
            title: '驾驶模拟培训地点',
            align: "center",
            dataIndex: 'virtualdirveplace'
          },
          {
            title: '预约网址',
            align: "center",
            dataIndex: 'appointnetwork'
          },
          {
            title: '预约电话',
            align: "center",
            dataIndex: 'appointphone'
          },
          {
            title: '合同总数',
            align: "center",
            dataIndex: 'totalcontract'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: "center",
            fixed: "right",
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: "/gzpt/appoint/list",
          delete: "/gzpt/appoint/delete",
          deleteBatch: "/gzpt/appoint/deleteBatch",
          exportXlsUrl: "/gzpt/appoint/exportXls",
          importExcelUrl: "gzpt/appoint/importExcel",

        },
        dictOptions: {},
        superFieldList: [],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig() {},
      getSuperFieldList() {
        let fieldList = [];
        fieldList.push({
          type: 'string',
          value: 'inscode',
          text: '机构编号'
        })
        fieldList.push({
          type: 'string',
          value: 'insname',
          text: '机构全称'
        })
        fieldList.push({
          type: 'string',
          value: 'classroomplace',
          text: '课堂教学地点'
        })
        fieldList.push({
          type: 'string',
          value: 'remotenetwork',
          text: '远程网络教学地址'
        })
        fieldList.push({
          type: 'string',
          value: 'virtualdirveplace',
          text: '驾驶模拟培训地点'
        })
        fieldList.push({
          type: 'string',
          value: 'basicdriveplace',
          text: '基础和场地驾驶教练场地'
        })
        fieldList.push({
          type: 'string',
          value: 'roaddriveplace',
          text: '道路驾驶路线时间区域'
        })
        fieldList.push({
          type: 'string',
          value: 'appointnetwork',
          text: '预约网址'
        })
        fieldList.push({
          type: 'string',
          value: 'appointphone',
          text: '预约电话'
        })
        fieldList.push({
          type: 'string',
          value: 'otherappointway',
          text: '其它预约方式'
        })
        fieldList.push({
          type: 'string',
          value: 'stumoneyto',
          text: '学驾人交付给'
        })
        fieldList.push({
          type: 'string',
          value: 'otherappoint',
          text: '其它预定'
        })
        fieldList.push({
          type: 'string',
          value: 'totalcontract',
          text: '合同总数'
        })
        fieldList.push({
          type: 'string',
          value: 'signendtime',
          text: '合同截止日期'
        })
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>