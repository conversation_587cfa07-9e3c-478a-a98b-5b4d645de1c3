<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart
                dict="treeselect"
                v-model="queryParam.inscode"
                placeholder="请选择培训机构"
              ></j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="银行名称">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.bankcode"
                dictCode="sys_bank"
                placeholder="请选择银行"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员编号">
              <a-input v-model="queryParam.stunum" placeholder="请输入学员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员姓名">
              <a-input v-model="queryParam.stuname" placeholder="请输入学员姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="冻结余额">
              <a-input v-model="queryParam.balanceAmount" placeholder="请输入冻结余额" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.status"
                dictCode="bank_status"
                placeholder="请选择状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="身份证">
              <a-input v-model="queryParam.idcard" placeholder="请输入身份证" />
            </a-form-item>
          </a-col>
          <a-button type="primary" icon="search" style="margin-right: 8px;" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:reg:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('学员开户冻结对象')" v-has="'gzpt:reg:export'">
        导出
      </a-button>
      <a-upload
        name="file"
        :showUploadList="false"
        :multiple="false"
        :headers="tokenHeader"
        :action="importExcelUrl"
        v-has="'gzpt:reg:dr'"
        @change="handleImportExcel"
      >
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-button type="primary" @click="() => (jiedongModalVisible = true)" v-has="'gzpt:reg:zzjdJx'">资金解冻</a-button>
      <a-button
        @click="handleSingleSync"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:reg:transfer'"
      >
        建行资金划转
      </a-button>
      <a-button
        @click="handleUnFreeze"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:reg:unFreeze'"
      >
        建行资金解冻
      </a-button>
      <a-button type="primary" @click="() => (changeBankModalVisible = true)" v-has="'gzpt:reg:updateBank'">
        更改所属银行
      </a-button>
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
<!--          <a-menu-item key="1" @click="batchDel">-->
<!--            <a-icon type="delete" v-has="'gzpt:reg:remove'" />删除-->
<!--          </a-menu-item>-->
          <a-menu-item key="2" @click="checkBalanceAmount(null)" v-has="'gzpt:reg:queryAmount'">
            冻结余额查询
          </a-menu-item>
          <a-menu-item key="3" @click="checkTrade(null)" v-has="'gzpt:reg:queryTransaction'">
            交易记录查询
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>
        已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a> 项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:reg:edit'">编辑</a>
          <a-divider type="vertical" v-has="'gzpt:reg:edit'" />
          <!-- <a-dropdown>
            <a class="ant-dropdown-link">建行开户<a-icon type="down" /></a>
            <a-menu slot="overlay">
             <a-menu-item key="1" @click="signContract(record)"><a-icon type="delete"/>确定签约</a-menu-item>
              <a-menu-item key="2" @click="cancelSignContract(record)">撤销签约</a-menu-item>
              <a-menu-item key="3" @click="checkTrade(null)">签约查询</a-menu-item>
            </a-menu>
          </a-dropdown>
          <a-divider type="vertical" /> -->
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:reg:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:reg:jhfrozen'">
                <a @click="handleFreeze(record, '/gzpt/bankReg/frozen')">建行冻结</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:reg:jxfrozen'">
                <!-- 修改冻结操作，打开弹窗输入冻结金额 -->
                <a @click="openFreezeModal(record, '/gzpt/bankReg/jxfrozen')">冻结</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:reg:transfer'">
                <a @click="handleFreeze(record, '/gzpt/bankReg/transfer', 'post')">资金划转</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <!-- 模态窗口部分 -->
    <bank-reg-modal ref="modalForm" @ok="modalFormOk"></bank-reg-modal>
    <bank-reg-jiedong-modal
      :visible="jiedongModalVisible"
      @close="() => (jiedongModalVisible = false)"
      @reload="loadData"
    ></bank-reg-jiedong-modal>
    <bank-reg-jianhang-jiedong-modal
      :visible="jiedongJianhangModalVisible"
      @close="() => (jiedongJianhangModalVisible = false)"
      @reload="loadData"
    ></bank-reg-jianhang-jiedong-modal>
    <bank-reg-change-bank-modal
      :visible="changeBankModalVisible"
      @close="() => (changeBankModalVisible = false)"
      @reload="loadData"
    ></bank-reg-change-bank-modal>
    <a-modal v-model="syncOpen" title="建行资金划转" @ok="submitSyncForm" :width="800">
      <a-form-model ref="syncForm" :model="syncForm" :rules="syncFormValidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="划转金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="transAmount">
              <a-input v-model="syncForm.transAmount" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <!-- 新增冻结金额输入弹窗 -->
    <a-modal v-model="freezeModalVisible" title="冻结金额" @ok="submitFreezeForm" :width="800">
      <a-form-model ref="freezeForm" :model="freezeForm" :rules="freezeFormValidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="冻结金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="frozenamount">
              <a-input v-model="freezeForm.frozenamount" placeholder="请输入冻结金额" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'
import BankRegModal from './modules/BankRegModal'
import BankRegJiedongModal from './modules/BankRegJiedongModal'
import BankRegJianhangJiedongModal from './modules/BankRegJianhangJiedongModal'
import BankRegChangeBankModal from './modules/BankRegChangeBankModal'

export default {
  name: 'BankRegList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    JTreeSelectDepart,
    BankRegModal,
    BankRegJiedongModal,
    BankRegJianhangJiedongModal,
    BankRegChangeBankModal
  },
  data() {
    return {
      syncOpen: false,
      syncForm: {},
      syncFormValidatorRules: {
        transAmount: [
          {
            required: true,
            message: '请输入划转金额!'
          }
        ]
      },
      // 新增冻结金额弹窗相关数据
      freezeModalVisible: false,
      freezeForm: {
        frozenamount: null
      },
      freezeRecord: null,
      freezeUrl: '',
      freezeFormValidatorRules: {
        frozenamount: [
          {
            required: true,
            message: '请输入冻结金额!'
          }
        ]
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      description: '学员开户冻结对象管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'stuname'
        },
        {
          title: '学员编号',
          align: 'center',
          dataIndex: 'stunum'
        },
        {
          title: '身份证',
          align: 'center',
          dataIndex: 'idcard'
        },
        {
          title: '驾校名称',
          align: 'center',
          dataIndex: 'insname'
        },
        {
          title: '订单状态',
          align: 'center',
          dataIndex: 'status',
          customRender: text => {
            return this.statusMap[text]
          }
        },
        {
          title: '银行名称',
          align: 'center',
          dataIndex: 'bankname'
        },
        {
          title: '初始冻结金额',
          align: 'center',
          dataIndex: 'balanceAmount'
        },
        {
          title: '冻结余额',
          align: 'center',
          dataIndex: 'remainingAmount'
        },
        {
          title: '银行冻结时间',
          align: 'center',
          dataIndex: 'bankdate'
        },
        {
          title: '说明',
          align: 'center',
          dataIndex: 'bankmsg'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/gzpt/bankReg/list',
        delete: '/gzpt/bankReg/delete',
        deleteBatch: '/gzpt/bankReg/deleteBatch',
        exportXlsUrl: '/gzpt/bankReg/exportXls',
        importExcelUrl: 'gzpt/bankReg/importExcel'
      },
      jiedongModalVisible: false,
      changeBankModalVisible: false,
      jiedongJianhangModalVisible: false,
      statusMap: ['开户', '开户成功', '开户失败', '冻结成功', '冻结中', '冻结失败'],
      dictOptions: {},
      superFieldList: {}
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleSingleSync() {
      this.syncOpen = true
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({
        type: 'string',
        value: 'stunum',
        text: '学员编号'
      })
      fieldList.push({
        type: 'string',
        value: 'inscode',
        text: '驾校编号'
      })
      fieldList.push({
        type: 'string',
        value: 'insname',
        text: '驾校名称'
      })
      fieldList.push({
        type: 'string',
        value: 'serialno',
        text: '圈存id 银行冻结后返回'
      })
      fieldList.push({
        type: 'int',
        value: 'status',
        text: '订单状态 0 开户 1 开户成功  2 开户失败 3 冻结成功 4冻结失败'
      })
      fieldList.push({
        type: 'string',
        value: 'bankcode',
        text: '对应银行编号'
      })
      fieldList.push({
        type: 'date',
        value: 'bankdate',
        text: '银行操作时间'
      })
      fieldList.push({
        type: 'string',
        value: 'bankname',
        text: '对应银行名称'
      })
      fieldList.push({
        type: 'number',
        value: 'balanceAmount',
        text: '冻结金额余额'
      })
      fieldList.push({
        type: 'string',
        value: 'stuname',
        text: '学员名字'
      })
      fieldList.push({
        type: 'string',
        value: 'blockid',
        text: '冻结编号'
      })
      fieldList.push({
        type: 'string',
        value: 'corpseqno',
        text: '企业流水号'
      })
      fieldList.push({
        type: 'string',
        value: 'bankmsg',
        text: '错误原因'
      })
      this.superFieldList = fieldList
    },
    checkBalanceAmount(record) {
      getAction('/gzpt/bankReg/queryAmount', {
        ids: record ? record.id : this.selectedRowKeys.join(',')
      }).then(res => {
        if (res.success) {
          this.$message.success('操作中,前往【冻结余额查询模块】中查看结果')
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    checkTrade(record) {
      getAction('/gzpt/bankReg/queryTransaction', {
        ids: record ? record.id : this.selectedRowKeys.join(',')
      }).then(res => {
        if (res.success) {
          this.$message.success('操作中,前往【交易记录查询模块】中查看结果')
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleFreeze(record, url, method) {
      if (method === 'post') {
        postAction(url, {
          ids: record.id,
          id: record.id
        }).then(res => {
          if (res.success) {
            this.$message.success('操作成功！')
            this.loadData()
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        getAction(url, {
          ids: record.id,
          id: record.id
        }).then(res => {
          if (res.success) {
            this.$message.success('操作成功！')
          } else {
            this.$message.error(res.message)
          }
        })
      }
    },
    // 新增：打开冻结金额弹窗，并保存当前记录和请求url
    openFreezeModal(record, url) {
      this.freezeRecord = record
      this.freezeUrl = url
      this.freezeModalVisible = true
    },
    // 新增：提交冻结金额表单，参数中带上 frozenamount
    submitFreezeForm() {
      this.$refs['freezeForm'].validate(valid => {
        if (valid) {
          this.loading = true
          getAction(this.freezeUrl, {
            id: this.freezeRecord.id,
            frozenamount: this.freezeForm.frozenamount
          })
            .then(response => {
              if (response.success) {
                this.$message.success(response.message)
                this.loadData()
              } else {
                this.$message.error(response.message)
              }
            })
            .catch(() => {})
            .finally(() => {
              this.loading = false
              this.freezeModalVisible = false
              this.freezeForm.frozenamount = null
            })
        }
      })
    },
    submitSyncForm() {
      this.syncForm.id = this.selectedRowKeys[0]
      this.$refs['syncForm'].validate(valid => {
        this.loading = true
        if (valid) {
          // 避免异步问题，暂时把第二个请求嵌在第一个里面
          postAction('/gzpt/bankReg/transfer', this.syncForm)
            .then(response => {
              if (response.success) {
                this.$message.success(response.message)
                this.syncOpen = false
                this.loading = false
                this.loadData()
              } else {
                this.$message.error(response.message)
              }
            })
            .catch(() => {
              this.loading = false
            })
            .finally(() => {
              this.loading = false
              this.syncOpen = false
              this.syncForm.transAmount = null
            })
        }
      })
    },
    handleUnFreeze() {
      let params = {
        id: this.selectedRowKeys[0]
      }
      // 避免异步问题，暂时把第二个请求嵌在第一个里面
      postAction('/gzpt/bankReg/unFreeze', params)
        .then(response => {
          if (response.success) {
            this.$message.success(response.message)
            this.loadData()
          } else {
            this.$message.error(response.message)
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';
</style>
