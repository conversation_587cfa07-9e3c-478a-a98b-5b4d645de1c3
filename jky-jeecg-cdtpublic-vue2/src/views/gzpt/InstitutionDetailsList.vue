<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode"> </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:institutionDetails:add'">新增</a-button>
      <a-dropdown :disabled="selectedRowKeys.length !== 1" v-has="'gzpt:institutionDetails:audit'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="handlePublish(1)">通过</a-menu-item>
          <a-menu-item key="2" @click="handlePublish(2)">不通过</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px" type="primary">发布<a-icon type="down" /></a-button>
      </a-dropdown>
      <a-button
        type="primary"
        icon="download"
        @click="handleExportXls('培训机构附加信息对象')"
        v-has="'gzpt:institutionDetails:export'"
        >导出</a-button
      >
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:institutionDetails:deleteBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"

        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, selectionRows: selectionRows, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:institutionDetails:edit'">修改申请</a>

          <a-divider type="vertical" v-has="'gzpt:institutionDetails:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:institutionDetails:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <institution-details-modal ref="modalForm" @ok="modalFormOk"></institution-details-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { postAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import InstitutionDetailsModal from './modules/InstitutionDetailsModal'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'

export default {
  name: 'InstitutionDetailsList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    InstitutionDetailsModal,
    JTreeSelectDepart,
  },
  data() {
    return {
      description: '培训机构附加信息对象管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '培训机构名称',
          align: 'center',
          dataIndex: 'inscode_dictText',
          ellipsis: true,
          width: 250,
        },
        {
          title: '地址',
          align: 'center',
          dataIndex: 'address',
          ellipsis: true,
          width: 250,
        },
        // {
        //   title: '联系电话',
        //   align: 'center',
        //   dataIndex: 'phone',
        //   // ellipsis:true,
        // },
        {
          title: '简介说明',
          align: 'center',
          dataIndex: 'introduction',
		      ellipsis:true,
        },
        // {
        //   title: '纬度',
        //   align: 'center',
        //   dataIndex: 'lat',
        //   // ellipsis:true,
        // },
        // {
        //   title: '经度',
        //   align: 'center',
        //   dataIndex: 'lng',
        //   // ellipsis:true,
        // },
        // {
        //   title: '投诉电话',
        //   align: 'center',
        //   dataIndex: 'complaintsHotline',
        // },
        {
          title: '发布情况',
          align: 'center',
          dataIndex: 'publish',
          width: 100,
          customRender: (text) => {
            return text === 1 ? '已发布':'未发布'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
         // fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/gzpt/institutionDetails/list',
        delete: '/gzpt/institutionDetails/delete',
        deleteBatch: '/gzpt/institutionDetails/deleteBatch',
        exportXlsUrl: '/gzpt/institutionDetails/exportXls',
        importExcelUrl: 'gzpt/institutionDetails/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
      auditModalVisible: false,
      auditReason: ''
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'inscode', text: '培训机构编号' })
      fieldList.push({ type: 'string', value: 'carouselIds', text: '轮播图 图片组' })
      fieldList.push({ type: 'string', value: 'introduction', text: '简介说明' })
      fieldList.push({ type: 'string', value: 'lngLat', text: '经纬度' })
      fieldList.push({ type: 'string', value: 'businessImgIds', text: '营业执照照片' })
      fieldList.push({ type: 'string', value: 'licenceImgIds', text: '道路运输许可证' })
      fieldList.push({ type: 'string', value: 'billboardImgIds', text: '收费公示栏' })
      fieldList.push({ type: 'date', value: 'cradate', text: '创建时间' })
      this.superFieldList = fieldList
    },
    handlePublish(publish) {
      postAction('/gzpt/institutionDetails/publish', {
        publish: publish,
        inscode: this.selectionRows[0].id
      }).then((res) => {
        if (res.success) {
          this.auditModalVisible = false;
          this.$message.success('操作成功！');
          this.auditReason = '';
          this.loadData(1);
        } else {
          this.$message.error(res.message);
        }
      })
    }
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>