<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="医院名称">
              <a-input v-model="queryParam.name" placeholder="请输入医院名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-form-item label="类型">
              <a-select v-model="queryParam.hospitalType" placeholder="请选择类型">
                <a-select-option value="1">医院</a-select-option>
                <a-select-option value="2">警医邮</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('base_zlb_hospital_info')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="text" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>



      </a-table>
    </div>

    <base-zlb-hospital-info-modal ref="modalForm" @ok="modalFormOk"></base-zlb-hospital-info-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import BaseZlbHospitalInfoModal from './modules/BaseZlbHospitalInfoModal'

  export default {
    name: 'BaseZlbHospitalInfoList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      BaseZlbHospitalInfoModal
    },
    data () {
      return {
        description: 'base_zlb_hospital_info管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'医院名称',
            align:"center",
            dataIndex: 'name'
          },
          {
            title:'医院logo',
            align:"center",
            dataIndex: 'logo',
			scopedSlots: { customRender: 'imgSlot' }
          },
          {
            title:'医院地址',
            align:"center",
            dataIndex: 'address'
          },
          {
            title:'经度',
            align:"center",
            dataIndex: 'longitude'
          },
          {
            title:'纬度',
            align:"center",
            dataIndex: 'latitude'
          },
          {
            title:'等级',
            align:"center",
            dataIndex: 'level'
          },
          {
            title:'电话',
            align:"center",
            dataIndex: 'phone'
          },
          // {
          //   title:'详情',
          //   align:"center",
          //   dataIndex: 'detail',
					// 	customRender:function (t,r,index) {
					// 	  return <p style="width:200px;white-space: pre-wrap;">{t}</p>;
					// 	}
          // },
          {
            title:'行政区划名称',
            align:"center",
            dataIndex: 'areaName'
          },
          {
            title:'体检均价',
            align:"center",
            dataIndex: 'price'
          },

          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/zlb/baseZlbHospitalInfo/list",
          delete: "/zlb/baseZlbHospitalInfo/delete",
          deleteBatch: "/zlb/baseZlbHospitalInfo/deleteBatch",
          exportXlsUrl: "/zlb/baseZlbHospitalInfo/exportXls",
          importExcelUrl: "zlb/baseZlbHospitalInfo/importExcel",

        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'name',text:'医院名称'})
        fieldList.push({type:'string',value:'address',text:'医院地址'})
        fieldList.push({type:'number',value:'longitude',text:'经度'})
        fieldList.push({type:'number',value:'latitude',text:'纬度'})
        fieldList.push({type:'string',value:'level',text:'等级'})
        fieldList.push({type:'string',value:'phone',text:'电话'})
        fieldList.push({type:'string',value:'detail',text:'详情'})
        fieldList.push({type:'string',value:'areaCode',text:'行政区划code'})
        fieldList.push({type:'string',value:'areaName',text:'行政区划名称'})
        fieldList.push({type:'number',value:'orderNum',text:'预约量'})
        fieldList.push({type:'number',value:'score',text:'评分'})
        fieldList.push({type:'number',value:'price',text:'体检均价'})
        fieldList.push({type:'string',value:'logo',text:'医院logo'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>