<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">

            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="培训机构">
                <!-- <a-tree-select checkable  :treeData="departTree" placeholder="请选择培训机构" /> -->
                <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
                </j-tree-select-depart>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="学员姓名">
                <a-input v-model="queryParam.stuname" placeholder="请输入学员姓名" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="身份证号">
                <a-input v-model="queryParam.idcard" placeholder="请输入身份证号" />
              </a-form-item>
            </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员编号">
              <a-input v-model="queryParam.stunum" placeholder="请输入学员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="审核状态">
              <j-dict-select-tag type="list" v-model="queryParam.auditStatus" dictCode="sys_general_audit"
                                 placeholder="请选择审核状态" />
            </a-form-item>
          </a-col>

          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
<!--      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:stuLogout:add'">新增</a-button>-->
<!--      <a-button type="primary" icon="download" @click="handleExportXls('学员注销申请')" v-has="'gzpt:stuLogout:dc'">导出</a-button>-->
      <!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown
        :disabled="selectedRowKeys.length=== 1 ? false : true" v-has="'gzpt:stuLogoutApply:audit'"
      >
        <a-menu slot="overlay" v-has="'gzpt:stuLogoutApply:audit'">
          <a-menu-item key="1" @click="handleAudit(true)">通过</a-menu-item>
          <a-menu-item key="2" @click="handleAudit(false)">不通过</a-menu-item>
        </a-menu>
        <a-button type="primary" style="margin-left: 8px">审核<a-icon type="down" /></a-button>
      </a-dropdown>
      <a-button type="primary" style="margin-left: 8px" :disabled="selectedRowKeys.length !== 1" @click="handleRePushJg">手动推送监管</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:stuLogout:delete'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:stuLogout:edit'">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:stuLogout:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <stu-logout-apply-modal ref="modalForm" @ok="modalFormOk"></stu-logout-apply-modal>
    <a-modal v-model="auditReasonModalVisible" title="审核不通过" @ok="submitAudit" :width="800">
      <a-form-model>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="原因">
              <a-textarea v-model="auditRemark"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
  import { postAction } from '@/api/manage'
  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import StuLogoutApplyModal from './modules/StuLogoutApplyModal'
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'

  export default {
    name: 'StuLogoutApplyList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,
      StuLogoutApplyModal
    },
    data () {
      return {
        description: '学员注销申请管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'学员名称',
            align:"center",
            dataIndex: 'stuname'
          },
          {
            title:'身份证号',
            align:"center",
            dataIndex: 'idcard'
          },
          {
            title:'学员编号',
            align:"center",
            dataIndex: 'stunum'
          },
          {
            title:'驾校名称',
            align:"center",
            dataIndex: 'insname'
          },
          {
            title:'注销原因',
            align:"center",
            dataIndex: 'stuReason'
          },
          {
            title:'申请时间',
            align:"center",
            dataIndex: 'registration'
          },
          {
            title:'审核状态',
            align:"center",
            dataIndex: 'auditStatus_dictText'
          },
          {
            title:'审核时间',
            align:"center",
            dataIndex: 'auditTime'
          },
          {
            title:'审核备注',
            align:"center",
            dataIndex: 'auditRemark'
          },
          {
            title:'推送监管',
            align:"center",
            dataIndex: 'status_dictText'
          },
          {
            title:'注销业务备注',
            align:"center",
            dataIndex: 'remark'
          },
          {
            title:'推送监管时间',
            align:"center",
            dataIndex: 'pushDate'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/gzpt/stuLogoutApply/list",
          delete: "/gzpt/stuLogoutApply/delete",
          deleteBatch: "/gzpt/stuLogoutApply/deleteBatch",
          exportXlsUrl: "/gzpt/stuLogoutApply/exportXls",
          importExcelUrl: "/gzpt/stuLogoutApply/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
        auditReasonModalVisible: false,
        auditRemark: '',
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'inscode',text:'驾校编码'})
        fieldList.push({type:'string',value:'insname',text:'驾校名称'})
        fieldList.push({type:'string',value:'stuname',text:'学员名称'})
        fieldList.push({type:'string',value:'idcard',text:'身份证号'})
        fieldList.push({type:'string',value:'stunum',text:'学员编号'})
        fieldList.push({type:'string',value:'stuReason',text:'注销原因'})
        fieldList.push({type:'datetime',value:'applydate',text:'申请时间'})
        fieldList.push({type:'int',value:'auditStatus',text:'驾校审核状态'})
        fieldList.push({type:'string',value:'auditRemark',text:'驾校审核备注'})
        fieldList.push({type:'datetime',value:'auditDate',text:'驾校审核时间'})
        this.superFieldList = fieldList
      },
      handleRePushJg() {
        postAction('/gzpt/stuLogoutApply/rePushJg', {
          id: this.selectedRowKeys.join(','),
        }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.loadData();
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleAudit(status) {
        if (status) {
          postAction('/gzpt/stuLogoutApply/audit', {
            id: this.selectedRowKeys.join(','),
            auditStatus: 1,
          }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData();
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.auditReasonModalVisible = true;
        }
      },
      submitAudit() {
        postAction('/gzpt/stuLogoutApply/audit', {
          id: this.selectedRowKeys.join(','),
          auditStatus: 2,
          auditRemark: this.auditRemark
        }).then((res) => {
          if (res.success) {
            this.auditReasonModalVisible = false;
            this.$message.success(res.message)
            this.loadData();
          } else {
            this.$message.error(res.message)
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>