<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<!-- <a-col :xl="6" :lg="7" :md="8" :sm="24">
				<a-form-item label="地市">
					<a-input v-model="queryParam.licnum" placeholder="请输入地市" />
				</a-form-item>
			</a-col> -->
					<!-- <a-col :xl="6" :lg="7" :md="8" :sm="24">
				<a-form-item label="所属区县">
					<a-input v-model="queryParam.licnum" placeholder="请输入区县" />
				</a-form-item>
			</a-col> -->
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="培训机构">
							<j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
							</j-tree-select-depart>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="教练姓名">
							<a-input v-model="queryParam.name" placeholder="请输入教练姓名" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="身份证号码">
							<a-input v-model="queryParam.idcard" placeholder="请输入身份证号码" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24" style="margin-bottom: 10px;">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
			<a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:securityguard:add'">新增</a-button>
			<a-button type="primary" icon="download" @click="handleExportXls('安全员管理')"
				v-has="'gzpt:securityguard:export'">导出</a-button>
			<!-- <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload> -->
			<a-dropdown :disabled="selectedRowKeys.length==1?false:true">
				<a-menu slot="overlay">
					<a-menu-item key="1" @click="PushQg" v-has="'gzpt:securityguard:pushQg'">
						全国平台
					</a-menu-item>
					<a-menu-item key="2" @click="PushJs" v-has="'gzpt:securityguard:pushjs'">
						计时平台
					</a-menu-item>
				</a-menu>
				<a-button> 推送
					<a-icon type="down" />
				</a-button>
			</a-dropdown>
			<!-- <a-button @click="sendMessage" type="primary" :disabled="selectedRowKeys.length==1?false:true">发送通知</a-button>
	 <a-button @click="approved" type="primary" :disabled="selectedRowKeys.length>0?false:true">批量审核通过</a-button> -->
			<a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:securityguard:deleteBatch'">
				<a-menu slot="overlay">
					<a-menu-item key="1" @click="batchDel">
						<a-icon type="delete" />删除
					</a-menu-item>
				</a-menu>
				<a-button style="margin-left: 8px"> 批量操作
					<a-icon type="down" />
				</a-button>
			</a-dropdown>
		</div>

		<!-- table区域-begin -->
		<div>
			<div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
				<i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
					style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
				<a style="margin-left: 24px" @click="onClearSelected">清空</a>
			</div>

			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="max-width:80px;font-size: 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>

				<span slot="action" slot-scope="text, record">
					<a @click="handleEdit(record)" v-has="'gzpt:securityguard:edit'">编辑</a>

					<a-divider type="vertical" v-has="'gzpt:securityguard:edit'" />
					<a-dropdown>
						<a class="ant-dropdown-link">更多
							<a-icon type="down" />
						</a>
						<a-menu slot="overlay">
							<a-menu-item>
								<a @click="handleDetail(record)">详情</a>
							</a-menu-item>
							<a-menu-item v-has="'gzpt:securityguard:remove'">
								<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
									<a>删除</a>
								</a-popconfirm>
							</a-menu-item>
						</a-menu>
					</a-dropdown>
				</span>

			</a-table>
		</div>

		<t-m-securityguard-modal ref="modalForm" @ok="modalFormOk"></t-m-securityguard-modal>
	</a-card>
</template>

<script>
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
	import TMSecurityguardModal from './modules/TMSecurityguardModal'
	import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
	import {
		PushTmsQgPt,
		PushTmsJsPt,
	} from "@/api/gzpt/institution";
	export default {
		name: 'TMSecurityguardList',
		mixins: [JeecgListMixin, mixinDevice],
		components: {
			TMSecurityguardModal,
			JTreeSelectDepart
		},
		data() {
			return {
				description: '安全员管理管理页面',
				// 表头
				columns: [{
						title: '序号',
						dataIndex: '',
						key: 'rowIndex',
						width: 60,
						align: "center",
						customRender: function(t, r, index) {
							return parseInt(index) + 1;
						}
					},
					{
						title: '区县编号',
						align: "center",
						dataIndex: 'district'
					},
					{
						title: '培训机构',
						align: "center",
						dataIndex: 'insname'
					},

					{
						title: '姓名',
						align: "center",
						dataIndex: 'name'
					},

					{
						title: '身份证号',
						align: "center",
						dataIndex: 'idcard'
					},
					{
						title: '手机号码',
						align: "center",
						dataIndex: 'mobile'
					},

					{
						title: '准驾车型',
						align: "center",
						dataIndex: 'dripermitted'
					},
					{
						title: '准教车型',
						align: "center",
						dataIndex: 'teachpermitted'
					},
					{
						title: '在职状态',
						align: "center",
						dataIndex: 'employstatus_dictText'
					},
					{
						title: '备案状态',
						align: "center",
						dataIndex: 'status_dictText'
					},

					{
						title: '审核状态',
						align: "center",
						dataIndex: 'auditstate_dictText'
					},
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/gzpt/tMSecurityguard/list",
					delete: "/gzpt/tMSecurityguard/delete",
					deleteBatch: "/gzpt/tMSecurityguard/deleteBatch",
					exportXlsUrl: "/gzpt/tMSecurityguard/exportXls",
					importExcelUrl: "gzpt/tMSecurityguard/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
			}
		},
		created() {

		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			// 推送全国
			PushQg(row) {
				console.log(this.selectionRows[0])
				const id = this.selectionRows[0].id;
				PushTmsQgPt(id).then((response) => {
					console.log(response);
					if (response.code == 200) {
						this.$message.success("推送成功");
						this.openpush = false;
					} else {
						this.$message.error(response.message);
					}
					this.loadData();
				});
			},
			// 推送计时
			PushJs(row) {
				const id = this.selectionRows[0].id;
				PushTmsJsPt(id).then((response) => {
					console.log(response);
					if (response.success) {
						this.$message.success('推送成功');
						this.openpush = false;
					} else {
						this.$message.error(response.message);
					}
				});
				this.loadData();
			},
		}
	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
