<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="教练员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
              <a-input v-model="model.coachnum" placeholder="请输入教练员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教练员名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachname">
              <a-input v-model="model.coachname" placeholder="请输入教练员名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="评价次数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="evaTimes">
              <a-input-number v-model="model.evaTimes" placeholder="请输入评价次数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教学质量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="quality">
              <a-input-number v-model="model.quality" placeholder="请输入教学质量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="教学态度" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="attitude">
              <a-input-number v-model="model.attitude" placeholder="请输入教学态度" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="车辆状况" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="carCondition">
              <a-input-number v-model="model.carCondition" placeholder="请输入车辆状况" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="总体评价" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="overall">
              <a-input-number v-model="model.overall" placeholder="请输入总体评价" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'EvaSumCoachForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           coachnum: [
              { required: true, message: '请输入教练员编号!'},
           ],
           coachname: [
              { required: true, message: '请输入教练员名称!'},
           ],
           evaTimes: [
              { required: true, message: '请输入评价次数!'},
           ],
           quality: [
              { required: true, message: '请输入教学质量!'},
           ],
           attitude: [
              { required: true, message: '请输入教学态度!'},
           ],
           carCondition: [
              { required: true, message: '请输入车辆状况!'},
           ],
           overall: [
              { required: true, message: '请输入总体评价!'},
           ],
        },
        url: {
          add: "/gzpt/evaSumCoach/add",
          edit: "/gzpt/evaSumCoach/edit",
          queryById: "/gzpt/evaSumCoach/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>