<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="单位名称">
							<a-input v-model="queryParam.companyName" placeholder="请输入版本名称" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="驾校编号列表">
							<j-tree-select-depart placeholder="请选择驾校" v-model="queryParam.inscodeList">
							</j-tree-select-depart>
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="模拟器供应商">
							<j-dict-select-tag type="list" v-model="queryParam.simulatorVendor" dictCode="sys_ins_device_supplier"
								placeholder="请选择模拟器供应商" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="联系人">
							<a-input v-model="queryParam.contactPerson" placeholder="请输入联系人" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="联系方式">
							<a-input v-model="queryParam.mobile" placeholder="请输入联系方式" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
			<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
			<a-button type="primary" icon="download" @click="handleExportXls('模拟器接入平台汇总')" v-has="'gzpt:totaltime:export'" >导出</a-button>
      <a-button @click="handleOn" type="primary"  :disabled="selectedRowKeys.length!=1" v-has="'gzpt:insDevice:on'">启用</a-button>
      <a-button @click="handleOff" type="primary"  :disabled="selectedRowKeys.length!=1" v-has="'gzpt:insDevice:off'">停用</a-button>

		</div>

		<!-- table区域-begin -->
		<div>
			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="max-width:80px;font-size: 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>
				<span slot="action" slot-scope="text, record">
					<a @click="handleEdit(record)" v-has="'gzpt:insDevice:edit'">编辑</a>
					<!-- <a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />
					<a @click="handleDetail(record)">详情</a> -->
					<a-divider type="vertical" v-has="'gzpt:insDevice:remove'" />
					<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)"  v-has="'gzpt:insDevice:remove'">
						<a>删除</a>
					</a-popconfirm>
				</span>
			</a-table>
		</div>
		<InsDeviceModal ref="modalForm" @ok="modalFormOk"></InsDeviceModal>
	</a-card>
</template>

<script>
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
	import InsDeviceModal from './modules/InsDeviceModal'
	import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
  import {  putAction } from '@/api/manage'
	export default {
		name: 'Mr890Info',
		mixins: [JeecgListMixin, mixinDevice],
		components: {
			InsDeviceModal,
			JTreeSelectDepart
		},
		data() {
			return {
				description: '模拟器接入平台汇总',
				dataSource2: [{
					deviceId: '123888',
					centerName: '东阳-恒通',
					status: '在线',
					isUseFace: '启用'
				}],
				// 表头
				columns: [
					{
						title: '驾校设备接入ID',
						align: "center",
						dataIndex: 'id'
					},
					{
						title: '单位名称',
						align: "center",
						dataIndex: 'companyName'
					},
					{
						title: '设备安装地址',
						align: "center",
						dataIndex: 'address'
					},
					{
						title: '联系人',
						align: "center",
						dataIndex: 'contactPerson'
					},
					{
						title: '联系方式',
						align: "center",
						dataIndex: 'mobile'
					},
					{
						title: '申请接入IP地址',
						align: "center",
						dataIndex: 'ipList'
					},
					{
						title: '模拟器供应商',
						align: "center",
						dataIndex: 'simulatorVendor_dictText'
					},
					{
						title: '应配设备数量',
						align: "center",
						dataIndex: 'deviceTotal'
					},
					{
						title: '实际设备数量',
						align: "center",
						dataIndex: 'deviceQuantity'
					},
          {
            title: '启用状态',
            align: "center",
            dataIndex: 'enable',
            customRender:function (t,r,index) {
              switch (t){
                case 0:
                  return "停用"
                  break;
                case 1:
                  return "启用"
                  break;
                default:
                  break;
              }
            }
          },
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/gzpt/insDevice/list",
					delete: "/gzpt/insDevice/delete",
					deleteBatch: "/gzpt/insDevice/deleteBatch",
					exportXlsUrl: "/gzpt/insDevice/exportXls",
					importExcelUrl: "gzpt/insDevice/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
			}
		},
		created() {
			this.getSuperFieldList();
		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			initDictConfig() {},
      handleOn(){
        var data = {
          id: this.selectedRowKeys[0],
          enable:1,
        }
        let  that=this
        this.$confirm({
          title: '是否确认启用选择的设备？',
          content: '',
          onOk() {
            putAction('/gzpt/insDevice/able', data).then((res) => {
              if (res.success) {
                that.loadData(1);
                that.$message.success('操作成功')
                // that.$message.success(res.message)
              } else {
                that.$message.error(res.message)
              }
              that.selectedRowKeys = [];
              that.selectionRows = [];
            }).catch((error) => {
              that.$message.error(error)
            })
          },
          onCancel() {

          },
        });

      },
      handleOff(){
        var data = {
          id: this.selectedRowKeys[0],
          enable:0,
        }
        let  that=this
        this.$confirm({
          title: '是否确认停用选择的设备？',
          content: '',
          onOk() {
            putAction('/gzpt/insDevice/able', data).then((res) => {
              if (res.success) {
                that.loadData(1);
                that.$message.success('操作成功')
                // that.$message.success(res.message)
              } else {
                that.$message.error(res.message)
              }
              that.selectedRowKeys = [];
              that.selectionRows = [];
            }).catch((error) => {
              that.$message.error(error)
            })
          },
          onCancel() {

          },
        });
      },


		}
	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
