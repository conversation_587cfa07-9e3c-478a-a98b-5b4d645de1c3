<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
          	<a-form-item label="科目">
          		<j-dict-select-tag type="list" v-model="queryParam.subject" dictCode="sys_km"
          			placeholder="请选择科目" />
          	</a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
          	<a-form-item label="培训类型">
          		<j-dict-select-tag type="list" v-model="queryParam.classtype" dictCode="sys_type"
          			placeholder="请选择培训类型" />
          	</a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
          	<a-form-item label="类型">
          		<j-dict-select-tag type="list" v-model="queryParam.type" dictCode="sys_TrainSubject"
          			placeholder="请选择类型" />
          	</a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
          	<a-form-item label="车型">
          		<j-dict-select-tag type="list" v-model="queryParam.trainCarType" dictCode="sys_stu_traintype"
          			placeholder="请选择车型" />
          	</a-form-item>
          </a-col>
          <a-button type="primary" icon="search" style="margin-right: 8px; margin-bottom: 8px;" @click="loadData(1)">搜索</a-button>
          <a-button icon="reload" @click="searchReset">重置</a-button>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:credit:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('额定学时对象')" v-has="'gzpt:credit:export'">导出</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:credit:deleteBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:credit:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:credit:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:credit:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <train-subject-credit-modal ref="modalForm" @ok="modalFormOk"></train-subject-credit-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import TrainSubjectCreditModal from './modules/TrainSubjectCreditModal'

  export default {
    name: 'TrainSubjectCreditList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      TrainSubjectCreditModal
    },
    data () {
      return {
        description: '额定学时对象管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'科目',
            align:"center",
            dataIndex: 'subject',
            customRender: (text) => {
              return this.subjectMap[text -1]
            }
          },
          {
            title:'培训类型',
            align:"center",
            dataIndex: 'classtype',
            customRender: (text) => {
              return this.classMap[text -1]
            }
          },
          {
            title:'额定学时',
            align:"center",
            dataIndex: 'creditration'
          },
          {
            title:'额定里程',
            align:"center",
            dataIndex: 'mileageration'
          },
          {
            title:'车型',
            align:"center",
            dataIndex: 'trainCarType'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/gzpt/trainSubjectCredit/list",
          delete: "/gzpt/trainSubjectCredit/delete",
          deleteBatch: "/gzpt/trainSubjectCredit/deleteBatch",
          exportXlsUrl: "/gzpt/trainSubjectCredit/exportXls",
          importExcelUrl: "gzpt/trainSubjectCredit/importExcel",
          
        },
        subjectMap: ['科目一', '科目二', '科目三', '科目四'],
        classMap: ['实操', '课堂', '模拟', '远程'],
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'int',value:'subject',text:'科目'})
        fieldList.push({type:'int',value:'classtype',text:'培训类型'})
        fieldList.push({type:'int',value:'creditration',text:'额定学时'})
        fieldList.push({type:'int',value:'mileageration',text:'额定里程'})
        fieldList.push({type:'string',value:'memo',text:'0'})
        fieldList.push({type:'string',value:'trainCarCode',text:'车型code'})
        fieldList.push({type:'string',value:'trainCarType',text:'车型'})
        fieldList.push({type:'int',value:'type',text:'类型 1新大纲 2 老大刚'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>