<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员名称">
              <a-input v-model="queryParam.name" placeholder="请输入学员名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员编号">
              <a-input v-model="queryParam.stunum" placeholder="请输入学员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="身份证">
              <a-input v-model="queryParam.idcard" placeholder="请输入身份证" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" icon="search" style="margin-right: 8px;" @click="loadData(1)">搜索</a-button>
            <a-button icon="reload" @click="searchReset">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:stuTrans:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('学员转校')" v-has="'gzpt:stuTrans:dc'">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
        @change="handleImportExcel">
        <a-button type="primary" icon="import" v-has="'gzpt:stuTrans:dr'">导入</a-button>
      </a-upload>
      <a-button @click="handleProcess" type="primary" :disabled="selectedRowKeys.length !==1"
                v-has="'gzpt:stuTrans:doTrans'">手动开启流程</a-button>
      <a-button @click="handleUnFreeze" type="primary" :disabled="selectedRowKeys.length !==1"
                v-has="'gzpt:stuTrans:doTrans'">手动解冻</a-button>
      <a-button @click="handleNotifyJg" type="primary" :disabled="selectedRowKeys.length !==1"
                v-has="'gzpt:stuTrans:doTrans'">手动推送监管</a-button>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal"
        @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown :disabled="selectedRowKeys.length=== 1 ? false : true">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="handleAudit(true)">通过</a-menu-item>
          <a-menu-item key="1" @click="handleAudit(false)">不通过</a-menu-item>
        </a-menu>
        <a-button type="primary" style="margin-left: 8px">审核<a-icon type="down" /></a-button>
      </a-dropdown>
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:stuTrans:sc'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
          style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
        :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:stuTrans:edit'">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:stuTrans:sc'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <stu-transfer-modal ref="modalForm" @ok="modalFormOk"></stu-transfer-modal>
    <a-modal v-model="auditReasonModalVisible" title="审核不通过" @ok="submitAudit" :width="800">
      <a-form-model>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="原因">
              <a-textarea v-model="insReason"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import StuTransferModal from './modules/StuTransferModal'
  import JTreeSelectDepart from "@comp/common/JTreeSelectDepart.vue";
  import {
    getAction,
    postAction
  } from '@/api/manage'
  export default {
    name: 'StuTransferList',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,
      StuTransferModal
    },
    data() {
      return {
        description: '学员转校管理页面',
        auditReasonModalVisible: false,
        // 表头
        columns: [{
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: "center",
            customRender: function(t, r, index) {
              return parseInt(index) + 1;
            }
          },
          {
            title: '学员名称',
            align: "center",
            dataIndex: 'name'
          },
          {
            title: '学员编号',
            align: "center",
            dataIndex: 'stunum'
          },
          {
            title: '身份证号',
            align: "center",
            dataIndex: 'idcard'
          },
          {
            title: '转出驾校名称',
            align: "center",
            dataIndex: 'oldInsname'
          },
          {
            title: '转入驾校名称',
            align: "center",
            dataIndex: 'insname'
          },
          {
            title: '转校理由',
            align: "center",
            dataIndex: 'stuReason'
          },
          {
            title: '申请时间',
            align: "center",
            dataIndex: 'crdate'
          },
          {
            title: '转出驾校审核状态',
            align: "center",
            dataIndex: 'insAudit_dictText'
          },
          {
            title: '转出驾校备注',
            align: "center",
            dataIndex: 'insReason'
          },
          {
            title: '转出驾校审核时间',
            align: "center",
            dataIndex: 'insAuditDate'
          },
          {
            title: '转入驾校审核状态',
            align: "center",
            dataIndex: 'status_dictText'
          },
          {
            title: '转入驾校备注',
            align: "center",
            dataIndex: 'reason'
          },
          {
            title: '转入驾校审核时间',
            align: "center",
            dataIndex: 'auditDate'
          },
          {
            title: '流程',
            align: "center",
            dataIndex: 'flow_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: "center",
            fixed: "right",
            width: 147,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: "/gzpt/stuTransfer/list",
          delete: "/gzpt/stuTransfer/delete",
          deleteBatch: "/gzpt/stuTransfer/deleteBatch",
          exportXlsUrl: "/gzpt/stuTransfer/exportXls",
          importExcelUrl: "gzpt/stuTransfer/importExcel",

        },
        dictOptions: {},
        superFieldList: [],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig() {},
      getSuperFieldList() {
        let fieldList = [];
        fieldList.push({
          type: 'string',
          value: 'name',
          text: '学员名称'
        })
        fieldList.push({
          type: 'string',
          value: 'stunum',
          text: '学员编号'
        })
        fieldList.push({
          type: 'string',
          value: 'idcard',
          text: '身份证号'
        })
        fieldList.push({
          type: 'string',
          value: 'oldInscode',
          text: '转出驾校'
        })
        fieldList.push({
          type: 'string',
          value: 'oldInsname',
          text: '转出驾校名称'
        })
        fieldList.push({
          type: 'string',
          value: 'inscode',
          text: '转入驾校'
        })
        fieldList.push({
          type: 'string',
          value: 'insname',
          text: '转入驾校名称'
        })
        fieldList.push({
          type: 'int',
          value: 'status',
          text: '审核状态'
        })
        fieldList.push({
          type: 'string',
          value: 'reason',
          text: '理由'
        })
        fieldList.push({
          type: 'datetime',
          value: 'auditDate',
          text: '审核时间'
        })
        fieldList.push({
          type: 'datetime',
          value: 'crdate',
          text: '申请时间'
        })
        this.superFieldList = fieldList
      },
      handleNotifyJg() {
        postAction('/gzpt/stuTransfer/notifyJg', {
          id: this.selectedRowKeys[0]
        }).then((res) => {
          if (res.success) {
            this.$message.success(res.message)
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleProcess() {
        postAction('/gzpt/stuTransfer/process', {
          id: this.selectedRowKeys[0]
        }).then((res) => {
          if (res.success) {
            this.$message.success('手动开启流程成功！')
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleUnFreeze() {
        postAction('/gzpt/stuTransfer/doUnfreeze', {
          id: this.selectedRowKeys[0]
        }).then((res) => {
          if (res.success) {
            this.$message.success('手动解冻提交!')
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleAudit(status) {
        if (status) {
          postAction('/gzpt/stuTransfer/audit', {
            id: this.selectedRowKeys.join(','),
            insAudit: 1,
          }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData();
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.auditReasonModalVisible = true;
        }
      },
      submitAudit() {
        postAction('/gzpt/stuTransfer/audit', {
          id: this.selectedRowKeys.join(','),
          insAudit: 2,
          insReason: this.insReason
        }).then((res) => {
          if (res.success) {
            this.auditReasonModalVisible = false;
            this.$message.success(res.message)
            this.loadData();
          } else {
            this.$message.error(res.message)
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>