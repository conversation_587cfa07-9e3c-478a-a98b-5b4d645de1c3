<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="学员名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="请输入学员名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
              <a-input v-model="model.idcard" placeholder="请输入身份证号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="转出驾校" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="oldInscode">
              <a-input v-model="model.oldInscode" placeholder="请输入转出驾校"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="转出驾校名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="oldInsname">
              <a-input v-model="model.oldInsname" placeholder="请输入转出驾校名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="转入驾校" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
              <a-input v-model="model.inscode" placeholder="请输入转入驾校"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="转入驾校名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="insname">
              <a-input v-model="model.insname" placeholder="请输入转入驾校名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag type="list" v-model="model.status" dictCode="sys_general_audit" placeholder="请选择审核状态" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="理由" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="reason">
              <a-input v-model="model.reason" placeholder="请输入理由"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="auditDate">
              <j-date placeholder="请选择审核时间"  v-model="model.auditDate" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="申请时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="crdate">
              <j-date placeholder="请选择申请时间"  v-model="model.crdate" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StuTransferForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           name: [
              { required: true, message: '请输入学员名称!'},
           ],
           stunum: [
              { required: true, message: '请输入学员编号!'},
           ],
           idcard: [
              { required: true, message: '请输入身份证号!'},
           ],
           oldInscode: [
              { required: true, message: '请输入转出驾校!'},
           ],
           oldInsname: [
              { required: true, message: '请输入转出驾校名称!'},
           ],
           inscode: [
              { required: true, message: '请输入转入驾校!'},
           ],
           insname: [
              { required: true, message: '请输入转入驾校名称!'},
           ],
           status: [
              { required: true, message: '请输入审核状态!'},
           ],
        },
        url: {
          add: "/gzpt/stuTransfer/add",
          edit: "/gzpt/stuTransfer/edit",
          queryById: "/gzpt/stuTransfer/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
        getAction(this.url.queryById, {
          id: this.model.id
        }).then((res) => {
          if (res.success) {
            this.model = res.result
          }
        })
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>