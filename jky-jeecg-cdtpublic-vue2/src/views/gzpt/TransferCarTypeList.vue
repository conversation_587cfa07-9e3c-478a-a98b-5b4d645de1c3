<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <!-- <a-tree-select checkable  :treeData="departTree" placeholder="请选择培训机构" /> -->
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode">
              </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员姓名">
              <a-input v-model="queryParam.stuname" placeholder="请输入学员姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="身份证号">
              <a-input v-model="queryParam.idcard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员编号">
              <a-input v-model="queryParam.stunum" placeholder="请输入学员编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="审核状态">
              <j-dict-select-tag type="list" v-model="queryParam.auditstate" dictCode="sys_general_audit"
                                 placeholder="请选择审核状态" />
            </a-form-item>
          </a-col>
          <a-col :xl="5" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
<!--      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
<!--      <a-button type="primary" icon="download" @click="handleExportXls('t_m_transfer_cartype')">导出</a-button>-->
<!--      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">-->
<!--        <a-button type="primary" icon="import">导入</a-button>-->
<!--      </a-upload>-->
      <a-dropdown
        :disabled="selectedRowKeys.length=== 1 ? false : true"
      >
        <a-menu slot="overlay" v-has="'gzpt:t_m_transfer_cartype:audit'">
          <a-menu-item key="1" @click="handleAudit(true)">通过</a-menu-item>
          <a-menu-item key="2" @click="handleAudit(false)">不通过</a-menu-item>
        </a-menu>
        <a-button type="primary" style="margin-left: 8px">审核<a-icon type="down" /></a-button>
      </a-dropdown>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" v-has="'gzpt:t_m_transfer_cartype:deleteBatch'">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="small"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:t_m_transfer_cartype:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:t_m_transfer_cartype:edit'"/>
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
<!--              <a-menu-item>-->
<!--                <a @click="handleDetail(record)">详情</a>-->
<!--              </a-menu-item>-->
              <a-menu-item v-has="'gzpt:t_m_transfer_cartype:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <transfer-car-type-modal ref="modalForm" @ok="modalFormOk"></transfer-car-type-modal>
    <a-modal v-model="auditReasonModalVisible" title="审核不通过" @ok="submitAudit" :width="800" v-has="'gzpt:t_m_transfer_cartype:audit'">
      <a-form-model>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="原因">
              <a-textarea v-model="insReason"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import TransferCarTypeModal from './modules/TransferCarTypeModal'
  import {postAction} from "@api/manage";
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'

  export default {
    name: 'TransferCarTypeList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      JTreeSelectDepart,TransferCarTypeModal
    },
    data () {
      return {
        description: 't_m_transfer_cartype管理页面',
        // 表头
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'学员姓名',
            align:"center",
            dataIndex: 'stuname'
          },
          {
            title:'学员编号',
            align:"center",
            dataIndex: 'stunum'
          },
          {
            title:'身份证',
            align:"center",
            dataIndex: 'idcard'
          },
          {
            title:'驾校名称',
            align:"center",
            dataIndex: 'insname'
          },
          {
            title:'新车型',
            align:"center",
            dataIndex: 'traintype'
          },
          {
            title:'旧车型',
            align:"center",
            dataIndex: 'oldtraintype'
          },
          {
            title:'审核状态',
            align:"center",
            dataIndex: 'auditstate_dictText'
          },
          {
            title:'审核时间',
            align:"center",
            dataIndex: 'auditdate',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title:'审核理由',
            align:"center",
            dataIndex: 'reason'
          },
          {
            title:'审核人',
            align:"center",
            dataIndex: 'audituser'
          },
          {
            title: '流程',
            align: "center",
            dataIndex: 'flow_dictText'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/gzpt/transferCarType/list",
          delete: "/gzpt/transferCarType/delete",
          deleteBatch: "/gzpt/transferCarType/deleteBatch",
          exportXlsUrl: "/gzpt/transferCarType/exportXls",
          importExcelUrl: "gzpt/transferCarType/importExcel",

        },
        dictOptions:{},
        superFieldList:[],
        insReason: '',
        auditReasonModalVisible: false,
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      handleAudit(status) {
        if (status) {
          postAction('/gzpt/transferCarType/audit', {
            id: this.selectedRowKeys.join(','),
            auditstate: 1,
          }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData();
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          this.auditReasonModalVisible = true;
        }
      },
      submitAudit() {
        postAction('/gzpt/transferCarType/audit', {
          id: this.selectedRowKeys.join(','),
          auditstate: 2,
          reason: this.insReason
        }).then((res) => {
          if (res.success) {
            this.auditReasonModalVisible = false;
            this.$message.success(res.message)
            this.loadData();
          } else {
            this.$message.error(res.message)
          }
        })
      },
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'stunum',text:' 学员编号'})
        fieldList.push({type:'string',value:'idcard',text:' 身份证'})
        fieldList.push({type:'string',value:'traintype',text:' 新车型'})
        fieldList.push({type:'string',value:'oldtraintype',text:' 旧车型'})
        fieldList.push({type:'int',value:'status',text:'审核状态 0 未审核 1.审核通过 3,未通过'})
        fieldList.push({type:'string',value:'platform',text:' 平台编码'})
        fieldList.push({type:'string',value:'inscode',text:'机构编码'})
        fieldList.push({type:'date',value:'crdate',text:'创建时间'})
        fieldList.push({type:'date',value:'auditdate',text:'审核时间'})
        fieldList.push({type:'string',value:'reason',text:'审核理由'})
        fieldList.push({type:'string',value:'audituser',text:'审核人'})
        fieldList.push({type:'string',value:'fileid',text:'科目一技能证明id'})
        fieldList.push({type:'string',value:'fileUrl',text:'科目一技能证明url'})
        fieldList.push({type:'string',value:'district',text:' 地市编码'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>