<template>
	<a-card :bordered="false">
		<!-- 查询区域 -->
		<div class="table-page-search-wrapper">
			<a-form layout="inline" @keyup.enter.native="searchQuery">
				<a-row :gutter="24">
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="版本名称">
							<a-input v-model="queryParam.versionName" placeholder="请输入版本名称" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-form-item label="版本url">
							<a-input v-model="queryParam.versionUrl" placeholder="请输入版本url" />
						</a-form-item>
					</a-col>
					<a-col :xl="6" :lg="7" :md="8" :sm="24">
						<a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
						<a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
						</a-button>
					</a-col>
				</a-row>
			</a-form>
		</div>
		<!-- 查询区域-END -->

		<!-- 操作按钮区域 -->
		<div class="table-operator">
			<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
			<a-button type="primary" icon="download" @click="handleExportXls('mr890版本管理')">导出</a-button>
		</div>

		<!-- table区域-begin -->
		<div>
			<a-table ref="table" size="middle" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
				:dataSource="dataSource" :pagination="ipagination" :loading="loading"
				:rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
				class="j-table-force-nowrap" @change="handleTableChange">

				<template slot="htmlSlot" slot-scope="text">
					<div v-html="text"></div>
				</template>
				<template slot="imgSlot" slot-scope="text,record">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
					<img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt=""
						style="max-width:80px;font-size: 12px;font-style: italic;" />
				</template>
				<template slot="fileSlot" slot-scope="text">
					<span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
					<a-button v-else :ghost="true" type="primary" icon="download" size="small"
						@click="downloadFile(text)">
						下载
					</a-button>
				</template>
				<span slot="action" slot-scope="text, record">
					<a @click="handleEdit(record)" v-has="'gzpt:studentinfo:edit'">编辑</a>
					<a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />
					<a @click="handleDetail(record)">详情</a>
					<a-divider type="vertical" v-has="'gzpt:studentinfo:edit'" />
					<a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
						<a>删除</a>
					</a-popconfirm>
				</span>
			</a-table>
		</div>
		<Mr890VersionModal ref="modalForm" @ok="modalFormOk"></Mr890VersionModal>
	</a-card>
</template>

<script>
	import '@/assets/less/TableExpand.less'
	import {
		mixinDevice
	} from '@/utils/mixin'
	import {
		JeecgListMixin
	} from '@/mixins/JeecgListMixin'
	import {
		craStage
	} from "@/api/gzpt/totaltime";
	import Mr890VersionModal from './modules/Mr890VersionModal'
	export default {
		name: 'Mr890RecordList',
		mixins: [JeecgListMixin, mixinDevice],
		components: {
			Mr890VersionModal
		},
		data() {
			return {
				description: 'mr890版本管理',
				dataSource2: [{
					versionId: '23',
					versionName: '1.0.6',
					versionUrl: 'hhhhhhhh',
					createTime: '2023-06-05'
				}],
				// 表头
				columns: [{
						title: '版本ID',
						align: "center",
						dataIndex: 'id'
					},
					{
						title: '版本名称',
						align: "center",
						dataIndex: 'versionName'
					},
					{
						title: '版本url',
						align: "center",
						dataIndex: 'versionUrl'
					},
					{
						title: '创建时间',
						align: "center",
						dataIndex: 'createTime'
					},
					{
						title: '操作',
						dataIndex: 'action',
						align: "center",
						fixed: "right",
						width: 147,
						scopedSlots: {
							customRender: 'action'
						}
					}
				],
				url: {
					list: "/api/tMMr890Version/list",
					delete: "/api/tMMr890Version/delete",
					deleteBatch: "/api/tMMr890Version/deleteBatch",
					exportXlsUrl: "/api/tMMr890Version/export",
					importExcelUrl: "/api/tMMr890Version/importExcel",

				},
				dictOptions: {},
				superFieldList: [],
			}
		},
		created() {
			this.getSuperFieldList();
		},
		computed: {
			importExcelUrl: function() {
				return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
			},
		},
		methods: {
			initDictConfig() {},
			getSuperFieldList() {
				let fieldList = [];
				fieldList.push({
					type: 'string',
					value: 'stunum',
					text: '学员编号'
				})
				fieldList.push({
					type: 'string',
					value: 'idcard',
					text: '身份证'
				})
				fieldList.push({
					type: 'int',
					value: 'subject',
					text: '科目'
				})
				fieldList.push({
					type: 'number',
					value: 'vehicletime',
					text: '实操学时'
				})
				fieldList.push({
					type: 'number',
					value: 'classtime',
					text: '课程'
				})
				fieldList.push({
					type: 'number',
					value: 'simulatortime',
					text: '模拟'
				})
				fieldList.push({
					type: 'number',
					value: 'networktime',
					text: '远程'
				})
				fieldList.push({
					type: 'number',
					value: 'isBaoshen',
					text: 'IS_BAOSHEN'
				})
				fieldList.push({
					type: 'number',
					value: 'mileage',
					text: '里程'
				})
				fieldList.push({
					type: 'date',
					value: 'crdate',
					text: '创建时间'
				})
				fieldList.push({
					type: 'string',
					value: 'traintype',
					text: '车型'
				})
				fieldList.push({
					type: 'string',
					value: 'inscode',
					text: '培训机构'
				})
				fieldList.push({
					type: 'string',
					value: 'insname',
					text: '培训机构编号'
				})
				fieldList.push({
					type: 'string',
					value: 'district',
					text: '市区'
				})
				fieldList.push({
					type: 'string',
					value: 'stuname',
					text: '姓名'
				})
				this.superFieldList = fieldList
			},
			handleStage() {
				let that = this
				this.$confirm({
					title: '是否生成报审？',
					content: '',
					onOk() {
						that.loading = true;
						craStage(that.selectedRowKeys[0]).then((response) => {
							that.loading = false;
							if (response.success) {
								that.$message.success("操作成功");
								that.loadData();
							} else {
								that.$message.error(response.message);
							}

						});
					},
					onCancel() {

					},
				});

			},
		}
	}
</script>
<style scoped>
	@import '~@assets/less/common.less';
</style>
