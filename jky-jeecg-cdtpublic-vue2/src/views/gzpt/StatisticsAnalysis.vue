<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="操作时间">
              <a-range-picker
                style="width: 210px"
                v-model="createTimeRange"
                format="YYYY-MM-DD"
                :placeholder="['开始时间', '结束时间']"
                @change="onDateChange"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="false"
        :loading="loading"
        class="j-table-force-nowrap"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text,record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" :preview="record.id" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>
      </a-table>
    </div>
  </a-card>
</template>

<script>
  import { getAction, postAction } from '@/api/manage'
  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  export default {
    name: 'StatisticsAnalysis',
    mixins: [JeecgListMixin, mixinDevice],
    components: {},
    data () {
      return {
        description: 'StatisticsAnalysis管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t,r,index) {
              return parseInt(index) + 1;
            }
          },
          {
            title: '姓名',
            align: 'center',
            dataIndex: 'username'
          },
          {
            title: '设置人脸识别通过',
            align: 'center',
            dataIndex: 'faceScanCount'
          },
          {
            title: '设置黑户',
            align: 'center',
            dataIndex: 'blacklistCount'
          },
          {
            title: '学员注销',
            align: 'center',
            dataIndex: 'logoutCount'
          },
          {
            title: '设备启用',
            align: 'center',
            dataIndex: 'enableCount'
          },
          {
            title: '设备停用',
            align: 'center',
            dataIndex: 'disableCount'
          },
          {
            title: '合计',
            align: 'center',
            dataIndex: 'totalCount'
          },
        ],
        url: {
          list: '/gzpt/statistics/analyse',
          delete: "/gzpt/statistics/delete",
          deleteBatch: "/gzpt/statistics/deleteBatch",
          exportXlsUrl: "/gzpt/statistics/exportXls",
          importExcelUrl: "gzpt/statistics/importExcel",
        },
        dictOptions:{},
        ipagination: {
          current: 1,
          pageSize: 150
        },
        createTimeRange: [],
      }
    },
    created() {
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig() {},
      onDateChange(value, dateString) {
        this.queryParam.createTimeStart = dateString[0];
        this.queryParam.createTimeEnd = dateString[1];
      },
      searchReset() {
        this.queryParam = {};
        this.loadData(1);
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>