<template>
  <div class="models" style="background: #fff;">
    <!-- <div class="btn">
      <a-button type="primary" @click="submits">
        保存
      </a-button>
    </div> -->
    <div class="modelAss">
      <ul class="modelList">
        <li
          v-for="(item, index) in modelData"
          :key="index"
          :class="{ active: act == index }"
          @click="tabModel(item.modelId, item.id, index)"
        >
          <p class="icon">
            <a-icon v-show="item.status == 1" type="check" style="color: #1FE633;" />
            <a-icon v-show="item.status != 1" type="close" style="color: red;" />
          </p>
          <p class="name">{{ item.name }}</p>
        </li>
      </ul>
      <div class="right">
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="8">
                <a-form-item label="节点名称">
                  <a-input placeholder="请输入节点名称" v-model="nodeName"></a-input>
                </a-form-item>
              </a-col>

              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-col :md="6" :sm="12">
                  <a-button type="primary" style="left: 10px" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px;left: 10px"
                    >重置</a-button
                  >
                </a-col>
              </span>
            </a-row>
          </a-form>
        </div>
        <div class="table-operator">
          <a-button @click="allDeal" type="primary" icon="plus">统一设置审核人</a-button>
        </div>
        <a-table
          class="nodeList"
          :columns="columns"
          :data-source="nodeData"
          :pagination="pagination"
          rowKey="nodeKey"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <span slot="tags" slot-scope="text, record">
            <p class="person_name" @click="personModel(record)">
              <span style="color: rgb(31, 230, 51);" v-if="record.realname != ''">
                {{ record.realname }}
              </span>
              <span style="color: red;" v-if="record.realname == '' || record.realname == null">
                点击选择
              </span>
            </p>
          </span>
          <span slot="action" slot-scope="text, record">
            <a-icon v-show="record.realname != null" type="check" style="color: #1FE633;" />
            <a-icon v-show="record.realname == null" type="close" style="color: red;" />
          </span>
        </a-table>
        <div class="imgs">
          <img :src="urlSrc" />
        </div>
      </div>

      <div class="checkPersons" v-show="perShow">
        <h4>选择人员</h4>
        <j-select-user-by-dep
          v-if="perShow"
          @change="changPerson"
          v-model="perChecker"
          :multi="true"
          :userIds="userIds"
        ></j-select-user-by-dep>
        <a-button class="btn_t" @click="perCancle">取 消</a-button>
        <a-button class="btn_t" type="primary" @click="perOk">确 定</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
export default {
  data() {
    return {
      modelData: [],
      nodeData: [],
      act: 0,
      perShow: false,
      perChecker: '',
      urlSrc: '',
      nodeName: '',
      nodeKeys: '',
      modelIds: '',
      ids: '',
      selectedRowKeys: [],
      selectCheck: 0, //多选1-单选0
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '节点',
          dataIndex: 'nodeName',
          key: 'nodeName'
        },
        {
          title: '审核人',
          key: 'realname',
          dataIndex: 'realname',
          scopedSlots: { customRender: 'tags' }
        },
        {
          title: '状态',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      pagination: {
        pageSize: 12
      },
      userIds: '' // 点击树时负责人选中IDS
    }
  },
  mounted() {
    this.getModelData()
  },
  methods: {
    // 查询
    searchQuery() {
      this.getNodeData(this.modelIds, this.ids)
    },
    // 重置
    searchReset() {
      this.nodeName = ''
      this.getNodeData(this.modelIds, this.ids)
    },
    // 批量操作
    onSelectChange(selectedRowKeys) {
      console.log('selectedRowKeys changed: ', selectedRowKeys)
      this.selectedRowKeys = selectedRowKeys
    },
    allDeal() {
      if (this.selectedRowKeys.length > 0) {
        this.selectCheck = 1
        this.perShow = true
        this.userIds = ''
        this.perChecker = ''
        this.realname = ''
      } else {
        this.$message.error('请选择至少一条')
      }
    },

    getModelData() {
      let params = {
        zx: true,
        column: 'name',
        order: 'desc',
        status: '1'
      }
      getAction('/activiti_process/areaListData', params).then(res => {
        if (res.success) {
          this.modelData = res.result
          this.modelIds = res.result[0].modelId
          this.ids = res.result[0].id
          this.getNodeData(res.result[0].modelId, res.result[0].id)
          this.urlSrc = window._CONFIG['domianURL'] + '/activiti/models/export?id=' + res.result[0].id
        }
      })
    },
    getNodeData(modelId, id) {
      getAction('/activiti/nodeCheckerByModelId?modelId=' + modelId + '&id=' + id + '&nodeName=' + this.nodeName).then(
        res => {
          if (res.success) {
            this.nodeData = res.result
          } else {
            this.nodeData = []
            this.$message.warning(res.message)
          }
        }
      )
    },
    tabModel(modelId, id, index) {
      this.act = index
      this.urlSrc = window._CONFIG['domianURL'] + '/activiti/models/export?id=' + id
      this.modelIds = modelId
      this.ids = id
      this.getNodeData(modelId, id)
    },
    personModel(record) {
      this.selectCheck = 0
      this.nodeKeys = record.nodeKey
      this.perShow = true
      // @fix wyk 2022.11.28 获取当前节点的审核人信息
      setTimeout(() => {
        const { realname, userName } = record
        // this.currentPerson = {
        //     realname, userName
        // }
        // console.log(realname, userName, '我是选中的数据');
        this.userIds = userName
        this.perChecker = userName
        this.realname = realname
      }, 100)
    },
    changPerson(e, t) {
      // @fix wyk 多选人员
      if (t && t.length) {
        this.realname = t.map(item => item.realname).join(',')
        this.userIds = t.map(item => item.username).join(',')
      } else {
        this.realname = ''
        this.userIds = ''
      }
      // this.realname=t[0].realname;
    },
    perCancle() {
      this.perShow = false
      this.perChecker = ''
      this.nodeKeys = ''
    },
    perOk() {
      // 赋值
      if (this.selectCheck == 0) {
        // 单选
        this.changeData(this.perChecker, this.realname)
        this.perShow = false
        this.perChecker = ''
      } else {
        // 多选
        this.changeDataMultiple(this.perChecker, this.realname, this.selectedRowKeys)
        this.perShow = false
        this.perChecker = ''
      }
    },
    changeData(checker, reals) {
      let that = this
      this.nodeData.map(obj => {
        console.log(obj)
        if (obj.nodeKey == that.nodeKeys) {
          obj.userName = checker
          obj.realname = reals
        }
      })
      that.submits()
    },
    changeDataMultiple(checker, reals, array) {
      // const checkerList = checker && checker.split()
      // console.log(checkerList,reals, array, '我是多选');
      let that = this
      that.nodeData.map((obj, index) => {
        // if(array.indexOf(index)!=-1){
        //   obj.userName=checker;
        //   obj.realname=reals
        // }
        // @fix wyk 修复翻页多选设置审核人失败问题
        if (array.includes(obj.nodeKey)) {
          obj.userName = checker
          obj.realname = reals
        }
      })
      that.submits('multiple')
    },
    submits(type) {
      // console.log(this.selectedRowKeys, this.nodeKeys, '我是选中数据');
      // @fix wyk 提交表单时只筛选改动的值
      let params = []
      let that = this
      this.nodeData.map(item => {
        if (
          (type === 'multiple' && this.selectedRowKeys.includes(item.nodeKey)) ||
          (!type && this.nodeKeys === item.nodeKey)
        ) {
          params.push(item)
        }
      })
      // let params=this.nodeData;
      postAction('/activiti/saveNodeCheckers', params).then(res => {
        if (res.success) {
          this.$message.success('保存成功')
          getAction('/activiti_process/areaListData', params).then(res => {
            if (res.success) {
              that.modelData = res.result
              that.selectedRowKeys = []
            }
          })
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
ul,
li,
p {
  margin: 0;
  padding: 0;
  list-style: none;
}
.btn {
  padding: 0px 20px;
  padding-top: 10px;
}
.modelAss {
  width: 100%;
  // height: 600px;
  display: flex;
  padding: 20px;
  justify-content: space-between;
  .modelList {
    width: 300px;
    height: 600px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    li {
      height: 30px;
      display: flex;
      cursor: pointer;
      font-weight: bold;
      .icon {
        margin-right: 10px;
        color: red;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .name {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
    li:hover {
      color: #1890ff;
    }
    .active {
      color: #1890ff;
    }
  }
  .right {
    width: calc(100% - 350px);
    border: 1px solid #ccc;
    padding: 20px;
    .imgs {
      width: 100%;
      // height: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 800px;
        margin-top: 20px;
        // height: 80%;
      }
    }
    .nodeList {
      width: 100%;
      // height: 250px;
      border-bottom: 1px solid #ccc;
      padding: 20px;
      overflow-y: auto;
      // li{
      //   width: 500px;
      //   height: 30px;
      //   display: flex;
      //   align-items: center;
      //   font-weight: bold;
      //   cursor: pointer;
      //   .node_name{
      //     margin-right: 10px;
      //   }
      //   .person_name{
      //     color: #1890FF;
      //   }
      //   .icon_ps{
      //     margin-left: 10px;
      //   }
      // }
      // li:hover{
      //   color: #1890FF;
      // }
      // .activeNode{
      //   color: #1890FF;
      // }
    }
  }
}
.checkPersons {
  width: 500px;
  height: 150px;
  position: absolute;
  left: calc(50% - 250px);
  top: 120px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #1890ff;
  padding: 10px;
  .btn_t {
    margin: 20px 10px;
  }
}
.person_name {
  cursor: pointer;
}
</style>
