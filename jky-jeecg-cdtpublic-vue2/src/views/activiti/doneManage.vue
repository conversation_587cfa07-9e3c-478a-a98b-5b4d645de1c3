<style lang="less">
  @import '~@assets/less/common.less';
</style>
<template>
  <div class="search">
    <a-card>
      <div class="table-page-search-wrapper">
        <a-form layout="inline" @keyup.enter.native="searchQuery">
          <a-row :gutter="24">
	        <a-col :md="6" :sm="8">
	            <a-form-item label="业务类型" prop="categoryId" >
	               <j-dict-select-tag v-model="queryParam.categoryId" placeholder="请选择业务类型"  dictCode="bpm_process_type"/>
	            </a-form-item>
	        </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="业务名称" prop="name">
                <a-input
                  type="text"
                  v-model="queryParam.name"
                  placeholder="请输入"
                  clearable
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="8">
              <a-form-item label="标题" prop="title">
                <a-input
                  type="text"
                  v-model="queryParam.title"
                  placeholder="模糊查询、可输入金额、财务编号"
                  clearable
                />
              </a-form-item>
            </a-col>
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
            <a-col :md="6" :sm="12" >
                <a-button @click="searchQuery" type="primary" icon="search">搜索</a-button>
                <a-button @click="handleReset" style="margin-left: 10px;">重置</a-button>
            </a-col>
          </span>
          </a-row>
        </a-form>
      </div>

      <a-row>
        <a-row>
          <a-table
            ref="table"
            size="middle"
            :scroll="{x:true}"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            class="j-table-force-nowrap"
            @change="handleTableChange">

            <template slot="htmlSlot" slot-scope="text">
              <div v-html="text"></div>
            </template>

            <span slot="action" slot-scope="text, record">
              <a href="javascript:void(0);" @click="detail(record)" >表单数据</a>
              <a-divider type="vertical" />
              <a href="javascript:void(0);" @click="history(record)" >审批历史</a>
              <a-divider type="vertical" />
             <!-- <a-popconfirm title="确定删除吗?" @confirm="() => remove(record)">
                <a style="color: red;">删除</a>
              </a-popconfirm> -->
            </span>

            <!-- 优先级 -->
            <template slot="customRenderPriority" slot-scope="p">
              <a-tag v-if="p==0" style="color: green;"> 普通 </a-tag>
              <a-tag v-else-if="p==1" style="color: orange;"> 重要 </a-tag>
              <a-tag v-else-if="p==2" style="color: red;"> 紧急 </a-tag>
              <a-tag v-else style="color: #999;"> 无 </a-tag>
            </template>

            <!-- 耗时 -->
            <template slot="customRenderDuration" slot-scope="duration">
              <span> {{millsToTime(duration)}} </span>
            </template>

			  <template slot="newlineSlot" slot-scope="text, record">
		         <div v-html="newline(record)"></div>
		      </template>
          </a-table>
        </a-row>
        <!--
        <a-table :scroll="scroll" bordered
          :loading="loading"
          rowKey="id"
          :dataSource="data"
          :pagination="ipagination"
          @change="handleTableChange"
          ref="table"
        >
          <a-table-column title="#"  :width="50">
            <template slot-scope="t,r,i" >
              <span> {{i+1}} </span>
            </template>
          </a-table-column>
          <a-table-column title="业务名称" dataIndex="processName"  :width="150" align="center">
            <template slot-scope="t,r,i">
              <span> {{t}} </span>
            </template>
          </a-table-column>
          <a-table-column title="标题" dataIndex="name"  :width="150" align="center">
            <template slot-scope="t,r,i">
              <span> {{t}} </span>
            </template>
          </a-table-column>
          <a-table-column title="优先级" dataIndex="priority"  :width="100" align="center"
                          key="priority" :sorter="(a,b)=>a.priority - b.priority"
          >
            <template slot-scope="t">
              <span v-if="t==0" style="color: green;"> 普通 </span>
              <span v-else-if="t==1" style="color: orange;"> 重要 </span>
              <span v-else-if="t==2" style="color: red;"> 紧急 </span>
              <span v-else style="color: #999;"> 无 </span>
            </template>
          </a-table-column>
          <a-table-column title="操作" dataIndex="deleteReason"  :width="100" align="center">
            <template slot-scope="t">
              <span> {{t}} </span>
            </template>
          </a-table-column>
          <a-table-column title="操作说明" dataIndex="comment"  :width="100" align="center">
            <template slot-scope="t">
              <j-ellipsis :value="t" :length="10"/>
            </template>
          </a-table-column>
          <a-table-column title="耗时" dataIndex="duration"  :width="100" align="center"
                          key="duration" :sorter="(a,b)=>a.duration - b.duration"
                          >
            <template slot-scope="t">
              <span> {{millsToTime(t)}} </span>
            </template>
          </a-table-column>
          <a-table-column title="创建时间" dataIndex="createTime"  :width="150" align="center">
            <template slot-scope="t">
              <span> {{t}} </span>
            </template>
          </a-table-column>
          <a-table-column title="操作" dataIndex="action"   align="center">
            <template slot-scope="t,r,i">
              <a href="javascript:void(0);" @click="detail(r)" >表单数据</a>
              <a-divider type="vertical" />
              <a href="javascript:void(0);" @click="history(r)" >审批历史</a>
              <a-divider type="vertical" />
              <a-popconfirm title="确定删除吗?" @confirm="() => remove(r)">
                <a style="color: red;">删除</a>
              </a-popconfirm>
            </template>
          </a-table-column>
        </a-table>
        -->
      </a-row>
    </a-card>
    <!---->
    <a-modal title="审批历史" v-model="modalLsVisible" :mask-closable="false" :width="'80%'" :footer="null">
      <div v-if="modalLsVisible">
        <component :is="historicDetail" :procInstId="procInstId"></component>
      </div>
    </a-modal>
    <!--流程表单-->
    <a-modal :title="lcModa.title" v-model="lcModa.visible" :footer="null" :maskClosable="false" width="80%">
      <component :disabled="lcModa.disabled" v-if="lcModa.visible" :is="lcModa.formComponent"
                 :processData="lcModa.processData" :isNew = "lcModa.isNew"
                 :formData="lcModa.formData"
                 :formBpm ="lcModa.formBpm"
                 @close="lcModa.visible=false,lcModa.disabled = false"></component>
    </a-modal>
  </div>
</template>

<script>
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import {activitiMixin} from "./mixins/activitiMixin";
  import {  getAction } from '@/api/manage'
export default {
  name: "done-manage",
  mixins:[activitiMixin,JeecgListMixin],
  data() {
    return {
      openSearch: true,
      openTip: true,
      loading: true, // 表单加载状态
      selectCount: 0, // 多选计数
      selectList: [], // 多选数据
      queryParam: {},
      modalType: 0, // 添加或编辑标识
      modalVisible: false, // 添加或编辑显示
      modalTitle: "", // 添加或编辑标题
      form: {
        // 添加或编辑表单对象初始化数据
        reason: ""
      },
      formValidate: {
        // 表单验证规则
      },
      submitLoading: false, // 添加或编辑提交状态
      data: [], // 表数据
      total: 0, // 表数据总数
      deleteId: "",
      url:{
        list:'/actTask/doneList',
        deleteHistoricTask:'/actTask/deleteHistoric/'
      },
      columns: [
        {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t, r, index) {
                return parseInt(index) + 1
            }
        },
        {
            title:'业务名称',
            align:"center",
            width: 150,
            ellipsis: true,
            dataIndex: 'processName'
        },
        {
            title:'申请人',
            align:"center",
            width: 90,
            ellipsis: true,
            dataIndex: 'applyer'
        },
        {
            title:'标题',
            align:"center",
            width: 350,
            dataIndex: 'title',
            ellipsis: true,
            scopedSlots: { customRender: 'newlineSlot' }
        },
      /**  {
            title:'优先级',
            align:"center",
            width: 100,
            dataIndex: 'priority',
            scopedSlots: { customRender: 'customRenderPriority' },
        },*/
        {
            title:'操作',
            align:"center",
            width: 100,
            dataIndex: 'deleteReason',
        },
        {
            title:'操作说明',
            align:"center",
            ellipsis: true,
            width: 150,
            dataIndex: 'comment',
        },
       /** {
            title:'耗时',
            align:"center",
            width: 100,
            dataIndex: 'duration',
            scopedSlots: { customRender: 'customRenderDuration' },
        },*/
        {
            title:'创建时间',
            align:"center",
            width: 120,
            dataIndex: 'startTime',
            customRender:function (text) {
                return !text?"":(text.length>10?text.substr(0,10):text)
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:150,
            scopedSlots: { customRender: 'action' }
        }
      ],
      modalLsVisible: false,
      procInstId: '',
      lcModa: {
        title:'',
        disabled:false,
        visible:false,
        formComponent : null,
        isNew : false
      },
    };
  },
  mounted() {
    //this.init();
  },
  methods: {

    /*loadData(arg) {
      if(!this.url.doneList){
        this.$message.error("请设置url.list属性!")
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      var params = this.getQueryParams();//查询条件
      this.loading = true;
      getAction(this.url.doneList, params).then((res) => {
        if (res.success && res.result) {
          this.data = res.result.records;
          this.ipagination.total = res.result.total;
        }
        if(res.code===510){
          this.$message.warning(res.message)
        }
        this.loading = false;
      })
    },*/
    newline: function (r) {
 		var val = r.title;
 		var index = val.indexOf('<br>');
 		if(index > -1){
 			val = '<div>'+ val.substr(0,index)+'</div><div>'+val.substr(index+4)+'</div>';
 		}
    	return val
    },
    
    init() {
      this.loadData();
    },
    /*
    loadData() {
      this.loading = true;
      this.postFormAction(this.url.doneList,this.queryParam).then(res => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records||[];
          this.ipagination.total = res.result.total;
        }else {
          this.$message.error(res.message)
        }
      });
    },
    searchQuery() {
      this.queryParam.pageNumber = 1;
      this.queryParam.pageSize = 10;
      this.loadData();
    },
    */
    handleReset() {
      this.queryParam={};
      this.queryParam.pageNumber = 1;
      this.queryParam.pageSize = 10;
      // 重新加载数据
      this.loadData();
    },
    handelCancel() {
      this.modalVisible = false;
    },
    detail(r) {
      if (!r.routeName) {
        this.$message.warning(
          "该流程信息未配置表单，请联系开发人员！"
        );
        return;
      }
      this.lcModa.disabled = true;
      this.lcModa.title = '查看流程业务信息：'+r.processName;
      this.lcModa.formComponent = this.getFormComponent(r.routeName).component;
      this.lcModa.processData = r;
      this.lcModa['formData']={'dataId':r.tableId,'disabled':true};
      this.lcModa['formBpm'] = true;
      this.lcModa.isNew = false;
      this.lcModa.visible = true;
    },
    history(v) {
      if (!v.procInstId) {
        this.$message.error("流程实例ID不存在");
        return;
      }
      this.procInstId = v.procInstId;
      this.modalLsVisible = true;
    },
    remove(v) {
      this.postFormAction(this.url.deleteHistoricTask+v.id).then(res => {
        if (res.success) {
          this.$message.success("操作成功");
          this.loadData();
        }else {
          this.$message.error(res.message);
        }
      });
    },
    /*
    handleTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      //TODO 筛选
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      // this.loadData();
    },
     */
  },
  watch: {
  }
};
</script>