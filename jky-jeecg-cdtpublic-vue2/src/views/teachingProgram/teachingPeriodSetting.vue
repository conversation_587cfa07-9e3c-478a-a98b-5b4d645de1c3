<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="名称">
              <a-input v-model="queryParam.templateName" placeholder="请输入名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教学计划类型">
              <a-select v-model="queryParam.teachType" placeholder="请选择教学计划类型" allowClear>
                <a-select-option :value="1">实车</a-select-option>
                <a-select-option :value="2">课堂</a-select-option>
                <a-select-option :value="3">模拟</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" icon="reload" style="margin-left: 8px" @click="searchReset">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="showAddModal" type="primary" icon="plus">新增</a-button>
      <a-button
        type="danger"
        icon="delete"
        @click="handleBatchDelete"
        :disabled="selectedRowKeys.length === 0"
        style="margin-left: 8px"
      >
        批量删除
      </a-button>
    </div>

    <!-- 主表格区域 -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange
        }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" style="margin-right: 8px">修改</a>
          <a-popconfirm title="确定删除吗？" @confirm="() => handleDelete(record.id)">
            <a style="color: red">删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </div>

    <!-- 新增弹窗 -->
    <a-modal v-model="isModalVisible" title="新增方案" @ok="handleSave" @cancel="handleCancel" :width="'35vw'">
      <a-form :form="form" :model="formData">
        <a-row>
          <a-col :span="24">
            <a-form-item
              label="方案名称"
              :labelCol="{ span: 6 }"
              :wrapperCol="{ span: 16 }"
              :rules="[{ required: true, message: '请输入方案名称' }]"
            >
              <a-input v-model="formData.templateName" placeholder="请输入方案名称" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="教学计划类型"
              :labelCol="{ span: 6 }"
              :wrapperCol="{ span: 16 }"
              :rules="[{ required: true, message: '请选择教学计划类型' }]"
            >
              <a-select v-model="formData.teachType" placeholder="请选择教学计划类型">
                <a-select-option :value="1">实车</a-select-option>
                <a-select-option :value="2">课堂</a-select-option>
                <a-select-option :value="3">模拟</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 编辑弹窗 -->
    <a-modal v-model="editModalVisible" title="修改方案" width="800px">
      <div>
        <!-- 编辑方案信息 -->
        <div class="edit-form-container" style="margin-bottom: 16px;">
          <a-form :form="editForm" :model="editFormData" layout="inline">
            <a-row type="flex" align="middle" style="width: 100%">
              <a-form-item label="方案名称">
                <a-input v-model="editFormData.templateName" placeholder="请输入方案名称" />
              </a-form-item>
              <a-button type="primary" @click="handleEditSave">修改方案名称</a-button>
            </a-row>
          </a-form>
        </div>
        <!-- 新增时间明细按钮与批量删除按钮 -->
        <div class="table-operator">
          <a-button @click="showAddTimeDetailModal" type="primary" icon="plus" style="margin-bottom: 16px">
            新增时间明细
          </a-button>
          <a-button
            type="danger"
            icon="delete"
            @click="handleDetailBatchDelete"
            :disabled="selectedDetailRowKeys.length === 0"
            style="margin-left: 8px; margin-bottom: 16px"
          >
            批量删除
          </a-button>
        </div>

        <!-- 编辑弹窗中的表格，增加分页属性 -->
        <a-table
          rowKey="id"
          :columns="editTableColumns"
          :dataSource="editTableData"
          :pagination="editPagination"
          :loading="editTableLoading"
          @change="handleEditTableChange"
          :rowSelection="{
            selectedRowKeys: selectedDetailRowKeys,
            onChange: onDetailSelectChange,
            type: 'checkbox'
          }"
        >
          <template slot="timeRange" slot-scope="text, record">
            <span>{{ record.startTime }} 至 {{ record.endTime }}</span>
          </template>
          <template slot="classHour" slot-scope="text, record">
            <span>{{ record.classHour }}</span>
          </template>
          <template slot="action" slot-scope="text, record">
            <a @click="handleTimeDetailEdit(record)" style="margin-right: 8px">编辑</a>
            <a-popconfirm title="确定删除吗?" @confirm="() => handleTimeDetailDelete(record.id)">
              <a style="color: red">删除</a>
            </a-popconfirm>
          </template>
        </a-table>
      </div>
      <template slot="footer">
        <a-button @click="handleEditCancel">关闭</a-button>
      </template>
    </a-modal>

    <!-- 新增时间明细弹窗 -->
    <a-modal
      v-model="addTimeDetailModalVisible"
      title="新增时间明细"
      @ok="handleAddTimeDetailSave"
      @cancel="handleAddTimeDetailCancel"
    >
      <a-form :form="addTimeDetailForm" :model="addTimeDetailFormData">
        <a-form-item
          label="时段类型"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请选择时段类型' }]"
        >
          <a-select v-model="addTimeDetailFormData.timeType" placeholder="请选择时段类型">
            <a-select-option :value="0">上午</a-select-option>
            <a-select-option :value="1">下午</a-select-option>
            <a-select-option :value="2">夜间加班</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="开始时间"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请输入开始时间' }]"
        >
          <a-time-picker
            v-model="addTimeDetailFormData.startTime"
            format="HH:mm"
            placeholder="开始时间"
            style="width: 100%"
            @change="handleAddDetailStartTimeChange"
          />
        </a-form-item>
        <a-form-item
          label="学时"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请输入学时' }]"
        >
          <!--          <a-input-number-->
          <!--            disabled="true"-->
          <!--            v-model="addTimeDetailFormData.classHour"-->
          <!--            :min="1"-->
          <!--            :max="24"-->
          <!--            style="width: 100%"-->
          <!--            @change="handleAddDetailClassHourChange"-->
          <!--          />-->
          <a-select
            v-model="addTimeDetailFormData.classHour"
            placeholder="请选择学时"
            style="width: 100%"
            @change="handleAddDetailClassHourChange"
          >
            <a-select-option v-for="n in maxClassHour" :key="n" :value="n">{{ n }} 小时</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="结束时间(自动计算)" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
          <span>{{ calculatedEndTime }}</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑时间明细弹窗 -->
    <a-modal
      width="50%"
      v-model="timeDetailEditModalVisible"
      title="编辑时间明细"
      @ok="handleTimeDetailEditSave"
      @cancel="handleTimeDetailEditCancel"
    >
      <a-form :form="timeDetailEditForm" :model="timeDetailEditFormData">
        <a-form-item
          label="时段类型"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请选择时段类型' }]"
        >
          <a-select v-model="timeDetailEditFormData.timeType" placeholder="请选择时段类型">
            <a-select-option :value="0">上午</a-select-option>
            <a-select-option :value="1">下午</a-select-option>
            <a-select-option :value="2">夜间加班</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="开始时间"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请输入开始时间' }]"
        >
          <a-time-picker
            v-model="timeDetailEditFormData.startTime"
            format="HH:mm"
            placeholder="开始时间"
            style="width: 100%"
            @change="handleTimeDetailEditStartTimeChange"
          />
        </a-form-item>
        <a-form-item
          label="学时"
          :labelCol="{ span: 6 }"
          :wrapperCol="{ span: 16 }"
          :rules="[{ required: true, message: '请输入学时' }]"
        >
          <a-input-number
            v-model="timeDetailEditFormData.classHour"
            :disabled="true"
            :min="1"
            :max="24"
            style="width: 100%"
            @change="handleTimeDetailEditClassHourChange"
          />
        </a-form-item>
        <a-form-item label="结束时间(自动计算)" :labelCol="{ span: 6 }" :wrapperCol="{ span: 16 }">
          <span>{{ timeDetailEditEndTime }}</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postAction, deleteAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'teachingPeriodSetting',
  mixins: [JeecgListMixin],
  data() {
    return {
      queryParam: {
        templateName: '',
        teachType: undefined
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          key: 'index',
          width: 70,
          align: 'center',
          customRender: (text, record, index) => {
            return (this.ipagination.current - 1) * this.ipagination.pageSize + index + 1
          }
        },
        {
          title: '方案名称',
          dataIndex: 'templateName',
          key: 'templateName',
          align: 'center'
        },
        {
          title: '教学计划类型',
          dataIndex: 'teachType',
          key: 'teachType',
          align: 'center',
          customRender: text => {
            const typeMap = {
              1: '实车',
              2: '课堂',
              3: '模拟'
            }
            return typeMap[text] || '未设置'
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' },
          width: 200
        }
      ],
      editTableColumns: [
        {
          title: '时段类型',
          dataIndex: 'timeType',
          key: 'timeType',
          align: 'center',
          customRender: text => {
            const typeMap = {
              0: '上午',
              1: '下午',
              2: '夜间加班'
            }
            return typeMap[text] || text
          }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          key: 'startTime',
          align: 'center'
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          key: 'endTime',
          align: 'center'
        },
        {
          title: '学时',
          dataIndex: 'classHour',
          key: 'classHour',
          width: 120,
          align: 'center',
          scopedSlots: { customRender: 'classHour' }
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      dataSource: [],
      editTableData: [],
      loading: false,
      editTableLoading: false,
      ipagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total, range) => {
          return `显示第 ${range[0]} 到 ${range[1]} 条，共 ${total} 条`
        },
        pageSizeOptions: ['10', '20', '50', '100']
      },
      // 针对编辑弹窗中的时间明细表格分页配置
      editPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total, range) => {
          return `显示第 ${range[0]} 到 ${range[1]} 条，共 ${total} 条`
        },
        pageSizeOptions: ['10', '20', '50', '100']
      },
      isModalVisible: false,
      editModalVisible: false,
      addTimeDetailModalVisible: false,
      timeDetailEditModalVisible: false,
      formData: {
        templateName: '',
        teachType: undefined
      },
      addTimeDetailFormData: {
        timeType: 0,
        startTime: null,
        endTime: null,
        classHour: 1
      },
      timeDetailEditFormData: {
        id: '',
        timeType: 0,
        startTime: null,
        endTime: null,
        classHour: 1
      },
      editFormData: {
        id: '',
        templateName: '',
        teachType: undefined,
        timeDetails: []
      },
      selectedRowKeys: [], // 主表格选中的行
      selectedDetailRowKeys: [], // 编辑弹窗中明细表格选中的行
      form: this.$form.createForm(this),
      editForm: this.$form.createForm(this),
      addTimeDetailForm: this.$form.createForm(this),
      timeDetailEditForm: this.$form.createForm(this)
    }
  },
  computed: {
    calculatedEndTime() {
      if (this.addTimeDetailFormData.startTime && this.addTimeDetailFormData.classHour) {
        const startMoment = moment(this.addTimeDetailFormData.startTime)
        const endMoment = startMoment.clone().add(this.addTimeDetailFormData.classHour, 'hours')
        return endMoment.format('HH:mm')
      }
      return ''
    },
    timeDetailEditEndTime() {
      if (this.timeDetailEditFormData.startTime && this.timeDetailEditFormData.classHour) {
        const startMoment = moment(this.timeDetailEditFormData.startTime)
        const endMoment = startMoment.clone().add(this.timeDetailEditFormData.classHour, 'hours')
        return endMoment.format('HH:mm')
      }
      return ''
    },
    maxClassHour() {
      // 根据编辑表单中的教学类型确定最大可选学时
      const teachType = this.editFormData.teachType
      if (teachType === 1) {
        // 实车
        return 1
      } else if (teachType === 2) {
        // 课堂
        return 6
      } else if (teachType === 3) {
        // 模拟
        return 4
      }
      // 默认为1，或者在没有 editFormData 时（理论上不应发生在此弹窗）
      return 1
    }
  },
  async created() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      const params = {
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize,
        templateName: this.queryParam.templateName,
        teachType: this.queryParam.teachType
      }
      let res = await getAction('/gzpt/timeTemplate/list', params)
      if (res.success) {
        const { records, total } = res.result
        // 如果当前页数据为空且页码大于1，则退一页后重新加载（防止删除后出现空页）
        if (records.length === 0 && this.ipagination.current > 1) {
          this.ipagination.current = this.ipagination.current - 1
          this.loadData()
          return
        }
        this.dataSource = records
        this.ipagination.total = total
      } else {
        this.$message.error('加载数据失败')
      }
      this.loading = false
    },
    loadEditTableData(templateId) {
      this.editTableLoading = true
      const params = {
        templateId: templateId,
        pageNo: this.editPagination.current,
        pageSize: this.editPagination.pageSize
      }
      getAction('/gzpt/timeTemplateDetails/list', params)
        .then(res => {
          if (res.success) {
            this.editTableData = res.result.records
            this.editPagination.total = res.result.total
          } else {
            this.$message.error('加载时间明细失败')
          }
          this.editTableLoading = false
        })
        .catch(() => {
          this.editTableLoading = false
          this.$message.error('加载时间明细失败')
        })
    },
    handleEditTableChange(pagination) {
      // 更新编辑弹窗中表格分页配置，并重新加载数据
      this.editPagination = Object.assign({}, this.editPagination, {
        current: pagination.current,
        pageSize: pagination.pageSize
      })
      this.loadEditTableData(this.editFormData.id)
    },
    // 主表格删除和批量删除方法
    handleDelete(id) {
      const hide = this.$message.loading('删除中..', 0)
      deleteAction(`/gzpt/timeTemplate/delete?id=${id}`)
        .then(res => {
          hide()
          if (res.success) {
            this.$message.success('删除成功')
            this.loadData()
            this.selectedRowKeys = this.selectedRowKeys.filter(key => key !== id)
          } else {
            this.$message.error(res.message || '删除失败')
          }
        })
        .catch(() => {
          hide()
          this.$message.error('删除失败')
        })
    },
    handleBatchDelete() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }
      this.$confirm({
        title: '确认删除',
        content: `确定删除选中的 ${this.selectedRowKeys.length} 条记录吗?`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          const hide = this.$message.loading('删除中..', 0)
          const ids = this.selectedRowKeys.join(',')
          deleteAction(`/gzpt/timeTemplate/deleteBatch?ids=${ids}`)
            .then(res => {
              hide()
              if (res.success) {
                this.$message.success('批量删除成功')
                this.selectedRowKeys = []
                this.loadData()
              } else {
                this.$message.error(res.message || '批量删除失败')
              }
            })
            .catch(() => {
              hide()
              this.$message.error('批量删除失败')
            })
        }
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    // 明细表格删除和批量删除方法
    handleTimeDetailDelete(id) {
      const hide = this.$message.loading('删除中..', 0)
      deleteAction(`/gzpt/timeTemplateDetails/delete?id=${id}`)
        .then(res => {
          hide()
          if (res.success) {
            this.$message.success('删除成功')
            this.loadEditTableData(this.editFormData.id)
            this.selectedDetailRowKeys = this.selectedDetailRowKeys.filter(key => key !== id)
          } else {
            this.$message.error(res.message || '删除失败')
          }
        })
        .catch(() => {
          hide()
          this.$message.error('删除失败')
        })
    },
    handleDetailBatchDelete() {
      if (!this.selectedDetailRowKeys.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }
      this.$confirm({
        title: '确认删除',
        content: `确定删除选中的 ${this.selectedDetailRowKeys.length} 条记录吗?`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          const hide = this.$message.loading('删除中..', 0)
          const ids = this.selectedDetailRowKeys.join(',')
          deleteAction(`/gzpt/timeTemplateDetails/deleteBatch?ids=${ids}`)
            .then(res => {
              hide()
              if (res.success) {
                this.$message.success('批量删除成功')
                this.selectedDetailRowKeys = []
                this.loadEditTableData(this.editFormData.id)
              } else {
                this.$message.error(res.message || '批量删除失败')
              }
            })
            .catch(() => {
              hide()
              this.$message.error('批量删除失败')
            })
        }
      })
    },
    onDetailSelectChange(selectedRowKeys) {
      this.selectedDetailRowKeys = selectedRowKeys
    },
    handleTimeDetailEdit(record) {
      this.timeDetailEditModalVisible = true
      this.timeDetailEditFormData = {
        id: record.id,
        timeType: record.timeType,
        startTime: moment(record.startTime, 'HH:mm'),
        classHour: record.classHour
      }
    },
    handleTimeDetailEditStartTimeChange(time) {
      this.timeDetailEditFormData.startTime = time
    },
    handleTimeDetailEditClassHourChange(value) {
      this.timeDetailEditFormData.classHour = value
    },
    handleTimeDetailEditSave() {
      if (!this.timeDetailEditFormData.startTime || !this.timeDetailEditFormData.classHour) {
        this.$message.warning('请填写完整信息')
        return
      }
      const params = {
        id: this.timeDetailEditFormData.id,
        templateId: this.editFormData.id,
        timeType: this.timeDetailEditFormData.timeType,
        startTime: moment(this.timeDetailEditFormData.startTime).format('HH:mm'),
        endTime: this.timeDetailEditEndTime,
        classHour: this.timeDetailEditFormData.classHour
      }
      postAction('/gzpt/timeTemplateDetails/edit', params)
        .then(res => {
          if (res.success) {
            this.$message.success('修改时间明细成功')
            this.loadEditTableData(this.editFormData.id)
            this.handleTimeDetailEditCancel()
          } else {
            this.$message.error(res.message || '修改时间明细失败')
          }
        })
        .catch(() => {
          this.$message.error('修改时间明细失败')
        })
    },
    handleTimeDetailEditCancel() {
      this.timeDetailEditModalVisible = false
      this.timeDetailEditFormData = {
        id: '',
        timeType: 0,
        startTime: null,
        classHour: null
      }
    },
    handleEdit(record) {
      this.editModalVisible = true
      this.editFormData = {
        id: record.id,
        templateName: record.templateName,
        teachType: record.teachType
      }
      // 每次打开编辑弹窗时重置编辑表格分页为第一页
      this.editPagination.current = 1
      this.loadEditTableData(record.id)
    },
    handleEditCancel() {
      this.editModalVisible = false
      this.editFormData = {
        id: '',
        templateName: '',
        teachType: undefined,
        timeDetails: []
      }
      this.selectedDetailRowKeys = []
      this.editTableData = []
    },
    handleEditSave() {
      if (!this.editFormData.templateName) {
        this.$message.warning('请输入方案名称')
        return
      }
      const params = {
        id: this.editFormData.id,
        templateName: this.editFormData.templateName
      }
      postAction('/gzpt/timeTemplate/edit', params)
        .then(res => {
          if (res.success) {
            this.$message.success('修改成功')
            this.loadData()
          } else {
            this.$message.error(res.message || '修改失败')
          }
        })
        .catch(() => {
          this.$message.error('修改失败')
        })
    },
    handleTableChange(pagination) {
      this.ipagination = Object.assign({}, this.ipagination, {
        current: pagination.current,
        pageSize: pagination.pageSize
      })
      this.loadData()
    },
    searchQuery() {
      this.ipagination.current = 1
      this.loadData()
    },
    searchReset() {
      this.queryParam.templateName = ''
      this.queryParam.teachType = undefined
      this.searchQuery()
    },
    showAddModal() {
      this.isModalVisible = true
    },
    handleCancel() {
      this.isModalVisible = false
      this.form.resetFields()
      this.formData.templateName = ''
      this.formData.teachType = undefined
    },
    async handleSave() {
      if (!this.formData.templateName) {
        this.$message.warning('请输入方案名称')
        return
      }
      if (this.formData.teachType === undefined) {
        this.$message.warning('请选择教学计划类型')
        return
      }
      const params = {
        templateName: this.formData.templateName,
        teachType: this.formData.teachType
      }
      let res = await postAction('/gzpt/timeTemplate/add', params)
      this.$message.success('新增成功')
      await this.loadData()
      this.handleCancel()
      // 找到匹配的记录并打开编辑弹窗
      const newRecord = this.dataSource.find(
        item => item.templateName === params.templateName && item.teachType === params.teachType
      )
      if (newRecord) {
        this.handleEdit(newRecord)
      }
    },
    showAddTimeDetailModal() {
      this.addTimeDetailModalVisible = true
      this.addTimeDetailFormData = {
        timeType: 0,
        startTime: null,
        endTime: null,
        classHour: 1
      }
    },
    handleAddTimeDetailCancel() {
      this.addTimeDetailModalVisible = false
      this.addTimeDetailForm.resetFields()
      this.addTimeDetailFormData = {
        timeType: 0,
        startTime: null,
        endTime: null,
        classHour: 1
      }
    },
    handleAddDetailStartTimeChange(time) {
      this.addTimeDetailFormData.startTime = time
      if (this.addTimeDetailFormData.classHour) {
        this.calculateEndTime()
      }
    },
    handleAddDetailClassHourChange(value) {
      this.addTimeDetailFormData.classHour = value
      if (this.addTimeDetailFormData.startTime) {
        this.calculateEndTime()
      }
    },
    calculateEndTime() {
      if (this.addTimeDetailFormData.startTime && this.addTimeDetailFormData.classHour) {
        const startMoment = moment(this.addTimeDetailFormData.startTime)
        this.addTimeDetailFormData.endTime = startMoment.clone().add(this.addTimeDetailFormData.classHour, 'hours')
      }
    },
    handleAddTimeDetailSave() {
      if (!this.addTimeDetailFormData.startTime || !this.addTimeDetailFormData.classHour) {
        this.$message.warning('请填写完整信息')
        return
      }
      const params = {
        templateId: this.editFormData.id,
        timeType: this.addTimeDetailFormData.timeType,
        startTime: moment(this.addTimeDetailFormData.startTime).format('HH:mm'),
        endTime: this.calculatedEndTime,
        classHour: this.addTimeDetailFormData.classHour
      }
      postAction('/gzpt/timeTemplateDetails/add', params)
        .then(res => {
          if (res.success) {
            this.$message.success('新增时间明细成功')
            this.loadEditTableData(this.editFormData.id)
            this.handleAddTimeDetailCancel()
          } else {
            this.$message.error(res.message || '新增时间明细失败')
          }
        })
        .catch(() => {
          this.$message.error('新增时间明细失败')
        })
    }
  }
}
</script>

<style scoped>
.table-operator {
  margin-bottom: 15px;
}
.table-operator button {
  margin-right: 8px;
}
</style>
