<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="模拟中心名称">
              <a-input v-model="queryParam.simulationCenterName" placeholder="请输入模拟中心名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <simulationCenterModal ref="modalForm" @ok="modalFormOk"></simulationCenterModal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import simulationCenterModal from './simulationCenterModal'
import { putAction } from '@/api/manage'
export default {
  name: 'Mr890Info',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    simulationCenterModal
  },
  data() {
    return {
      description: '模拟中心名称',
      dataSource2: [
        {
          deviceId: '123888',
          centerName: '东阳-恒通',
          status: '在线',
          isUseFace: '启用'
        }
      ],
      // 表头
      columns: [
        {
          title: '#', // 显示行号
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '模拟中心名称',
          align: 'center',
          dataIndex: 'simulationCenterName'
        },
        {
          // 恢复操作列
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/scheduling/schedulingImiCenter/list',
        delete: '/scheduling/schedulingImiCenter/delete', // 添加删除接口
        deleteBatch: '/scheduling/schedulingImiCenter/deleteBatch' // 添加批量删除接口
        // exportXlsUrl: "/scheduling/schedulingImiCenter/exportXls", // 导出接口暂时注释
        // importExcelUrl: "scheduling/schedulingImiCenter/importExcel", // 导入接口暂时注释
      },
      dictOptions: {}
      // superFieldList: [], // 高级查询字段暂时注释
    }
  },
  created() {
    // this.getSuperFieldList(); // 高级查询相关，暂时注释
  },
  computed: {
    // importExcelUrl: function() { // 导入相关，暂时注释
    //   return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
    // },
  },
  methods: {
    initDictConfig() {}
    // handleOn(){ // 启用方法暂时注释
    // },
    // handleOff(){ // 停用方法暂时注释
    // },
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
