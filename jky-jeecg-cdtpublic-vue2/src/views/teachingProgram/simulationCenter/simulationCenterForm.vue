<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container>
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="模拟中心名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="simulationCenterName">
              <a-input v-model="model.simulationCenterName" placeholder="请输入模拟中心名称" :disabled="formDisabled"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾校列表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscodeList">
              <j-select-depart
                v-model="model.inscodeList"
                :multi="true"
                @back="backDepartInfo"
                :backDepart="true"
              >
              </j-select-depart>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import { formMixins } from '@/mixins/formMixins'
export default {
  name: 'Mr890VersionForm',
  components: {
    JTreeSelectDepart
  },
  mixins: [formMixins],
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        simulationCenterName: [{ required: true, message: '请输入单位名称!' }]
      },
      url: {
        add: '/scheduling/schedulingImiCenter/add',
        edit: '/scheduling/schedulingImiCenter/edit',
        queryById: '/scheduling/schedulingImiCenter/queryById'
      },
      title: '',
      versionList: [],
      deviceList: []
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
  },
  methods: {
    add() {
      this.edit(this.modelDefault)
    },
    edit(record) {
      if (record.hasOwnProperty('deviceId')) {
        this.title = 'edit'
      } else {
        this.title = 'add'
      }
      this.model = Object.assign({}, record)
      this.visible = true
    },
    backDepartInfo(info) {
      this.model.departIds = this.model.selecteddeparts
      this.nextDepartOptions = info.map((item, index, arr) => {
        let c = { label: item.text, value: item.value + '' }
        return c
      })
    },

    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>
