<template>
  <a-modal v-model="visible" title="添加学员" okText="提交" @ok="handleConfirmAdd" :width="'60%'">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="身份证号">
              <a-input v-model="queryParam.idcard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columnsStudent"
        :dataSource="dataSource"
        :loading="loading"
        :pagination="pagination"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'checkbox' }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
      </a-table>
    </div>
  </a-modal>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { postAction, getAction } from '@api/manage'

export default {
  mixins: [JeecgListMixin, mixinDevice],
  props: {
    classId: {
      type: String,
      default: ''
    },
    scheduleType: {
      type: String,
      default: '1' // 排班类型 1-实操、2-课堂、3-模拟
    }
  },
  data() {
    return {
      disableMixinCreated:true,
      visible: false,
      queryParam: {
        idcard: ''
      },
      columnsStudent: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '学员编号',
          align: 'center',
          dataIndex: 'stunum'
        },
        {
          title: '身份证号',
          align: 'center',
          dataIndex: 'idcard'
        },
        {
          title: '学员手机号',
          align: 'center',
          dataIndex: 'phone'
        }
      ],
      dataSource: [],
      url: {
        list: '/openClass/student/queryStu'
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条数据`
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadData()
      }
    }
  },
  methods: {
    openModal() {
      this.searchReset()
      this.visible = true
    },
    searchQuery() {
      this.loadData(1)
    },
    searchReset() {
      this.queryParam.idcard = ''
      this.selectedRowKeys = []  // 重置选中的行
      this.selectionRows = []    // 重置选中的数据
      this.loadData(1)
    },
    loadData(page = this.pagination.current) {
      this.loading = true
      const params = {
        ...this.queryParam,
        pageNo: page,
        pageSize: this.pagination.pageSize
      }
      getAction(this.url.list, params)
        .then((res) => {
          if (res.success) {
            this.dataSource = res.result.records
            this.pagination.total = res.result.total
            this.pagination.current = page
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleTableChange(pagination) {
      this.selectionRows=[]
      this.selectedRowKeys=[]
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.loadData(pagination.current)
    },
    handleConfirmAdd() {
      console.log(this.selectedRowKeys, this.selectionRows); // 查看选中的数据
      if (this.selectionRows.length > 0) {
        if (this.selectionRows.length <= 10) {
          this.$confirm({
            title: '提示',
            content: '是否确认添加选中的学员?',
            onOk: () => {
              const ids = this.selectionRows.map((item) => item.stunum).join(',');
              postAction('/openClass/student/add', {
                openClassId: this.classId,
                stunum: ids
              }).then((res) => {
                if (res.success) {
                  this.$message.success(res.message);
                  this.$emit('refresh');
                  this.visible = false;
                } else {
                  this.$message.error(res.message);
                }
              });
            },
            onCancel: () => {
              console.log('取消添加操作');
            }
          });
        } else {
          this.$message.error('最多只能选择10个学员');
        }
      } else {
        this.$message.error('请至少选择一个学员');
      }
    }

  }
}
</script>
