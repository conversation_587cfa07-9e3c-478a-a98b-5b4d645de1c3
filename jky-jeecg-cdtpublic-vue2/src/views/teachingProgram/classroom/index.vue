<template>
  <a-card :bordered="false">
    <!-- 教学计划(课堂) -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="开班时间">
              <a-date-picker
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryParam.classDate_begin"
                valueFormat="YYYY-MM-DD"
              >
              </a-date-picker>
              <span class="query-group-split-cust"></span>
              <a-date-picker
                placeholder="请选择结束日期"
                class="query-group-cust"
                v-model="queryParam.classDate_end"
                valueFormat="YYYY-MM-DD"
              >
              </a-date-picker>
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <!-- <a-button type="primary" icon="printer" v-has="'gzpt:securityguard:export'">打印</a-button> -->
    </div>
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: '100%' }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="handleShowStudents(record)">{{ record.curAmount }}</a>
        </span>
      </a-table>
    </div>
    <class-manage-add-modal ref="modalForm" @ok="modalFormOk"></class-manage-add-modal>
    <class-manage-student-modal
      ref="studentModalForm"
      title="开班学员管理"
      scheduleType="2"
      :classInfo="curClassInfo"
      @refresh="loadData"
    ></class-manage-student-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction } from '@api/manage'
import ClassManageAddModal from './modules/ClassManageAddModal.vue'
import ClassManageStudentModal from './modules/ClassManageStudentModal.vue'
export default {
  name: 'classManage',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    ClassManageAddModal,
    ClassManageStudentModal
  },
  data() {
    return {
      description: '学员开班管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '班次编号',
          align: 'center',
          dataIndex: 'id' ,width: 250, // 限定宽度
        },
        {
          title: '班次名称',
          align: 'center',
          dataIndex: 'className',
          width: 300, // 限定宽度
          ellipsis: true, // 文字超出省略
          customRender: (text) => {
            return (
              <a-tooltip placement="topLeft">
                <template slot="title">{text}</template>
                <span>{text}</span>
              </a-tooltip>
            )
          }
        },
        {
          title: '所属驾校 ',
          width: 300, // 限定宽度
          align: 'center',
          dataIndex: 'inscode_dictText'
        },
        // {
        //   title: '教练名称',
        //   align: 'center',
        //   dataIndex: 'coachnum_dictText'
        // },
        // {
        //   title: '培训车型',
        //   align: 'center',
        //   dataIndex: 'traintype'
        // },
        // {
        //   title: '科目',
        //   align: 'center',
        //   dataIndex: 'subject',
        //   customRender: text => {
        //     if (text === 5) {
        //       return '总体'
        //     } else if (text === 1) {
        //       return '科目一'
        //     } else if (text === 2) {
        //       return '科目二'
        //     } else if (text === 3) {
        //       return '科目三'
        //     } else if (text === 4) {
        //       return '科目四'
        //     }
        //   }
        // },
        // {
        // 	title: '班次名称',
        // 	align: 'center',
        // 	dataIndex: 'className'
        // },
        {
          title: '开班日期',
          align: 'center',
          dataIndex: 'classDate'
        },
        // {
        //   title: '开班时间',
        //   align: 'center',
        //   dataIndex: 'classTime'
        // },
        {
          title: '当前开班人数',
          align: 'center',
          dataIndex: 'curAmount',
          scopedSlots: {
            customRender: 'action'
          }
        },
        // {
        //   title: '开班状态',
        //   align: 'center',
        //   dataIndex: 'auditStatus_dictText'
        // }
      ],
      url: {
        list: '/openClass/list'
      },
      dataSource: [],
      queryParam: {
        classDate_begin: '',
        classDate_end: ''
      },
      curClassInfo: {}
    }
  },
  created() {
    //this.getSuperFieldList();
  },
  computed: {},
  methods: {
    // handleSelectionChange(selectedRowKeys, record) {
    //   this.selectedRowKeys = selectedRowKeys
    //   console.log(selectedRowKeys, '选中', record)
    //   // this.ids = selection.map(item => item.id)
    //   // this.stunum = selection.map(item => item.stunum)
    //   // this.inscodes = selection.map(item => item.inscodes)
    //   this.stunum = record[0].stunum
    //   this.signpath = record[0].signpath
    //   this.inscodes = record[0].inscodes
    //   // this.single = selection.length !== 1;
    //   // this.multiple = !selection.length;
    // },
    handleShowStudents(record) {
      console.log(record)
      this.curClassInfo = { ...record }
      setTimeout(() => {
        console.log(this.$refs.studentModalForm)
        this.$refs.studentModalForm.openModal(record)
      }, 0)
      // getAction('/schedule/class/apply/list', { classId: record.id}).then((res) => {
      //   if (res.success) {
      //     this.studentManageModalVisible = true
      //     console.log(res)
      //     this.dataSourceStudent = res.result.records
      //   } else {
      //     this.$message.error(res.message)
      //   }
      // })
    },
    handleAddStudent() {
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
