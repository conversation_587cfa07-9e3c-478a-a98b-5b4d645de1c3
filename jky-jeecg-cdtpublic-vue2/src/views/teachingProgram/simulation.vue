<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <!-- 第一行：开课日期、科目、教练员 -->
        <a-row :gutter="24">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="开课日期">
              <a-date-picker
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryParam.classDate_begin"
                valueFormat="YYYY-MM-DD"
              />
              <span class="query-group-split-cust"></span>
              <a-date-picker
                placeholder="请选择结束日期"
                class="query-group-cust"
                v-model="queryParam.classDate_end"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="科目">
              <a-select v-model="queryParam.subject" placeholder="请选择科目">
                <a-select-option :value="2">科目二</a-select-option>
                <a-select-option :value="3">科目三</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="教练员">
              <a-select
                :value="queryParam.coachnum"
                placeholder="请选择教练员"
                show-search
                @change="handleSearch"
                optionFilterProp="title"
              >
                <a-select-option
                  v-for="(item, index) in cocachList"
                  :key="item.coachnum"
                  :value="item.coachnum"
                  :title="item.name"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第二行：新增查询条件：教室与是否发布 -->
        <a-row :gutter="24" style="margin-top: 10px;">
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="教室">
              <a-input v-model="queryParam.classRoomName" placeholder="请输入教室名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="是否发布">
              <a-select v-model="queryParam.publish" placeholder="请选择是否发布">
                <a-select-option value="">全部</a-select-option>
                <a-select-option :value="1">已发布</a-select-option>
                <a-select-option :value="0">未发布</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第三行：查询、重置按钮 -->
        <a-row :gutter="24" style="margin-top: 10px;">
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="margin-top: 16px;">
      <a-button @click="handleAdd" type="primary" icon="plus" v-if="!belongsToSimulationCenter">批量新增</a-button>
      <a-button
        @click="handleBatchPublish"
        type="primary"
        icon="upload"
        style="margin-left: 8px;"
        v-if="!belongsToSimulationCenter"
        >批量发布</a-button
      >
      <!-- 新增人工预约按钮 -->
      <a-button
        @click="handleManualReservation"
        type="primary"
        icon="user"
        style="margin-left: 8px;"
        v-if="!belongsToSimulationCenter"
        >人工预约</a-button
      >
      <!-- 添加批量删除按钮 -->
      <a-button
        @click="handleBatchDelete"
        type="danger"
        icon="delete"
        style="margin-left: 8px;"
        v-if="!belongsToSimulationCenter"
        >批量删除</a-button
      >
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: 2100 }"
        bordered
        rowKey="id"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange
        }"
        :columns="computedColumns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <!-- 自定义“已约”列显示 -->
        <template slot="inviteSlot" slot-scope="text, record">
          <span v-if="belongsToSimulationCenter">{{ record.curAmount }}</span>
          <a @click="handleShowStudents(record)" v-if="!belongsToSimulationCenter">{{ record.curAmount }}</a>
        </template>
        <!-- 操作列 -->
        <span slot="action" slot-scope="text, record">
          <a @click="handleCanclePublish(record)" v-if="record.curAmount === 0">
            {{ record.publish ? '取消发布' : '发布' }}
          </a>
          <a-divider type="vertical" v-if="record.curAmount === 0 && record.publish === 0" />
          <a @click="handleEdit(record)" v-if="record.publish === 0">修改</a>
          <a-divider type="vertical" v-if="record.publish === 0" />
          <a-popconfirm title="确定删除吗?" v-if="record.publish === 0" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <!-- table区域-END -->

    <!-- 弹窗区域 -->
    <SimulationAddModal ref="modalForm" @ok="modalFormOk"></SimulationAddModal>
    <teachManageStudentModal
      ref="studentModalForm"
      :classInfo="curClassInfo"
      scheduleType="3"
      title="教学计划模拟新增学员"
      @refresh="loadData"
    ></teachManageStudentModal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import SimulationAddModal from './modules/SimulationAddModal'
import teachManageStudentModal from './modules/teachManageStudentModal.vue'
import { getCocachList, getCocachList2 } from '@api/gzpt/studentinfo'
import { postAction, deleteAction, getAction } from '@api/manage'

export default {
  name: 'simulation',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    SimulationAddModal,
    teachManageStudentModal
  },
  data() {
    return {
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '50', '100'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      cocachList: [],
      description: '教学计划(模拟)页面',
      // 表头定义（包含新增“是否发布”和“已约”列）
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (t, r, index) => {
            return parseInt(index) + 1
          }
        },
        {
          title: '驾校',
          align: 'center',
          width: 300,
          ellipsis: true,
          dataIndex: 'inscode_dictText'
        },
        {
          title: '模拟教室',
          width: 300,
          align: 'center',
          ellipsis: true,
          dataIndex: 'classRoomName'
        },
        {
          title: '教练',
          width: 100,
          align: 'center',
          dataIndex: 'coachnum_dictText'
        },
        {
          title: '车型',
          align: 'center',
          dataIndex: 'traintype'
        },
        {
          title: '科目',
          align: 'center',
          dataIndex: 'subject',
          customRender: t => {
            if (t == 1) return '科目一'
            else if (t == 2) return '科目二'
            else if (t == 3) return '科目三'
            else if (t == 4) return '科目四'
            else if (t == 5) return '全部'
            else return ''
          }
        },
        {
          title: '课程日期',
          align: 'center',
          dataIndex: 'classDate'
        },
        {
          title: '课程时间',
          align: 'center',
          dataIndex: 'classTime'
        },
        {
          title: '学时',
          width: 100,
          align: 'center',
          dataIndex: 'classHours'
        },
        {
          title: '是否发布',
          width: 100,
          align: 'center',
          dataIndex: 'publish',
          customRender: (text, record) => {
            return record.publish ? '已发布' : '未发布'
          }
        },
        {
          title: '可约',
          width: 100,
          align: 'center',
          dataIndex: 'stuAmount'
        },
        {
          title: '已约',
          width: 100,
          align: 'center',
          dataIndex: 'curAmount',
          scopedSlots: { customRender: 'inviteSlot' }
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 接口地址设置（含 deleteBatch 用于批量删除）
      url: {
        list: '/schedule/imi/list',
        delete: '/schedule/imi/delete',
        deleteBatch: '/schedule/imi/deleteBatch'
      },
      // 查询条件定义
      queryParam: {
        classDate_begin: '',
        classDate_end: '',
        createTime_begin: '',
        createTime_end: '',
        subject: null,
        coachnum: null,
        classRoomName: '',
        publish: ''
      },
      curClassInfo: {},
      // 是否属于模拟中心的标识
      belongsToSimulationCenter: false
    }
  },
  computed: {
    // 根据是否属于模拟中心动态计算表格列
    computedColumns() {
      if (this.belongsToSimulationCenter) {
        // 如果属于模拟中心，过滤掉操作列
        return this.columns.filter(column => column.title !== '操作')
      }
      return this.columns
    }
  },
  created() {
    this.getCocachListWrapper()
    // 让 a-select placeholder 生效
    this.queryParam.coachnum = undefined
    // 检查当前驾校是否属于模拟中心
    this.checkSimulationCenterStatus()
  },
  methods: {
    handleSearch(e) {
      this.$set(this.queryParam, 'coachnum', e)
    },
    getCocachListWrapper() {
      getCocachList2({
        teachtype: 3,
        pageSize: 9999,
        pageNo: 1
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
        } else {
          this.$message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    handleShowStudents(record) {
      this.curClassInfo = { ...record }
      // 保证弹窗引用已更新
      setTimeout(() => {
        this.$refs.studentModalForm.openModal(this.curClassInfo)
      }, 0)
    },
    // 单条【发布/取消发布】操作
    handleCanclePublish(record) {
      let remindTxt = '是否确认'
      let nextStatus = 0
      if (record.publish) {
        remindTxt += '取消发布'
      } else {
        remindTxt += '发布'
        nextStatus = 1
      }
      remindTxt += '该计划？'
      this.$confirm({
        title: '提示',
        content: remindTxt,
        onOk: () => {
          postAction('/schedule/imi/pubOrCancel', {
            ids: record.id,
            publish: nextStatus
          }).then(res => {
            this.$message.success(res.message)
            this.loadData()
            // 切换页面时清空选择
            this.selectedRowKeys = []
            this.selectionRows = []
          })
        }
      })
    },
    // 批量发布操作
    handleBatchPublish() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请至少选择一条记录进行操作')
        return
      }
      this.$confirm({
        title: '提示',
        content: '是否确认批量发布该计划？',
        onOk: () => {
          const ids = this.selectedRowKeys.join(',')
          postAction('/schedule/imi/pubOrCancel', { ids, publish: 1 }).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              this.onClearSelected()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 新增人工预约操作：判断是否仅选中一条记录，满足则调用 handleShowStudents 方法
    handleManualReservation() {
      if (this.selectionRows.length !== 1) {
        this.$message.warning('请勾选一条记录进行人工预约')
        return
      }
      this.handleShowStudents(this.selectionRows[0])
    },
    // 批量删除方法
    handleBatchDelete() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }

      // 检查选中的记录中是否有已发布或已预约的
      const hasPublishedOrAppointed = this.selectionRows.some(record => record.publish === 1 || record.curAmount > 0)

      if (hasPublishedOrAppointed) {
        this.$message.error('选中记录中包含已发布或已预约的课程，不可删除')
        return
      }

      this.$confirm({
        title: '确认删除',
        content: `确定删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return deleteAction(this.url.deleteBatch, {
            ids: this.selectedRowKeys.join(',')
          })
            .then(res => {
              if (res.success) {
                this.$message.success('删除成功')
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message || '删除失败')
              }
            })
            .catch(err => {
              console.error('删除失败:', err)
              this.$message.error('删除失败')
            })
        }
      })
    },
    // 重写 handleTableChange 方法
    handleTableChange(pagination, filters, sorter) {
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field
        this.isorter.order = 'ascend' == sorter.order ? 'asc' : 'desc'
      }
      this.ipagination = pagination
      // 切换页面时清空选择
      this.selectedRowKeys = []
      this.selectionRows = []
      this.loadData()
    },
    // 检查当前驾校是否属于模拟中心
    checkSimulationCenterStatus() {
      getAction('/schedule/imi/checkIfBelongsToSimulationCenter')
        .then(res => {
          if (res.success) {
            this.belongsToSimulationCenter = res.success
          } else {
            console.error('检查模拟中心状态失败:', res.message)
          }
        })
        .catch(error => {
          console.error('检查模拟中心状态出错:', error)
        })
    }
  }
}
</script>

<style scoped lang="less">
@import '~@assets/less/common.less';

.table-page-search-wrapper {
  .query-group-cust {
    width: 126px;
  }
  .query-group-split-cust {
    width: 14px;
    display: inline-block;
    text-align: center;
  }
  /* 调整日期控件宽度 */
  /deep/ .ant-calendar-picker {
    width: 45% !important;
  }
}
</style>
