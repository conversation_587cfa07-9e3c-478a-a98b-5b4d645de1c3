<template>
  <a-modal
    v-model="studentManageModalVisible"
    :title="title"
    :width="'80%'"
    :footer="null"
    :afterClose="handleModalClose"
  >
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="姓名">
              <a-input v-model="queryParam.stuname" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="学员手机">
              <a-input v-model="queryParam.phone" placeholder="请输入学员手机" />
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAddStudent" type="primary" icon="plus">添加学员</a-button>
    </div>
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: 1800 }"
        bordered
        rowKey="id"
        :columns="columnsStudent"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="classNameSlot">
          <!--          <div>{{ record.className }}</div>-->
          <a-tooltip placement="topLeft">
            <template slot="title">{{ classInfo.className }}</template>
            <span>{{ classInfo.className }}</span>
          </a-tooltip>
        </template>
        <template slot="classDateSlot">
          <div>{{ classInfo.classDate }}</div>
        </template>
        <template slot="classTimeSlot">
          <div>{{ classInfo.classTime }}</div>
        </template>
        <template slot="subjectSlot">
          <div>{{ getSubjectTxt(classInfo.subject) }}</div>
        </template>
        <template slot="coachSlot">
          <div>{{ classInfo.coachnum_dictText }}</div>
        </template>
        <template slot="actionSlot" slot-scope="text, record">
          <a-popconfirm
            title="确定删除这条记录吗?"
            @confirm="() => handleDelete(record.id)"
            okText="确定"
            cancelText="取消"
          >
            <a>删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </div>
    <teachManageStudentAddModal
      ref="studentAddModal"
      :scheduleType="scheduleType"
      :classId="classInfo.id"
      @refresh="loadData"
    ></teachManageStudentAddModal>
  </a-modal>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, deleteAction } from '@api/manage'
import teachManageStudentAddModal from './teachManageStudentAddModal'

export default {
  mixins: [JeecgListMixin, mixinDevice],
  components: { teachManageStudentAddModal },
  props: {
    title: {
      type: String,
      default: '管理'
    },
    classInfo: {
      type: Object,
      default: {}
    },
    scheduleType: {
      type: String,
      default: '1' // 排班类型 1-实操、2-课堂、3-模拟
    }
  },
  data() {
    return {
      record: {},
      disableMixinCreated: true,
      studentManageModalVisible: false,
      queryParam: {
        stuname: '',
        phone: '',
        classId: this.classInfo.id || ''
      },
      studentClassColumns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        // {
        //   title: '班次编号',
        //   align: 'center',
        //   dataIndex: 'classId'
        // },
        // {
        //   title: '班次名称',
        //   align: 'center',
        //   dataIndex: 'className',
        //   width: 300, // 限定宽度
        //   ellipsis: true, // 文字超出省略
        //   scopedSlots: {
        //     customRender: 'classNameSlot'
        //   }
        //   // customRender: (text) => {
        //   //   return (
        //   //     <a-tooltip placement="topLeft">
        //   //       <template slot="title">{text}</template>
        //   //       <span>{text}</span>
        //   //     </a-tooltip>
        //   //   )
        //   // }
        // },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'stuname'
        },
        {
          title: '学员编号',
          align: 'center',
          dataIndex: 'stunum'
        },
        {
          title: '学员手机号',
          align: 'center',
          dataIndex: 'phone'
        },
        {
          title: '申请时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: { customRender: 'actionSlot' }
        }
      ],
      teachingColumns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '开课时间',
          align: 'center',
          scopedSlots: {
            customRender: 'classDateSlot'
          }
        },
        {
          title: '课程时间',
          align: 'center',
          scopedSlots: {
            customRender: 'classTimeSlot'
          }
        },
        {
          title: '科目',
          align: 'center',
          scopedSlots: {
            customRender: 'subjectSlot'
          }
        },
        {
          title: '教练员',
          align: 'center',
          scopedSlots: {
            customRender: 'coachSlot'
          }
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'stuname'
        },
        {
          title: '学员手机号',
          align: 'center',
          dataIndex: 'phone'
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: { customRender: 'actionSlot' }
        }
      ],
      dataSource: [],
      url: {
        list: '/schedule/apply/list',
        delete: '/schedule/apply/oprImi/delete'
      }
    }
  },
  computed: {
    columnsStudent() {
      return this.scheduleType === '2' ? this.studentClassColumns : this.teachingColumns
    }
  },
  methods: {
    getSubjectTxt(t) {
      if (t == 1) {
        return '科目一'
      } else if (t == 2) {
        return '科目二'
      } else if (t == 3) {
        return '科目三'
      } else if (t == 4) {
        return '科目四'
      } else if (t == 5) {
        return '全部'
      }
    },
    openModal(record) {
      this.record = record
      this.queryParam.classId = this.classInfo.id
      this.classInfo.id && this.loadData()
      this.studentManageModalVisible = true
    },
    searchReset() {
      this.queryParam.stuname = ''
      this.queryParam.phone = ''
      this.loadData(1)
    },
    handleAddStudent() {
      this.$refs.studentAddModal.openModal(this.record)
    },
    handleModalClose() {
      this.$emit('refresh')
    },
    handleDelete(id) {
      deleteAction(this.url.delete, { id: id, type: this.scheduleType }).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(res.message || '删除失败')
        }
      })
    }
  }
}
</script>
<style></style>
