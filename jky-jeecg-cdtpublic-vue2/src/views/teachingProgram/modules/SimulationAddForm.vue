<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="教练员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
              <a-select
                :value="model.coachnum"
                placeholder="请选择教练员"
                show-search
                @change="handleSearch"
                optionFilterProp="title"
              >
                <a-select-option
                  v-for="(item, index) in cocachList"
                  :value="item.coachnum"
                  :key="item.coachnum"
                  :title="item.name"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="教室" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classRoomName">
              <a-select v-model="model.classRoomName" placeholder="请选择教室" @change="handleClassroomChange">
                <a-select-option v-for="(room, index) in classroomList" :key="room.id" :value="room.classRoomName">
                  {{ room.classRoomName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subject">
              <a-select v-model="model.subject" placeholder="请选择科目">
                <a-select-option :value="2">科目二</a-select-option>
                <a-select-option :value="3">科目三</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="课程容量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuAmount">
              <a-input-number
                v-model:value="model.stuAmount"
                style="width: 250px"
                :min="1"
                :step="1"
                :formatter="value => (value ? Math.floor(value) : '')"
                :parser="value => value.replace(/[^\d]/g, '')"
                placeholder="请输入课程容量"
                @blur="handleStuAmountChange"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12" v-if="!isEditMode">
            <a-form-model-item label="开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classDateStart">
              <a-date-picker
                placeholder="请选择开始日期"
                :disabledDate="disabledClassDate"
                v-model="model.classDateStart"
                valueFormat="YYYY-MM-DD"
                style="width: 250px"
                @change="handleDateChange"
              >
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="!isEditMode">
            <a-form-model-item label="结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classDateEnd">
              <a-date-picker
                placeholder="请选择结束日期"
                :disabledDate="disabledClassDateEnd"
                v-model="model.classDateEnd"
                valueFormat="YYYY-MM-DD"
                style="width: 250px"
                @change="handleDateChange"
              >
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="!isEditMode">
            <!-- 时间方案选择 -->
            <a-form-model-item label="时间方案" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="templateId">
              <a-select
                v-model="model.templateId"
                placeholder="请选择时间方案"
                @change="handleTemplateChange"
                style="width: 250px"
              >
                <a-select-option v-for="template in timeTemplateList" :key="template.id" :value="template.id">
                  {{ template.templateName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <!--          <a-col :span="12">-->
          <!--            &lt;!&ndash; 新增的：学时选择 &ndash;&gt;-->
          <!--            <a-form-model-item-->
          <!--              label="学时"-->
          <!--              :labelCol="labelCol"-->
          <!--              :wrapperCol="wrapperCol"-->
          <!--              prop="classHours"-->
          <!--            >-->
          <!--              <a-select-->
          <!--                v-model="model.classHours"-->
          <!--                placeholder="请选择学时"-->
          <!--                style="width: 250px"-->
          <!--                @change="handleClassHoursChange"-->
          <!--              >-->
          <!--                <a-select-option v-for="n in 4" :key="n" :value="n">{{ n }} 小时</a-select-option>-->
          <!--              </a-select>-->
          <!--            </a-form-model-item>-->
          <!--          </a-col>-->
          <a-col :span="12">
            <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <j-dict-select-tag
                type="list"
                v-model="model.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import moment from 'moment'
import { getCocachList2 } from '@api/gzpt/studentinfo'

export default {
  name: 'SimulationAddForm',
  props: {
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    // 添加日期范围验证函数
    const validateDateRange = (rule, value, callback) => {
      const startDate = this.model.classDateStart ? moment(this.model.classDateStart) : null;
      const endDate = this.model.classDateEnd ? moment(this.model.classDateEnd) : null;

      // 只有当两个日期都有效时才进行比较
      if (startDate && endDate) {
        if (endDate.isBefore(startDate)) {
          // 校验失败
          callback(new Error('结束日期必须大于等于开始日期'));
        } else {
          // 校验成功
          callback();
        }
      } else {
        // 如果任一日期无效（例如清空了），则认为范围校验通过，依赖 required 规则
        callback();
      }
    };

    return {
      cocachList: [],
      classroomList: [],
      timeTemplateList: [], // 时间方案列表
      timeTemplateDetails: [], // 选中的时间方案详情
      isEditMode: false, // 新增：标记是否为编辑模式
      model: {
        coachnum: '',
        subject: '',
        traintype: '',
        classDateStart: moment()
          .add(1, 'day')
          .format('YYYY-MM-DD'),
        classDateEnd: moment()
          .add(7, 'day')
          .format('YYYY-MM-DD'), // 结束日期默认为一周后
        templateId: undefined, // 时间方案ID
        classTime1: null,
        classTime2: null, // 自动计算： 开课结束时间 = 开课开始时间 + 学时
        classRoomName: '',
        stuAmount: '',
        classHours: null // 新增字段：学时（int 类型）
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        subject: [{ required: true, message: '请输入科目' }],
        classRoomName: [{ required: true, message: '请选择教室' }],
        classDateStart: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' }
        ],
        classDateEnd: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' }
        ],
        templateId: [{ required: true, message: '请选择时间方案', trigger: 'change' }],
        stuAmount: [{ required: true, message: '请输入课程容量' }],
        coachnum: [{ required: true, message: '请选择教练员' }],
        traintype: [{ required: true, message: '请选择培训车型' }],
        // 新增验证规则：分别校验开课开始时间和学时
        classTime1: [{ required: false, message: '请选择开课开始时间', trigger: 'change' }],
        classHours: [{ required: false, message: '请选择学时', trigger: 'change' }]
      },
      url: {
        add: '/schedule/imi/add',
        edit: '/schedule/imi/edit'
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    // 备份 model 原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    this.getCocachListWrapper()
    this.getClassroomList()
    this.getTimeTemplateList()
  },
  methods: {
    // 新增：日期变化时触发两个日期字段的校验
    handleDateChange() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField(['classDateStart', 'classDateEnd'], () => {});
        }
      });
    },
    moment,
    handleStuAmountChange(value) {
      if (!value || value < 1) {
        this.model.stuAmount = 1
        this.$message.warning('课程容量必须为正整数且大于 0')
      }
    },
    disabledClassDate(current) {
      // 禁止选择今天及今天之前的日期
      const tomorrow = moment()
        .add(0, 'day')
        .startOf('day')
      return current < tomorrow
    },
    disabledClassDateEnd(current) {
      // 结束日期不能早于开始日期
      const startDate = this.model.classDateStart
        ? moment(this.model.classDateStart).startOf('day')
        : moment()
            .add(1, 'day')
            .startOf('day')
      return current < startDate
    },
    // onStartTimeChange(value) {
    //   // 当用户选择开课开始时间时，如果学时已选，则自动计算结束时间
    //   if (this.model.classHours) {
    //     this.model.classTime2 = moment(value).clone().add(this.model.classHours, 'hours')
    //   }
    //   this.$refs.form.validateField('classTime1')
    // },
    // handleClassHoursChange(value) {
    //   // 当用户选择学时时，如果开始时间已选，则自动计算结束时间
    //   if (this.model.classTime1) {
    //     this.model.classTime2 = moment(this.model.classTime1).clone().add(value, 'hours')
    //   }
    // },
    handleSearch(e) {
      this.$set(this.model, 'coachnum', e)
    },
    getCocachListWrapper() {
      getCocachList2({
        teachtype: 3,
        pageSize: 9999,
        pageNo: 1
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
        } else {
          this.$message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    getClassroomList() {
      // 请求教室列表，classRoomType: 3 表示“模拟”
      getAction('/gzpt/classRoomDetails/dropBox', { classRoomType: 3 }).then(res => {
        if (res.success) {
          this.classroomList = res.result
        } else {
          this.$message.error('获取教室列表失败')
          this.classroomList = []
        }
      })
    },
    handleClassroomChange(value) {
      const selectedRoom = this.classroomList.find(room => room.classRoomName === value)
      if (selectedRoom) {
        this.model.stuAmount = selectedRoom.stuAmount
      }
    },
    // 获取时间方案列表
    getTimeTemplateList() {
      getAction('/gzpt/timeTemplate/list', { teachType: 3, pageSize: 999 }).then(res => {
        if (res.success) {
          this.timeTemplateList = res.result.records || []
        } else {
          this.$message.error('获取时间方案失败')
        }
      })
    },
    // 获取时间方案详情
    // getTimeTemplateDetails(templateId) {
    //   if (!templateId) {
    //     this.timeTemplateDetails = []
    //     return
    //   }
    //   getAction('/gzpt/timeTemplateDetails/list', { templateId: templateId, pageSize: 999 }).then(res => {
    //     if (res.success) {
    //       this.timeTemplateDetails = res.result.records || []
    //     } else {
    //       this.$message.error('获取时间方案详情失败')
    //     }
    //   })
    // },
    // 处理时间方案选择变化
    handleTemplateChange(value) {
      // this.getTimeTemplateDetails(value)
    },
    add() {
      this.edit(this.modelDefault)
      //注意放下面覆盖代码
      this.isEditMode = false // 新增：添加模式
    },
    edit(record) {
      // 清空可能存在的校验状态
      this.$refs.form && this.$refs.form.clearValidate()
      this.isEditMode = true // 新增：编辑模式
      this.model = Object.assign({}, record)

      // 如果有时间方案ID，加载时间方案详情
      // if (this.model.templateId) {
      //   this.getTimeTemplateDetails(this.model.templateId)
      // }

      // 如果存在 classTime 字符串，则拆分转换为 moment 对象并计算学时
      if (this.model.classTime && typeof this.model.classTime === 'string') {
        const times = this.model.classTime.split('-')
        if (times.length === 2) {
          const start = moment(times[0], 'HH:mm')
          const end = moment(times[1], 'HH:mm')
          this.$set(this.model, 'classTime1', start)
          this.$set(this.model, 'classTime2', end)
          // 计算学时（单位：小时）
          // const hoursDiff = end.diff(start, 'hours')
          // this.model.classHours = hoursDiff > 0 ? hoursDiff : 1
        }
      }
      this.visible = true
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查日期范围是否超过一个月
          const startDate = moment(this.model.classDateStart)
          const endDate = moment(this.model.classDateEnd)
          const diffDays = endDate.diff(startDate, 'days')

          if (diffDays > 31) {
            this.$message.warning('开始时间和结束时间间隔不能超过一个月')
            return
          }

          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  },
}
</script>

<style scoped lang="less">
/deep/ .timeRange {
  label::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
</style>
