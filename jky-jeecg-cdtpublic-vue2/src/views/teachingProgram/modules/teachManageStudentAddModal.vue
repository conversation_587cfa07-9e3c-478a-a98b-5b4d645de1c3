<template>
  <a-modal v-model="visible" title="新增" okText="提交" @ok="handleConfirmAdd" :width="'60%'">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="身份证号">
              <a-input v-model="queryParam.idcard" placeholder="请输入身份证号" />
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- table区域-begin -->
    <div>
      <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">-->
      <!--        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择-->
      <!--        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a-->
      <!--        >项-->
      <!--        <a style="margin-left: 24px" @click="onClearSelected">清空</a>-->
      <!--      </div>-->
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :pagination="ipagination"
        :columns="columnsStudent"
        :dataSource="dataSource"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'radio' }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
      </a-table>
    </div>
  </a-modal>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postAction } from '@api/manage'
export default {
  mixins: [JeecgListMixin, mixinDevice],
  props: {
    classId: {
      type: String,
      default: ''
    },
    scheduleType: {
      type: String,
      default: '1' // 排班类型 1-实操、2-课堂、3-模拟
    }
  },
  data() {
    return {
      disableMixinCreated: true,
      visible: false,
      queryParam: {
        idcard: ''
      },
      columnsStudent: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '学员编号',
          align: 'center',
          dataIndex: 'stunum'
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'name'
        },
        // {
        // 	title: '学驾车型',
        // 	align: 'center',
        // 	dataIndex: 'inscode_dictText'
        // },
        {
          title: '学员手机号',
          align: 'center',
          dataIndex: 'phone'
        }
      ],
      dataSource: [],
      url: {
        list: '/gzpt/studentinfo/list'
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    openModal(record) {
      // this.queryParam.classId = this.classInfo.id
      // this.classInfo.id && this.loadData()
      this.traintype = record.traintype
      this.searchReset()
      this.visible = true
    },
    searchQuery() {
      this.queryParam.idcard && this.loadData()
    },
    searchReset() {
      this.queryParam.idcard = ''
      this.selectedRowKeys = []  // 重置选中的行
      this.selectionRows = []    // 重置选中的数据
      this.loadData()
      // this.dataSource = []
    },
    loadData() {
      this.loading = true
      getAction('/gzpt/studentinfo/teachManageStudent/list', {
        ...this.queryParam,
        traintype: this.traintype,
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize
      })
        .then(res => {
          if (res.success) {
            this.dataSource = res.result.records
            this.ipagination.total = res.result.total
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleConfirmAdd() {
      console.log(this.selectedRowKeys, this.selectionRows)
      const that = this
      this.$confirm({
        title: '提示',
        content: '是否确认添加该学员?',
        onOk() {
          if (that.selectionRows[0]) {
            postAction('/schedule/apply/oprImi/add', {
              classId: that.classId,
              stunum: that.selectionRows[0].stunum,
              type: that.scheduleType
              // traintype: that.selectionRows[0].traintype
            }).then(res => {
              res.success ? that.$message.success(res.message) : that.$message.error(res.message)
              that.$emit('refresh')
              // that.visible = false
            })
          } else {
            that.$message.error('请选择学员再提交')
          }
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    }
  }
}
</script>
<style></style>
