<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <!-- 第一行：开课日期、科目、教练员 -->
        <a-row :gutter="24">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="开课日期" class="classDate">
              <a-date-picker
                width="50%"
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryParam.classDate_begin"
                valueFormat="YYYY-MM-DD"
              />
              <span class="query-group-split-cust"></span>
              <a-date-picker
                width="50%"
                placeholder="请选择结束日期"
                class="query-group-cust"
                v-model="queryParam.classDate_end"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="科目">
              <a-select v-model="queryParam.subject" placeholder="请选择科目">
                <a-select-option :value="2">科目二</a-select-option>
                <a-select-option :value="3">科目三</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="教练员">
              <a-select
                :value="queryParam.coachnum"
                placeholder="请选择教练员"
                show-search
                @change="handleSearch"
                optionFilterProp="title"
              >
                <a-select-option
                  v-for="item in cocachList"
                  :key="item.coachnum"
                  :value="item.coachnum"
                  :title="item.name"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：新增查询条件：车型、是否发布、是否预约 -->
        <a-row :gutter="24" style="margin-top: 10px;">
          <a-col :xl="4" :lg="4" :md="4" :sm="24">
            <a-form-item label="车型">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="4" :md="4" :sm="24">
            <a-form-item label="是否发布">
              <a-select v-model="queryParam.publish" placeholder="请选择是否发布">
                <a-select-option value="">全部</a-select-option>
                <a-select-option :value="1">已发布</a-select-option>
                <a-select-option :value="0">未发布</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="4" :md="4" :sm="24">
            <a-form-item label="是否预约">
              <a-select v-model="queryParam.appoint" placeholder="请选择是否预约">
                <a-select-option value="">全部</a-select-option>
                <a-select-option :value="1">已预约</a-select-option>
                <a-select-option :value="0">未预约</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第三行：查询、重置按钮 -->
        <a-row :gutter="24" style="margin-top: 10px;">
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="margin-top: 16px;">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button @click="handleAddBatch" type="primary" icon="plus" style="margin-left: 8px;">批量新增</a-button>
      <a-button @click="handleBatchPublish" type="primary" icon="upload" style="margin-left: 8px;">批量发布</a-button>
      <a-button @click="handlePublishToday" type="primary" icon="cloud-upload" style="margin-left: 8px;"
        >一键发布今日计划</a-button
      >
      <a-button @click="handleManualReservation" type="primary" icon="user" style="margin-left: 8px;"
        >人工预约</a-button
      >
      <a-button
        @click="handleBatchDelete"
        type="danger"
        icon="delete"
        style="margin-left: 8px;"
        :disabled="!selectedRowKeys.length"
        >批量删除</a-button
      >
      <!-- 新增批量撤销发布按钮 -->
      <a-button
        @click="handleBatchCancelPublish"
        type="primary"
        icon="rollback"
        style="margin-left: 8px;"
        :disabled="!selectedRowKeys.length"
      >
        批量撤销发布
      </a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :rowSelection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange
        }"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <!-- 自定义预约状态显示 -->
        <template slot="inviteSlot" slot-scope="text, record">
          <a @click="handleShowStudents(record)">
            {{ record.appoint === 0 ? '未预约' : '已预约' }}
          </a>
        </template>
        <!-- 操作列 -->
        <span slot="action" slot-scope="text, record">
          <a @click="handleTogglePublish(record)" v-if="record.appoint === 0">
            {{ record.publish ? '取消发布' : '发布' }}
          </a>
          <a-divider type="vertical" v-if="record.appoint === 0 && record.publish === 0" />
          <a @click="handleEdit(record)" v-if="record.publish === 0">修改</a>
          <a-divider type="vertical" v-if="record.publish === 0" />
          <a-popconfirm
            title="确定删除这条记录吗?"
            @confirm="() => handleDelete(record.id)"
            v-if="record.publish === 0"
            okText="确定"
            cancelText="取消"
          >
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>

    <!-- 弹窗区域 -->
    <VehicleAddModal ref="modalForm" @ok="modalFormOk" />
    <VehicleBatchAddModal ref="batchModalForm" :key="VehicleBatchAddModalKey" @ok="modalFormOk" />
    <teachManageStudentModal
      ref="studentModalForm"
      :classInfo="curClassInfo"
      scheduleType="1"
      title="教学计划模拟新增学员"
      @refresh="loadData"
    ></teachManageStudentModal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { mixinDevice } from '@/utils/mixin'
import { deleteAction, postAction } from '@/api/manage'
import VehicleAddModal from './modules/VehicleAddModal'
import VehicleBatchAddModal from './modules/VehicleBatchAddModal'
import { getCocachList2 } from '@api/gzpt/studentinfo'
import { message } from 'ant-design-vue'
import teachManageStudentModal from './modules/teachManageStudentModal.vue'

export default {
  name: 'vehicle',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    teachManageStudentModal,
    VehicleAddModal,
    VehicleBatchAddModal
  },
  data() {
    return {
      VehicleBatchAddModalKey: 1,
      cocachList: [],
      description: '教学计划(实车)页面',
      // 表头定义
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => {
            return parseInt(index) + 1
          }
        },
        {
          title: '驾校',
          align: 'center',
          dataIndex: 'inscode_dictText'
        },
        {
          title: '教练',
          align: 'center',
          dataIndex: 'coachnum_dictText'
        },
        {
          title: '车型',
          align: 'center',
          dataIndex: 'traintype'
        },
        {
          title: '科目',
          align: 'center',
          dataIndex: 'subject',
          customRender: text => {
            const subjects = {
              1: '科目一',
              2: '科目二',
              3: '科目三',
              4: '科目四',
              5: '全部'
            }
            return subjects[text] || ''
          }
        },
        {
          title: '课程日期',
          align: 'center',
          dataIndex: 'classDate'
        },
        {
          title: '课程时间',
          align: 'center',
          dataIndex: 'classTime'
        },
        {
          title: '学时',
          align: 'center',
          dataIndex: 'classHours',
          customRender: text => {
            return 1
          }
        },
        {
          title: '预约状态',
          align: 'center',
          dataIndex: 'appoint',
          scopedSlots: { customRender: 'inviteSlot' }
        },
        {
          title: '发布状态',
          align: 'center',
          dataIndex: 'publish_dictText'
        },
        {
          title: '发布日期',
          align: 'center',
          dataIndex: 'publishTime'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 接口地址设置
      url: {
        list: '/schedule/opr/list',
        delete: '/schedule/opr/delete',
        deleteBatch: '/schedule/opr/deleteBatch'
      },
      // 自定义查询条件，同时在 JeecgListMixin 中会与此 queryParam 合并
      queryParam: {
        classDate_begin: '',
        classDate_end: '',
        createTime_begin: '',
        createTime_end: '',
        subject: null,
        coachnum: null,
        traintype: '',
        publish: '',
        appoint: ''
      },
      curClassInfo: {},
      // 添加自定义分页配置，覆盖混入中的默认值
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '50', '100'],
        showTotal: (total, range) => {
          return range[0] + '-' + range[1] + ' 共' + total + '条'
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      }
    }
  },
  created() {
    this.getCocachListWrapper()
    // 让 a-select placeholder 生效
    this.queryParam.coachnum = undefined
  },
  methods: {
    handleAddBatch() {
      this.VehicleBatchAddModalKey++
      setTimeout(() => {
        // 打开批量新增弹窗
        this.$refs.batchModalForm.openModal()
      })
    },
    handleAdd() {
      // 打开新增弹窗
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.disableSubmit = false
    },
    handleEdit(record) {
      if (record.publish === 0) {
        this.$refs.modalForm.edit(record)
        this.$refs.modalForm.title = '编辑'
        this.$refs.modalForm.disableSubmit = false
      } else {
        this.$message.warning('已发布，不能编辑！')
      }
    },
    handleSearch(value) {
      this.$set(this.queryParam, 'coachnum', value)
    },
    getCocachListWrapper() {
      getCocachList2({
        teachtype: 1,
        pageSize: 9999,
        pageNo: 1
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
        } else {
          message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    handleShowStudents(record) {
      this.curClassInfo = { ...record }
      this.$nextTick(() => {
        this.$refs.studentModalForm.openModal(record)
      })
    },
    //单条发布/撤销发布
    handleTogglePublish(record) {
      const nextStatus = record.publish ? 0 : 1
      const actionText = record.publish ? '取消发布' : '发布'
      this.$confirm({
        title: '提示',
        content: `是否确认${actionText}该计划？`,
        onOk: () => {
          postAction('/schedule/opr/pubOrCancel', {
            ids: record.id,
            publish: nextStatus
          }).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              // 切换页面时清空选择
              this.selectedRowKeys = []
              this.selectionRows = []
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    //批量发布
    handleBatchPublish() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请至少选择一条记录进行操作')
        return
      }
      this.$confirm({
        title: '提示',
        content: '是否确认批量发布该计划？',
        onOk: () => {
          const ids = this.selectedRowKeys.join(',')
          postAction('/schedule/opr/pubOrCancel', { ids, publish: 1 }).then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
              // 清空已选行（mixins 中定义了 onClearSelected 方法）
              this.onClearSelected()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    // 批量撤销发布方法
    handleBatchCancelPublish() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择要撤销发布的记录')
        return
      }

      // 检查选中的记录中是否有已预约的
      const hasAppointed = this.selectionRows.some(record => record.appoint === 1)

      if (hasAppointed) {
        this.$message.error('存在已预约的教学计划，不可批量撤销发布')
        return
      }

      // 检查是否有未发布的记录
      const hasUnpublished = this.selectionRows.some(record => record.publish === 0)

      if (hasUnpublished) {
        this.$message.warning('选中的记录中包含未发布的计划，请重新选择')
        return
      }

      // 确认撤销发布
      this.$confirm({
        title: '确认撤销发布',
        content: `确定撤销发布选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          const ids = this.selectedRowKeys.join(',')
          return postAction('/schedule/opr/batchCancel', {
            ids: ids,
            publish: 0 // 0表示撤销发布
          })
            .then(res => {
              if (res.success) {
                this.$message.success(res.message || '撤销发布成功')
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message || '撤销发布失败')
              }
            })
            .catch(err => {
              console.error('撤销发布失败:', err)
              this.$message.error('撤销发布失败')
            })
        }
      })
    },
    // 新增人工预约方法：选中一条记录时与点击表格中预约状态文字效果一致
    handleManualReservation() {
      if (this.selectionRows.length !== 1) {
        this.$message.warning('请勾选一条记录进行人工预约')
        return
      }
      this.handleShowStudents(this.selectionRows[0])
    },
    // 批量删除使用 JeecgListMixin 中的 batchDel 方法，此方法会自动读取 url.deleteBatch 参数
    handlePublishToday() {
      this.$confirm({
        title: '确认提示',
        content: '是否确认发布今天所有未发布的计划？',
        onOk: () => {
          postAction('/schedule/opr/publishToday').then(res => {
            if (res.success) {
              this.$message.success(res.message)
              this.loadData()
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    modalFormOk(needRefresh) {
      // 判断是否需要刷新
      if (needRefresh) {
        this.loadData()
      }
    },
    // 批量删除方法
    handleBatchDelete() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }

      // 检查选中的记录中是否有已预约的
      const hasAppointed = this.selectionRows.some(record => record.appoint === 1)

      if (hasAppointed) {
        this.$message.error('存在已预约的教学计划，不可批量删除')
        return
      }

      // 确认删除
      this.$confirm({
        title: '确认删除',
        content: `确定删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // 调用删除接口
          return deleteAction('/schedule/opr/deleteBatch', { ids: this.selectedRowKeys.join(',') })
            .then(res => {
              if (res.success) {
                this.$message.success('删除成功')
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message || '删除失败')
              }
            })
            .catch(err => {
              console.error('删除失败:', err)
              this.$message.error('删除失败')
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.table-page-search-wrapper {
  .query-group-cust {
    width: 126px;
  }
  .query-group-split-cust {
    width: 14px;
    display: inline-block;
    text-align: center;
  }
}
/deep/ .classDate {
  .ant-calendar-picker {
    width: 45% !important;
  }
}
</style>
