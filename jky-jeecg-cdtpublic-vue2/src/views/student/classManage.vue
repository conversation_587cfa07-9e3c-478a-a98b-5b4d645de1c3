<template>
  <a-card :bordered="false">
    <!-- 查询区域 教学计划-课堂-->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <!-- 添加教练选择 -->
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教练员">
              <a-select
                v-model="queryParam.coachnum"
                placeholder="请选择教练员"
                show-search
                @change="handleSearch"
                optionFilterProp="title"
                style="width: 100%"
              >
                <a-select-option
                  v-for="(item, index) in cocachList"
                  :key="item.coachnum"
                  :value="item.coachnum"
                  :title="item.name"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24">
            <a-form-item label="开课时间">
              <!-- 原有的日期选择器保持不变 -->
              <a-date-picker
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="queryParam.classDate_begin"
                valueFormat="YYYY-MM-DD"
              />
              <span class="query-group-split-cust"></span>
              <a-date-picker
                placeholder="请选择结束日期"
                class="query-group-cust"
                v-model="queryParam.classDate_end"
                valueFormat="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="12" :lg="12" :md="12" :sm="24" style="margin-bottom: 10px;">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">批量新增</a-button>
      <a-button @click="handleManualReservation" type="primary" icon="user" style="margin-left:8px;">人工预约</a-button>
      <!-- 添加批量删除按钮 -->
      <a-button @click="handleBatchDelete" type="danger" icon="delete" style="margin-left:8px;">批量删除</a-button>
    </div>
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: 1800 }"
        bordered
        rowKey="id"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <span slot="inviteSlot" slot-scope="text, record">
          <a @click="handleShowStudents(record)">{{ record.curAmount }}</a>
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-if="record.curAmount === 0">修改</a>
          <a-divider type="vertical" v-if="record.curAmount === 0" />
          <a-popconfirm
            title="确定删除这条记录吗?"
            @confirm="() => handleDelete(record.id)"
            v-if="record.curAmount === 0"
            okText="确定"
            cancelText="取消"
          >
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <class-manage-add-modal ref="modalForm" @ok="modalFormOk"></class-manage-add-modal>
    <class-manage-student-modal
      ref="studentModalForm"
      title="开课学员管理"
      scheduleType="2"
      :classInfo="curClassInfo"
      @refresh="loadData"
    ></class-manage-student-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// 教学计划 课堂
import ClassManageAddModal from './modules/ClassManageAddModal.vue'
import ClassManageStudentModal from './modules/ClassManageStudentModal.vue'
import { getCocachList, getCocachList2 } from '@api/gzpt/studentinfo'
import { postAction, deleteAction } from '@api/manage'
export default {
  name: 'classManage',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    ClassManageAddModal,
    ClassManageStudentModal
  },
  data() {
    return {
      ipagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '50', '100'],
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
      },
      description: '学员开课管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '所属驾校',
          width: 300,
          align: 'center',
          ellipsis: true,
          dataIndex: 'inscode_dictText'
        },
        {
          title: '课堂教室',
          width: 300,
          align: 'center',
          ellipsis: true,
          dataIndex: 'classRoomName'
        },
        {
          title: '教练名称',
          align: 'center',
          dataIndex: 'coachnum_dictText'
        },
        {
          title: '培训车型',
          align: 'center',
          dataIndex: 'traintype'
        },
        {
          title: '科目',
          align: 'center',
          dataIndex: 'subject',
          customRender: text => {
            if (text === 5) {
              return '总体'
            } else if (text === 1) {
              return '科目一'
            } else if (text === 2) {
              return '科目二'
            } else if (text === 3) {
              return '科目三'
            } else if (text === 4) {
              return '科目四'
            }
          }
        },
        {
          title: '开课日期',
          align: 'center',
          dataIndex: 'classDate'
        },
        {
          title: '开课时间',
          align: 'center',
          dataIndex: 'classTime'
        },
        {
          title: '学时',
          align: 'center',
          dataIndex: 'classHours'
        },
        {
          title: '可约',
          align: 'center',
          dataIndex: 'stuAmount'
        },
        {
          title: '已约',
          align: 'center',
          dataIndex: 'curAmount',
          scopedSlots: {
            customRender: 'inviteSlot'
          }
        },
        {
          title: '操作',
          align: 'center',
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/schedule/class/list',
        delete: '/schedule/class/delete',
        deleteBatch: '/schedule/class/deleteBatch'
      },
      dataSource: [],
      // 添加教练列表数据
      cocachList: [],
      queryParam: {
        classDate_begin: '',
        classDate_end: '',
        coachnum: undefined
      },
      curClassInfo: {}
    }
  },
  created() {
    // 可在此处获取其他需要的数据
    this.getCocachListWrapper()
  },
  methods: {
    handleShowStudents(record) {
      console.log(record)
      this.curClassInfo = { ...record }
      // 延时打开子组件的弹窗
      setTimeout(() => {
        this.$refs.studentModalForm.openModal(record)
      }, 0)
    },
    // 新增的人工预约方法，勾选一条记录时调用，与点击“已约”数字效果一致
    handleManualReservation() {
      if (this.selectionRows.length !== 1) {
        this.$message.warning('请勾选一条记录进行人工预约')
        return
      }
      this.handleShowStudents(this.selectionRows[0])
    },
    handleAddStudent() {
      // 此处可根据需求扩展人工预约新增学员逻辑
    },
    // 添加教练筛选相关方法
    handleSearch(e) {
      this.$set(this.queryParam, 'coachnum', e)
      this.loadData()
    },
    getCocachListWrapper() {
      getCocachList2({
        teachtype: 2,
        pageSize: 9999,
        pageNo: 1
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
        } else {
          this.$message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    // 重置方法需要清空教练选择
    searchReset() {
      this.queryParam = {
        classDate_begin: '',
        classDate_end: '',
        coachnum: undefined
      }
      this.loadData(1)
    },
    // 添加批量删除方法
    handleBatchDelete() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }

      // 检查选中的记录中是否有已预约的课程
      const hasAppointed = this.selectionRows.some(record => record.curAmount > 0)

      if (hasAppointed) {
        this.$message.error('选中记录中包含已预约的课程，不可删除')
        return
      }

      this.$confirm({
        title: '确认删除',
        content: `确定删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          return deleteAction(this.url.deleteBatch, { ids: this.selectedRowKeys.join(',') })
            .then(res => {
              if (res.success) {
                this.$message.success('删除成功')
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message || '删除失败')
              }
            })
            .catch(err => {
              console.error('删除失败:', err)
              this.$message.error('删除失败')
            })
        }
      })
    },
    // 重写 handleTableChange 方法，确保切换页面时清空选择
    handleTableChange(pagination, filters, sorter) {
      console.log(pagination)
      if (Object.keys(sorter).length > 0) {
        this.isorter.column = sorter.field;
        this.isorter.order = "ascend" == sorter.order ? "asc" : "desc"
      }
      this.ipagination = pagination;
      // 切换页面时清空选择
      this.selectedRowKeys = []
      this.selectionRows = []
      this.loadData();
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';
</style>
