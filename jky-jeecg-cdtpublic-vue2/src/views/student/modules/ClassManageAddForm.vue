<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="教练员" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnum">
              <a-select
                v-model="model.coachnum"
                placeholder="请选择教练员"
                show-search
                @change="handleSearch"
                optionFilterProp="title"
                style="width: 100%"
              >
                <a-select-option
                  v-for="(item, index) in cocachList"
                  :key="item.coachnum"
                  :value="item.coachnum"
                  :title="item.name"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="subject">
              <a-select v-model="model.subject" placeholder="请选择科目" style="width: 100%">
                <a-select-option :value="1">科目一</a-select-option>
                <a-select-option :value="4">科目四</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="教室" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classRoomName">
              <a-select
                v-model="model.classRoomName"
                placeholder="请选择教室"
                @change="handleClassroomChange"
                style="width: 100%"
              >
                <a-select-option v-for="(room, index) in classroomList" :key="room.id" :value="room.classRoomName">
                  {{ room.classRoomName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="班级容量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stuAmount">
              <a-input-number
                v-model:value="model.stuAmount"
                :min="1"
                :step="1"
                :formatter="value => (value ? Math.floor(value) : '')"
                :parser="value => value.replace(/[^\d]/g, '')"
                placeholder="请输入班级容量"
                style="width: 100%"
                @blur="handleStuAmountChange"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="!isEditMode">
            <a-form-model-item label="开始日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classDateStart">
              <a-date-picker
                placeholder="请选择开始日期"
                class="query-group-cust"
                v-model="model.classDateStart"
                :disabledDate="disabledClassDate"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
                @change="handleDateChange"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="!isEditMode">
            <a-form-model-item label="结束日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classDateEnd">
              <a-date-picker
                placeholder="请选择结束日期"
                class="query-group-cust"
                v-model="model.classDateEnd"
                :disabledDate="disabledClassDateEnd"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
                @change="handleDateChange"
              />
            </a-form-model-item>
          </a-col>
          <!-- 时间方案选择 -->
          <a-col :span="12" v-if="!isEditMode">
            <a-form-model-item label="时间方案" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="templateId">
              <a-select
                v-model="model.templateId"
                placeholder="请选择时间方案"
                @change="handleTemplateChange"
                style="width: 100%"
              >
                <a-select-option v-for="template in timeTemplateList" :key="template.id" :value="template.id">
                  {{ template.templateName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="traintype">
              <j-dict-select-tag
                type="list"
                v-model="model.traintype"
                dictCode="sys_stu_traintype"
                placeholder="请选择培训车型"
                style="width: 100%"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import moment from 'moment'
import { getCocachList2 } from '@api/gzpt/studentinfo'

export default {
  name: 'ClassManageAddForm',
  props: {
    // 表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    // 添加日期范围验证函数
    const validateDateRange = (rule, value, callback) => {
      const startDate = moment(this.model.classDateStart)
      const endDate = moment(this.model.classDateEnd)
      if (startDate && endDate && endDate.isBefore(startDate)) {
        callback(new Error('结束日期必须大于等于开始日期'))
      } else {
        callback()
      }
    }

    return {
      cocachList: [],
      classroomList: [],
      timeTemplateList: [], // 时间方案列表
      timeTemplateDetails: [], // 选中的时间方案详情
      isEditMode: false, // 新增：标记是否为编辑模式
      model: {
        coachnum: '',
        subject: '',
        traintype: '',
        classDateStart: moment()
          .add(1, 'day')
          .format('YYYY-MM-DD'),
        classDateEnd: moment()
          .add(7, 'day')
          .format('YYYY-MM-DD'), // 结束日期默认为一周后
        templateId: undefined, // 时间方案ID
        classRoomName: '',
        stuAmount: '200'
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        coachnum: [{ required: true, message: '请选择教练员', trigger: 'change' }],
        subject: [{ required: true, message: '请选择科目', trigger: 'change' }],
        traintype: [{ required: true, message: '请选择培训车型', trigger: 'change' }],
        classDateStart: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' }
        ],
        classDateEnd: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' }
        ],
        templateId: [{ required: true, message: '请选择时间方案', trigger: 'change' }],
        classHours: [{ required: true, message: '请选择学时', trigger: 'change' }],
        classRoomName: [{ required: true, message: '请选择教室', trigger: 'change' }],
        stuAmount: [{ required: true, message: '请输入班级容量', trigger: 'blur' }]
      },
      url: {
        add: '/schedule/class/add',
        edit: '/schedule/class/edit',
        queryById: '/schedule/class/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    // 备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    this.getCocachListWrapper()
    this.getClassroomList()
    this.getTimeTemplateList()
  },
  methods: {
    moment,
    // 新增：日期变化时触发两个日期字段的校验
    handleDateChange() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField(['classDateStart', 'classDateEnd'], () => {});
        }
      });
    },
    disabledClassDate(current) {
      // 禁止选择今天及今天之前的日期
      const tomorrow = moment()
        .add(0, 'day')
        .startOf('day')
      return current < tomorrow
    },
    disabledClassDateEnd(current) {
      // 结束日期不能早于开始日期
      const startDate = this.model.classDateStart
        ? moment(this.model.classDateStart).startOf('day')
        : moment()
            .add(1, 'day')
            .startOf('day')
      return current < startDate
    },
    handleStuAmountChange(value) {
      if (!value || value < 1) {
        this.model.stuAmount = 1
        this.$message.warning('班级容量必须为正整数且大于 0')
      }
    },
    // 获取时间方案列表
    getTimeTemplateList() {
      getAction('/gzpt/timeTemplate/list', { teachType: 2, pageSize: 999 }).then(res => {
        if (res.success) {
          this.timeTemplateList = res.result.records || []
        } else {
          this.$message.error('获取时间方案失败')
        }
      })
    },
    // 获取时间方案详情
    // getTimeTemplateDetails(templateId) {
    //   if (!templateId) {
    //     this.timeTemplateDetails = []
    //     return
    //   }
    //   getAction('/gzpt/timeTemplateDetails/list', { templateId: templateId, pageSize: 999 }).then(res => {
    //     if (res.success) {
    //       this.timeTemplateDetails = res.result.records || []
    //     } else {
    //       this.$message.error('获取时间方案详情失败')
    //     }
    //   })
    // },
    // 处理时间方案选择变化
    handleTemplateChange(value) {
      // this.getTimeTemplateDetails(value)
    },
    // handleClassHoursChange(value) {
    //   // 学时选择变化
    //   this.model.classHours = value
    // },
    handleSearch(e, option) {
      this.$set(this.model, 'coachnum', e)
    },
    getCocachListWrapper() {
      getCocachList2({
        teachtype: 2,
        pageSize: 9999,
        pageNo: 1
      }).then(res => {
        if (res.success) {
          this.cocachList = res.result.records.filter(item => item.coachnum)
        } else {
          this.$message.error('该培训机构下无可选实操教练!')
          this.cocachList = []
        }
      })
    },
    getClassroomList() {
      // 请求教室列表，classRoomType: 2 表示“课堂”，可根据需求调整类型
      getAction('/gzpt/classRoomDetails/dropBox', { classRoomType: 2 }).then(res => {
        if (res.success) {
          this.classroomList = res.result
        } else {
          this.$message.error('获取教室列表失败')
          this.classroomList = []
        }
      })
    },
    handleClassroomChange(value) {
      const selectedRoom = this.classroomList.find(room => room.classRoomName === value)
      if (selectedRoom) {
        this.model.stuAmount = selectedRoom.stuAmount
      }
    },
    add() {
      this.edit(this.modelDefault)
      //注意放下面覆盖代码
      this.isEditMode = false // 新增：添加模式
    },
    edit(record) {
      // 清空可能存在的校验状态
      this.$refs.form && this.$refs.form.clearValidate()
      this.isEditMode = true // 新增：编辑模式
      // 深拷贝原始记录
      this.model = Object.assign({}, record)

      // 如果有时间方案ID，加载时间方案详情
      // if (this.model.templateId) {
      //   this.getTimeTemplateDetails(this.model.templateId)
      // } else if (this.timeTemplateList && this.timeTemplateList.length > 0) {
      //   // 如果没有时间方案ID，但有时间方案列表，默认选择第一个
      //   this.model.templateId = this.timeTemplateList[0].id
      //   this.getTimeTemplateDetails(this.model.templateId)
      // }

      // 处理 classTime 字段：分解为开课开始时间和学时（计算学时差）
      if (this.model.classTime && typeof this.model.classTime === 'string') {
        const timeParts = this.model.classTime.split('-')
        if (timeParts.length === 2) {
          const start = moment(timeParts[0], 'HH:mm')
          const end = moment(timeParts[1], 'HH:mm')
          this.$set(this.model, 'classTime1', start)
          this.$set(this.model, 'classTime2', end)
          // 计算学时（单位为小时）
          // const hoursDiff = end.diff(start, 'hours')
          // this.model.classHours = hoursDiff > 0 ? hoursDiff : 1
        }
      }
      this.visible = true
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查日期范围是否超过一个月
          const startDate = moment(this.model.classDateStart)
          const endDate = moment(this.model.classDateEnd)
          const diffDays = endDate.diff(startDate, 'days')

          if (diffDays > 31) {
            this.$message.warning('开始时间和结束时间间隔不能超过一个月')
            return
          }

          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  },
  // watch: {
  //   // 监听日期变化
  //   'model.classDateStart'(val) {
  //     if (val && this.model.classDateEnd) {
  //       this.$refs.form.validateField(['classDateStart', 'classDateEnd'])
  //     }
  //   },
  //   'model.classDateEnd'(val) {
  //     if (val && this.model.classDateStart) {
  //       this.$refs.form.validateField(['classDateStart', 'classDateEnd'])
  //     }
  //   }
  // }
}
</script>

<style scoped lang="less">
/deep/ .timeRange {
  label::before {
    display: inline-block;
    margin-right: 4px;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
</style>
