<template>
  <div class="container">
    <div class="top">
      <div class="gutter-row">
        <div class="gutter-box">
          <div class="left">
            <img src="@/assets/analysis/1.png">
          </div>
          <div class="right">
            <!-- <p>学员总数</p>
						<p>{{chartData.studentTotal}}</p> -->
            <a-statistic title="当年报名人数" :value="chartData.studentTotal" style="">
            </a-statistic>
          </div>
        </div>
      </div>

      <div class="gutter-row">
        <div class="gutter-box">
          <div class="left">
            <img src="@/assets/analysis/2.png">
          </div>
          <div class="right">
            <!-- <p>教练员</p>
						<p>{{chartData.coachTotal}}</p> -->
            <a-statistic title="教练员" :value="chartData.coachTotal" style="">

            </a-statistic>
          </div>
        </div>
      </div>
      <div class="gutter-row">
        <div class="gutter-box">
          <div class="left">
            <img src="@/assets/analysis/3.png">
          </div>
          <div class="right">
            <!-- <p>教练车</p>
						<p>{{chartData.instructionalCarTotal}}</p> -->
            <a-statistic title="教练车" :value="chartData.instructionalCarTotal" style="">
            </a-statistic>
          </div>
        </div>
      </div>
      <div class="gutter-row">
        <div class="gutter-box">
          <div class="left">
            <img src="@/assets/analysis/4.png">
          </div>
          <div class="right">
            <!-- <p>驾校</p>
						<p>{{chartData.drivingSchoolTotal}}</p> -->
            <a-statistic title="驾校" :value="chartData.drivingSchoolTotal" style="">
            </a-statistic>
          </div>
        </div>
      </div>
    </div>
    <div class="chart">
      <div class="mychart" ref="mychart">

      </div>
    </div>
  </div>
</template>

<script>
  import {
    getAction
  } from "@/api/manage.js"
  const echarts = require('echarts');
  export default {
    name: 'Analysis',
    components: {},
    data() {
      return {
        myChart: null,
        chartData: {
          coachTotal: '',
          drivingSchoolTotal: '',
          instructionalCarTotal: '',
          studentTotal: ''
        }
      };
    },
    mounted() {
      this.myChart = echarts.init(this.$refs.mychart);
      this.chartInit()
      this.getChartData()
    },
    methods: {
      getChartData() {
        getAction('/dashboard/total').then(res => {
          if (res.success) {
            this.chartData = res.result
            if (res.result.weekAddStudentTotal.length > 0) {
              let chartData = res.result.weekAddStudentTotal
              let xdata = chartData.map(item => item.create_time)
              let ydata = chartData.map(item => item.count)
              this.chartInit(xdata, ydata)
            } else {
              this.chartInit([], [])
            }
          } else {
            this.$message.error(res.message)
          }
        })
      },
      chartInit(xData, yData) {
        let option = {
          backgroundColor: 'transparent',
          title: {
            text: '最近7天新增学员数量统计',
            top: '5%',
            textAlign: 'center',
            left: '8%',
            textStyle: {
              color: '#262626',
              fontSize: 18,
              fontWeight: '600',
            },
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '5%',
            right: '15%',
            bottom: '5%',
            top: '25%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: xData,
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              color: 'rgba(73,80,87,0.9)',
              fontSize: 12,
              fontWeight: 600,
              interval: 0,
              padding: [8, 0, 0, 0]
            },
          }],
          yAxis: [{
            type: 'value',
            name: '',
            nameTextStyle: {
              color: "rgba(73,80,87,0.9)",
              fontSize: 12,
              padding: [0, 0, 6, -60],
            },
            minInterval: 1,
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(73,80,87,0.9)',

              },
              padding: 10
            },
          }],
          series: [{
            name: '新增',
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            barWidth: 20,
            itemStyle: {
              color: '#40c9c6'
            },
            label: {
              normal: {
                show: true,
                position: "top",
                formatter: function(data) {
                  return '{a0|' + data.value + '}';
                },
                rich: {
                  a0: {
                    fontSize: 12,
                    fontFamily: 'DIN',
                    fontWeight: 'bold'
                  },
                }
              },
            },
            data: yData,
          }]
        };
        this.myChart.setOption(option);
      },
    }
  }
</script>

<style lang="less" scoped>
  .container {
    height: 100vh;

    .top {
      height: 13%;
      width: 97%;
      margin: 0 auto;
      margin-top: 3%;
      display: flex;
      justify-content: space-between;

      .gutter-row {
        height: 100%;
        width: 22%;

        .gutter-box {
          height: 100%;
          background-color: #fff;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          p:nth-child(1) {
            line-height: 18px;
            color: rgba(0, 0, 0, .45);
            font-size: 16px;
          }

          p:nth-child(2) {
            color: rgba(0, 0, 0, .45);
            font-size: 20px;
            font-weight: 700;
            margin-top: 10px;
          }
        }
      }

    }

    .chart {
      height: 40%;
      width: 97%;
      margin: 0 auto;
      background-color: #fff;
      margin-top: 3%;

      .mychart {
        height: 100%;
        width: 100%;
        //padding: 0 30px;
      }
    }
  }

  p {
    margin: 0;
    padding: 0;
  }

  /deep/.ant-statistic-title {
    font-size: 17px;
  }

  /deep/.ant-statistic {
    text-align: center;
  }
</style>