<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="资料目录">
              <a-input v-model="categoryName" disabled />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="资料名称">
              <a-input placeholder="请输入资料名称" v-model="queryParam.docname"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="资料类型">
                <j-dict-select-tag v-model="queryParam.doctype" placeholder="请选择资料类型" dictCode="document_type" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="关键字">
                <a-input placeholder="请输入关键字" v-model="queryParam.dockey"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="来源">
                <j-dict-select-tag v-model="queryParam.docly" placeholder="请选择资料来源" dictCode="document_source" />
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="发布人">
                <a-input placeholder="请输入发布人" v-model="queryParam.docfzr"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="创建时间">
                <j-date placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.createTime_begin"></j-date>
                <span class="query-group-split-cust"></span>
                <j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.createTime_end"></j-date>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleBatchDownload" type="primary" icon="download">批量下载</a-button>
      <!--      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
            <a-dropdown>
              <a-menu slot="overlay">
                <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
                <a-menu-item key="2" @click=""><a-icon type="delete"/>下载</a-menu-item>
              </a-menu>
              <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
            </a-dropdown>-->
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
          style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table ref="table" size="small" :scroll="{x:true}" bordered rowKey="id" :columns="columns"
        :components="dragTable(columns)" :dataSource="dataSource" :pagination="ipagination" :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}" class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;" />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">审核</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handlePreviewFile(record)">预览</a>
              </a-menu-item>
              <a-menu-item v-show="record.downloadFlag">
                <a @click="handleClickDownloadFile(record)">下载</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <document-check-modal ref="modalForm" @ok="modalFormOk"></document-check-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import {
    getAction,
    getFileAccessHttpUrl
  } from '@/api/manage'
  import {
    mixinDevice
  } from '@/utils/mixin'
  import {
    axios
  } from '@/utils/request'
  import {
    JeecgListMixin
  } from '@/mixins/JeecgListMixin'
  import JTreeDict from "@/components/jeecg/JTreeDict.vue"
  import JCategorySelect from '@comp/jeecg/JCategorySelect'
  import DocumentCheckModal from './DocumentCheckModal'
  import JDate from '@/components/jeecg/JDate.vue'
  import JSZip from 'jszip'
  import FileSaver from 'file-saver'

  const Base64 = require('js-base64').Base64;

  export default {
    name: 'DocumentListRight',
    mixins: [JeecgListMixin, mixinDevice],
    components: {
      JDate,
      JTreeDict,
      JCategorySelect,
      DocumentCheckModal
    },
    props: ['value'],
    /*props:{
      currentCaterogy:{
        type: Object,
        default: ()=>({}),
        required: false
      }
    },*/
    data() {
      return {
        description: '资料列表',
        categoryName: '',
        // 表头
        columns: [{
            title: '资料名称',
            align: 'left',
            ellipsis: true,
            width: 200,

            dataIndex: 'docname'
          },
          {
            title: '审核状态',
            align: 'left',
            ellipsis: true,

            width: 100,
            dataIndex: 'actStatus_dictText'
          },
          {
            title: '发布人',
            align: 'left',
            ellipsis: true,
            width: 100,
            dataIndex: 'docfzr'
          },
          {
            title: '资料类型',
            align: 'left',
            ellipsis: true,
            width: 120,

            dataIndex: 'doctype_dictText'
          },
          {
            title: '关键字',
            align: 'left',
            ellipsis: true,

            width: 150,
            dataIndex: 'dockey'
          },
          {
            title: '来源',
            align: 'left',
            ellipsis: true,
            width: 200,

            dataIndex: 'docly_dictText'
          },
          /*{
            title:'资料大小',
            align: 'left', ellipsis: true,
            dataIndex: 'filesize'
          },
          {
            title:'创建用户',
            align: 'left', ellipsis: true,

            dataIndex: 'createBy'
          },*/
          {
            title: '创建时间',
            align: 'left',
            ellipsis: true,
            dataIndex: 'createTime',
            width: 120,
            customRender: function(text) {
              return !text ? "" : (text.length > 10 ? text.substr(0, 10) : text)
            }
          },
          /*{
            title:'修改时间',
            align: 'left', ellipsis: true,
            dataIndex: 'updateTime',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },*/
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: "right",
            width: 150,
            scopedSlots: {
              customRender: 'action'
            }
          }
        ],
        url: {
          list: "/document/documentInfo/listForKnowledge",
          delete: "/document/documentInfo/delete",
          deleteBatch: "/document/documentInfo/deleteBatch",
          exportXlsUrl: "/document/documentInfo/exportXls",
          importExcelUrl: "document/documentInfo/importExcel",
          getCategoryName: "/document/documentInfo/getCategoryName",
          batchFileDown: "/document/documentInfo/batchFileDown"
        },
        dictOptions: {},
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(code) {
          this.getCategoryName(code);

          this.dataSource = []
          this.queryParam.categoryCode = code
          this.loadData(1, code)
        }
      },
      /*currentCaterogy:{
        immediate: true,
        handler(currentCaterogy) {
          console.log("currentCaterogy1 = ", currentCaterogy.docCode)
          console.log("currentCaterogy2 = ", currentCaterogy.cateName)

        }
      }*/
    },
    created() {},
    computed: {
      importExcelUrl: function() {
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig() {},
      loadData(pageNum, code) {
        let params = {
          "actStatus": 1
        }
        this.loading = true
        if (pageNum === 1) {
          this.ipagination.current = 1
        }
        // update-begin- --- author:wangshuai ------ date:20200102 ---- for:传过来的部门编码为空全查
        if (!code) {
          /*getAction(this.url.list, {
            ...params,
            ...this.getQueryParams()
          }).then((res) => {
            if (res.success) {
              this.dataSource = res.result.records
              this.ipagination.total = res.result.total
            }
          }).finally(() => {
            this.loading = false
            this.cardLoading = false
          })*/
          // update-end- --- author:wangshuai ------ date:20200102 ---- for:传过来的部门编码为空全查
          this.loading = false
          this.cardLoading = false
        } else {
          //加载数据 若传入参数1则加载第一页的内容
          getAction(this.url.list, {
            code,
            ...params,
            ...this.getQueryParams()
          }).then((res) => {
            if (res.success) {
              this.dataSource = res.result.records
              this.ipagination.total = res.result.total
            }
          }).finally(() => {
            this.loading = false
            this.cardLoading = false
          })
        }
      },

      searchQuery() {
        this.loadData(1, this.value)
      },
      searchReset() {
        this.queryParam = {}
        this.loadData(1, this.value)
      },

      handleTableChange(pagination, filters, sorter) {
        if (Object.keys(sorter).length > 0) {
          this.isorter.column = sorter.field
          this.isorter.order = 'ascend' === sorter.order ? 'asc' : 'desc'
        }
        this.ipagination = pagination
        this.loadData(null, this.value)
      },
      getCategoryName(code) {
        let params = {
          "code": code
        }
        getAction(this.url.getCategoryName, params).then((res) => {
          if (res.success) {
            this.categoryName = res.result;
          }
        })
      },
      handleAdd: function() {
        this.$refs.modalForm.edit({
          "categoryCode": this.queryParam.categoryCode
        });
        this.$refs.modalForm.title = "新增";
        this.$refs.modalForm.disableSubmit = false;
      },
      handleEdit: function(record) {
        this.$refs.modalForm.edit(record);
        this.$refs.modalForm.title = "审核";
        this.$refs.modalForm.disableSubmit = false;
      },
      handleClickDownloadFile(record) {
        if (-1 != record.docpath.indexOf(",")) {
          let paths = record.docpath.split(",");
          for (let p in paths) {
            if (p) {
              let url = getFileAccessHttpUrl(p)
              window.open(url)
            }
          }
        } else {
          if (record.docpath) {
            let url = getFileAccessHttpUrl(record.docpath)
            window.open(url)
          }
        }
      },
      handlePreviewFile(record) {
        if ((typeof record == 'object')) {
          if (-1 != record.docpath.indexOf(",")) {
            let paths = record.docpath.split(",");
            for (let p in paths) {
              if (p) {
                let url = window._CONFIG['onlinePreviewDomainURL'] + '?url=' + encodeURIComponent(Base64.encode(
                  getFileAccessHttpUrl(p)))
                window.open(url, '_blank')
              }
            }
          } else {
            if (record.docpath) {
              let url = window._CONFIG['onlinePreviewDomainURL'] + '?url=' + encodeURIComponent(Base64.encode(
                getFileAccessHttpUrl(record.docpath)))
              window.open(url, '_blank')
            }
          }
        } else {
          if (record) {
            let url = window._CONFIG['onlinePreviewDomainURL'] + '?url=' + encodeURIComponent(Base64.encode(
              getFileAccessHttpUrl(record)))
            window.open(url, '_blank')
          }
        }
      },
      getFile(url) {
        return new Promise((resolve, reject) => {
          axios({
            method: 'get',
            url,
            responseType: 'arraybuffer'
          }).then(data => {
            resolve(data)
          }).catch(error => {
            reject(error.toString())
          })
        })
      },
      handleBatchDownload() {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！');
        } else {
          let selectLength = this.selectedRowKeys.length;
          let ids = ''
          for (let a = 0; a < selectLength; a++) {
            ids += this.selectedRowKeys[a] + ','
          }
          let that = this;
          this.$confirm({
            title: "确认下载",
            content: "是否下载选中数据?",
            onOk: function() {
              that.loading = true;
              getAction(that.url.batchFileDown, {
                ids: ids
              }).then((res) => {
                if (res.success) {
                  let result = res.result;
                  if (null != result && 0 < result.length) {
                    let downloadLength = result.length;
                    if (downloadLength < selectLength) {
                      that.$message.warning('部分资料由于不具备下载权限，可能无法下载！');
                    }

                    let paths = new Array();
                    result.forEach(doc => {
                      if (doc.docpath) {
                        paths.push(getFileAccessHttpUrl(doc.docpath));
                      }
                    })
                    const zip = new JSZip();
                    const cache = {};
                    const promises = [];
                    paths.forEach(item => {
                      const promise = that.getFile(item).then(data => { // 下载文件, 并存成ArrayBuffer对象
                        const arr_name = item.split("/")
                        const file_name = arr_name[arr_name.length - 1] // 获取文件名
                        zip.file(file_name, data, {
                          binary: true
                        }) // 逐个添加文件
                        cache[file_name] = data
                      })
                      promises.push(promise)
                    });
                    Promise.all(promises).then(() => {
                      zip.generateAsync({
                        type: "blob"
                      }).then(content => { // 生成二进制流
                        FileSaver.saveAs(content, "资料包.zip") // 利用file-saver保存文件
                      })
                    })

                    that.$message.success("资料下载完成！")
                  }
                } else {
                  that.$message.success("资料下载失败！")
                }
              })
              that.loading = false;
            }
          });
        }
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
