<template>
  <!--全屏：switchFullscreen-->
  <j-modal
    title="切换资料目录"
    :width="modalWidth"
    :visible="categoryModalVisible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    cancelText="关闭">
    <a-spin tip="Loading..." :spinning="false">
<!--      <a-input-search style="margin-bottom: 1px" placeholder="请输入表单名称按回车进行搜索" @search="onSearch" />-->
      <a-tree
        checkable
        :treeData="treeDataSource"
        :replaceFields="{children:'children', title:'title', key:'id' }"
        :checkStrictly="true"
        @check="onCheck"
        @select="onSelect"
        @expand="onExpand"
        :autoExpandParent="autoExpandParent"
        :expandedKeys="expandedKeys"
        :checkedKeys="checkedKeys">

        <template slot="title" slot-scope="{title}">
          <span v-if="title.indexOf(searchValue) > -1">
            {{title.substr(0, title.indexOf(searchValue))}}
            <span style="color: #f50">{{searchValue}}</span>
            {{title.substr(title.indexOf(searchValue) + searchValue.length)}}
          </span>
          <span v-else>{{title}}</span>
        </template>
      </a-tree>

    </a-spin>
  </j-modal>
</template>

<script>
import { loadDeptCategoryTreeData, loadCategoryTreeBySceneType } from '@/api/api'
import { deleteAction, getAction, postAction, downFile } from '@/api/manage'
export default {
  name: 'DocumentCategoryModal',
  props:['categoryModalVisible','selectedRowKeys','modalWidth','multi','rootOpened', 'docScene'],
  data(){
    return {
      visible:false,
      confirmLoading:false,
      treeDataSource:[],
      autoExpandParent:true,
      expandedKeys:[],
      dataList:[],
      checkedKeys:[],
      checkedNames:[],
      checkedRows:[],
      searchValue:"",
      url: {
        actFromList: '/activiti/actFrom/list'
      }
    }
  },
  created(){
    this.loadTreeList();
  },
  watch:{
    categoryModalVisible: {
      handler() {
        /*if (this.modelFormId) {
          this.checkedKeys = this.modelFormId.split(",");
        } else {
          this.checkedKeys = [];
        }*/
      }
    }
  },
  methods:{
    show(){
      this.visible=true
      this.checkedRows=[]
      this.checkedKeys=[]
      this.checkedNames=[]
    },
    loadTreeList(){
			let that = this;
			switch (this.docScene) {
				case 0:
					loadDeptCategoryTreeData({
					  reqSource: 'base'
					}).then(res=>{
					  if(res.success){
					    let arr = [...res.result]
					    that.reWriterWithSlot(arr)
					    that.treeDataSource = arr
					    if(that.rootOpened){
					      that.initExpandedKeys(res.result)
					    }
					  }
					})
					break;
				case 1:
				case 2:
					loadCategoryTreeBySceneType({
						orgCode: 'A01',
						sceneType: this.docScene
					}).then(res=>{
					  if(res.success){
					    let arr = [...res.result]
					    that.reWriterWithSlot(arr)
					    that.treeDataSource = arr
					    if(that.rootOpened){
					      that.initExpandedKeys(res.result)
					    }
					  }
					});
					break;
				default:
					break;
			}
    },
    reWriterWithSlot(arr){
      for(let item of arr){
        if(item.children && item.children.length>0){
          this.reWriterWithSlot(item.children)
          let temp = Object.assign({},item)
          temp.children = {}
          this.dataList.push(temp)
        }else{
          this.dataList.push(item)
          item.scopedSlots={ title: 'title' }
        }
      }

    },
    initExpandedKeys(arr){
      if(arr && arr.length>0){
        let keys = []
        for(let item of arr){
          if(item.children && item.children.length>0){
            keys.push(item.id)
          }
        }
        this.expandedKeys=[...keys]
      }else{
        this.expandedKeys=[]
      }

    },
    onCheck (checkedKeys,info) {
      if(!this.multi){
        let arr = checkedKeys.checked.filter(item => this.checkedKeys.indexOf(item) < 0)
        this.checkedKeys = [...arr]
        this.checkedRows = (this.checkedKeys.length === 0) ? [] : [info.node.dataRef]
      }else{
        this.checkedKeys = checkedKeys.checked
        this.checkedRows = this.getCheckedRows(this.checkedKeys)
      }
    },
    onSelect(selectedKeys,info) {
      let keys = []
      keys.push(selectedKeys[0])
      if(!this.checkedKeys || this.checkedKeys.length===0 || !this.multi){
        this.checkedKeys = [...keys]
        this.checkedRows=[info.node.dataRef]
      }else{
        let currId = info.node.dataRef.id
        if(this.checkedKeys.indexOf(currId)>=0){
          this.checkedKeys = this.checkedKeys.filter(item=> item !==currId)
        }else{
          this.checkedKeys.push(...keys)
        }
        this.checkedRows = this.getCheckedRows(this.checkedKeys)
      }
    },
    onExpand (expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    handleSubmit(){
      if(!this.checkedKeys || this.checkedKeys.length==0){
        this.$message.success("请选择目录！")
      }else{
        let row = this.checkedRows[0];
        if(row.categoryType){
          this.$emit("handleCategoryOk",this.checkedRows,this.checkedKeys)
          this.handleClear()
        }
        else{
          this.$message.success("当前层级无法切换，请选择资料目录！")
        }
      }
    },
    handleCancel(){
      this.handleClear();
    },
    handleClear(){
      this.visible=false
      this.checkedKeys=[]

      this.$emit('handleCategoryVisiable', false)
    },
    getParentKey(currId,treeDataSource){
      let parentKey
      for (let i = 0; i < treeDataSource.length; i++) {
        const node = treeDataSource[i]
        if (node.children) {
          if (node.children.some(item => item.id === currId)) {
            parentKey = node.id
          } else if (this.getParentKey(currId, node.children)) {
            parentKey = this.getParentKey(currId, node.children)
          }
        }
      }
      return parentKey
    },
    onSearch(value){
      const expandedKeys = this.dataList.map((item) => {
        if (item.title.indexOf(value) > -1) {
          return this.getParentKey(item.id,this.treeDataSource)
        }
        return null
      }).filter((item, i, self) => item && self.indexOf(item) === i)

      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      })


    },
    // 根据 checkedKeys 获取 rows
    getCheckedRows(checkedKeys) {
      const forChild = (list, key) => {
        for (let item of list) {
          if (item.id === key) {
            return item
          }
          if (item.children instanceof Array) {
            let value = forChild(item.children, key)
            if (value != null) {
              return value
            }
          }
        }
        return null
      }

      let rows = []
      for (let key of checkedKeys) {
        let row = forChild(this.treeDataSource, key)
        if (row != null) {
          rows.push(row)
        }
      }
      return rows
    },

  }
}

</script>

<style scoped>

</style>