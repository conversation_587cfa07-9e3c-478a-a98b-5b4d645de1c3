<template>
  <a-card :loading="false" :bordered="false" style="height: 100%;">
    <a-spin :spinning="false">
      <tree-editable :editable="true" @treeNodeSelected="handleTreeNodeSelected"></tree-editable>
    </a-spin>
  </a-card>
</template>

<script>
  import { loadDeptCategoryTreeData, loadCategoryTreeBySceneType} from '@/api/api'
  import {getAction, putAction, postAction, deleteAction} from "@api/manage";
  import TreeEditable from '@/components/TreeEditable/TreeEditable.vue'
  export default {
    name: 'DocumentListLeft',
    components: {
      TreeEditable
    },
    watch: {
			treeDataSource: {
				handler(newValue) {
					this.treeDataSource = newValue;
				},
				deep: true
			},
		},
		methods: {
      handleTreeNodeSelected(nodeInfo) {
        this.$emit('treeNodeSelected', nodeInfo)
      },
      emitInput(categoryInfo) {
        this.$emit('input', categoryInfo.code)
        this.$emit('handleSelectCategory',categoryInfo);
      },
			iteratorArray(array) {
				array.forEach((item) => {
					item.isAdd = false;
					item.isEdit = false;
					item.scopedSlots = {title: 'custom' };
					if (item.children) {
						this.iteratorArray(item.children)
					}
				});
				return array;
			}
    }
  }
</script>