<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="学员姓名">
              <a-input placeholder="请输入学员姓名" v-model="queryParam.name"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="身份证号">
              <a-input placeholder="请输入身份证号" v-model="queryParam.idcard"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="studyUrlSlot" slot-scope="text, record">
          <div v-if="text">
            <a @click="handlePreview(text)">查看</a>
          </div>
          <div v-else>未上传</div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>
      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 图片预览 -->
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, deleteAction } from '@/api/manage'

export default {
  name: '学习证明管理',
  components: {},
  mixins: [JeecgListMixin, mixinDevice],
  data() {
    return {
      description: '学员科目一学习证明管理页面',
      // 查询条件
      queryParam: {
        name: '',
        idcard: ''
      },
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '身份证号',
          align: 'center',
          dataIndex: 'idcard'
        },
        {
          title: '学习证明编号',
          align: 'center',
          dataIndex: 'studyNum'
        },
        {
          title: '培训车型',
          align: 'center',
          dataIndex: 'traintype'
        },
        {
          title: '有效期起始日期',
          align: 'center',
          dataIndex: 'startDate',
          customRender: function(text) {
            return text ? text.substring(0, 10) : ''
          }
        },
        {
          title: '有效期截止日期',
          align: 'center',
          dataIndex: 'endDate',
          customRender: function(text) {
            return text ? text.substring(0, 10) : ''
          }
        },
        {
          title: '学习证明',
          align: 'center',
          dataIndex: 'studyUrl',
          scopedSlots: { customRender: 'studyUrlSlot' }
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 图片预览
      previewVisible: false,
      previewImage: '',
      url: {
        list: '/zlb/studentStudyProof/list',
        delete: '/zlb/studentStudyProof/delete',
        deleteBatch: '/zlb/studentStudyProof/deleteBatch'
      }
    }
  },
  methods: {
    // 图片预览
    handlePreview(url) {
      this.previewImage = url
      this.previewVisible = true
    },
    handleCancel() {
      this.previewVisible = false
    },
    // 删除
    handleDelete(id) {
      deleteAction(this.url.delete, { id: id }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>
.ant-card-body .table-operator {
  margin-bottom: 18px;
}
</style>
