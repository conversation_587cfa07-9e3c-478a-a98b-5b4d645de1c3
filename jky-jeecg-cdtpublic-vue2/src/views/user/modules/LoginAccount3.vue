<template>
    <div>
      <a-form-model ref="form" :model="model" :rules="validatorRules">
        <a-form-model-item required prop="username">
          <a-input v-model="model.username" size="large" placeholder="请输入帐户名">
            <img slot="prefix" src="@/assets/login3/user-logo.png"/>
          </a-input>
        </a-form-model-item>
        <a-form-model-item required prop="password">
          <a-input v-model="model.password" size="large" type="password" autocomplete="false" placeholder="请输入密码">
            <img slot="prefix" src="@/assets/login3/pass-logo.png"/>
          </a-input>
        </a-form-model-item>
       <a-row :gutter="0">
          <template v-if="showCode">
	          <a-col :span="14">
	            <a-form-model-item required prop="inputCode">
	              <a-input v-model="model.inputCode" size="large" type="text" placeholder="请输入验证码">
	                <img slot="prefix" src="@/assets/login3/capt-logo.png" style="margin-right: 10px;"/>
	              </a-input>
	            </a-form-model-item>
	          </a-col>
	          <a-col :span="8" :offset="1" style="text-align: right">
	            <img v-if="requestCodeSuccess" style="margin-top: 2px;" :src="randCodeImage" @click="handleChangeCheckCode"/>
	            <img v-else style="margin-top: 2px;" src="@/assets/checkcode.png" @click="handleChangeCheckCode"/>
	          </a-col>
           </template>
       </a-row>
      </a-form-model>
      <a-modal :visible="secondCheckModalVisible" @ok="handleFinalLogin" @cancel="handleSecondCheckModalClose">
        <a-row :gutter="0">
           <a-col :span="22">
             <a-form-item required prop="inputCode">
               <a-input v-model="userPhone" size="large" type="text" disabled>
                 <img slot="prefix"  src="@/assets/login3/user-logo.png" style="margin-right: 10px;"/>
               </a-input>
             </a-form-item>
           </a-col>
        </a-row>
        <a-row :gutter="0">
           <a-col :span="14">
             <a-form-item required prop="inputCode">
               <a-input v-model="smsCode" size="large" type="text" placeholder="请输入验证码">
                 <img slot="prefix" src="@/assets/login3/capt-logo.png" style="margin-right: 10px;"/>
               </a-input>
             </a-form-item>
           </a-col>
           <a-col :span="8" style="text-align: right">
             <a-button type="primary" style="width: 90%;" @click="handleSendMsg" size="large" :disabled="timer < 60">{{ timer < 60 ? timer + 's' : '获取验证码'}}</a-button>
           </a-col>
        </a-row>
      </a-modal>
      <user-password ref="userPassword" @success="loginSelectOk(2)"></user-password>
    </div>
</template>

<script>
  import { ACCESS_TOKEN, USER_NAME,USER_INFO,UI_CACHE_DB_DICT_DATA } from "@/store/mutation-types"
  import {  ENCRYPTED_STRING } from '@/store/mutation-types'
  import { getAction, postAction } from '@/api/manage'
  import { encryption ,getEncryptedString} from '@/utils/encryption/aesEncrypt'
  import Vue from 'vue'
  import { mapActions } from 'vuex'
  import { welcome } from "@/utils/util"
  import UserPassword from '@/components/tools/UserPassword.vue'

  export default {
    name: 'LoginAccount',
    components: {
      UserPassword,
    },
    data(){
      return {
        requestCodeSuccess: false,
        randCodeImage: '',
        currdatetime: '',
        loginType: 0,
        showCode: true,
        userPhone: '',
        msgId: '',
        smsCode: '',
        timer: 60,
        secondCheckModalVisible: false,
        model:{
          username: '',
          password: '',
          inputCode: ''
        },
        validatorRules:{
          username: [
            { required: true, message: '请输入用户名!' },
            { validator: this.handleUsernameOrEmail }
          ],
          password: [{
            required: true, message: '请输入密码!', validator: 'click'
          }]
        ,
          inputCode: [{
            required: true, message: '请输入验证码!'
          }]
        },
        encryptedString:{
            key:"",
            iv:"",
          },

      }
    },
    created() {
      this.handleChangeCheckCode();
      this.getEncrypte();
    },
    methods:{
      ...mapActions(['Login']),
      /**刷新验证码*/
      handleChangeCheckCode(){
        this.currdatetime = new Date().getTime();
        this.model.inputCode = ''
        getAction(`/sys/randomImage/${this.currdatetime}`).then(res=>{
          if(res.success){
            this.randCodeImage = res.result
            this.requestCodeSuccess=true
          }else{
            this.$message.error(res.message)
            this.requestCodeSuccess=false
          }
        }).catch(()=>{
          this.requestCodeSuccess=false
        })
      },
      // 判断登录类型
      handleUsernameOrEmail (rule, value, callback) {
        const regex = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
        if (regex.test(value)) {
          this.loginType = 0
        } else {
          this.loginType = 1
        }
        callback()
      },
      /**
       * 验证字段
       * @param arr
       * @param callback
       */
      validateFields(arr, callback){
        let promiseArray = []
        for(let item of arr){
          let p = new Promise((resolve, reject) => {
            this.$refs['form'].validateField(item, (err)=>{
              if(!err){
                resolve();
              }else{
                reject(err);
              }
            })
          });
          promiseArray.push(p)
        }
        Promise.all(promiseArray).then(()=>{
          callback()
        }).catch(err=>{
          callback(err)
        })
      },
      acceptUsername(username){
        this.model['username'] = username
      },
      //账号密码登录
      handleLogin(rememberMe){
        this.validateFields([ 'username', 'password'], (err)=>{
          if(!err){
        	let that = this;
            let loginParams = {
              username: this.model.username,
//               password: this.model.password,
              password: encryption(this.model.password,that.encryptedString.key,that.encryptedString.iv),
              captcha: this.model.inputCode,
              checkKey: this.currdatetime,
              remember_me: rememberMe,
            }
            this.Login(loginParams).then((res) => {
              this.$emit('closeBtnLoaing')
              if (res.result.force === 1) {
                this.$refs.userPassword.show(res)
              } else {
                this.secondCheckModalVisible = true;
                this.userPhone = res.result.phone;
                this.msgId = res.result.id;
              }
            }).catch((err) => {
              if(err.code == 9999){
                  that.showCode =true;
                  this.handleChangeCheckCode();
              }
              this.$emit('fail', err)
            });
          }else{
            this.$emit('validateFail')
          }
        })
      },
      //获取密码加密规则
      getEncrypte(){
        var encryptedString = Vue.ls.get(ENCRYPTED_STRING);
//         alert(JSON.stringify(encryptedString))
        if(encryptedString == null){
          getEncryptedString().then((data) => {
            this.encryptedString = data
          });
        }else{
          this.encryptedString = encryptedString;
        }
      },
      handleSendMsg() {
        postAction('/pub/jky/sms/send', {
          id: this.msgId
        }).then((res) => {
          if (res.success) {
            this.$message.success('验证码已发送！')
            this.countTimer();
          } else {
            this.$message.error(res.message)
          }
        })
      },
      handleFinalLogin() {
        postAction('/pub/jky/sms/login', {
          id: this.msgId,
          smsCode: this.smsCode
        }).then((res) => {
          if (res.success) {
            const result = res.result
            const userInfo = result.userInfo
            Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
            Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)
            Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
            Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)
            this.$store.commit('SET_TOKEN', result.token)
            this.$store.commit('SET_INFO', userInfo)
            this.$store.commit('SET_NAME', { username: userInfo.username,realname: userInfo.realname, welcome: welcome() })
            this.$store.commit('SET_AVATAR', userInfo.avatar)
            this.$emit('success', result)
            this.handleSecondCheckModalClose()
          } else {
            this.$message.error(res.message);
            this.$emit('fail', res)
          }
        }).finally(() => {
          this.$emit('closeBtnLoaing')
        })
      },
      handleSecondCheckModalClose() {
        this.secondCheckModalVisible = false;
        this.userPhone = '';
        this.msgId = '',
        this.smsCode = ''
        this.$emit('closeBtnLoaing')
      },
      countTimer() {
        let count = setInterval(() => {
          if (this.timer > 0) {
            this.timer = this.timer - 1;
          } else {
            this.timer = 60;
            clearInterval(count);
            count = null;
          }
        }, 1000)
      },
    }

  }
</script>

<style lang="less" scoped>
  /deep/.ant-input {
    padding-left: 40px!important;
    border:1px solid #BFBFBF;
  }
  /deep/.ant-input-prefix img {
    width: 25px;
  }

</style>