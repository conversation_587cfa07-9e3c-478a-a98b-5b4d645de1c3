// import {formMixins} from '@/views/businessCom/desensitizationInput/formMixins.js'
// mixins: [formMixins],

export const formMixins = {
  data() {
    return {
      desensitList: [
        // 公路养护企业信息
        'linkman', // 联系人姓名
        'linkmanDept', // 联系人所在部门
        'linkmanZw', // 联系人职务
        'linkmanMobile', // 联系人移动电话
        'linkmanFax', // 联系人传真
        'tel', // 联系人办公电话
        'fddbr', // 法定代表人
        'fddbrsfz', // 法定代表人身份证号码
        'fddbrtel', // 法定代表人联系电话
        'postcode', // 邮编
        'mainLeaderId', // 主要负责人身份证号
        'mainLeader', // 主要负责人姓名
        'actualLeaderId', // 实际控制人身份证号
        'actualLeader', // 实际控制人姓名
        // 公路养护业主信息
        'email', // 邮箱
        'phone', // 手机
        'fax', // 企业传真
        // 港口安评机构基本信息
        'address', // 注册详细地址
        'frdb', // 法人代表
        'frdbIdcard', // 法人代表身份证
        'frdbPhone', // 法人代表联系电话
        'lxrName', // 联系人姓名
        'lxrDepart', // 联系人所在部门
        'lxrJob', // 联系人职务
        'lxrPhone', // 联系人移动电话
        'lxrCz', // 联系人传真
        'zyfzrName', // 主要负责人姓名
        'zyfzrIdcard', // 主要负责人身份证
        'sjkzrName', // 实际控制人姓名
        'sjkzrIdcard', // 实际控制人身份证
        '',
        '',
        ''
      ],
      sourceValue: '' // 数据源
    }
  },
  mounted() {
    const self = this
    if (this.edit) {
      this.edit = new Proxy(this.edit, {
        apply: function(target, Object, args) {
          // 值含*的为脱敏字段，进行拷贝数据
          let copyData = JSON.parse(JSON.stringify(args[0]))
          const sourceValue = {}
          for (const key in copyData) {
            const _data = copyData[key]
            if (_data && String(_data).includes('*')) {
              sourceValue[key] = _data
            }
          }
          self.sourceValue = sourceValue
          console.log(self.sourceValue, args[0], '我是数据坎坎坷坷')
          return target(...args)
        }
      })
    }
    if (this.submitForm) {
      this.submitForm = new Proxy(this.submitForm, {
        apply: function(target, Object, args) {
          if (self.model.id) {
            console.log(self.submitFormBefore, '8888iiii')
            if (self.submitFormBefore) {

              const result = self.submitFormBefore()
              console.log(result)
              if (!result) return
              self.validateDesensit()
            } else {
              self.validateDesensit()
            }
          }
          return target(...args)
        }
      })
    }
  },
  methods: {
    // 校验脱敏字段是否变更，不变更去除必填校验，字段不上传
    validateDesensit() {
      console.log(this.sourceValue, '我是校验字段')
      for (const key in this.sourceValue) {
        if (this.model[key] && this.model[key] === this.sourceValue[key]) {
          console.log(this.model[key])
          console.log(this.validatorRules[key])
          delete this.validatorRules[key]
            this.model[key] = null
        }
      }
      // this.desensitList.map(item => {
      //   if (this.model[item]) {
      //     if (this.model[item] === this.sourceValue[item]) {
      //       delete this.validatorRules[item]
      //       this.model[item] = null
      //     }
      //     // else {
      //     //   this.model[`isSave_${item}`] = true
      //     // }
      //   }
      // })
    }
  }
}

