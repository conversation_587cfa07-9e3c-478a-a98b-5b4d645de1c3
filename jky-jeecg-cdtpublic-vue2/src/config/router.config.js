import { UserLayout, TabLayout, RouteView, BlankLayout, PageView } from '@/components/layouts'

/**
 * 基础路由
 * @type { *[] }
 */
export var constantRouterMap = [
  {
    path: '/user',
    component: BlankLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/register/RegisterResult')
      },
      {
        path: 'alteration',
        name: 'alteration',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/alteration/Alteration')
      },

    ]
  },
  {
    // OAuth2 APP页面路由
    path: '/oauth2-app',
    component: BlankLayout,
    redirect: '/oauth2-app/login',
    children: [
      {
        // OAuth2 登录路由
        path: 'login',
        name: 'oauth2-app-login',
        component: () => import(/* webpackChunkName: "oauth2-app.login" */ '@/views/user/oauth2/OAuth2Login')
      },
    ]
  },

  {
    path: '/test',
    component: TabLayout,
    redirect: '/test/home',
    children: [
      {
        path: 'home',
        name: 'TestHome',
        component: () => import('@/views/Home')
      },
      {
        path: 'documentList',
        name: 'DocumentList',
        component: () => import('@/views/documentList/Index')
      }
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },
	// {
	//   path: '/dashboard/analysis',
	//   name: 'dashboard',
	//   component: () => import( '@/views/dashboard/Analysis'),
	//   meta: { title: '首页' },
	// },
	// {
	//   path: '/processManagement/classRecordDetailList',
	//   name: 'ClassRecordDetailList',
	//   component: () => import( '@/views/processManagement/ClassRecordDetailList'),
	//   meta: { title: '电子教学日志' },
	// },
	// {
	//   path: '/processManagement/stageTrainningTimeList',
	//   name: 'StageTrainningTimeList',
	//   component: () => import( '@/views/processManagement/StageTrainningTimeList'),
	//   meta: { title: '阶段报审申请' },
	// },
	// {
	//   path: '/processManagement/studentinfoEnterList',
	//   name: 'StudentinfoEnterList',
	//   component: () => import( '@/views/processManagement/StudentinfoEnterList'),
	//   meta: { title: '在线报名学员' },
	// },
	// {
	//   path: '/processManagement/studentinfoList',
	//   name: 'StudentinfoList',
	//   component: () => import( '@/views/processManagement/StudentinfoList'),
	//   meta: { title: '学员信息管理' },
	// },
	// {
	//   path: '/processManagement/totalTimeList',
	//   name: 'TotalTimeList',
	//   component: () => import( '@/views/processManagement/TotalTimeList'),
	//   meta: { title: '学员学时汇总' },
	// },


]

/**
 * 走菜单，走权限控制
 * @type {[null,null]}
 */
export var asyncRouterMap = [

  {
    path: '/',
    name: 'dashboard',
    component: TabLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: [

    ]
  },
  {
    path: '*', redirect: '/404', hidden: true
  }
]